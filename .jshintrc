{
  // Details: https://github.com/victorporof/Sublime-JSHint#using-your-own-jshintrc-options
  // Example: https://github.com/jshint/jshint/blob/master/examples/.jshintrc
  // Documentation: http://www.jshint.com/docs/options/
  "globals": {
      "require": true,
      "Db": true,
      "squel": true,
      "module": true,
      "async": true,
      "__filename": true,
      "loggers": true,
      "purchaseDataManager": true,
      "_": true,
      "notificationFormatter": true,
      "sails": true,
      "LocationService": true,
      "eventNotifications": true,
      "manageTeamsEntranceService": true,
      "EmailService": true,
      "__dirname": true,
      "teamsEntranceErrorHandler": true,
      "process": true,
      "cronNotifications": true,
      "angular": true,
      "console": true,
      "$": true,
      "OfficialsService": true,
      "WebpointQueue": true,
      "WebpointDataReceiver": true,
      "callerDataService": true,
      "StripeService": true,
      "WebpointUtility": true,
      "XLSXService": true,
      "CDHashService": true,
      "ErrorSender": true,
      "TicketsCron": true,
      "EventUtils": true,
      "staticHashService": true,
      "PGTriggersService": true,
      "PGNotificationService": true,
      "Buffer": true,
      "ClubMessageSender": true,
      "Stripe": true,
      "RosterSnapshotService": true,
      "TeamsEntryInvoiceService": true,
      "HousingService": true,
      "CKEDITOR": true,
      "eventOwnerService": true,
      "TicketsStatisticsService": true,
      "RedisService": true,
      "confirm": true,
      "CheckInRosterService": true,
      "campsStatisticsService": true,
      "UserService": true,
      "PaymentService": true
  },
  "asi": true,
  "lastsemic": true,
  "esversion": 6,
  "laxcomma": true,
  "laxbreak": true,
  "maxerr": 1000,
  "multistr": true,
  "-W041": false
}
