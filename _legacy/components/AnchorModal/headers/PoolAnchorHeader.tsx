import { modalTeamIdSignal } from '@/signals/modalTeamIdSignal';
import closeIcon from '@assets/close-black-icon.svg';

import { NextPrevPool } from '@/generated/graphql';

import {
	AnchorModal__HeaderCloseButton,
	AnchorPoolModal__Header,
	AnchorPoolModal__HeaderTitle,
	AnchorPoolModal__HeaderWrapper,
	AnchorPoolModal__Nav,
	StyledAnchorPoolModalPrevButton,
	StyledAnchorPoolModalPrevButtonWrapper,
} from '../styled';
import { PoolBracketUUID } from '../types';
import prevIcon from './img/prev.icon.svg';

type PropsT = {
	close: (_isButtonClosed?: boolean, _isBackButton?: boolean) => void;
	name: string;
	next?: NextPrevPool | null;
	prev?: NextPrevPool | null;
	updatePoolData: (_poolData: PoolBracketUUID) => void;
	refreshButton?: () => JSX.Element;
};

export const PoolAnchorHeader = ({
	close,
	name,
	prev,
	next,
	updatePoolData,
	refreshButton,
}: PropsT) => {
	return (
		<AnchorPoolModal__Header>
			<AnchorPoolModal__HeaderWrapper>
				{modalTeamIdSignal.value && (
					<StyledAnchorPoolModalPrevButtonWrapper>
						<StyledAnchorPoolModalPrevButton onClick={() => close(false, true)}>
							<img src={prevIcon} alt="" />
						</StyledAnchorPoolModalPrevButton>
					</StyledAnchorPoolModalPrevButtonWrapper>
				)}
				<AnchorPoolModal__HeaderTitle>{name}</AnchorPoolModal__HeaderTitle>
				{refreshButton && refreshButton()}
			</AnchorPoolModal__HeaderWrapper>
			<AnchorModal__HeaderCloseButton onClick={() => close(true)}>
				<img src={closeIcon} alt="" />
			</AnchorModal__HeaderCloseButton>
			<AnchorPoolModal__Nav>
				{prev ? (
					<span onClick={() => updatePoolData({ is_pool: !!prev.is_pool, uuid: prev.uuid! })}>
						&lt;{prev.name}{' '}
					</span>
				) : (
					<span>&nbsp;</span>
				)}
				{next && (
					<span onClick={() => updatePoolData({ is_pool: !!next.is_pool, uuid: next.uuid! })}>
						{next.name} &gt;
					</span>
				)}
			</AnchorPoolModal__Nav>
		</AnchorPoolModal__Header>
	);
};
