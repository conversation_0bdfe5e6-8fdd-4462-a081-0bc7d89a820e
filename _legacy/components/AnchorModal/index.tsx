import { bracketGridMatchNameSignal } from '@/signals/bracketGridMatchNameSignal';
import { useEffect } from 'react';
import ReactDOM from 'react-dom';
import { StringParam, useQueryParams } from 'use-query-params';

import { DivisionPool, TeamSingleQuery } from '@/generated/graphql';

import { BracketModal } from '../BracketModal';
import { Loader } from '../Loader';
import { useAnchorModal } from './hooks/useAnchorModal';
import {
	AnchorModal__AllTabsWrapper,
	AnchorModal__Anchor,
	AnchorModal__ContentWrapper,
	AnchorModal__Nav,
	AnchorModal__Wrapper,
} from './styled';

type PropsT = {
	close: () => void;
	teamPoolData: TeamSingleQuery;
	poolOfTeam?: DivisionPool | null;
	isPool?: boolean;
	teamRefetch?: () => void;
	matchesRefetch?: () => void;
};

export const AnchorModal = ({
	close,
	teamPoolData,
	poolOfTeam,
	isPool,
	teamRefetch,
	matchesRefetch,
}: PropsT) => {
	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	const closeTeamModal = () => {
		setQueryParams({ divisionId: null });
		close();
	};
	const {
		activeSection,
		scrollToSection,
		wrapper,
		sectionRefs,
		PREFIX,
		tabs,
		header,
		isTeamModal,
		isLoading,
		isLarge,
		currentBracket,
		isShowBracketModal,
		closeBracketModal,
		rounds,
	} = useAnchorModal({
		closeTeamModal,
		teamPoolData,
		poolOfTeam,
		isPool,
		teamRefetch,
		matchesRefetch,
	});

	const mount = document.getElementById('portal')!;
	const overlay = document.createElement('div');
	overlay.setAttribute('id', 'overlay');
	overlay.addEventListener('click', close);
	useEffect(() => {
		document.body.appendChild(overlay);
		return () => {
			document.body.removeChild(overlay);
			overlay.removeEventListener('click', close);
		};
	}, [close, mount, overlay]);

	useEffect(() => {
		return () => {
			bracketGridMatchNameSignal.value = '';
		};
	}, []);

	const renderMobile = () => {
		return (
			<>
				{isLoading && <Loader />}

				{isShowBracketModal && currentBracket && !currentBracket.is_pool && !isTeamModal ? (
					<BracketModal
						isShowOverlay={false}
						poolOfTeam={currentBracket}
						bracketRounds={rounds.flat()}
						poolId={currentBracket.uuid!}
						close={closeBracketModal}
					/>
				) : (
					<AnchorModal__Wrapper ref={wrapper} $isTeamModal={isTeamModal}>
						{header}
						<AnchorModal__Nav>
							{Object.keys(tabs).map((anchor, index) => (
								<AnchorModal__Anchor
									$isActive={activeSection === index}
									key={index}
									href={`#${PREFIX}${index + 1}`}
									onClick={(e) => {
										e.preventDefault();
										scrollToSection(index);
									}}
								>
									{anchor}
								</AnchorModal__Anchor>
							))}
						</AnchorModal__Nav>
						<AnchorModal__AllTabsWrapper>
							{Object.values(tabs).map((content, index) => (
								<AnchorModal__ContentWrapper
									key={index}
									id={`${PREFIX}${index + 1}`}
									ref={(el) => (sectionRefs.current[index] = el)}
								>
									{content}
								</AnchorModal__ContentWrapper>
							))}
						</AnchorModal__AllTabsWrapper>
					</AnchorModal__Wrapper>
				)}
			</>
		);
	};

	const renderDesktop = (child: JSX.Element) => {
		const portalRoot = document.getElementById('portal');
		if (!portalRoot) {
			console.error("Portal root element with id 'portal-root' not found.");
			return null;
		}

		return ReactDOM.createPortal(<div id="modal">{child}</div>, portalRoot);
	};

	if (isLarge) {
		return renderDesktop(renderMobile());
	}

	return renderMobile();
};
