import styled from 'styled-components';

const CONTENT_PADDING_BOTTOM = 44;
export const AnchorModal__Wrapper = styled.div<{ $isTeamModal?: boolean }>`
	position: fixed;
	width: 100%;
	height: 100%;
	background: #fff;
	outline: none;
	top: 0;
	left: 0;
	overflow-y: scroll;
	z-index: 999;
	padding-top: 90px;
`;
export const AnchorTeamModal__Header = styled.div<{ $isShowStreamForMobile?: boolean }>`
	padding: ${({ $isShowStreamForMobile }) =>
		$isShowStreamForMobile ? '15px 20px 9px 46px' : '30px 20px 9px 46px'};
	border-bottom: 1px solid #f4f6f8;
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	background: #fff;
	z-index: 9;
`;
export const AnchorPoolModal__Header = styled.div`
	padding: 16px 16px 16px 16px;
	border-bottom: 1px solid #f4f6f8;
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	background: #fff;
	z-index: 9;
`;

export const AnchorPoolModal__HeaderTitle = styled.div`
	font-size: 14px;
	font-weight: 700;
	line-height: 22px;
	text-align: center;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
	}
`;
export const AnchorPoolModal__Nav = styled.div`
	display: flex;
	justify-content: space-between;
	span {
		line-height: 18px;
		font-size: 14px;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			font-weight: 400;
			line-height: 22px;
		}
		cursor: pointer;
		&:hover {
			text-decoration: underline;
		}
	}
`;
export const AnchorTeamModal__HeaderTeamName = styled.span`
	font-size: 14px;
	line-height: 22px;
	position: relative;
	margin: 0 0 3px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 700;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 20px;
		line-height: 27px;
	}
	img {
		position: absolute;
		left: -32px;
		top: 0px;
	}
`;
export const AnchorTeamModal__HeaderTeamCode = styled.span`
	color: #637381;
	font-size: 14px;
	line-height: 18px;
	font-weight: 400;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		line-height: 24px;
	}
`;
export const AnchorTeamModal__HeaderTeamStreamLinkWrapper = styled.div`
	display: flex;
	align-items: center;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		justify-content: space-between;
		margin-top: 5px;
	}
	gap: 20px;
	img {
		position: static;
	}
`;
export const AnchorTeamModal__HeaderTeamStreamLink = styled.a`
	position: relative;
	color: ${(props) => props.theme.colors.blue};
	font-size: 14px;
	font-weight: 400;
	line-height: 20px;
	text-decoration: none;
	display: flex;
	align-items: center;
	flex-direction: row-reverse;
	gap: 5px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		flex-direction: row;
	}
	&:hover {
		text-decoration: underline;
	}
	img {
		position: static;
	}
`;
export const AnchorTeamModal__HeaderClubName = styled.p`
	color: #637381;
	font-size: 14px;
	line-height: 18px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		line-height: 24px;
	}
`;

export const AnchorModal__Nav = styled.nav`
	background: #fff;
	padding: 0 10px;
	height: ${CONTENT_PADDING_BOTTOM}px;
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 60px;
	border-top: 1px solid #f4f6f8;
	font-size: 14px;
	z-index: 9;
	font-weight: 700;
	line-height: 22px;
`;
export const AnchorModal__Anchor = styled.a<{ $isActive: boolean }>`
	text-decoration: none;
	font-size: 14px;
	position: relative;
	background: #fff;
	font-weight: normal;
	color: #637381;
	font-weight: ${({ $isActive }) => ($isActive ? 700 : 400)};
	&:focus,
	&:active {
		background: inherit !important;
		color: inherit !important;
	}
	&::before {
		content: '';
		height: 2px;
		background: ${({ $isActive }) =>
			$isActive ? (props) => props.theme.colors.blue : 'transparent'};
		width: 100%;
		position: absolute;
		left: 0;
		bottom: -10px;
		border-radius: 2px;
	}
`;
export const AnchorModal__ContentWrapper = styled.div``;
export const AnchorModal__ContentItem = styled.li`
	display: flex;
	width: 100%;
	justify-content: space-between;
`;
export const AnchorModal__ContentLeft = styled.ul`
	width: 50%;
`;
export const AnchorModal__ContentRight = styled.ul`
	width: 50%;
	white-space: nowrap;
	overflow-x: scroll;
	li {
		min-height: 51px;
		min-width: 100%;
		width: fit-content;
	}
`;
export const AnchorModal__AllTabsWrapper = styled.div`
	padding: 10px 0 40px;
`;

const getTopForCloseButton = ({
	$isTeamModal,
	$isShowStreamForMobile,
}: {
	$isTeamModal?: boolean;
	$isShowStreamForMobile?: boolean;
}) => {
	if ($isTeamModal && !$isShowStreamForMobile) {
		return 34;
	}
	if ($isTeamModal && $isShowStreamForMobile) {
		return 16;
	}
	return 15;
};
export const AnchorModal__HeaderCloseButton = styled.div<{
	$isTeamModal?: boolean;
	$isShowStreamForMobile?: boolean;
}>`
	position: absolute !important;
	cursor: pointer;
	right: 10px;
	top: ${({ $isTeamModal, $isShowStreamForMobile }) =>
		getTopForCloseButton({ $isTeamModal, $isShowStreamForMobile }) + 'px'};
	&:hover img {
		transform: rotate(360deg);
		transition: all 0.4s ease-in-out;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		border-radius: 4px;
		width: 34px;
		height: 34px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #fff;
		box-shadow: 0px 11.619px 124.28px 0px rgba(37, 72, 153, 0.17);
		position: absolute;
		top: -43px;
		right: -49px;
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
		right: 0;
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		right: 15px;
		top: ${({ $isTeamModal, $isShowStreamForMobile }) =>
			getTopForCloseButton({ $isTeamModal, $isShowStreamForMobile }) + 'px'};
	}
`;

export const StyledAnchorModalRefreshButton = styled.button<{
	$isTeamModal?: boolean;
	$isShowStreamForMobile?: boolean;
}>`
	border: none;
	background: none;
	width: 18px;
	height: 18px;
	z-index: 10;
	cursor: pointer;
	&:focus {
		outline: none;
	}

	&:hover img {
		transform: rotate(360deg);
		transition: all 0.4s ease-in-out;
	}
`;
export const AnchorPoolModal__HeaderWrapper = styled.div`
	display: flex;
	position: relative;
	margin: 0 0 16px;
	align-items: center;
	justify-content: center;
	gap: 5px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		${StyledAnchorModalRefreshButton} {
			position: absolute;
			right: 0;
		}
	}
`;
export const StyledAnchorPoolModalPrevButtonWrapper = styled.div`
	position: absolute;
	left: 0;
	z-index: 10;
	width: 18px;
	height: 18px;
`;
export const StyledAnchorPoolModalPrevButton = styled.button`
	border: none;
	background: none;
	cursor: pointer;
`;
