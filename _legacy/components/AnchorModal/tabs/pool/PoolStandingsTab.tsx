import { useFavoriteTeams } from '@/shared/hooks/useFavoriteTeams';
import { addOrdinalSuffix } from '@/utils';
import favoriteIcon from '@assets/favoriteCheckedSmall-icon.svg';
import { Fragment } from 'react/jsx-runtime';

import { PoolStandings } from '@/generated/graphql';

import { StandingsTab__Container, StandingsTab__FavoriteIcon, Tab__Wrapper } from '../styled';
import { EmptyTableRow } from './components/EmptyTableRow';

type PropsT = {
	standingList: PoolStandings[];
};

export const PoolStandingsTab = ({ standingList }: PropsT) => {
	const { pb_stats } = standingList[0];
	const { favoriteTeamsIds } = useFavoriteTeams();

	return (
		<Tab__Wrapper>
			<StandingsTab__Container>
				<table>
					<thead>
						<tr>
							<td colSpan={2}>Standings</td>
							<td>Match W-L</td>
							<td>Set W-L</td>
							<td>Point%</td>
						</tr>
					</thead>
					<tbody>
						{[...pb_stats]
							.sort((a, b) => Number(a.rank! || 0) - Number(b.rank! || 0))
							.map((item) => {
								return (
									<Fragment key={item.team_id}>
										<EmptyTableRow />
										<tr>
											<td>{!!Number(item.rank) && addOrdinalSuffix(Number(item.rank))}</td>
											<td>
												{favoriteTeamsIds.includes(item.team_id!) && (
													<StandingsTab__FavoriteIcon src={favoriteIcon} alt="" />
												)}
												{item.name ? item.name : '-'}
											</td>
											<td>
												{item.matches_won}-{item.matches_lost}
											</td>
											<td>
												<b>
													{item.sets_won}-{item.sets_lost}{' '}
												</b>
												({Math.round(item.sets_pct!)}%)
											</td>
											{item.points_ratio ? <td>{item.points_ratio.toFixed(3)}</td> : <td>-</td>}
										</tr>
									</Fragment>
								);
							})}
					</tbody>
				</table>
			</StandingsTab__Container>
		</Tab__Wrapper>
	);
};
