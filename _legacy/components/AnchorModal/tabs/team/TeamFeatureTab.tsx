import { addOrdinalSuffix } from '@/utils';
import arrowRight from '@assets/arrowRightBlue-icon.svg';
import { Fragment } from 'react';

import { ModalTitle } from '@/components/ModalTitle';

import { DivisionPool, EventUpcoming } from '@/generated/graphql';

import { getFormateTime } from '@/utils/time';

import {
	AnchorModal__ContentItem,
	AnchorModal__ContentLeft,
	AnchorModal__ContentRight,
} from '../../styled';
import { PoolBracketUUID } from '../../types';
import {
	FeatureTab__Container,
	ScheduleFeatureTab__Item,
	ScheduleFeatureTab__List,
	Tab__NextModalLink,
	Tab__Wrapper,
} from '../styled';

type PropsT = {
	openPoolModal: (_data?: PoolBracketUUID) => void;
	teamList: EventUpcoming[];
	poolOfTeam?: DivisionPool | null;
};

export const TeamFeatureTab = ({ teamList, openPoolModal }: PropsT) => {
	return (
		<Tab__Wrapper>
			<FeatureTab__Container>
				<ModalTitle title="Future" />
				<Tab__NextModalLink>
					<span
						onClick={() => {
							openPoolModal(
								teamList[0]
									? {
											uuid: teamList[0].pool_bracket_id || '',
											is_pool: !!teamList[0].is_pool || false,
										}
									: undefined,
							);
						}}
					>
						{teamList[0].round_name} {teamList[0].pb_name}
					</span>
					<img src={arrowRight} alt="" />
				</Tab__NextModalLink>
				<ScheduleFeatureTab__List>
					<AnchorModal__ContentItem>
						<AnchorModal__ContentLeft>
							{teamList.map((data, index) => {
								return (
									<Fragment key={`anchor_${index}_${data.match_id}`}>
										<ScheduleFeatureTab__Item>
											<span>
												vs {addOrdinalSuffix(index + 1)} {data.display_name}
											</span>
										</ScheduleFeatureTab__Item>
										{data.match_id && (
											<ScheduleFeatureTab__Item $isWork>
												<span>vs {addOrdinalSuffix(index + 1)} Work</span>
											</ScheduleFeatureTab__Item>
										)}
									</Fragment>
								);
							})}
						</AnchorModal__ContentLeft>
						<AnchorModal__ContentRight>
							{teamList.map((data, index) => {
								return (
									<Fragment key={`anchor_${index}_${data.match_id}`}>
										<ScheduleFeatureTab__Item>
											<span>
												{data.date_start &&
													`${getFormateTime({
														time: data.date_start!,
														format: 'EEE, h:mmaaa',
													})}-${data.court_name}`}
											</span>
										</ScheduleFeatureTab__Item>
										{data.match_id && (
											<ScheduleFeatureTab__Item $isWork>
												<span>
													{data.date_start &&
														`${getFormateTime({
															time: data.date_start!,
															format: 'EEE, h:mmaaa',
														})}-${data.court_name}`}
												</span>
											</ScheduleFeatureTab__Item>
										)}
									</Fragment>
								);
							})}
						</AnchorModal__ContentRight>
					</AnchorModal__ContentItem>
				</ScheduleFeatureTab__List>
			</FeatureTab__Container>
		</Tab__Wrapper>
	);
};
