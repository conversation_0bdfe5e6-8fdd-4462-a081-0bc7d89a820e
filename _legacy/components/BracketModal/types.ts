import { BracketMatchResults } from '@/generated/graphql';

import { IMatch } from './components/BracketSingle/interfaces/match.interface';

interface IColumnTemplate {
	columnIndex: number;
	id: string;
	name: string;
}

interface IMatchParticipantsTemplate {
	id: string;
	name: string;
	isByes?: boolean;
}
export interface IMatchTemplate {
	show_previously_accepted_bid_team1: string | null;
	show_previously_accepted_bid_team2: string | null;
	columnId: string;
	columnIndex: number;
	description: string;
	id: string;
	isLoser: boolean;
	matchName: string;
	nextMatchId: string;
	ref_name: string;
	date_start: number;
	court_name: string;
	prevMatchId: string[];
	matchNumber: number;
	results: BracketMatchResults;
	participants: IMatchParticipantsTemplate[];
}
export interface IBracketTemplate {
	cancelationMatchesAreRight: boolean;
	created_at: number;
	updated_at: number;
	id: string;
	isHigherSeedsTeamsLogic: boolean;
	isLoser3dMatch: boolean;
	isLoser5dMatch: boolean;
	name: string;
	columns: IColumnTemplate[];
	matches: IMatchTemplate[];
}

export type IMatchData = IMatch & {
	results: BracketMatchResults;
	ref_name: string;
	court_name: string;
	date_start: number;
	show_previously_accepted_bid_team1: string | null;
	show_previously_accepted_bid_team2: string | null;
	source: {
		ref: {
			name: string;
		};
	};
};
