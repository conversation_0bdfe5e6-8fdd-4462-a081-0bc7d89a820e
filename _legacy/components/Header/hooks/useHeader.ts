import { useLocation, useParams } from 'react-router-dom';

export const useHeader = () => {
	const { pathname } = useLocation();
	const params = useParams();

	const getActiveTab = () => {
		const prefix = `/events/${params.id}`;
		if (
			`${prefix}/divisions/${params.divisionId}` ===
			pathname
				.split('/')
				.slice(0, pathname.split('/').length - 1)
				.join('/')
		) {
			return { title: 'Divisions', path: `${prefix}/divisions` };
		}
		if (pathname === `${prefix}/favorites`) {
			return { title: 'Favorites', path: '' };
		}
		if (pathname === `${prefix}/clubs-teams`) {
			return { title: 'Clubs & Teams', path: '' };
		}
		if (pathname === `${prefix}/schedule` || pathname === `${prefix}/court-grid`) {
			return { title: 'Court Grid', path: '' };
		}
		if (pathname === `${prefix}/teams`) {
			return { title: 'Teams', path: '' };
		}
		if (pathname === `${prefix}/roster`) {
			return { title: 'Roster', path: '' };
		}
		if (pathname === `${prefix}/athletes`) {
			return { title: 'Athletes', path: '' };
		}
		if (pathname === `${prefix}/divisions`) {
			return { title: 'Divisions', path: '' };
		}
		if (pathname === `${prefix}/qualified`) {
			return { title: 'Qualified Teams', path: '' };
		}
		if (pathname === `/events`) {
			return { title: 'All Events', path: '' };
		}
		if (pathname === `${prefix}/about`) {
			return { title: 'About', path: '' };
		}

		return { title: 'Sportwrench', path: '' };
	};

	return {
		activeTab: getActiveTab(),
	};
};
