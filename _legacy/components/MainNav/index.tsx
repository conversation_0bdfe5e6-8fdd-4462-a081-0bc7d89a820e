import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { Link, useLocation } from 'react-router-dom';

import clubsTeamsIcon from './img/clubs-teams-icon.svg';
import courtGridIcon from './img/court-grid-icon.svg';
import divisionsIcon from './img/divisions-icon.svg';
import favoritesIcon from './img/favorites-icon.svg';
import qualifiedIcon from './img/qualified-icon.svg';
import rosterIcon from './img/roster-icon.svg';
import { MainNav__Icon, MainNav__Item, MainNav__List, MainNav__Wrapper } from './styled';

export const MainNav = () => {
	const location = useLocation();
	const currentPath = location.pathname.split('/')[3];
	const { eswId, event } = useEventDetails();

	const isQualified = event?.is_with_prev_qual || false;
	const isHasRosters = !!event?.has_rosters;

	return (
		<MainNav__Wrapper>
			<MainNav__List>
				<MainNav__Item $isActive={currentPath === 'favorites'}>
					<MainNav__Icon src={favoritesIcon} alt="" />
					<Link to={`/events/${eswId}/favorites`}>Favorites</Link>
				</MainNav__Item>
				<MainNav__Item $isActive={currentPath === 'clubs-teams'}>
					<MainNav__Icon src={clubsTeamsIcon} alt="" />
					<Link to={`/events/${eswId}/clubs-teams`}>Clubs & Teams</Link>
				</MainNav__Item>
				{isHasRosters && (
					<MainNav__Item $isActive={currentPath === 'roster'}>
						<MainNav__Icon src={rosterIcon} alt="" />
						<Link to={`/events/${eswId}/roster`}>Roster</Link>
					</MainNav__Item>
				)}
				<MainNav__Item $isActive={currentPath === 'divisions'}>
					<MainNav__Icon src={divisionsIcon} alt="" />
					<Link to={`/events/${eswId}/divisions`}>Divisions</Link>
				</MainNav__Item>
				{isQualified && (
					<MainNav__Item $isActive={currentPath === 'qualified'}>
						<MainNav__Icon src={qualifiedIcon} alt="" />
						<Link to={`/events/${eswId}/qualified`}>Qualified</Link>
					</MainNav__Item>
				)}
				<MainNav__Item $isActive={currentPath === 'court-grid'}>
					<MainNav__Icon src={courtGridIcon} alt="" />
					<Link to={`/events/${eswId}/court-grid`}>Court Grid </Link>
				</MainNav__Item>
			</MainNav__List>
		</MainNav__Wrapper>
	);
};
