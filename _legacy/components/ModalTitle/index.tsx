import arrowBottom from './img/arrowBottom.svg';
import { Modal__AccordionButton, Modal__Title } from './styled';

type PropsT = {
	title: string;
	isShowAccordion?: boolean;
	isOpen?: boolean;
	setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>;
};
export const ModalTitle = ({ title, isShowAccordion, isOpen, setIsOpen }: PropsT) => {
	return (
		<Modal__Title>
			{title}{' '}
			{isShowAccordion && (
				<Modal__AccordionButton
					$isOpen={isOpen}
					src={arrowBottom}
					alt=""
					onClick={() => setIsOpen && setIsOpen(!isOpen)}
				/>
			)}
		</Modal__Title>
	);
};
