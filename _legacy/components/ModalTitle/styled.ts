import styled from 'styled-components';

import { ThemeT } from '@/styles/theme';

export const Modal__Title = styled.header<ThemeT>`
	font-size: 14px;
	font-weight: 700;
	line-height: 22px;
	background: ${(props) => props.theme.colors.blue};
	color: #fff;
	display: flex;
	align-items: center;
	padding: 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		font-weight: 700;
		line-height: 24px;
	}
`;
export const Modal__AccordionButton = styled.img<{ $isOpen?: boolean }>`
	cursor: pointer;
	transform: ${({ $isOpen }) => (!$isOpen ? 'rotate(180deg)' : 'rotate(0)')};
`;
