import { useState } from 'react';

import bidEarned from './img/bidEarned.svg';
import previouslyQualifiedBadge from './img/previouslyQualifiedBadge.svg';
import {
	StyledBidEarnedInfoWrapper,
	StyledBidEarnedInfoWrapperOpen,
	StyledPreviouslyQualifiedInfoWrapper,
	StyledPreviouslyQualifiedInfoWrapperOpen,
} from './styled';
import { PrevQualifiedType } from './types';

type Props = {
	info?: string | null;
	type?: PrevQualifiedType;
};
export const PreviouslyQualifiedInfo = ({ info, type = 'previously_accepted_bid' }: Props) => {
	const [isOpen, setIsOpen] = useState(false);

	const showInfo = (e: React.MouseEvent<HTMLDivElement>) => {
		e.stopPropagation();
		setIsOpen(true);
	};
	const hideInfo = (e: React.MouseEvent<HTMLDivElement>) => {
		e.stopPropagation();
		setIsOpen(false);
	};

	if (type === 'previously_accepted_bid') {
		if (isOpen) {
			return (
				<StyledPreviouslyQualifiedInfoWrapperOpen onClick={hideInfo}>
					<p>Previously Qualified: {info}</p>
				</StyledPreviouslyQualifiedInfoWrapperOpen>
			);
		}
		return (
			<StyledPreviouslyQualifiedInfoWrapper onClick={showInfo}>
				<img src={previouslyQualifiedBadge} alt="" />
			</StyledPreviouslyQualifiedInfoWrapper>
		);
	}

	if (type === 'accepted_bid') {
		if (isOpen) {
			return (
				<StyledBidEarnedInfoWrapperOpen onClick={hideInfo}>
					<p>Bid Earned </p>
					<img src={bidEarned} alt="" />
				</StyledBidEarnedInfoWrapperOpen>
			);
		}
		return (
			<StyledBidEarnedInfoWrapper onClick={showInfo}>
				<img src={bidEarned} alt="" />
			</StyledBidEarnedInfoWrapper>
		);
	}

	return null;
};
