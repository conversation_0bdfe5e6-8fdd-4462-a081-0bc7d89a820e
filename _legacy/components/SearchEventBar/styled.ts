import styled from 'styled-components';

import { ThemeT } from '@/styles/theme';

export const SearchBar__Wrapper = styled.div<ThemeT & { $isGlobalSearch?: boolean }>`
	background: ${(props) => props.theme.colors.blue};
	padding: 16px;
	position: fixed;
	width: 100%;
	height: ${(props) => (props.$isGlobalSearch ? 132 : 110)}px;
	z-index: 9;
`;
export const SearchBar__EventInfo = styled.div<{ $isFullWidth?: boolean }>`
	width: ${({ $isFullWidth }) => ($isFullWidth ? '100%' : 'calc(100% - 100px)')};
`;
export const SearchBar__EventTitle = styled.p`
	font-size: 14px;
	line-height: 22px;
	color: #fff;
	font-weight: 700;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
`;
export const SearchBar__EventDescription = styled.p`
	color: #f9fafb;
	font-size: 12px;
	line-height: 18px;
	margin: 2px 0 0;
`;
export const SearchBar__FormWrapper = styled.div`
	display: flex;
	justify-content: space-between;
	position: relative;
`;
export const SearchBar__TextFieldWrapper = styled.div`
	width: 100%;
`;
export const SearchBar__TextFieldInner = styled.div`
	position: relative;
`;
export const SearchBar__TextField = styled.input<{ $isGlobalSearch?: boolean }>`
	border-radius: ${(props) => (props.$isGlobalSearch ? 8 : 4)}px;
	border: 1px solid #f9fafb;
	width: 100%;
	min-height: 40px;
	background: ${(props) => (props.$isGlobalSearch ? '#fff' : 'transparent')};
	padding: 0 40px 0 16px;
	font-size: 14px;
	line-height: 22px;
	color: ${(props) => (props.$isGlobalSearch ? props.theme.colors.main : '#f9fafb')};
	outline: none;
	caret-color: #f9fafb;
	&::placeholder {
		color: ${(props) => (props.$isGlobalSearch ? '#C4CDD5' : '#f9fafb')};
	}
`;
export const SearchBar__AdmissionTitle = styled.a`
	font-size: 10px;
	text-decoration: underline;
	line-height: 18px;
	color: #f9fafb;
	text-align: right;
	max-width: 90px;
	display: flex;
	align-items: center;
	gap: 4px;
	width: 100%;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		max-width: 140px;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
		img {
			width: 20px;
			height: 14px;
		}
	}
`;
export const SearchBar__SearchIcon = styled.img`
	position: absolute;
	right: 15px;
	top: 50%;
	transform: translateY(-50%);
`;
export const SearchBar__TitleWrapper = styled.div`
	display: flex;
	justify-content: space-between;
	margin: 0 0 16px;
	align-items: center;
`;
