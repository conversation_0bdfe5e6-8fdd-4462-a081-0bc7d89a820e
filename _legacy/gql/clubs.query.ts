import { gql } from '@apollo/client';

export const GET_PAGINATED_CLUBS_TEAMS = gql`
	query PaginatedClubsTeams($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
		paginatedClubs(eventKey: $eswId, page: $page, pageSize: $pageSize, search: $search) {
			items {
				roster_club_id
				club_name
				state
				club_code
				teams_count
				teams {
					team_id
					club_id
					team_name
					division_id
					division_name
					division_standing {
						matches_won
						matches_lost
						sets_won
						sets_lost
						seed
						rank
					}
					next_match {
						secs_start
						external {
							opponent_display_name
							court_info {
								short_name
							}
						}
					}
				}
			}
			page_info {
				page
				page_size
				page_count
				item_count
			}
		}
	}
`;
