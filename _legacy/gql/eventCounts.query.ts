import { gql } from '@apollo/client';

export const GET_EVENT_COUNTS = gql`
	query EventCounts($eswId: ID!) {
		paginatedAthletes(eventKey: $eswId, page: 1, pageSize: 1) {
			page_info {
				item_count
			}
		}
		paginatedStaff(eventKey: $eswId, page: 1, pageSize: 1) {
			page_info {
				item_count
			}
		}
		paginatedClubs(eventKey: $eswId, page: 1, pageSize: 1) {
			page_info {
				item_count
			}
		}
	}
`;
