import { gql } from '@apollo/client';

export const GET_POOLS = gql`
	query Pools($id: ID!, $divisionId: ID!) {
		pools(id: $id, divisionId: $divisionId) {
			uuid
			r_uuid
			r_name
			pb_name
			display_name
			division_short_name
			date_start
			court_start
			is_pool
			settings {
				PlayAllSets
				SetCount
				WinningPoints
			}
			teams {
				sets_pct
				opponent_team_name
				rank
				opponent_team_id
				matches_won
				matches_lost
				sets_won
				sets_lost
				opponent_organization_code
				points_ratio
				info {
					seed_current
				}
			}
		}
	}
`;
