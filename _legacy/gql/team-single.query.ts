import { gql } from '@apollo/client';

export const GET_TEAM_SINGLE = gql`
	query TeamSingle($id: ID!, $teamId: ID!) {
		teamSingle(id: $id, teamId: $teamId) {
			roster_team_id
			master_team_id
			division_id
			team_name
			matches_won
			matches_lost
			manual_club_name
			sets_won
			sets_lost
			organization_code
			points_won
			points_lost
			roster_club_id
			club_name
			state
			upcoming {
				match_type
				match_id
				unix_finished
				results
				display_name
				date_start_formatted
				date_start
				division_id
				division_short_name
				court_name
				pool_name
				pool_bracket_id
				is_pool
				opponent_team_name
				opponent_organization_code
				pb_name
				round_name
				footnote_play
				footnote_team1
				footnote_team2
				settings {
					SetCount
					PlayAllSets
					WinningPoints
				}
			}
			athletes {
				first
				last
				uniform
				short_position
				gradyear
				height
			}
			staff {
				first
				last
				sort_order
				role_name
			}
			results {
				uuid
				is_pool
				pb_name
				round_name
				sort_priority
				pb_stats {
					name
					rank
					team_id
					sets_pct
					sets_won
					sets_lost
					points_won
					matches_pct
					matches_won
					points_lost
					matches_lost
					points_ratio
				}
				matches {
					date_start
					date_start_formatted
					display_name
					division_id
					division_short_name
					match_id
					match_type
					opponent_organization_code
					opponent_team_id
					opponent_team_name
					pool_bracket_id
					unix_finished
					results {
						set1
						set2
						set3
						winner
						team1 {
							heading
							heading_sort
							matches_lost
							matches_pct
							matches_won
							overallSeed
							points_lost
							points_ratio
							points_won
							roster_team_id
							scores
							sets_lost
							sets_pct
							sets_won
							title
						}
						team2 {
							heading
							heading_sort
							matches_lost
							matches_pct
							matches_won
							overallSeed
							points_lost
							points_ratio
							points_won
							roster_team_id
							scores
							sets_lost
							sets_pct
							sets_won
							title
						}
					}
				}
			}
		}
	}
`;
