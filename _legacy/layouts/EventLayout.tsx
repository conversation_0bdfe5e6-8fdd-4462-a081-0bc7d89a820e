import { EventDetailsContext } from '@/shared/contexts/eventDetails.context.tsx';
import { eventSignal } from '@/signals/eventSignal';
import { useEventDetailsQuery } from '@generated/graphql.tsx';
import { useEffect } from 'react';
import { Outlet, useParams } from 'react-router-dom';

import { Event } from '@/generated/graphql';

export const EventLayout = () => {
	const eswId = useParams().id!;
	const { data, loading } = useEventDetailsQuery({
		skip: !eswId,
		variables: {
			eswId,
		},
	});

	const eventDetailsValue = {
		eswId,
		event: data?.event || null,
		divisions: data?.divisions || null,
		loading,
	};

	useEffect(() => {
		eventSignal.value = eventDetailsValue.event as Event;
	}, [eventDetailsValue.event]);

	return (
		<EventDetailsContext.Provider value={eventDetailsValue}>
			<Outlet />
		</EventDetailsContext.Provider>
	);
};
