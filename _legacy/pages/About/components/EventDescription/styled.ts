import styled from 'styled-components';

export const EventDescription__Wrapper = styled.div`
	padding-bottom: 100px;
`;
export const EventDescription__Header = styled.div`
	font-size: 14px;
	font-weight: 700;
	line-height: 18px;
	text-align: center;
	border-radius: 12px 12px 0 0;
	background: #f4f6f8;
	padding: 13px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		font-weight: 700;
		line-height: 24px;
		padding: 18px;
	}
`;
export const EventDescription__Content = styled.div`
	background: #fff;
	border-radius: 0 0 12px 12px;
	padding: 16px;
`;
export const EventDescription__Description = styled.p`
	font-size: 14px;
	line-height: 18px;
	margin-bottom: 16px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		line-height: 24px;
	}
`;
export const EventDescription__Footer = styled.footer``;
export const EventDescription__FooterTitle = styled.p`
	font-size: 14px;
	font-weight: 700;
	line-height: 18px;
	margin: 0 0 4px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		line-height: 24px;
	}
`;
export const EventDescription__FooterList = styled.ul``;
export const EventDescription__FooterItem = styled.li`
	display: flex;
	margin: 0 0 4px;
	span {
		font-size: 14px;
		line-height: 18px;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			font-size: 16px;
			line-height: 24px;
		}
		&:first-child {
			width: 62px;
			@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
				width: 70px;
			}
		}
	}
`;
