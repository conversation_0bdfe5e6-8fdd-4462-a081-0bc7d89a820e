import { SEARCH_DEBOUNCE_TIME } from '@/config';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useSearchResultCounterStore } from '@/store/searchResultCounter.store';
import { sanitizeSearchInput } from '@/utils';
import { useCallback, useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { StringParam, useQueryParams } from 'use-query-params';

import { usePaginatedClubsAndTeams } from './usePaginatedClubsAndTeams';

export const useClubsAndTeams = () => {
	const { eswId } = useEventDetails();
	const [queryParams] = useQueryParams({ search: StringParam });
	const { setCount } = useSearchResultCounterStore();

	const [search, setSearch] = useState(queryParams.search || '');
	const [searchDebounce] = useDebounce(sanitizeSearchInput(search), SEARCH_DEBOUNCE_TIME);
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const { fetchNext, clubs, clubsCount, loading, isFetched, pagesResponses } =
		usePaginatedClubsAndTeams({
			eswId,
			search: searchDebounce,
		});

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);
	useEffect(() => {
		if (!isFetched || loading) return;

		pagesResponses?.length && setCount({ clubsCount });
	}, [clubsCount, isFetched, loading, pagesResponses, setCount]);

	return {
		clubs,
		loading,
		search,
		onChangeSearch,
		fetchNext,
	};
};
