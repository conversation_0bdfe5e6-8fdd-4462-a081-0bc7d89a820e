import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useMemo } from 'react';

import { ALL_DIVISIONS_KEY } from '../constants';
import { MatchesTimeRange } from '../types';

type DaysMatchesTimeRangeMap = Map<string, MatchesTimeRange>;

// Returns a map of matches time ranges per day for each division
// division_id -> day -> time range
export const useDivisionsMatchesTimeRanges = (): Map<string, DaysMatchesTimeRangeMap> | null => {
	const { divisions } = useEventDetails();

	return useMemo(() => {
		if (!divisions) return null;
		// Collecting unique days, start times, end times and time ranges across all divisions
		const daysSet = new Set<string>();
		const divisionsTimeRanges = new Map<string, DaysMatchesTimeRangeMap>();
		divisions.forEach(({ division_id, matches_time_ranges }) => {
			const map: DaysMatchesTimeRangeMap = new Map();
			matches_time_ranges.forEach((timeRange) => {
				const { day } = timeRange;
				daysSet.add(day);
				map.set(day, timeRange);
				divisionsTimeRanges.set(division_id, map);
			});
		});
		const summaryTimeRanges: DaysMatchesTimeRangeMap = new Map();
		const sortedDays = [...daysSet].sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

		sortedDays.forEach((day) => {
			let startTime = '';
			let endTime = '';
			divisionsTimeRanges.forEach((divisionTimeRanges) => {
				const timeRange = divisionTimeRanges.get(day);
				if (timeRange) {
					const { start_time, end_time } = timeRange;
					startTime = startTime ? (start_time < startTime ? start_time : startTime) : start_time;
					endTime = endTime ? (end_time > endTime ? end_time : endTime) : end_time;
				}
			});
			summaryTimeRanges.set(day, { start_time: startTime, end_time: endTime });
		});
		divisionsTimeRanges.set(ALL_DIVISIONS_KEY, summaryTimeRanges);
		return divisionsTimeRanges;
	}, [divisions]);
};
