import { DivisionRoundPollBracket } from '@/shared/types/round.types';

const getMinTime = (...times: (number | null | undefined)[]): number | null => {
	const validTimes = times.filter((t) => t != null && !isNaN(t)) as number[];
	return validTimes.length > 0 ? Math.min(...validTimes) : null;
};

const getMaxTime = (...times: (number | null | undefined)[]): number | null => {
	const validTimes = times.filter((t) => t != null && !isNaN(t)) as number[];
	return validTimes.length > 0 ? Math.max(...validTimes) : null;
};

export const mergeRounds = (
	ra: DivisionRoundPollBracket,
	rb: DivisionRoundPollBracket,
): DivisionRoundPollBracket => {
	return {
		...ra,
		first_match_start: getMinTime(ra.first_match_start, rb.first_match_start),
		last_match_start: getMaxTime(ra.last_match_start, rb.last_match_start),
		pool_brackets:
			ra.sort_priority < rb.sort_priority
				? [...ra.pool_brackets, ...rb.pool_brackets]
				: [...rb.pool_brackets, ...ra.pool_brackets],
	};
};

export const filterRounds = (
	rounds: DivisionRoundPollBracket[],
	search: string,
): DivisionRoundPollBracket[] => {
	return (
		rounds
			.map((round) => {
				return {
					...round,
					pool_brackets: round.pool_brackets
						.map((pb) => {
							return {
								...pb,
								teams: pb.teams.filter((team) => {
									return team.team_name.toLowerCase().includes(search);
								}),
							};
						})
						// Remove pool brackets without teams
						.filter((pb) => pb.teams.length),
				};
			})
			// Remove rounds without pool brackets
			.filter((round) => round.pool_brackets.length)
	);
};
