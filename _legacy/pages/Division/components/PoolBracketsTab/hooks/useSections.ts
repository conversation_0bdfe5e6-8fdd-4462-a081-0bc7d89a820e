import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { isToday } from 'date-fns';
import { useCallback, useEffect, useRef, useState } from 'react';

import { Round } from '@/generated/graphql';

type Props = {
	roundsStartDates?: Pick<Round, 'first_match_start' | 'uuid'>[];
	divisionId: string;
};
export const useSections = ({ roundsStartDates, divisionId }: Props) => {
	const sectionRefs = useRef<Array<HTMLUListElement | null>>([]);
	const poolListRef = useRef<HTMLDivElement | null>(null);
	const mobileWrapper = useRef<HTMLDivElement | null>(null);
	const desktopWrapper = useRef<HTMLDivElement | null>(null);

	const [isFixed, setIsFixed] = useState(false);
	const OFFSET_ANCHOR = isFixed ? 10 : 50;
	const { breakPont } = useCurrentSize();
	const isLarge = breakPont !== 'small';
	const currentWrapper = isLarge ? desktopWrapper.current || window : mobileWrapper.current;
	const scrollToSection = useCallback(
		(index: number) => {
			const targetRef = sectionRefs.current[index];

			const targetSection = targetRef;
			if (targetSection && currentWrapper) {
				const top = isLarge
					? index
						? targetSection.offsetTop - (poolListRef.current?.offsetTop || 0)
						: 0
					: index
						? targetSection.offsetTop + OFFSET_ANCHOR
						: 0;
				currentWrapper?.scrollTo({
					top,
					behavior: 'instant',
				});
			}
			setTimeout(() => {
				setActiveSection(index);
			});
		},
		[OFFSET_ANCHOR, currentWrapper, isLarge],
	);

	const [activeSection, setActiveSection] = useState<number | null>(null);
	const prevDivisionId = useRef(divisionId);
	const IS_SET_ACTIVE_TAB_BY_DATE = true;

	useEffect(() => {
		if (prevDivisionId.current !== divisionId) {
			prevDivisionId.current = divisionId;
			setActiveSection(null);
		}

		if (roundsStartDates && roundsStartDates.length > 0 && activeSection === null) {
			if (IS_SET_ACTIVE_TAB_BY_DATE) {
				const currentTimestamp = new Date().getTime();
				const sortedRounds = [...roundsStartDates].sort(
					(a, b) =>
						new Date(a.first_match_start || 0).getTime() -
						new Date(b.first_match_start || 0).getTime(),
				);

				const firstMatchTime = new Date(sortedRounds[0].first_match_start || 0).getTime();
				const lastMatchTime = new Date(
					sortedRounds[sortedRounds.length - 1].first_match_start || 0,
				).getTime();

				if (currentTimestamp < firstMatchTime) {
					//* Before event starts
					scrollToSection(0);
					setActiveSection(0);
					return;
				}

				if (currentTimestamp > lastMatchTime) {
					//* After event ends
					const lastIndex = sortedRounds.length - 1;
					scrollToSection(lastIndex);
					setActiveSection(lastIndex);
					return;
				}

				//* During event
				const todayIndex = sortedRounds.findIndex(({ first_match_start }) =>
					isToday(new Date(first_match_start || 0)),
				);

				if (todayIndex !== -1) {
					scrollToSection(todayIndex);
					setActiveSection(todayIndex);
				} else {
					//* Fallback to last past round
					const lastPastIndex = sortedRounds
						.map(({ first_match_start }, index) => ({ time: first_match_start, index }))
						.filter(({ time }) => new Date(time as number).getTime() <= currentTimestamp)
						.at(-1)?.index;

					if (lastPastIndex !== undefined) {
						scrollToSection(lastPastIndex);
						setActiveSection(lastPastIndex);
					} else {
						scrollToSection(0);
						setActiveSection(0);
					}
				}
			} else {
				scrollToSection(0);
				setActiveSection(0);
			}
		}
	}, [activeSection, roundsStartDates, scrollToSection, divisionId, IS_SET_ACTIVE_TAB_BY_DATE]);

	useEffect(() => {
		const handleScroll = () => {
			const scrollPosition =
				((currentWrapper instanceof HTMLElement
					? currentWrapper?.scrollTop
					: currentWrapper?.scrollY) || 0) + (poolListRef.current?.offsetTop || 0);

			sectionRefs.current.forEach((section, index) => {
				const top = (section as HTMLElement)?.offsetTop;
				const bottom = top + (section as HTMLElement)?.clientHeight;

				if (scrollPosition >= top && scrollPosition < bottom) {
					setActiveSection(index);
				}
			});
		};

		currentWrapper?.addEventListener('scroll', handleScroll);

		return () => {
			currentWrapper?.removeEventListener('scroll', handleScroll);
		};
	}, [currentWrapper]);

	useEffect(() => {
		const handleScroll = () => {
			const newScrollTop =
				(currentWrapper instanceof HTMLElement
					? currentWrapper?.scrollTop
					: currentWrapper?.scrollY) || 0;
			setIsFixed(newScrollTop > OFFSET_ANCHOR);
		};

		currentWrapper?.addEventListener('scroll', handleScroll);

		return () => {
			currentWrapper?.removeEventListener('scroll', handleScroll);
		};
	}, [OFFSET_ANCHOR, currentWrapper]);

	return {
		activeSection,
		scrollToSection,
		isFixed,
		mobileWrapper,
		desktopWrapper,
		sectionRefs,
		poolListRef,
	};
};
