import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useMemo } from 'react';

import { useDivisionTeamsStandingQuery } from '@/generated/graphql';

export const useDivisionTeamsStanding = (divisionId: string) => {
	const { eswId } = useEventDetails();
	const { data, loading } = useDivisionTeamsStandingQuery({
		variables: {
			eswId,
			divisionId,
		},
	});

	const hasRanks = useMemo(() => {
		if (!data || loading) return false;
		// If any team has a rank, considering the teams have ranks
		return data.divisionTeamsStanding.some((team) => !!team.division_standing?.rank);
	}, [data, loading]);

	return { data, loading, hasRanks };
};
