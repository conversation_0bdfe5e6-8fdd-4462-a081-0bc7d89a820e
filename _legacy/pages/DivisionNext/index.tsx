import { useParams } from 'react-router-dom';

import { useDivisionOverview } from './hooks/useDivisionOverview';

type Props = {
	// activeTab: POOL_TABS;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const DivisionNext = (_props: Props) => {
	const { divisionId } = useParams();
	const overview = useDivisionOverview(divisionId);

	console.log('overview', overview);

	return <div>UI DEMO</div>;
};

export default DivisionNext;
