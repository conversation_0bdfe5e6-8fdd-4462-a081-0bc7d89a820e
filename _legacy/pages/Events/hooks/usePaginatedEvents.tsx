import { DEFAULT_PAGE_SIZE } from '@/config';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
	Event,
	PaginatedEventsQuery,
	PaginatedEventsQueryVariables,
	usePaginatedEventsLazyQuery,
} from '@/generated/graphql';

type Params = Omit<PaginatedEventsQueryVariables, 'page' | 'pageSize'>;

const INITIAL_PAGE = 1;

export const usePaginatedEvents = (params: Params): [() => void, Event[], boolean] => {
	const { search, startBefore, startAfter, endBefore, endAfter, years, asc } = params;
	const [pagesResponses, setPagesResponses] = useState<PaginatedEventsQuery[]>([]);

	const [fetchEvents, { data, loading }] = usePaginatedEventsLazyQuery();

	useEffect(() => {
		setPagesResponses([]);
		fetchEvents({
			variables: {
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
				startBefore,
				startAfter,
				endBefore,
				endAfter,
				years,
				asc,
			},
		});
	}, [fetchEvents, search, years, asc, startBefore, startAfter, endBefore, endAfter]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;
		setPagesResponses((pagesResponses) => {
			const responses = [...pagesResponses];
			responses[data.paginatedEvents.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all events from all pages
	const events = useMemo(() => {
		return pagesResponses.reduce<Event[]>((acc, response) => {
			if (!response) return acc;
			return [...acc, ...((response?.paginatedEvents?.items as Event[]) ?? [])];
		}, []);
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedEvents.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchEvents({
			variables: {
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
				startBefore,
				startAfter,
				endBefore,
				endAfter,
				years,
				asc,
			},
		});
	}, [
		pagesResponses,
		fetchEvents,
		search,
		startBefore,
		startAfter,
		endBefore,
		endAfter,
		years,
		asc,
	]);

	return [fetchNext, events, loading];
};
