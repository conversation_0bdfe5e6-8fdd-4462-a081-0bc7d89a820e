import styled from 'styled-components';

import { ThemeT } from '@/styles/theme';

const FILTER_HEIGHT = 74;

export const Events__Wrapper = styled.div``;
export const Events__SearchContainer = styled.div<ThemeT>`
	padding: 16px;
	background: ${(props) => props.theme.colors.blue};
	position: fixed;
	width: 100%;
	z-index: 2;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: static;
		height: 200px;
		padding: 45px 16px;
	}
`;
export const Events__TabsWrapper = styled.div<{ $browserHeight: number }>`
	padding: 62px 0 48px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 100px 24px 24px;
		width: 680px;
		min-width: 648px;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		z-index: 99;
		top: 240px;
		/* overflow-y: auto; */
		overflow-x: hidden;
		height: calc(100vh - 246px);
	}
`;
export const Events__FilterContainer = styled.div`
	padding: 16px;
	height: ${FILTER_HEIGHT}px;
	position: fixed;
	width: 100%;
	background: #fff;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: fixed;
		padding: 0;
		height: auto;
		top: 257px;
		width: 600px;
		z-index: 8;
	}
`;
export const Events__SelectWrapper = styled.div`
	min-width: 114px;
	width: fit-content;
`;
export const Events__DataWrapper = styled.div`
	padding: ${FILTER_HEIGHT}px 0 0;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 16px 0 56px 0;
	}
`;
