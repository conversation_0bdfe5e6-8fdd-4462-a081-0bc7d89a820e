import { FavoriteTeam } from '@/shared/types/team.types';
import { useMemo } from 'react';

import { UNKNOWN_STANDING_GROUP } from '../constants';

export type StandingGroup = {
	heading: string;
	priority: number;
	teams: FavoriteTeam[];
};

export const useTeamsStandingGroups = (teams: FavoriteTeam[]): StandingGroup[] => {
	return useMemo(() => {
		const groupsMap = new Map<string, StandingGroup>();

		teams.forEach((team) => {
			const heading = team.division_standing?.heading || UNKNOWN_STANDING_GROUP;
			const group = groupsMap.get(heading);
			if (group) {
				group.teams.push(team);
			} else {
				groupsMap.set(heading, {
					heading,
					priority: team.division_standing?.heading_priority || 0,
					teams: [team],
				});
			}
		});

		const sortedGroups = Array.from(groupsMap.values()).map((group) => {
			group.teams.sort((a, b) => a.team_name.localeCompare(b.team_name));
			return group;
		});

		return sortedGroups.sort((a, b) => {
			if (a.priority !== b.priority) {
				return a.priority - b.priority;
			}
			return a.heading.localeCompare(b.heading);
		});
	}, [teams]);
};
