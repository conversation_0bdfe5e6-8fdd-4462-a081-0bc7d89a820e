import { addOrdinalSuffix } from '@/utils';

import { TableHead } from '@mui/material';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableRow from '@mui/material/TableRow';

import { DivisionPoolTeam } from '@/generated/graphql';

type PropsT = {
	teams: DivisionPoolTeam[];
};

export const Round = ({ teams }: PropsT) => {
	return (
		<TableContainer component={Paper}>
			<Table>
				<TableHead>
					<TableRow>
						<TableCell>Teams</TableCell>
						<TableCell align="right">Match W-L</TableCell>
						<TableCell align="right">Set W-L</TableCell>
						<TableCell align="right">Point Ratio</TableCell>
						<TableCell align="right">Code</TableCell>
					</TableRow>
				</TableHead>
				<TableBody>
					{[...teams]
						.sort((a, b) => a.rank! - b.rank!)
						.map((team) => (
							<TableRow
								key={team.opponent_team_id}
								sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
							>
								<TableCell component="th" scope="row">
									({addOrdinalSuffix(team.rank!)}) {team.opponent_team_name}
								</TableCell>
								<TableCell align="right">
									{team.matches_won} - {team.matches_lost}
								</TableCell>
								<TableCell align="right">
									{team.sets_won} - {team.sets_lost}
								</TableCell>
								<TableCell align="right">{team.points_ratio}</TableCell>
								<TableCell align="right">{team.opponent_organization_code}</TableCell>
							</TableRow>
						))}
				</TableBody>
			</Table>
		</TableContainer>
	);
};
