import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useLazyScrollTrigger } from '@/shared/hooks/useLazyScrollTrigger';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { PaginatedAthlete } from '@/shared/types/athlete.types';
import { Loader } from '@components/Loader';

import { NotFound } from '@/components/NotFound';
import { Spinner } from '@/components/Spinner';

import {
	Roster__TabContentItem,
	Roster__TabContentList,
	Roster__TabContentWrapper,
} from '../../styled';
import { RosterCard } from '../RosterCard';

type PropsT = {
	athletes: PaginatedAthlete[];
	isLoading: boolean;
	onScrollEnd: () => void;
	scrollRef: React.RefObject<HTMLDivElement>;
	isTab?: boolean;
};

export const AthleteList = ({ athletes, isLoading, onScrollEnd, scrollRef, isTab }: PropsT) => {
	const isMobile = useCurrentSize().breakPont === 'small';
	useLazyScrollTrigger(onScrollEnd, isMobile ? null : scrollRef);

	const { teamModalElement, openTeamModal, teamDataLoading } = useTeamModal();

	return (
		<>
			{isLoading && <Spinner />}
			{teamDataLoading && <Loader />}
			{teamModalElement}
			<Roster__TabContentWrapper $isTab={!!isTab}>
				{!athletes.length && !isLoading && <NotFound type="roster" />}
				<Roster__TabContentList>
					{athletes.map((athlete) => {
						const header = `${athlete.uniform ? `#${athlete.uniform} ` : ''} ${
							athlete.short_position || ''
						} ${athlete.first} ${athlete.last}`;
						const footer = `${athlete.club_name}, ${athlete.state}`;
						const content = `${athlete.team_name} - ${athlete.organization_code}`;

						return (
							<Roster__TabContentItem
								data-testid="athlete-item"
								key={athlete.athlete_id}
								onClick={() => openTeamModal(athlete.team_id)}
							>
								<RosterCard header={header} content={content} footer={footer} />
							</Roster__TabContentItem>
						);
					})}
				</Roster__TabContentList>
			</Roster__TabContentWrapper>
		</>
	);
};
