import { SEARCH_DEBOUNCE_TIME } from '@/config';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useSearchResultCounterStore } from '@/store/searchResultCounter.store';
import { sanitizeSearchInput } from '@/utils';
import { useCallback, useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { StringParam, useQueryParams } from 'use-query-params';

import { AthleteList } from '../components/AthleteList';
import { StaffList } from '../components/StaffList';
import { usePaginatedAthletes } from './usePaginatedAthletes';
import { usePaginatedStaff } from './usePaginatedStaff';

type Props = {
	scrollRef: React.RefObject<HTMLDivElement>;
	isTab?: boolean;
};

export const useRoster = ({ isTab, scrollRef }: Props) => {
	const { eswId, event, loading: isLoadingEvent } = useEventDetails();
	const [activeTab, setActiveTab] = useState('Athletes');
	const [queryParams] = useQueryParams({ search: StringParam });
	const { setCount } = useSearchResultCounterStore();

	const [search, setSearch] = useState(queryParams.search || '');
	const [searchDebounce] = useDebounce(sanitizeSearchInput(search), SEARCH_DEBOUNCE_TIME);
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	// Disable fetching the athletes and staff if the event has no rosters
	const disabled = !event?.has_rosters;

	const {
		fetchNext: fetchNextAthletes,
		athletes,
		athletesCount,
		pagesResponses,
		loading: isLoadingAthletes,
		isFetched: isFetchedAthletes,
	} = usePaginatedAthletes({
		eswId,
		search: searchDebounce,
		disabled,
	});

	const {
		fetchNext: fetchNextStaff,
		staff,
		staffCount,
		loading: isLoadingStaff,
		isFetched: isFetchedStaff,
	} = usePaginatedStaff({
		eswId,
		search: searchDebounce,
		disabled,
	});

	const rosterCount = (athletesCount || 0) + (staffCount || 0);
	const loading = isLoadingAthletes || isLoadingStaff || isLoadingEvent;

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);

	useEffect(() => {
		if (
			!isFetchedAthletes ||
			!isFetchedStaff ||
			loading ||
			(athletesCount === null && staffCount === null)
		)
			return;

		pagesResponses?.length && setCount({ rosterCount });
	}, [
		rosterCount,
		loading,
		disabled,
		isFetchedAthletes,
		isFetchedStaff,
		setCount,
		athletesCount,
		staffCount,
		pagesResponses,
	]);

	const [isInitialRender, setIsInitialRender] = useState(false);
	useEffect(() => {
		if (!search || !isFetchedAthletes || !isFetchedStaff) {
			return;
		}

		if (!athletes.length && !isInitialRender && pagesResponses?.length) {
			setActiveTab('Staff');
			setIsInitialRender(true);
		}
	}, [
		search,
		pagesResponses,
		isFetchedAthletes,
		isFetchedStaff,
		athletes,
		staff,
		isInitialRender,
		athletesCount,
		staffCount,
	]);

	const TABS = {
		Athletes: (
			<>
				{athletes && (
					<AthleteList
						athletes={athletes}
						isLoading={isLoadingAthletes || isLoadingEvent}
						isTab={isTab}
						onScrollEnd={fetchNextAthletes}
						scrollRef={scrollRef}
					/>
				)}
			</>
		),
		Staff: (
			<>
				{staff && (
					<StaffList
						staff={staff}
						isLoading={isLoadingStaff || isLoadingEvent}
						isTab={isTab}
						onScrollEnd={fetchNextStaff}
						scrollRef={scrollRef}
					/>
				)}
			</>
		),
	};

	return {
		activeTab,
		setActiveTab,
		tabs: Object.entries(TABS),
		search,
		onChangeSearch,
	};
};
