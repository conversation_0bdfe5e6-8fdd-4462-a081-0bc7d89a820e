import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDescription } from '@/shared/hooks/useEventDescription';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useEffect, useRef } from 'react';

import { SearchBar } from '@/components/SearchBar';
import { Tabs } from '@/components/Tabs';

import { placeholders } from '@/config/searchPlaceholder';

import { useRoster } from './hooks/useRoster';
import { Roster__Wrapper } from './styled';

type Props = {
	isTab?: boolean;
};

const Roster = ({ isTab }: Props) => {
	const { event } = useEventDetails();
	const description = useEventDescription();

	const scrollRef = useRef<HTMLDivElement>(null);
	const { search, onChangeSearch, activeTab, setActiveTab, tabs } = useRoster({
		isTab,
		scrollRef,
	});
	useAnalytics(`Roster (${activeTab})`, search);

	useEffect(() => {
		window.scrollTo(0, 0);
		scrollRef.current?.scrollTo(0, 0);
	}, [activeTab]);

	const { browserHeight } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({ page: 'roster' });
	return (
		<>
			{!isTab && (
				<SearchBar
					placeholder={placeholders.roster}
					description={description}
					eventName={event?.long_name || ''}
					search={search}
					onChangeSearch={onChangeSearch}
					isShowBuyAdmission={!!event?.tickets_published}
					tickets_code={event?.tickets_code || ''}
					isAssignedTickets={!!event?.is_require_recipient_name_for_each_ticket}
				/>
			)}
			<Roster__Wrapper
				$browserHeight={browserHeight}
				$isDesktopFixed={isDesktopFixed}
				data-testid="roster"
			>
				<Tabs
					activeTab={activeTab}
					handleChange={(_e, value) => setActiveTab(value)}
					tabs={tabs}
					wrapperRef={scrollRef}
				/>
			</Roster__Wrapper>
		</>
	);
};

export default Roster;
