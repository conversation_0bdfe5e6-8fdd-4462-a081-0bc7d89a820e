import ReactGA from 'react-ga4';

const { VITE_APP_ENV, VITE_GA_MEASUREMENT_ID, PROD } = import.meta.env;

export const useReactGA = (): typeof ReactGA | null => {
	if (ReactGA.isInitialized) return ReactGA;
	if (!VITE_GA_MEASUREMENT_ID) return null;
	if (PROD && VITE_APP_ENV !== 'production') return null; // Prod build should only run in production
	ReactGA.initialize(VITE_GA_MEASUREMENT_ID, {
		testMode: !PROD,
		gtagOptions: {
			send_page_view: false,
		},
	});
	return ReactGA;
};
