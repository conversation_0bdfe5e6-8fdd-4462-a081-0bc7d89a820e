import { modalTeamIdSignal } from '@/signals/modalTeamIdSignal';
import { AnchorModal } from '@components/AnchorModal';
import { useCallback } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import {
	DivisionPool,
	usePoolByTeamIdLazyQuery,
	useUpcomingMatchesLazyQuery,
} from '@/generated/graphql';

import { useEventDetails } from './useEventDetails';

export const useTeamModal = () => {
	const { eswId } = useEventDetails();
	const [getPoolByTeamId, { data, loading, refetch: teamRefetch }] = usePoolByTeamIdLazyQuery();
	const [, { refetch: matchesRefetch }] = useUpcomingMatchesLazyQuery({
		variables: {
			id: eswId,
		},
	});

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
		modalTeam: StringParam,
	});

	const openTeamModal = useCallback(
		(teamId: string) => {
			getPoolByTeamId({
				variables: {
					id: eswId,
					teamId,
				},
				fetchPolicy: 'network-only',
				onCompleted: () => {
					modalTeamIdSignal.value = teamId;
				},
			});
		},
		[getPoolByTeamId, eswId],
	);

	const close = () => {
		setQueryParams({ divisionId: null, modalTeam: null });
		modalTeamIdSignal.value = '';
	};

	const teamModalElement =
		modalTeamIdSignal.value && data ? (
			<AnchorModal
				matchesRefetch={matchesRefetch}
				teamRefetch={teamRefetch}
				key={modalTeamIdSignal.value}
				teamPoolData={data}
				close={close}
				poolOfTeam={data.poolIdByTeamId as DivisionPool}
			/>
		) : null;

	return {
		teamModalElement,
		teamDataLoading: loading,
		openTeamModal,
	};
};
