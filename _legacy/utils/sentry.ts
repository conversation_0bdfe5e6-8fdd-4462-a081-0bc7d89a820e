import * as Sentry from '@sentry/react';

const { VITE_APP_ENV, VITE_SENTRY_DSN, VITE_BASE_URL, VITE_BRACKETS_API_URL, VITE_SWT_URL, PROD } =
	import.meta.env;

Sentry.init({
	environment: VITE_APP_ENV,
	enabled: PROD,
	debug: false,
	dsn: VITE_SENTRY_DSN,
	integrations: [
		Sentry.browserTracingIntegration(),
		Sentry.replayIntegration({
			maskAllText: false,
			blockAllMedia: false,
		}),
	],
	tracesSampleRate: PROD ? 0.01 : 1.0,
	tracePropagationTargets: ['localhost:5173', VITE_BASE_URL, VITE_BRACKETS_API_URL, VITE_SWT_URL],
	// Session Replay
	replaysSessionSampleRate: PROD ? 0.01 : 1.0, // Setting the sample rate to 1% in production
	replaysOnErrorSampleRate: PROD ? 0.01 : 1.0,
});
