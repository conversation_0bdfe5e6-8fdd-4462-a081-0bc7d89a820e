import numpy as np
import soundfile as sf
import matplotlib.pyplot as plt

def plot_spectrum(signal, fs, title, Nfft=2**20):
    N = min(len(signal), Nfft)
    spectrum = np.fft.fftshift(np.fft.fft(signal[:N]))
    freqs = np.fft.fftshift(np.fft.fftfreq(N, 1/fs))
    plt.figure(figsize=(10, 4))
    plt.plot(freqs/1e6, 20*np.log10(np.abs(spectrum) + 1e-12))
    plt.title(title)
    plt.xlabel('Frequency (MHz)')
    plt.ylabel('Magnitude (dB)')
    plt.grid(True)
    plt.tight_layout()
    plt.show()
    # Return freqs for user reference
    return freqs

def plot_time(signal, fs, title, N=100000):
    t = np.arange(min(len(signal), N)) / fs
    plt.figure(figsize=(10, 3))
    plt.plot(t*1e3, signal[:N])
    plt.title(title)
    plt.xlabel('Time (ms)')
    plt.ylabel('Amplitude')
    plt.grid(True)
    plt.tight_layout()
    plt.show()

def print_stats(signal, label):
    print(f"{label}:")
    print(f"  Mean: {np.mean(signal):.6f}")
    print(f"  Std:  {np.std(signal):.6f}")
    print(f"  Min:  {np.min(signal):.6f}")
    print(f"  Max:  {np.max(signal):.6f}")
    print()

def shift_iq(iq, fs, freq_shift_hz):
    """Shift the IQ data by freq_shift_hz (can be positive or negative)"""
    if freq_shift_hz == 0:
        return iq
    t = np.arange(len(iq)) / fs
    return iq * np.exp(-2j * np.pi * freq_shift_hz * t)

def main(filename, freq_shift_mhz=0.0):
    # Load IQ data
    data, fs = sf.read(filename, always_2d=True)
    I = data[:, 0]
    Q = data[:, 1]
    iq = I + 1j * Q

    print(f"Loaded {len(iq):,} samples at {fs/1e6:.2f} MHz sample rate.")

    # --- Raw IQ spectrum and time ---
    print_stats(np.abs(iq), "Raw IQ magnitude")
    freqs = plot_spectrum(iq, fs, "Raw IQ Spectrum")
    plot_time(np.abs(iq), fs, "Raw IQ Envelope (AM-like)")

    # --- Frequency shift (if needed) ---
    if freq_shift_mhz != 0.0:
        print(f"Shifting IQ by {freq_shift_mhz:.3f} MHz...")
        iq = shift_iq(iq, fs, freq_shift_mhz * 1e6)
        print("  Done. Plotting shifted spectrum:")
        plot_spectrum(iq, fs, f"Shifted IQ Spectrum (centered at {freq_shift_mhz:.3f} MHz)")
    else:
        print("No frequency shift applied.")

    # --- AM Demodulation ---
    am = np.abs(iq)
    am -= np.mean(am)
    print_stats(am, "AM Demodulated")
    plot_spectrum(am, fs, "AM Demodulated Spectrum")
    plot_time(am, fs, "AM Demodulated Time Domain")

    # --- FM Demodulation ---
    fm = np.angle(iq[1:] * np.conj(iq[:-1]))
    fm -= np.mean(fm)
    print_stats(fm, "FM Demodulated")
    plot_spectrum(fm, fs, "FM Demodulated Spectrum")
    plot_time(fm, fs, "FM Demodulated Time Domain")

if __name__ == "__main__":
    # --- USER SETTINGS ---
    filename = "./signals/PAL Video 6.5MHz.wav"  # Change to your file
    # filename = "./signals/vtx_24_20_side-80dbm_2025-05-27_10.27.45.205114_2375.0_MHz.wav"  # Change to your file
    # freq_shift_mhz = -5.0  # <--- Set this to the center frequency you want (in MHz, can be negative)
    freq_shift_mhz = 0.0  # Set to 0 for no shift

    main(filename, freq_shift_mhz)