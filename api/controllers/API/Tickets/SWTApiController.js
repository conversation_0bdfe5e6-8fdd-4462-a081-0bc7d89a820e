'use strict';
let optimist            = require('optimist'),
    swUtils             = require('../../../lib/swUtils'),
    paymentValidator    = require('../../../validation-schemas/tickets'),
    co                  = require('co'),
    moment              = require('moment-timezone'),
    numeral             = require('numeral');

const LOOKUP_NOT_FOUND_FORMAT     = 'Tickets not found for "{prop}" = "{val}" ({exact} search)',
      SCANNER_NOT_FOUND_FORMAT    = 'No transactions found for "{0}"';

// Tickets Api (v3 of BVT App)
module.exports = {
    // covered 😄👍
    // GET /api/swt/ping
    ping: function (req, res) {
        return res.status(200).json({'success':true, 'reload_test':3});
    },

    // covered 😄👍
    // POST /api/swt/lookup
    lookup: function (req, res) {
        let $event_id   = req.body.event_id,
            $last       = req.body.last,
            $zip        = req.body.zip,
            $exact      = (req.body.exact === 'true'),
            $limit      = parseInt(req.body.limit, 10),
            $offset     = parseInt(req.body.offset, 10);
        
        if (!$last && !$zip) {
            return res.status(400).json({ success: false, type: 'validation', message: 'Last or zip required' });
        }
        if ($zip) {
            if (!/^(?:[0-9A-Za-z]{1,15})$/.test($zip)) {
                return res.status(400).json({ success: false, type: 'validation', 'message': 'Invalid Zip' });
            }
        }

        if ($limit && swUtils.isNumberLessThanZero($limit)) {
            return res.status(400).json({
                success: false, type: 'validation', message: 'Limit should be a Number'
            });
        }

        if ($offset && swUtils.isNumberLessThanZero($offset)) {
            return res.status(400).json({
                success: false, type: 'validation', message: 'Offset should be a Number'
            });
        }

        let query = SWTAPIService.getPurchaseQuery();

        query.field('p.event_id');

        if (Array.isArray($event_id)) {
            let eventIDsStr;

            try {
                eventIDsStr = swUtils.numArrayToString($event_id, null, true);
            } catch (err) {
                return res.status(400).json({ success: false, type: 'validation', message: 'Invalid event identifiers' })
            }
            
            query.where(`p.event_id IN(${eventIDsStr})`);
        } else if (Number($event_id)) {
            query.where('p.event_id = ?', $event_id);
        } else {
            return res.status(400).json({ success: false, type: 'validation', message: 'Event id required' });
        }

        if ($zip) {
            if ($exact) {
                query.where('p.zip = ?', $zip);
            } else {
                query.where('p.zip ILIKE ?', `%${$zip}%`);
            }
        } else {
            $last = $exact ? $last : `%${$last}%`;

            query.where(
                squel.expr({ replaceSingleQuotes: true, singleQuoteReplacement: "''" })
                .and('u."last" ILIKE ?', $last)
                .or('p."last" ILIKE ?', $last)
            );
        }
        query.order('p.created', false);
        
        if ($limit) {
            query.limit($limit);
        }
        
        if ($offset) {
            query.offset($offset);
        }

        Db.query(query.toString()).then(function (result) {
            if(!result.rows.length) {
                res.status(200).json({
                    success         : false,
                    type            : 'not_found',
                    message         : LOOKUP_NOT_FOUND_FORMAT.format({
                        prop    : ($zip ? 'Zip' : 'Last'),
                        val     : ($zip || $last),
                        exact   : ($exact?'Exact':'Not exact')
                    })
                });
            } else {
                res.status(200).json({
                    success     : true,
                    rows        : result.rows
                });
            }
        }).catch(err => {
            ErrorSender.swtError(err, {
                url         : req.path,
                method      : req.method,
                ip          : req.connection.remoteAddress,
                userAgent   : req.headers['user-agent']
            })
            res.status(500).json({ error: err, message: 'DB error', type: 'internal', success: false });
        })
    },

    // POST /api/swt/test
    test: async function (req, res) {
        var $event_id  = Number(req.body.event_id);
        var $ticket    = Number(req.body.ticket);

        if(!$event_id) return res.status(400).json({ success: false, type: 'validation', message: 'Event id required' });
        if(!$ticket) return res.status(400).json({ success: false, type: 'validation', message: 'Ticket barcode required' });

        let codeType = SWTAPIService.checkTicketCodeType($ticket);

        let query = SWTAPIService.getPurchaseQuery()
            .where('p.event_id = ?', $event_id);

        if(codeType === SWTAPIService.BARCODE_TYPE.RECEIPT) {
            query.where('p.ticket_barcode = ?', $ticket);
        } else if(codeType === SWTAPIService.BARCODE_TYPE.WRISTBAND_SERIAL) {
            query.where('p.wristband_serial = ?', $ticket);
        } else {
            return res.status(400).json({
                success : false,
                type    : 'validation',
                message : 'Incorrect ticket barcode'
            });
        }

        Db.query(query).then(function (result) {
            if(!result.rows.length) {
                res.status(200).json({
                    success     : false,
                    type        : 'not_found',
                    message     : `Ticket #${$ticket} not found`
                });
            } else {
                let ticket = result.rows[0];

                if (ticket.is_deactivated) {
                    return TicketsService.barcode.getDeactivateReason(ticket.code)
                        .then(response => {
                            const reasonMessage = response && response.reason;

                            return res.status(400).json({
                                success: false,
                                type: 'validation',
                                message: `Ticket is deactivated ${TicketsService.barcode.formatDeactivateReason(reasonMessage)}`
                            })
                        })
                }

                SWTAPIService.addTicketsGroup(ticket).then(ticket => {
                    res.status(200).json({
                        success     : true,
                        ticket      : ticket
                    });
                })
            }
        }).catch(err => {
            ErrorSender.swtError(err, {
                url         : req.path,
                method      : req.method,
                ip          : req.connection.remoteAddress,
                userAgent   : req.headers['user-agent']
            });
            res.status(500).json({ error: err, success: false, type: 'internal', message: 'DB error'});
        })
    },

    // covered 😄👍 
    // POST /api/swt/scan
    scan: async function (req, res) {
        var $event_id       = parseInt(req.body.event_id, 10),
            $scan_event_id  = parseInt(req.body.scan_event_id || req.body.event_id, 10),
            $ticket         = parseInt(req.body.ticket, 10),
            $types          = req.body.types, // an array of ticket types to be scanned
            $scanner_id     = req.body.scanner,
            $location       = req.body.location,
            isReentry       = req.body.reentry,
            hasCovidTest    = req.body.has_covid_test,
            $reset          = (req.body.reset === 'true'); // Allow resets on production


        if(!$event_id)
            return res.status(400).json({ success: false, type: 'validation', message: 'Event id required' });
        if(!$ticket)
            return res.status(400).json({success: false, type: 'validation', message: 'Ticket barcode required' });
        if(!$scanner_id) 
            return res.status(400).json({success: false, type: 'validation', message: 'Scanner id required' });
        if(!$location)
            return res.status(400).json({success: false, type: 'validation', message: 'Scanner location required' });
        // 2016-01-15 Doug: can you quickly disable the need to send types[] in the scan API?
        //if($types && !_.isArray($types))
        //    return res.status(400).json({success: false, type: 'validation', message: 'Expecting "types" to be an array' });

        let barcodeType = SWTAPIService.checkTicketCodeType($ticket);

        if(barcodeType === SWTAPIService.BARCODE_TYPE.WRISTBAND_SERIAL) {

            return SWTAPIService.scan.scanWristbandSerial($event_id, $ticket, $scanner_id, $location)
                .then(ticket => {

                    if(!ticket) {
                        res.status(200).json({
                            success : false,
                            type    : 'not_exists',
                            message : 'Ticket #{0} does not exist'.format($ticket)
                        })
                    } else {
                        if (ticket.is_deactivated) {
                            return TicketsService.barcode.getDeactivateReason(ticket.code)
                                .then(response => {
                                    const reasonMessage = response && response.reason;

                                    return res.status(400).json({
                                        success: false,
                                        type: 'validation',
                                        message: `Ticket is deactivated ${TicketsService.barcode.formatDeactivateReason(reasonMessage)}`
                                    })
                                })
                        }

                        res.status(200).json({
                            success : true,
                            ticket  : ticket
                        })
                    }
                })
                .catch(err => {
                    res.status(500).json({
                        error   : err,
                        success : false,
                        type    : 'internal',
                        message : 'DB error'
                    });
                })
        } else if(barcodeType === SWTAPIService.BARCODE_TYPE.RECEIPT) {
            try {
                const {statusCode, response} = await Cache.getResult(
                    `SWTAPIService.scan.scanTicketReceipt:${JSON.stringify({
                        $event_id,
                        $scan_event_id,
                        $ticket,
                        $location,
                        $scanner_id,
                        $reset,
                        $types,
                        isReentry,
                        hasCovidTest
                    })}`,
                    async () => {
                        try {
                            const ticket = await SWTAPIService.scan.scanTicketReceipt(
                                $event_id, $scan_event_id, $ticket, $location, $scanner_id, $reset, $types, isReentry, hasCovidTest
                            );
                            return {
                                statusCode: 200,
                                response: {
                                    success: true,
                                    ticket: ticket
                                },
                            };
                        }
                        catch(err) {
                            let notAllowedToTriggerEmailErrorTypes = [
                                'already_scanned', 'not_exists', 'not_scanned', 'covid_test_required'
                            ];

                            // can we remove this check at all?
                            if (!notAllowedToTriggerEmailErrorTypes.includes(err.type)) {
                                ErrorSender.swtError(err, {
                                    url         : req.path,
                                    method      : req.method,
                                    ip          : req.connection.remoteAddress,
                                    userAgent   : req.headers['user-agent']
                                });
                            }

                            if (err.type === 'allow_reentry') {
                                return {
                                    statusCode: 400,
                                    response: err.message,
                                };
                            }

                            if(_.has(err, 'success')) {
                                const statusCode = err.type === 'validation' ? 400 : 200;
                                return {
                                    statusCode,
                                    response: err,
                                };
                            }
                            throw err;
                        }
                    },
                    {
                        ttl: SWTAPIService.scan.RESULT_CACHE_TTL,
                    }
                );
                return res.status(statusCode).json(response);
            }
            catch(err) {
                return res.status(500).json({
                    error: err,
                    success: false,
                    type: 'internal',
                    message: 'DB error'
                });
            }

        } else {
            res.status(200).json({
                success : false,
                type    : 'not_exists',
                message : 'Invalid barcode format #{0}'.format($ticket)
            })
        }

    },

    // POST /api/swt/redeem
    redeem: async function (req, res) {
        const eventID       = Number(req.body.event_id);
        const barcode       = Number(req.body.ticket);
        const scannerID     = req.body.scanner;
        const location      = req.body.location;
        const tickets       = parseTickets(req.body.tickets);
        const scanEventID   = Number(req.body.scan_event_id || eventID);

        if(!eventID)
            return res.status(400).json({ success: false, type: 'validation', message: 'Event id required' });
        if(!barcode)
            return res.status(400).json({ success: false, type: 'validation', message: 'Ticket barcode required'});
        if(!scannerID)
            return res.status(400).json({ success: false, type: 'validation', message: 'Scanner id required'});
        if(!location)
            return res.status(400).json({ success: false, type: 'validation', message: 'Scanner location required'});
        if(!_.isArray(tickets))
            return res.status(400).json({ success: false, type: 'validation', message: 'Tickets should be an array'});
        if(_.isEmpty(tickets))
            return res.status(400).json({ success: false, type: 'validation', message: 'Tickets should not be empty'});

        let tr = null;

        try {
            let isDeactivated = await TicketsService.barcode.checkTicketDeactivateStatus(barcode);

            if(isDeactivated) {
                let { reason } = await TicketsService.barcode.getDeactivateReason(barcode);

                throw {
                    success : false,
                    type    : 'validation',
                    message : `Ticket is deactivated ${TicketsService.barcode.formatDeactivateReason(reason)}`
                }
            }

            let details = tickets.reduce((lines, ticket) => {
                lines += '#{0} count {1}'.format(ticket.id, ticket.count);

                return lines;
            }, '');

            // TODO: Read event timezone!
            let historyLine = SWTAPIService.generateScanText(
                SWTAPIService.SCAN_TEXT_FORMAT.REDEEM_LINE,
                {
                    scanner : scannerID,
                    location: location,
                    details : details,
                    timezone: SWTAPIService.DEFAULT_TICKETS_TIMEZONE
                }
            );

            tr = await Db.begin();

            let historyQuery = squel.update({ replaceSingleQuotes: true, singleQuoteReplacement: "''" })
                .table('purchase', 'p')
                .set(`tickets_scan = concat_ws(chr(10), tickets_scan, '${historyLine.replace(/'/g, "''")}')`)
                .setFields({
                    scanner_id      : scannerID,
                    scanner_location: location,
                    scanned_at      : null
                })

                .where('ticket_barcode = ?', barcode)
                .where('event_id = ?', eventID)
                .where(`payment_for = 'tickets'`)
                .where('canceled_date IS NULL')
                //.where('date_refunded IS NULL')
                // This was a problem with redeem for partially refunded tickets
                .returning('purchase_id');

            let { purchase_id } = await tr.query(historyQuery).then(({rows}) => rows[0] || {});

            // TODO: Return better error message for different cases like Ticket canceled, etc.

            if(!purchase_id) {
                throw {
                    success : false,
                    type    : 'not_found',
                    message : 'Ticket #{0} does not exist'.format(barcode)
                }
            }

            let queryPt = squel.update().table('purchase_ticket').where('purchase_id = ?', purchase_id);

            await Promise.all(
                tickets.map(ticket => {
                    let query = queryPt.clone()
                        .set(
                            'available = (CASE WHEN {0} > quantity THEN quantity ELSE {0} END)'.format(ticket.count)
                        )
                        .where('event_ticket_id = ?', ticket.id);

                    return tr.query(query);
                })
            );

            let ticketQuery = SWTAPIService.getPurchaseQuery()
                .where('p.event_id = ?'         , eventID)
                .where('p.ticket_barcode = ?'   , barcode)
                .where('p.purchase_id = ?'      , purchase_id);

            let ticketData = await tr.query(ticketQuery).then(({rows}) => rows[0]);

            if(!ticketData) {
                throw {
                    success: false,
                    type: 'not_found',
                    message: 'Ticket #{0} not found after redeem'.format(barcode)
                }
            }

            SWTAPIService.scan.validateRelatedEvent(scanEventID, eventID, ticketData);

            await tr.commit();

            res.status(200).json(_.extend(ticketData, {success: true}));
        } catch (err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }

            ErrorSender.swtError(err, {
                url         : req.path,
                method      : req.method,
                ip          : req.connection.remoteAddress,
                userAgent   : req.headers['user-agent']
            });

            if(_.has(err, 'success')) {
                const statusCode = err.type === 'validation' ? 400 : 200;

                return res.status(statusCode).json(err);
            }

            return res.status(500).json({ error: err, success: false, type: 'internal', message: 'DB error'});
        }
    },

    // POST /api/swt/recent
    recent: function (req, res) {
        let $event_id   = req.body.event_id,
            $scanner_id = req.body.scanner,
            $limit      = parseInt(req.body.limit, 10);

        if(!$scanner_id) {
            return res.status(400).json({success: false, type: 'validation', message: 'Scanner not passed'});
        }

        let query = SWTAPIService.getPurchaseQuery()
                .where('p.scanner_id = ?', $scanner_id);

        query.field('p.event_id');

        if (Array.isArray($event_id)) {
            let eventIDsStr;

            try {
                eventIDsStr = swUtils.numArrayToString($event_id, null, true);
            } catch (err) {
                return res.status(400).json({ success: false, type: 'validation', message: 'Invalid event identifiers' })
            }
            
            query.where(`p.event_id IN(${eventIDsStr})`);
        } else if (Number($event_id)) {
            query.where('p.event_id = ?', $event_id);
        } else {
            return res.status(400).json({ success: false, type: 'validation', message: 'Event id required' });
        }

        if($limit) {
            query.limit($limit);
        }

        Db.query(query.toString()).then(function (result) {
            if(!result.rows.length) {
                res.status(200).json({
                    success         : false,
                    type            : 'not_found',
                    message         : SCANNER_NOT_FOUND_FORMAT.format($scanner_id)
                });
            } else {
                res.status(200).json({
                    success     : true,
                    rows        : result.rows
                });
            }
        }).catch(err => {
            ErrorSender.swtError(err, {
                url             : req.path,
                method          : req.method,
                ip              : req.connection.remoteAddress,
                userAgent       : req.headers['user-agent']
            })
            res.status(500).json({ error: err, success: false, type: 'internal', message: 'DB error'});
        })
    },

    // covered 😄👍 
    // POST /api/swt/buy
    buy: function (req, res) {
        let paymentBody = {
            method      : req.body.type,
            token       : req.body.token || { 
                            number      : req.body.card_num,
                            exp_month   : req.body.card_month,
                            exp_year    : req.body.card_year,
                            name        : `${
                                            req.body.card_first || req.body.user_first
                                           } ${req.body.card_last || req.body.user_last}`,
                            address_zip : req.body.zip,
                            cvc         : /^[0-9]{2,5}$/.test(req.body.card_cvv2) ? req.body.card_cvv2 : (void 0)
                          },
            cardholder  : { 
                            first :  req.body.card_first || req.body.user_first, 
                            last  :  req.body.card_last || req.body.user_last
                          },
            user_data   : {
                            first   : req.body.user_first || req.body.card_first,
                            last    : req.body.user_last  || req.body.card_last,
                            email   : buyTicketsService.defaultUserEmail(),
                            zip     : req.body.zip,
                            country : req.body.country || paymentValidator.CA_ZIP.test(req.body.zip)?'ca':'us'
                          },
            receipt     : parseAndReformatTypes(req.body.tickets),
            total       : req.body.amount,
            event       : req.body.event_id,
            type        : 'tickets',
            scanner     : {
                            scanner_id  : req.body.scanner,
                            location    : req.body.location
                          },
            ip          : req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            skip_duplicate_check: req.body.skip_duplicate_check
        }


        buyTicketsService.buy(paymentBody, {
            source: 'api'
        }).then(resultData => {
            let {payments: createdPayments} = resultData;
            /* Payment object ("is_payment" = true) is always first */
            let paymentData = createdPayments[0];

            let tickets = getCreatedTickets(createdPayments);
            
            res.status(200).json({
                success     : true,
                message     : `Amount: ${paymentBody.total}, ${paymentData.description}`,
                id          : paymentData.purchase_id, 
                ticket_code : paymentData.barcode,
                tickets
            })
        }).catch(err => {
            /**
            * "last_used" property is defined for duplicate payment attempt errors
             * "paymentBodyValidation" is property defined to mark payment body validation errors
            * Stripe Error types are explained here: 
            *           https://github.com/stripe/stripe-node/wiki/Error-Handling 
            **/
            if (!Number.isInteger(err.last_used) && (err.type !== 'StripeCardError') && !err.paymentBodyValidation) {
                ErrorSender.swtError(err, {
                    url         : req.path,
                    method      : req.method,
                    ip          : paymentBody.ip,
                    userAgent   : req.headers['user-agent']
                });
            }

            if (err.validation) {
                res.swtValidation(err.validation, err.last_used, err.payment)
            } else {
                res.customRespError(err, {status: 400});
            }
        });
    },

    // GET /api/swt/events
    events: function (req, res) {
        const event_id   = parseInt(req.query.event, 10);
        const mod = parseInt(req.query.mod, 10);
        const kiosk = req.query.kiosk === 'true';
        const event_start = parseInt(req.query.event_start, 10);
        const event_end = parseInt(req.query.event_end, 10);
    
        if(event_start && event_end && (event_start > event_end)) {
            return res.status(400).json({
                success: false,
                message: 'event_end must be greeter event_start!',
                type: 'validation'
            })
        }
        
        SWTAPIService.findEvents(req, event_id, mod, kiosk, event_start, event_end)
            .then(function (events) {
                if (events.length === 0) {
                    if (mod)
                        return res.status(200).json({success: true, modified: false});
                    else
                        return res.status(200).json({success: false, type: 'not_found', message: 'No events found'});
                } else {
                    res.status(200).json({success: true, events});
                }
            })
            .catch(err => {
                ErrorSender.swtError(err, {
                    url: req.path,
                    method: req.method,
                    ip: req.connection.remoteAddress,
                    userAgent: req.headers['user-agent']
                });
                res.status(500).json({error: err, message: 'DB error', success: false, type: 'internal'});
            });
    },

    // get /api/swt/debug
    debugQuery: function (req, res) {
        if(!optimist.argv['dev']) return res.ok();

        Db.query(SWTAPIService.getPurchaseQuery().where('p.event_id = ?', parseInt(req.query.event, 10) || 33))
        .then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            ErrorSender.swtError(err, {
                url         : req.path,
                method      : req.method,
                ip          : req.connection.remoteAddress,
                userAgent   : req.headers['user-agent']
            })
            res.status(500).json({ error: err, message: 'DB error', success: false, type: 'internal' });
        })
    },

    // POST /api/swt/log_error
    appErrorLogger: function (req, res) {
        try {
            var data = JSON.stringify(req.body);
            loggers.errors_log.error('TicketsGuruCrashReport', data);
            Db.query(
                'INSERT INTO "public"."request_log" \
                ("type", "request_url", "verb", "remote_ip", "user_agent", "data") \
                VALUES( \
                        \'swt_crash_report\', \'' + req.path + '\',\'' + req.method + 
                        '\', \'' + req.connection.remoteAddress + '\', \'' + req.headers['user-agent'] + 
                        '\', \'' + data + '\' )'
            ).then(function () {
                res.ok();
            }).catch(() => {
                res.ok();
            })
        } catch (e) {
            loggers.errors_log.error(e);
            res.ok();
        }
    },
    // covered 😄👍
    // POST /api/swt/wristband
    assignWristbandSerial: function (req, res) {
        SWTAPIService.assignWristbandSerial(req.body)
            .then(purchase => {
                res.status(200).json({
                    success : true,
                    ticket  : purchase
                })
            })
            .catch(err => {
                if(err.validation) {
                    return res.status(200).json({
                        success: false,
                        type: 'validation',
                        message: err.validation
                    });
                }

                if(err.not_exists) {
                    return res.status(200).json({
                        success : false,
                        type    : 'not_exists',
                        message : 'Ticket #{0} does not exist'.format(req.body.ticket)
                    })
                }

                loggers.errors_log.error(err);

                res.status(500).json({
                    error   : err,
                    success : false,
                    type    : 'internal',
                    message : 'DB error'
                });
            })
    },

    // POST /api/swt/pending/recent
    pendingPurchases: function(req, res) {
        const validationResult = paymentValidator.validatePendingPurchases(req.body);

        if (validationResult.error) {
            return res.status(400).json({
                success: false,
                message: validationResult
            })
        }

        co(function* () {
                const query = SWTAPIService.getPendingRecentQuery(req.body);

                let purchases = yield Db.query(query);

                if (!purchases.rows.length) {
                    return res.status(400).json({
                        success: false,
                        message: 'Payments not found'
                    })
                }

                for (let i = 0; i < purchases.rows.length; i++) {
                    const purchase = purchases.rows[i];

                    if (purchase.tickets && purchase.tickets.length) {
                        purchase.description = getDescription(purchase.tickets);
                    }
                }

                res.status(200).json({ success: true, rows: purchases.rows });
        }).catch(err => {
            res.status(500).json({
                success: false,
                message: (err.validation || err.message || 'Internal Error')
            })
        })
    },

    // POST /api/swt/pending/void
    voidPending: function(req, res) {
        const validationResult = paymentValidator.validateVoidPending(req.body);

        if (validationResult.error) {
            return res.status(400).json({ success: false, message: validationResult })
        }


        const { payment_id, scanner, location, event_id } = req.body;

        co(function* () {
            const payment = yield Db.query(PURCHASE_SQL, [payment_id, event_id]);

            if (!payment.rows.length) {
                return res.status(400).json({
                    success: false,
                    message: `Pending Payment with id #${payment_id} not found`
                })
            }


            const { error, message } = validatePayment(_.first(payment.rows));

            if (error) {
                return res.status(400).json({ success: false, message });
            }


            const tr = yield Db.begin();

            try {
                const purchase = yield tr.query(UPDATE_PURCHASE_SQL, [payment_id, event_id]);

                if (!purchase.rowCount) {
                    throw new Error('Pending payment not found');
                }

                yield tr.query(UPDATE_TICKET_SQL, [payment_id, event_id]);
                yield tr.query(UPDATE_PURCHASE_TICKET_SQL, [payment_id, event_id]);
                yield tr.query(setPurchaseHistorySql(scanner, location), [payment_id]);
            } catch (e) {
                if (tr && !tr.isCommited) {
                    tr.rollback();
                }

                return res.status(400).json({ success: false, message: e.message });
            }

            yield tr.commit();

            //TODO: send correct response
            res.status(200).json({ success: true });
        }).catch(err => {
            res.status(500).json({ 
                success: false, 
                message: (err.validation || err.message || 'Internal Error')
            })
        })
    },
    // covered 😄👍
    // POST /api/swt/pending/pay
    payForPendingPayment: function (req, res) {
        let paymentBody = SWTAPIService.formatPaymentBody(req);

        buyTicketsService.payForPendingPayment(paymentBody)
        .then(payment => {
            res.status(200).json({
                success             : true,
                id                  : payment.id,
                message             : getPaidPendingPaymentMsg(payment),
                tickets             : payment.tickets,
                canceled_tickets    : payment.canceled_tickets
            });
        })
        .catch(err => {
            if (err.validation) {
                res.swtValidation(err.validation, err.last_used, err.payment, 400, err.value)
            } else {
                res.customRespError(err, {status: 400});
            }
        });
    },
    //get /api/swt/event/:event/csv
    getScanHistoryCsv (req, res) {
        let eventID     = Number(req.params.event);
        let location    = req.query.location;
        let period      = Number(req.query.period);

        let fileName = `/${eventID}-${location}-${period}.csv`;

        SWTAPIService.history.exportCsv(eventID, location, period)
            .then(data => {
                res.setHeader('Content-Type', 'text/csv');

                res.setHeader(
                    'Content-Disposition',
                    `attachment; filename="${fileName}"`);

                res.write(`Scans data for\n Event: ${eventID}\n Location: ${location}\n`);

                res.write(`SCAN PERIOD, BARCODE\n`);

                data.forEach(scan => {
                    if(scan.created && scan.barcode) {
                        res.write(`${scan.created},${scan.barcode}\n`);
                    }
                });

                res.end();
            })
            .catch(err => {
                res.customRespError(err, {status: 400});
            })
    },

    //POST /api/swt/purchases
    async getPurchasedTickets (req, res) {
        let eventID     = Number(req.body.event_id);
        let afterID     = Number(req.body.after_id);
        let limit       = Number(req.body.limit);
        let modified    = req.body.mod;
        let scannerID   = req.body.scanner;
        let location    = req.body.location;

        let validationError;

        if(!eventID && _.isNaN(eventID)) {
            validationError = 'Event ID is not valid';
        }

        if(modified && !SWTAPIService.modifiedIsValid(modified)) {
            validationError = 'Date Modified is not valid';
        }

        if(limit > 200) {
            validationError = 'Limit can be 200 or less';
        }

        if(!scannerID) {
            validationError = 'Scanner id required';
        }

        if(!location) {
            validationError = 'Scanner location required';
        }

        if(validationError) {
            return res.status(400).json({
                success : false,
                type    : 'validation',
                message : validationError
            })
        }

        try {
            let list = await SWTAPIService.getPurchasedTickets({eventID, modified, afterID, limit});

            if(!list.length) {
                return res.status(200).json({
                    success: false,
                    type   : 'not_found',
                    err    : 'Purchases not found'
                })
            }

            res.status(200).json({
                success : true,
                after_id: list[list.length-1].id, // last purchase id in list
                rows    : list,
            })
        } catch (err) {
            res.status(400).json({
                success: false,
                type   : 'internal',
                err    : err.message
            })
        }
    }
};

function getPaidPendingPaymentMsg (payment) {
    return `Amount $${numeral(payment.amount).format('0,0.00')} paid by ${payment.method
    }, Tickets: ${
        payment.tickets.map(t => 
            `"${t.label}" ($${numeral(t.amount).format('0,0.00')}) for ${t.first} ${t.last}`
        ).join(', ')
    }`;
}

function getDescription(tickets) {
    const groupedTicketsByLabel = _.groupBy(tickets, 'label');
    const keys = Object.keys(groupedTicketsByLabel);

    return keys.map(key => `${key} * ${groupedTicketsByLabel[key].length};`).join(' ');
}

function getDateWithTimezone(date, timezone) {
    return moment(date, moment.ISO_8601).tz(timezone).format('MM/DD/YYYY HH:mm');
}

function validatePayment(payment) {
    const {
        status,
        type,
        timezone,
        canceled_date,
        date_paid
    } = payment;

    const error = true;

    if (status === 'canceled') {
        return {
            error,
            message: `Pending Payment was canceled at ${getDateWithTimezone(canceled_date, timezone)}`,
        }
    }

    if (status === 'paid') {
        return {
            error,
            message: `Already paid at ${getDateWithTimezone(date_paid, timezone)}`,
        };
    }

    if (!_.isNull(type)) {
        return {
            error,
            message: `Corrupted Pending Payment Data. Invalid "type" value`
        };
    }

    return { error: false };
}

const PURCHASE_SQL = `
    SELECT p.*, e.timezone
        FROM purchase p
        LEFT JOIN event e
            ON e.event_id = p.event_id
        WHERE purchase_id = $1
          AND is_payment IS TRUE
          AND kiosk IS NOT NULL
          AND kiosk <> '{}'::JSONB
          AND e.event_id = $2
`;

const UPDATE_PURCHASE_SQL = `
    UPDATE purchase
    SET
        status = 'canceled',
        canceled_date = NOW()
    WHERE purchase_id = $1
      AND status <> 'canceled'
      AND event_id = $2
`;

const UPDATE_TICKET_SQL = `
    UPDATE purchase 
        SET status = 'canceled',
        canceled_date = NOW()
    WHERE linked_purchase_id = $1
      AND is_ticket IS TRUE
      AND status <> 'canceled'
      AND event_id = $2
`;

const UPDATE_PURCHASE_TICKET_SQL = `
    UPDATE purchase_ticket
        SET canceled = NOW()
    WHERE purchase_id IN 
        (
            SELECT purchase_id
            FROM purchase
            WHERE (purchase_id = $1 OR linked_purchase_id = $1)
              AND event_id = $2
        )
    AND canceled IS NULL
`;

function setPurchaseHistorySql(scanner, location) {
    return `
        INSERT INTO purchase_history (purchase_id, action, description)
        VALUES($1, 'payment.canceled', 'Pending Payment was canceled via ${scanner} at ${location}')
    `
}

function getCreatedTickets (purchaseRows) {
    const getTicketObj = purchase => 
                            _.pick(purchase, 
                                'barcode', 'amount', 'first', 'last', 
                                'description', 'wristband_serial');

    let tickets = purchaseRows
                    .filter(p => p.is_ticket)
                    .map(p => getTicketObj(p));

    return tickets;
}

function parseAndReformatTypes (ticketTypesStr) {
    try {
        return (_.isObject(ticketTypesStr)?ticketTypesStr:JSON.parse(ticketTypesStr)).map(type => {
            type.quantity = type.count;
            delete type.count;
            return type;
        })
    } catch (e) {
        loggers.errors_log.error(e);
        return null;
    }
}

function parseTickets (ticketsStr) {
    try {
        return JSON.parse(ticketsStr);
    } catch (e) {
        loggers.errors_log.error(e)
        return null;
    }
}
