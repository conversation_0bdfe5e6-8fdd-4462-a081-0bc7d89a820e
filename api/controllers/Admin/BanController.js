const swUtils = require("../../lib/swUtils");

module.exports = {
    // GET api/admin/ban/email
    listEmail: async  (req, res) => {
        try {
            
            let limit = parseInt(req.query.limit, 10);
            let page = parseInt(req.query.page, 10);
            
            if(!limit || swUtils.isNumberLessThanZero(limit)) {
                limit = 100;
            }
            if(!page || swUtils.isNumberLessThanZero(page)) {
                page = 1;
            }
            
            let search = req.query.search;
            const emails = await AdminBanService.email.getEmailBanInfo(page, limit, search);
            res.status(200).json(emails);
        }
        catch (err) {
            res.customRespError(err);
        }
    },

    // POST api/admin/ban/email
    addEmail: async (req, res) => {
        try {
            const {email, reason} = req.body;
            const row = await AdminBanService.email.insertEmailBan(email, reason);
            res.status(200).json(row);
        }
        catch (err) {
            res.customRespError(err);
        }
    },

    // DELETE api/admin/ban/email/:email
    removeEmail: (req, res) => {
        const banned_email_id = parseInt(req.params.email, 10);
        AdminBanService.email.deleteEmailBan(banned_email_id)
            .then(() => {
                res.ok();
            })
            .catch(err => {
                res.customRespError(err);
            });
    },

    // GET /api/admin/ban/fingerprint
    listFingerprint: async (req, res) => {
        try {
            let limit = parseInt(req.query.limit, 10),
                page  = parseInt(req.query.page, 10),
                search = req.query.search;
            const fingerprints = await AdminBanService.fingerprint.getFingerprintBanInfo(page, limit, search);
            res.status(200).json(fingerprints);
        }
        catch (err) {
            res.customRespError(err);
        }
    },

    // POST api/admin/ban/fingerprint
    addFingerprint: async (req, res) => {
        try {
            const {fingerprint, reason} = req.body;
            const row = await AdminBanService.fingerprint.insertFingerprintBan(fingerprint, reason);
            res.status(200).json(row);
        }
        catch (err) {
            res.customRespError(err);
        }
    },

    // DELETE api/admin/ban/fingerprint/:fingerprint
    removeFingerprint: (req, res) => {
        const banned_fingerprint_id = parseInt(req.params.fingerprint, 10);
        AdminBanService.fingerprint.deleteFingerprintBan(banned_fingerprint_id)
            .then(() => {
                res.ok();
            })
            .catch(err => {
                res.customRespError(err);
            });
    }
}
