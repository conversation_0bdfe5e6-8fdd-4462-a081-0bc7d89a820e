'use strict';

module.exports = {
    index: function (req, res) {
        Db.query(
            `SELECT e.event_id "id", '[' || e.event_id || '] ' || e.long_name "name",
                    TO_CHAR(e.date_start, 'Mon DD, YYYY, HH12:MI AM') "date_start"
             FROM "event" e
             WHERE e.deleted IS NULL
              AND e.allow_teams_registration = TRUE
              AND e.date_start > (NOW() - INTERVAL '2 days')
             ORDER BY e.date_start
            `
        ).then(function (result) {
            res.status(200).json({ events: result.rows });
        }).catch(err => {
            res.serverError(err);
        })
    }
}
