'use strict';

module.exports = {
    find_empty_members: function (req, res) {
        var event_id = +req.param('event');

        var query  =
            'SELECT ev.event_id, ev.name, ev.invalid_teams \
            FROM ( \
                SELECT \
                    e.event_id, \
                    e.name, \
                    ( \
                        SELECT array_to_json(array_agg(row_to_json(teams))) \
                        FROM ( \
                            SELECT  \
                                rt.roster_team_id, \
                                rt.team_name, \
                                count(ma.*) AS athletes, \
                                count(msr.*) AS staff_roles \
                            FROM roster_team rt \
                            LEFT JOIN master_athlete ma ON rt.master_team_id = ma.master_team_id \
                            LEFT JOIN master_staff_role msr ON msr.master_team_id = rt.master_team_id  \
                            WHERE rt.event_id = e.event_id \
                            GROUP BY rt.roster_team_id, rt.team_name \
                            HAVING count(ma.*) < 6 OR count(msr.*) < 3 \
                        ) teams \
                    ) AS invalid_teams \
                FROM "event" e \
            ) ev \
            WHERE ev.invalid_teams IS NOT NULL';
        if(event_id)
            query += ' AND e.event_id = ' + event_id;
        Db.query(query).then(function (result) {
            res.status(200).json({ events: result.rows });
        }).catch(err => {
            res.serverError(err);
        })
    }
}
