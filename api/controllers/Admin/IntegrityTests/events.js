module.exports = [
    {
        title: ' Events with no payment method set for teams entry payments (live future events only) ',
        query:  `SELECT e.event_id, e.date_start, e.long_name
                FROM "event" e
                WHERE e.date_start > NOW()
                  AND e.is_test IS FALSE
                  AND e.allow_check_payments IS FALSE
                  AND e.allow_card_payments IS FALSE
                  AND e.allow_ach_payments IS FALSE
                  AND e.allow_teams_registration IS TRUE
                  AND e.teams_use_clubs_module IS TRUE
                ORDER BY e.date_start`
    },
    {
        title: 'Events without teams_entry_sw_fee',
        query: ` SELECT "teams_entry_sw_fee",
                       stripe_teams_percent,
                       stripe_teams_fixed,
                       stripe_tickets_percent,
                       stripe_tickets_fixed,
                       "event_id",
                       "long_name",
                       date_start,
                       (SELECT COUNT(*) FROM purchase p WHERE p.event_id = e.event_id) purchases
                FROM "event" e
                WHERE e.date_start > NOW()
                  AND (e."teams_entry_sw_fee" = 0 OR e."teams_entry_sw_fee" IS NULL)
                  AND e."is_test" IS FALSE
                  AND e.allow_teams_registration IS TRUE  AND e.teams_use_clubs_module IS TRUE`
    },
    {
        title: ' Events without tickets_sw_fee ',
        query: ` SELECT "tickets_sw_fee", "event_id", "long_name", date_start, tickets_use_connect
                FROM "event" e
                WHERE e.date_start > NOW()
                  AND (e."tickets_sw_fee" = 0 OR e."tickets_sw_fee" IS NULL)
                  AND e."is_test" IS FALSE
                  AND e.allow_ticket_sales IS TRUE
                `,
        fix: function (done) {
            Db.query(
                `
                    UPDATE "event" e
                    SET "tickets_sw_fee" = 1                        
                    WHERE e.date_start > NOW()
                      and (e."tickets_sw_fee" = 0 or e."tickets_sw_fee" is null)
                      and e."is_test" is false
                `
            ).then(function (result) {
                loggers.debug_log.verbose('Updated ', result.rowCount, ' events to have default Stripe Fee')
                done();
            }).catch(err => {
                done(err);
            });
        }
    },
    {
        title: ' Events without Stripe Fee ',
        query: ` select stripe_teams_percent, stripe_teams_fixed,
                  stripe_tickets_percent, stripe_tickets_fixed,
                  ach_teams_percent, ach_teams_max_fee,
                  "event_id", "long_name", date_start,
                  "published",
                  (select count(*) from purchase p where p.event_id = e.event_id) purchases
                from "event" e
                where e.date_start > now()
                    and (e."stripe_teams_percent" = 0
                        or e."stripe_teams_percent" is null
                        or e."stripe_tickets_percent" = 0
                        or e."stripe_tickets_percent" is null
                        or e.ach_teams_percent = 0
                        or e.ach_teams_percent is null
                    )
                    and e."is_test" is false
                    `,
        fix: function (done) {
            Db.query(
                `
                    UPDATE "event" e
                    SET "stripe_teams_percent" = 2.7,
                        "stripe_teams_fixed" = 0.3,
                        "stripe_tickets_percent" = 2.7,
                        "stripe_tickets_fixed" = 0.3,
                        "ach_teams_percent" = 0.8,
                        "ach_teams_max_fee" = 5                        
                    WHERE e.date_start > NOW()
                      and (e."stripe_teams_percent" = 0
                        or e."stripe_teams_percent" is null
                        or e."stripe_tickets_percent" = 0
                        or e."stripe_tickets_percent" is null
                        or e.ach_teams_percent = 0
                        or e.ach_teams_percent is null
                           )
                      and e."is_test" is false
                `
            ).then(function (result) {
                loggers.debug_log.verbose('Updated ', result.rowCount, ' events to have default Stripe Fee')
                done();
            }).catch(err => {
                done(err);
            });
        }

    }, {
        title: 'Events with amount collected by checks more than amount collected by cards',
        query: `
            select ee.event_id,
                   ee.long_name,
                   ee.card_amounts,
                   ee.check_amounts,
                   COALESCE(ee.card_amounts, 0) - ee.check_amounts difference
            from (SELECT e.event_id,
                         e.long_name,
                         COALESCE(SUM(p.amount) FILTER ( WHERE p.type = 'card' ), 0)  card_amounts,
                         COALESCE(SUM(p.amount) FILTER ( WHERE p.type = 'check' ), 0) check_amounts
                  FROM event e
                           JOIN purchase p ON p.event_id = e.event_id AND p.status = 'paid'
                  WHERE e.season = ${sails.config.sw_season.current}
                  GROUP BY e.event_id) ee
            where ee.card_amounts - ee.check_amounts < 0
        `
    }
];
