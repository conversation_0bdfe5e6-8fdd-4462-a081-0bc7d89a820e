module.exports = [
	{
		title: 'Removed Roster Club with NOT removed Roster Teams (for upcoming Events only)',
		query:
			`SELECT 
			    e.long_name "Event", 
			    TO_CHAR(e.date_start, 'Mon DD, YYYY, HH12:MI AM') "Event Starts At",
			    rc.roster_club_id, 
			    rc.master_club_id,
			    rc.club_name "Club Name", 
			    TO_CHAR(rc.created, 'Mon DD, YYYY, HH12:MI AM') "Created",
			    TO_CHAR(rc.deleted, 'Mon DD, YYYY, HH12:MI AM') "Deleted",
			    u.email "Account"
			FROM roster_club rc
			INNER JOIN "event" e 
			    ON e.event_id = rc.event_id
			INNER JOIN roster_team rt 
			    ON rt.roster_club_id = rc.roster_club_id 
			    AND rt.deleted is null
			LEFT JOIN "master_club" mc
			    ON mc.master_club_id = rc.master_club_id
			INNER JOIN "club_owner" co
			    ON co.club_owner_id = mc.club_owner_id
			INNER JOIN "user" u
			    ON u.user_id = co.user_id
			WHERE e.date_start > NOW()
			    AND rc.deleted IS NOT NULL
			ORDER BY u.email, rc.created desc`,
		fix: function (done) {
			Db.query(
				`UPDATE roster_club 
				 SET deleted = NULL
				 WHERE roster_club_id IN (
				   SELECT rc.roster_club_id
				   FROM roster_club rc
				   INNER JOIN event e 
				   		ON e.event_id = rc.event_id
				   INNER JOIN roster_team rt 
				     	ON rt.roster_club_id = rc.roster_club_id 
				     	AND rt.deleted IS NULL
				   WHERE e.date_start > NOW()
				         AND rc.deleted IS NOT NULL
				 )`
			).then(function () {
				done();
			}, function (err) {
				done(err)
			})
		}
	}
]