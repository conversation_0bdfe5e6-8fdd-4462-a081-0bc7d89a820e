module.exports = [
    {
        title: 'Master-Roster Members Disparity ( not existing roster members for master members)',
        query:
            `select
                d.event_id "Event", d.roster_team_id "Team", d.team_name "Team Name", d.created, d.imports,
                d.athletes "Athletes Count", d.staff "Staff Count", d.master_club_id "club",
                d.user_id "user", d.email
            from (
                select rt.event_id, rt.roster_team_id, mt.master_club_id,
                       u.user_id, u.email, mt.team_name, to_char(rt.created, 'Mon DD, YYYY, HH12:MI AM') created,
                    (
                        select string_agg(to_char(wq.requested, '<PERSON> DD, YYYY, HH12:MI AM'), ', ')
                        from "webpoint_queue" wq
                        where wq.master_club_id = mt.master_club_id
                            and wq.requested > '2015-09-01 00:00:00'::timestamp
                    ) "imports", (
                     select count(ma.master_athlete_id)
                     from master_athlete ma
                     where ma.master_team_id = mt.master_team_id
                        and ma.deleted is null
                        and ma.season = ${sails.config.sw_season.current}
                        and ma.master_athlete_id not in (
                            select ra.master_athlete_id
                            from roster_athlete ra
                            where ra.roster_team_id = rt.roster_team_id
                                and ra.event_id = rt.event_id
                        )
                    ) "athletes", (
                     select count(msr.master_staff_role_id)
                     from master_staff_role msr
                     inner join master_staff ms
                        on ms.master_staff_id = msr.master_staff_id
                        and ms.season = ${sails.config.sw_season.current}
                     where msr.master_team_id = mt.master_team_id
                         and not exists (
                             select rsr.roster_staff_role_id
                             from roster_staff_role rsr
                             where rsr.master_staff_id = msr.master_staff_id
                                 and rsr.master_team_id = msr.master_team_id
                                 and rsr.roster_team_id = rt.roster_team_id
                         )
                    ) "staff"
                from "roster_team" rt
                inner join master_team mt
                    on mt.master_team_id = rt.master_team_id
                    and mt.deleted is null
                left join master_club mc
                    on mc.master_club_id = mt.master_club_id
                left join "club_owner" co
                    on co.club_owner_id = mc.club_owner_id
                left join "user" u
                    on u.user_id = co.user_id
                inner join "event" e
                    on e.event_id = rt.event_id
                where e.date_reg_open <= (now() AT TIME ZONE e.timezone)
                    and e.roster_deadline >= (now() AT TIME ZONE e.timezone)
                    and e.registration_method = 'club'
                    and e.live_to_public is true
                    and e.published is true
                    and e.teams_use_clubs_module is true
                    and rt.deleted is null
            ) "d"
            where d.staff > 0 or d.athletes > 0
            order by d.email, d.team_name, d.event_id`,
        fix: async function (done) {
            try {
                const clubsList = await Db.query(findClubsSQL).then(r => r.rows);
                const season = sails.config.sw_season.current;
                const errors = [];
                for(const club of clubsList) {
                    await RosterSnapshotService.clubSnapshot(club.id, season, 'insert')
                        .catch(err => errors.push(err))
                }
                if(errors.length > 0) {
                    throw errors;
                }
                return done && done();
            }
            catch(err) {
                loggers.errors_log.error(err);
                return done && done(err)
            }
        }
    }, {
        title: 'Wrong Members Count',
        query: 'select team.* \
                from ( \
                    select \
                        e.event_id "Id", e.long_name "Event Name", \
                        rt.team_name "Team Name", rt."roster_athletes_count",  \
                        rt."roster_staff_count", \
                        (select count(ra.*) from roster_athlete ra    \
                                where ra.roster_team_id = rt.roster_team_id    \
                                    and ra.deleted is null ) "athletes", \
                        (select count(rsr.*) from roster_staff_role rsr    \
                                where rsr.roster_team_id = rt.roster_team_id   \
                                    and rsr.deleted is null)  "staff" \
                    from "roster_team" rt \
                    inner join "event" e   \
                        on rt.event_id = e.event_id   \
                        and e.date_reg_open <= (now() AT TIME ZONE e.timezone)   \
                        and e.roster_deadline >= (now() AT TIME ZONE e.timezone)   \
                        and e.registration_method = \'club\' \
                        and e.live_to_public is true \
                        and e.published is true\
                        and e.teams_use_clubs_module is true \
                    where rt.deleted is null \
                ) "team" \
                where team.roster_athletes_count <> team.athletes  \
                    or team.roster_staff_count <> team.staff',
        fix: function (done) {
            var sql = 
                'update "roster_team" rt \
                set "roster_athletes_count" = \
                        (select count(ra.*) from roster_athlete ra \
                            where ra.roster_team_id = rt.roster_team_id \
                                and ra.deleted is null ),\
                    "roster_staff_count" = \
                        (select count(rsr.*) from roster_staff_role rsr \
                            where rsr.roster_team_id = rt.roster_team_id  \
                                and rsr.deleted is null)  \
                where rt.roster_team_id in(\
                    select team.roster_team_id\
                from (\
                    select\
                        rt.roster_team_id,\
                        rt."roster_athletes_count", \
                        rt."roster_staff_count",\
                        (select count(ra.*) from roster_athlete ra   \
                                where ra.roster_team_id = rt.roster_team_id   \
                                    and ra.deleted is null ) "athletes",\
                        (select count(rsr.*) from roster_staff_role rsr   \
                                where rsr.roster_team_id = rt.roster_team_id  \
                                    and rsr.deleted is null)  "staff"\
                    from "roster_team" rt\
                    inner join "event" e  \
                        on rt.event_id = e.event_id  \
                        and e.date_reg_open <= (now() AT TIME ZONE e.timezone)  \
                        and e.roster_deadline >= (now() AT TIME ZONE e.timezone)  \
                        and e.registration_method = \'club\'\
                        and e.live_to_public is true\
                        and e.published is true \
                        and e.teams_use_clubs_module is true \
                    where rt.deleted is null\
                    ) "team"\
                    where team.roster_athletes_count <> team.athletes \
                        or team.roster_staff_count <> team.staff\
                )'
            Db.query(sql).then(function () {
                done()
            }).catch(err => {
                done(err)
            })
        }
    }, {
        title: 'Duplicate Roster Staff Role Lines',
        query: 
            `select distinct on (d."USAV") d.*, u.email "Email" 
            from (
                select  rsr.roster_staff_role_id "rsrId", rsr.created, rsr.modified, 
                        rsr.role_id "RoleID", rsr.master_team_id "Master Team", 
                        rsr.roster_team_id "Team Id", rsr.primary "Primary", rsr.master_staff_id "Master Staff", 
                        rsr.deleted "Deleted", ms.organization_code "USAV", rt.event_id "Event", 
                        ms.master_club_id "Club", ms.last "Last", array_agg(dup.roster_staff_role_id) "Dups"
                from "roster_staff_role" rsr
                inner join "roster_team" rt
                    on rt.roster_team_id = rsr.roster_team_id
                inner join "roster_staff_role" dup
                    on dup.roster_staff_role_id <> rsr.roster_staff_role_id
                    and dup.roster_team_id = rsr.roster_team_id
                    and dup.master_staff_id = rsr.master_staff_id
                inner join master_staff ms
                    on ms.master_staff_id = rsr.master_staff_id
                    --and dup.deleted is not null
                --where rsr.deleted is not null
                group by rsr.roster_staff_role_id, rt.event_id, 
                         ms.organization_code, ms.master_club_id, ms.last
                having count(dup.*) > 0
                order by rsr.master_staff_id, rsr.roster_team_id
            ) "d"
            left join "master_club" mc
                on mc.master_club_id = d."Club"
            left join "club_owner" co
                on co.club_owner_id = mc.club_owner_id
            left join "user" u
                on u.user_id = co.user_id`
    }, {
        title: 'Roster Staff Roles with removed Master Staff Roles (upcoming events)',
        query: 
            `SELECT rsr.*
             FROM "roster_staff_role" rsr 
             INNER JOIN "roster_team" rt 
                 ON rt.roster_team_id = rsr.roster_team_id
                 AND rt.deleted IS NULL
                 AND rt.status_entry <> 11
             INNER JOIN "division" d
                 ON d.division_id = rt.division_id
             INNER JOIN "event" e 
                 ON e.event_id = rt.event_id
                 AND e.date_start > NOW()
             LEFT JOIN "master_staff_role" msr 
                 ON msr.master_staff_id = rsr.master_staff_id
                 AND msr.master_team_id = rsr.master_team_id
             INNER join "master_staff" ms 
                 ON rsr.master_staff_id = ms.master_staff_id
                 AND ms.season = (${sails.config.sw_season.current})::INTEGER
                 AND ms.deleted IS NULL
             WHERE msr is null 
                 AND rsr.deleted is NULL
                 AND (
                     COALESCE(d.roster_deadline, e.roster_deadline) > (NOW() AT TIME ZONE e.timezone)
                 )`,
        fix: done => {
            Db.query(
                `UPDATE "roster_staff_role" 
                 SET "deleted" = NOW() 
                 WHERE "roster_staff_role_id" IN (
                     SELECT rsr.roster_staff_role_id
                     FROM "roster_staff_role" rsr 
                     INNER JOIN "roster_team" rt 
                         ON rt.roster_team_id = rsr.roster_team_id
                         AND rt.deleted IS NULL
                         AND rt.status_entry <> 11
                     INNER JOIN "division" d
                         ON d.division_id = rt.division_id
                     INNER JOIN "event" e 
                         ON e.event_id = rt.event_id
                         AND e.date_start > NOW()
                     LEFT JOIN "master_staff_role" msr 
                         ON msr.master_staff_id = rsr.master_staff_id
                         AND msr.master_team_id = rsr.master_team_id
                     INNER join "master_staff" ms 
                         ON rsr.master_staff_id = ms.master_staff_id
                         AND ms.season = $1
                         AND ms.deleted IS NULL
                     WHERE msr is null 
                         AND rsr.deleted is NULL
                         AND (
                             COALESCE(d.roster_deadline, e.roster_deadline) > (NOW() AT TIME ZONE e.timezone)
                         )
                 )`,
                [sails.config.sw_season.current]
            ).then(() => {
                done();
            }).catch(err => {
                done(err);
            });
        }
    }
];

var findClubsSQL = `
select distinct d.master_club_id "id"
from ( 
    select mt.master_club_id, ( 
         select count(ma.master_athlete_id) 
         from master_athlete ma  
         where ma.master_team_id = mt.master_team_id  
            and ma.deleted is null  
            and ma.season = ${sails.config.sw_season.current}  
            and ma.master_athlete_id not in (  
                select ra.master_athlete_id   
                from roster_athlete ra  
                where ra.roster_team_id = rt.roster_team_id  
                    and ra.event_id = rt.event_id 
            )  
     ) "athletes", (  
            select count(msr.master_staff_role_id) 
            from master_staff_role msr 
            inner join master_staff ms 
                on ms.master_staff_id = msr.master_staff_id 
                and ms.season = ${sails.config.sw_season.current}  
            where msr.master_team_id = mt.master_team_id 
                and not exists (  
                    select rsr.roster_staff_role_id  
                    from roster_staff_role rsr  
                    where rsr.master_staff_id = msr.master_staff_id 
                        and rsr.master_team_id = msr.master_team_id 
                        and rsr.roster_team_id = rt.roster_team_id    
                )  
     ) "staff" 
    from "roster_team" rt  
    inner join master_team mt  
        on mt.master_team_id = rt.master_team_id  
        and mt.deleted is null 
    left join master_club mc 
        on mc.master_club_id = mt.master_club_id 
    left join "club_owner" co 
        on co.club_owner_id = mc.club_owner_id 
    left join "user" u 
        on u.user_id = co.user_id 
    inner join "event" e 
        on e.event_id = rt.event_id 
    where e.date_reg_open <= (now() AT TIME ZONE e.timezone) 
        and e.roster_deadline >= (now() AT TIME ZONE e.timezone) 
        and e.registration_method = 'club' 
        and e.live_to_public is true  
        and e.published is true
        and e.teams_use_clubs_module is true 
        and rt.deleted is null 
) "d" 
where d.staff > 0 or d.athletes > 0`;
