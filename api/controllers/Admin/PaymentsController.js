'use strict';

module.exports = {
    index: function (req, res) {
        var event_id = req.param('event'),
            type = req.param('type');

        var query =
            squel.select().from('purchase', 'p')
            .field('p.purchase_id', 'id')
            .field('p.amount')
            .field('COALESCE(p.amount_refunded, 0) AS amount_refunded')
            .field('p.type')
            .field('p.created')
            .left_join('event',  'e', 'e.event_id = p.event_id')
            .left_join('event_owner', 'eo', 'eo.event_owner_id = e.event_owner_id')
            .order('p.created');
        if(event_id)
            query.where('p.event_id = ?', event_id);
        if(type)
            query.where('p.type = ?', type);
        Db.query(query).then(function (result) {
            res.status(200).json({payments: result.rows});
        }).catch(err => {
            res.serverError(err);
        })
    },
    refunded: async function (req, res) {
        const payment_id = req.param('payment');

        if(!payment_id) {
            return res.status(400).json({error: 'No payment identifier specified.'});
        }

        try {
            const refund = await AdminPaymentsService.checkPaymentRefunded(payment_id);

            return res.status(200).json({refund});
        } catch (err) {
            return res.serverError();
        }
    },
    roster_teams: async function (req, res) {
        const eventID = req.param('event');

        try {
            const result = await AdminPaymentsService.getRosterTeamsData(eventID);
            return res.status(200).json({
                event: [],
                teams: result,
            });
        } catch (err) {
            return res.customRespError(err);
        }
    },
    check_team_payment: async function (req, res) {
        const roster_team_id = req.param('team');

        if(!roster_team_id) {
            return res.status(400).json({error: 'No team identifier passed'});
        }

        try {
            const responseData = await AdminPaymentsService.checkTeamPayment(roster_team_id);

            return res.status(200).json(responseData);
        } catch (err) {
            return res.serverError();
        }
    },
    check_card_payment: async function (req, res) {
        const payment_id = req.param('payment');

        if(!payment_id) {
            return res.status(400).json({error: 'No payment identifier passed.'});
        }

        try {
            const errors = await AdminPaymentsService.checkCardPayment(payment_id);

            return res.status(200).json({errors: errors});
        } catch (err) {
            return res.serverError(err);
        }
    },
    // GET /api/admin/payments/ban_disputes
    banLostDisputes: function (req, res) {
        banService.banLostDisputes()
        .then(() => {
            res.ok();
        }).catch(err => {
            res.customRespError(err);
        })
    }
}
