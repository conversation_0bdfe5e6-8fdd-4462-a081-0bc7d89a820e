module.exports = {
    // GET /api/admin/redis/keys/all
    all_keys: function (req, res) {
        const type = req.param('type');
        let tasks = [];
        let result = {};

        if(type === 'cache') {
            tasks.push(Cache.getKeys('*').then((keys) => result.keys = keys));
        }

        result.cache_time = Object.keys(Cache.KEYS_MAP).reduce(
            (acc, key)=>{
                acc[Cache.KEYS_MAP[key]] = Cache.getTtl(Cache.KEYS_MAP[key]);
                return acc;
            },
            {}
        );

        Promise.all(tasks)
            .then(() => res.status(200).json(result))
            .catch(res.customRespError);
    },

    // POST /api/admin/redis/key/retrieve
    key_value: function (req, res) {
        const key = req.param('key');
        if(!key) return res.status(400).json({error: 'No key specified'});
        RedisService.getKey(key, function (err, result) {
            if(err) return res.status(500).json({error: err});
            return res.status(200).json({value: result});
        });
    },

    // POST /api/admin/redis/key/remove
    remove_by_mask: async function (req, res) {
        const type = req.param('type');
        let count = 0;
        const actions = {
            async cache() {
                count = await Cache.removeByMask('*') + count;
            },
            async session() {
                count = await RedisService.deleteSessionKeys() + count;
            },
            async all() {
                return Promise.all(
                    Object.keys(this)
                        .filter(k => k !== 'all')
                        .map(k => this[k]())
                )
            }
        };
        try {
            if(actions[type]) {
                await actions[type]();
            }
            else {
                throw { validation: 'invalid action' };
            }
            res.json({
                removed: count,
            });
            loggers.debug_log.verbose(`Removed ${count} keys from redis.`);
        }
        catch(err) {
            res.customRespError(err);
        }
    },

    // POST /api/admin/redis/cache/expires
    setExpiresCacheTime: async function (req, res) {
        try {
            const ALLOWED_TTL_KEYS = Object.values(Cache.KEYS_MAP);
            const key = req.param('key');
            const value = Number(req.param('value'));
            if (!ALLOWED_TTL_KEYS.includes(key)) {
                return res.validation('Invalid key');
            }
            if (Number.isNaN(value) || value < 0) {
                return res.validation('Invalid ttl value');
            }

            await Cache.updateCacheTime(value, key);
            res.status(201).send();
        }
        catch(err) {
            res.customRespError(err);
        }
    }
}
