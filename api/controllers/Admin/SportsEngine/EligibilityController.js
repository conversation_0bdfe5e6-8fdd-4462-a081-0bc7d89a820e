
module.exports = {
    // GET /api/admin/sportsengine/members
    async findMembers(req, res) {
        const filters = req.query;
        try {
            const members = [];
            for await (const member of SportsEngineService.getEligibility(filters)) {
                members.push(member)
            }
            res.status(200).json(members);
        }
        catch(err) {
            return res.customRespError(err);
        }
    },
};
