'use strict';

module.exports = {
    renderInvoice: function (req, res) {
        let id = parseInt(req.params.id, 10);
        if(!id) {
        	return res.send('Invalid invoice identifier passed');
        }

        TeamsEntryInvoiceService.findPurchaseData(id)
        .then(invoiceData => {
        	res.render('invoice.ejs', invoiceData);
        }).catch(err => {
        	if(err.validation) {
        		res.render('500', { error: err.validation });
        	} else {
        		res.serverError();
        	}
        });
    }
}
