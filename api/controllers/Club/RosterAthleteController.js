'use strict';

module.exports = {
    // get /api/club/event/:event/athlete/:athlete/info
    getAthlete: function (req, res) {
        var $id                 = parseInt(req.params.athlete, 10),
            $event_id           = parseInt(req.options.event || req.params.event, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10);
        
        if(!$id) 
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id) 
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id) 
            return res.forbidden('No club found. Forbidden')

        Db.query(
            `SELECT  
                ra.roster_athlete_id, ra.master_athlete_id, ra.event_id, 
                COALESCE(ra.jersey::TEXT, 'N/A') "jersey_txt", COALESCE(ma.jersey::TEXT, 'N/A') "default_jersey_txt",
                COALESCE(ra.aau_jersey::TEXT, 'N/A') "aau_jersey_txt", COALESCE(ma.aau_jersey::TEXT, 'N/A') "default_aau_jersey_txt",
                ra.jersey, ra.sport_position_id, ra.aau_jersey, ma.aau_jersey "default_aau_jersey",
                INITCAP(ar.name) AS role,
                ma.jersey "default_jersey", ma.sport_position_id "default_position",
                COALESCE(sp.short_name, 'N/A') "default_position_name",
                COALESCE(spr.short_name, 'N/A') "position_name",
                ma.first, ma.last, (
                    CASE
                        WHEN ra.deleted_by_user IS NOT NULL 
                            THEN TO_CHAR((ra."deleted_by_user" AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MI AM') 
                        ELSE NULL
                    END
                ) "deleted_by_user",
                ma.organization_code "usav_code",
                COALESCE(ma.gradyear::TEXT, 'N/A') "gradyear",
                INITCAP(ma.gender::TEXT) "gender",
                TO_CHAR(ma.birthdate, 'MM/DD/YYYY') "birthdate",
                ma.age, 
                COALESCE(ma.membership_status, 'N/A') "membership_status"
            FROM roster_athlete ra  
            INNER JOIN "event" e 
                ON e.event_id = ra.event_id
            INNER JOIN master_athlete ma 
                ON ma.master_athlete_id = ra.master_athlete_id 
                AND ma.master_club_id = $3 
            LEFT JOIN "sport_position" sp 
                ON sp.sport_position_id = ma.sport_position_id
            LEFT JOIN "sport_position" spr 
                ON spr.sport_position_id = ra.sport_position_id
            LEFT JOIN "athlete_role" ar
                ON ar.athlete_role_id = COALESCE(ra.role, ma.role)  
            WHERE ra.roster_athlete_id = $1 
                AND ra.event_id = $2`, 
            [$id, $event_id, $master_club_id]
        ).then(result => {
            res.status(200).json({ athlete: result.rows[0] || {} });
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // post /api/club/event/:event/athlete/:athlete/withdraw
    withdraw: function (req, res) {
        var $id                 = parseInt(req.options.event || req.params.athlete, 10),
            $event_id           = parseInt(req.params.event, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10);

        if(!$id) 
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id) 
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id) 
            return res.forbidden('No club found. Forbidden');

        RosterSnapshotService.withdrawByUser($id, $master_club_id, $event_id, 'athlete')
        .then(function () {
            res.ok()
        }).catch(function (err) {
            res.customRespError(err);
        });
    },
    // put /api/club/event/:event/athlete/:athlete/update
    update: function (req, res) {
        const $id               = parseInt(req.params.athlete, 10),
            $event_id           = parseInt(req.options.event || req.params.event, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10),
            $user_id            = parseInt(req.session.passport.user.user_id),
            /**
             * @type {Object} $defaultChanges
             * @type {Number} $defaultChanges.jersey
             * @type {Number} $defaultChanges.aau_jersey
             * @type {String} $defaultChanges.position
             */
            $defaultChanges       = req.body.d,
            $override                     = req.body.o;

        if(!$id) 
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id) 
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id) 
            return res.forbidden('No club found. Forbidden');
        if(_.isEmpty($defaultChanges) && _.isEmpty($override))
            return res.validation('Nothing to save: no data changed');

        const PRIVATE_LINK_REG = CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK;

        Db.query(
            `SELECT 
                ra.roster_athlete_id, ra.master_athlete_id, rt.locked, rt.roster_team_id,
                COALESCE(ra.jersey, ma.jersey) jersey, COALESCE(ra.aau_jersey, ma.aau_jersey) aau_jersey,
                sp.short_name position_name, FORMAT('%s %s', ma.first, ma.last) "name",
                TO_CHAR(COALESCE(d.roster_deadline, e.roster_deadline), 'Mon DD, YYYY, HH12:MI AM') "deadline",
                (COALESCE(d.roster_deadline, e.roster_deadline) < (NOW() AT TIME ZONE e.timezone)) "deadline_passed",
                ma.jersey master_jersey, ma.aau_jersey master_aau_jersey, spm.short_name master_position
             FROM "roster_athlete" ra 
             INNER JOIN "roster_team" rt
                ON rt.roster_team_id = ra.roster_team_id
                AND rt.event_id = $2
                AND rt.deleted IS NULL 
             INNER JOIN "event" e 
                ON e.event_id = rt.event_id
                AND (
                    CASE WHEN rt.reg_method = $4
                        THEN TRUE
                        ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                    END    
                )
                AND e.registration_method = 'club'  
                AND e.live_to_public IS TRUE 
                AND e.published IS TRUE
                AND e.teams_use_clubs_module IS TRUE
             INNER JOIN "division" d 
                 ON d.division_id = rt.division_id 
             LEFT JOIN master_team mt  
                 ON mt.master_team_id = rt.master_team_id 
             INNER JOIN master_club mc  
                 ON mc.master_club_id = mt.master_club_id 
                 AND mc.master_club_id = $3
             LEFT JOIN master_athlete ma
                 ON ma.master_athlete_id = ra.master_athlete_id
             LEFT JOIN sport_position sp
                 ON sp.sport_position_id = COALESCE(ra.sport_position_id, ma.sport_position_id)
             LEFT JOIN sport_position spm
                 ON spm.sport_position_id = ma.sport_position_id
             WHERE ra.roster_athlete_id = $1
                AND ra.deleted IS NULL`,
            [$id, $event_id, $master_club_id, PRIVATE_LINK_REG]
        ).then(function (result) {
            var athlete = _.first(result.rows);
            if(_.isEmpty(athlete)) {
                throw {
                    validation: `Operation Failed: Athlete not found has been removed 
                    or the Athlete's Team has been removed`
                }
            }
            if (athlete.locked) {
                throw {
                    validation: `Can't change roster information. Roster is locked. 
                    ${(!_.isEmpty($defaultChanges)?'To Update the default values, please, use "Athletes Tab"':'')}`
                }
            }
            return athlete;
        }).then(function (athlete) {
            return Db.begin()
            .then(function (tr) {
                return Promise.all([
                    __updateAthleteRow(tr, $defaultChanges, athlete.master_athlete_id, 'master_athlete'),
                    __updateAthleteRow(tr, $override, athlete.roster_athlete_id, 'roster_athlete')
                ]).then(function (result) {
                    var updatedDefault  = result[0],
                        updatedOverride = result[1];
                    if(!(updatedDefault || updatedOverride)) {
                        throw {
                            validation: 'Update Failed: No rows to change found'
                        }
                    }
                    
                    let updatedData = {
                        d: {
                            position_name   : updatedDefault && updatedDefault.position_name,
                            jersey          : updatedDefault && updatedDefault.jersey,
                            aau_jersey      : updatedDefault && updatedDefault.aau_jersey
                        },
                        o: {
                            position_name   : updatedOverride && updatedOverride.position_name,
                            jersey          : updatedOverride && updatedOverride.jersey,
                            aau_jersey      : updatedOverride && updatedOverride.aau_jersey
                        }
                    }
                    
                    if(_.isNull(updatedData.o.jersey)) {
                        updatedData.o.jersey = athlete.master_jersey;
                    }
                    if(_.isNull(updatedData.o.aau_jersey)) {
                        updatedData.o.aau_jersey = athlete.master_aau_jersey;
                    }
                    if(_.isNull(updatedData.o.position_name)) {
                        updatedData.o.position_name = athlete.master_position;
                    }
                    
                    return updatedData;
                }).then((updatedData) => {
                    
                    const result = {
                        data    : updatedData,
                        tr      : tr
                    };
                    
                    const oldData = {
                        name: athlete.name,
                        jersey: _.has($override, 'jersey') ? athlete.jersey
                            : _.has($defaultChanges, 'jersey') ? athlete.master_jersey : undefined,
                        aau_jersey: _.has($override, 'aau_jersey') ? athlete.aau_jersey
                            : _.has($defaultChanges, 'aau_jersey') ? athlete.master_aau_jersey : undefined,
                        sportPosition: _.has($override, 'position') ? athlete.position_name
                            : _.has($defaultChanges, 'position') ? athlete.master_position : undefined
                    }
                    
                    const newData = {
                        name: athlete.name,
                        jersey: _.has($override, 'jersey') ? updatedData.o.jersey
                            : _.has($defaultChanges, 'jersey') ? updatedData.d.jersey : undefined,
                        aau_jersey: _.has($override, 'aau_jersey') ? updatedData.o.aau_jersey
                            : _.has($defaultChanges, 'aau_jersey') ? updatedData.d.aau_jersey : undefined,
                        sportPosition: _.has($override, 'position') ? updatedData.o.position_name
                            : _.has($defaultChanges, 'position') ? updatedData.d.position_name : undefined
                    }
                    
                    if(!_.isEqual(oldData, newData)) {
                        const query= knex('event_change').insert({
                            event_id: $event_id,
                            roster_team_id: athlete.roster_team_id,
                            user_id: $user_id,
                            action: 'team.roster.athlete.changed',
                            old_data: JSON.stringify(oldData),
                            new_data: JSON.stringify(newData)
                        })
                        return tr.query(query).then(() => {
                            return result;
                        });
                        
                    } else {
                        return result;
                    }
                }).catch(function (err) {
                    if(tr && !tr.isCommited) {
                        tr.rollback()
                    }
                    throw err;
                })
            }).then(function (updatedData) {
                return updatedData.tr.commit().then(function () {
                    return updatedData.data
                });
            })
        }).then(function (responseBody) {
            res.status(200).json(responseBody)
        }, function (err) {
            res.customRespError(err);
        })
    },
    // post /api/v2/club/athletes/events_list
    getAssignedEvents: function (req, res) {
        var $athletes = req.body.athletes,
            $master_club_id = parseInt(req.session.passport.user.master_club_id, 10);

        if(!_.isArray($athletes))
            return res.validation('Expecting athletes to be an array of integers');
        if(!$athletes.length)
            return res.validation('Expecting athletes not to be empty');
        if(!$master_club_id)
            return res.validation('No club found. Access denied', 403);

        let athletesIds = _.filter($athletes, Number.isInteger);

        if(!athletesIds.length)
            return res.validation('Expecting athletes items to be integers');

        let athletesIdsPGArr = `{${athletesIds.join(',')}}`;

        const PRIVATE_LINK_REG = CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK;

        let FIND_ASSIGNED_EVENTS = 
        `SELECT DISTINCT e.event_id, e.long_name, e.name
        FROM roster_athlete ra 
        INNER JOIN master_athlete ma 
            ON ma.master_athlete_id = ra.master_athlete_id 
            AND ma.master_club_id = $1 
        INNER JOIN roster_team rt 
            ON rt.roster_team_id = ra.roster_team_id 
            AND rt.deleted IS NULL 
            AND rt.status_entry IN (12, 13, 14) 
        LEFT JOIN "division" d 
            ON d.division_id = rt.division_id 
        INNER JOIN "event" e 
            ON e.event_id = ra.event_id 
            AND (
                CASE WHEN rt.reg_method = $2
                    THEN TRUE
                    ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                END    
            )
            AND COALESCE(d.roster_deadline, e.roster_deadline) >= (NOW() AT TIME ZONE e.timezone) 
            AND e.registration_method = 'club' 
            AND e.live_to_public IS TRUE 
            AND e.published IS TRUE 
            AND e.teams_use_clubs_module IS TRUE
        WHERE ra.master_athlete_id  = ANY($3)
            AND ra.deleted IS NULL`;

        Db.query(
            FIND_ASSIGNED_EVENTS, 
            [$master_club_id, PRIVATE_LINK_REG, athletesIdsPGArr]
        ).then(result => {
            res.status(200).json({ events: result.rows });
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // post /api/club/event/:event/athlete/:athlete/reinstate
    reinstate: function (req, res) {
        let $id                 = parseInt(req.params.athlete, 10),
            $event_id           = parseInt(req.params.event, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10);

        if(!$id) 
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id) 
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id) 
            return res.forbidden('No club found. Forbidden');

        RosterSnapshotService.reinstateMember($id, $master_club_id, $event_id, 'athlete')
        .then(function () {
            res.ok()
        }).catch(function (err) {
            res.customRespError(err);
        });
    }
}

function __updateAthleteRow (tr, data, id, table) {
    if(_.isEmpty(data))
        return Promise.resolve({});

    var sql = squel.update().table(table).returning('*')
            .where(`${table}_id = ?`, id)
            .returning(
                `(SELECT p.short_name FROM "sport_position" p
                  WHERE p.sport_position_id = "${table}".sport_position_id
                 ) "position_name", "jersey", "aau_jersey"`
            );

    if(data.position !== undefined) {
        sql.set('sport_position_id', data.position)
    }

    if(data.jersey !== undefined) {
        if(!_.isNull(data.jersey) && data.jersey <= 0) {
            return Promise.reject({
                validation: 'Uniform must be a positive number'
            })
        }
        sql.set('jersey', data.jersey)
    }

    if(data.aau_jersey !== undefined) {
        if(!_.isNull(data.aau_jersey) && data.aau_jersey <= 0) {
            return Promise.reject({
                validation: 'AAU Uniform must be a positive number'
            })
        }
        sql.set('aau_jersey', data.aau_jersey)
    }

    return tr.query(sql)
    .then(function (result) {
        return _.first(result.rows);
    })
}
