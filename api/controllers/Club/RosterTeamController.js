'use strict';

const argv = require('optimist').argv;
const co = require('co');

module.exports = {
    // TODO
    all: function (req, res) {
        return res.ok();
    },
    // TODO
    find: function (req, res) {
        return res.ok();
    },
    // TODO
    create: function (req, res) {
        return res.ok();
    },
    // TODO
    removeTeam: function (req, res) {
        return res.ok();
    },
    // post /api/club/event/:event/team/assign
    enterEvent: async function (req, res) {
        try {
            var $event_id           = parseInt(req.options.event || req.params.event, 10),
                $master_team_id     = parseInt(req.body.team, 10),
                $division_id        = parseInt(req.body.division, 10),
                $club_owner_id      = parseInt(req.session.passport.user.club_owner_id, 10),
                $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10),
                $reg_method         = req.options.event
                    ? CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK
                    : CheckInRosterService.ROSTER_TEAM_REG_METHOD.REGULAR;

            if(!$event_id)          return res.validation('No event identifier passed');
            if(!$master_team_id)    return res.validation('No team identifier passed');
            if(!$division_id)       return res.validation('No division identifier passed');
            if(!$club_owner_id)     return res.validation('Not a club owner');
            if(!$master_club_id)    return res.validation('No club created');

            if ($division_id > 0) {
                const isValidAge = await CheckInRosterService.validateAge({
                    masterTeamId: $master_team_id,
                    rosterTeamId: null,
                    divisionId: $division_id
                });
                if(!isValidAge) {
                    return res.validation('Division age must be not less than team age');
                }

                const sanctionValidationResult = await CheckInRosterService.validateSanction($master_club_id, $event_id);

                if (!sanctionValidationResult) {
                    return res.validation('Your club does not have the correct sanctioning to enter this event');
                }

                await CheckInRosterService.validateSeasonality({
                    masterTeamID: $master_team_id,
                    eventID: $event_id,
                    divisionID: $division_id
                });

                const idValidationResult =
                    await CheckInRosterService.validateMasterTeamRosterWithMasterTeamId($event_id, $master_team_id);

                if (idValidationResult.length) {
                    return res.validation(_.first(idValidationResult));
                }

                await EventRegistrationService.registrationRules.checkAdditionalRules({
                    eventID: $event_id,
                    masterClubID: $master_club_id
                });
            }

            manageTeamsEntranceService.manage2({
                event_id        : $event_id,
                master_team_id  : $master_team_id,
                division_id     : $division_id,
                club_owner_id   : $club_owner_id,
                master_club_id  : $master_club_id,
                reg_method      : $reg_method
            }, async function (err, team, division, club) {
                try {
                    if(err) {
                        loggers.errors_log.error(err);
                        switch(err.type) {
                            case 'Argument':
                            case 'Entry':
                            case 'Entrance':
                                return res.validation(err.validation);
                            default:
                                return res.serverError();
                        }
                    }

                    const { roster_club_id, distance_to_event } = club;

                    if (!distance_to_event) {
                        await ClubService.geo.calculateDistanceToEvent(Db, roster_club_id, $master_club_id, $event_id);
                    }

                    return res.status(201).json({ team: team });
                } catch (e) {
                    res.customRespError(e);
                }
            });
        } catch (error) {
            res.customRespError(error);
        }
    },
    // post /api/club/event/:event/team/update
    updateEntry: async function (req, res) {
        var $event_id = parseInt(req.options.event || req.params.event, 10),
            $division_id = parseInt(req.body.division, 10),
            $roster_team_id = parseInt(req.body.team, 10),
            $roster_club_id = parseInt(req.body.roster_club, 10),
            $master_club_id = parseInt(req.session.passport.user.master_club_id, 10);

        if(!$event_id) return res.validation('No event identifier passed');
        if(!$roster_team_id) return res.validation('No team identifier passed');
        if(!$division_id) return res.validation('No division identifier passed');
        if(!$roster_club_id) return res.validation('No club identifier passed');

        try {
            const rosterValidationResult =
                await CheckInRosterService.validateMasterTeamRosterWithRosterTeamId($event_id, $roster_team_id);

            if (rosterValidationResult.length && $division_id !== -1) {
                return res.validation(_.first(rosterValidationResult));
            } else if ($division_id > 0) {
    
                const isValidAge = await CheckInRosterService.validateAge({
                    masterTeamId: null,
                    rosterTeamId: $roster_team_id,
                    divisionId: $division_id
                });

                if(!isValidAge) {
                    return res.validation('Division age must be not less than team age');
                }

                await CheckInRosterService.validateSeasonality({
                    rosterTeamID: $roster_team_id,
                    eventID: $event_id,
                    divisionID: $division_id
                });

                await EventRegistrationService.registrationRules.checkAdditionalRules({
                    eventID: $event_id,
                    masterClubID: $master_club_id
                });
            }

            let rosterTeam, division;

            try {
                const mangedTeam = await manageTeamsEntranceService.manage2({
                    event_id: $event_id,
                    roster_team_id: $roster_team_id,
                    division_id: $division_id,
                    roster_club_id: $roster_club_id
                });

                rosterTeam = mangedTeam.rosterTeam;
                division = mangedTeam.division;
            } catch (err) {
                if (err) {
                    switch (err.type) {
                        case 'Argument':
                            return res.validation(err.validation);
                        case 'Entry':
                        case 'Entrance':
                            return res.status(500).json({validation: err.validation});
                        default:
                            return res.serverError();
                    }
                }
            }

            if ($division_id > 0){
                return res.status(200).json({team: rosterTeam, division});
            }

            if (!rosterTeam) {
                return res.status(404);
            }

            return res.status(200).json({team: rosterTeam});
        } catch (err) {
            return res.customRespError(err);
        }
    },
    // get /api/club/event/:event/team/:team/members
    getMembersList: function (req, res) {
        var $event_id           = parseInt(req.options.event || req.params.event, 10),
            $roster_team_id     = parseInt(req.params.team, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id);

        if(!$event_id)       return res.validation('Invalid event identifier provided');
        if(!$roster_team_id) return res.validation('Invalid team identifier provided');
        if(!$master_club_id) return res.validation('No club created');


        Promise.all([
            Db.query(MEMBERS_LIST_SQL, [$master_club_id, $event_id, $roster_team_id])
            .then(result => result.rows[0]),
            CheckInRosterService.validateTeam($event_id, $roster_team_id)
        ]).then(function (results) {
            let teamResult          = results[0],
                checkinValidation    = results[1];

            if (_.isEmpty(teamResult)) {
                res.status(200).json({
                    team                : {},
                    members             : {},
                    checkinValidation
                })
            } else {
                let members = renderMembersContacts(teamResult.members);
                let team    = _.omit(teamResult, 'members');

                return res.status(200).json({ team, members, checkinValidation });
            }
        }).catch(res.customRespError.bind(res));
    },
    // get /api/club/event/:event/team/:team/validate-jersey
    validateJerseys: function (req, res) {
        const $eventId  = req.options.event || req.params.event,
              $teamId   = Number(req.params.team);

        if(!$teamId)
            return res.validation('Invalid Team Identifier');

        CheckInRosterService.validateTeamJerseys($eventId, $teamId)
        .then(function (validationResult) {
            res.status(200).json({
                jersey: validationResult
            })
        }).catch(function (error) {
            res.customRespError(error);
        })
    },
    // get /api/club/event/:event/team/:team/validate-aau-jersey
    validateAAUJerseys: function (req, res) {
        const $eventId  = req.options.event || req.params.event,
              $teamId   = Number(req.params.team);

        if(!$teamId)
            return res.validation('Invalid Team Identifier');

        CheckInRosterService.validateTeamAAUJerseys($eventId, $teamId)
        .then(function (validationResult) {
            res.status(200).json({
                aauJersey: validationResult
            })
        }).catch(function (error) {
            res.customRespError(error);
        })
    },
    // get /api/club/event/:event/team/:team/validate-roster
    validateRoster: function (req, res) {
        const
            $eventId  = req.options.event || req.params.event,
            $teamId   = Number(req.params.team);

        if(!$teamId)
            return res.validation('Invalid Team Identifier');

        CheckInRosterService.validateTeam($eventId, $teamId)
        .then(function (validationResult) {
            res.status(200).json({
              checkinValidation: validationResult
            })
        }).catch(function (err) {
            res.customRespError(err);
        })
    },
    // post /api/club/event/:event/teams/validate-roster
    validateTeamsRosters: function (req, res) {
        const 
            $eventId  = req.options.event || req.params.event,
            $teams    = req.body.teams;

        if(!_.isArray($teams))
            return res.validation('Expecting teams to be an array');

        let filteredTeams = _.filter(req.body.teams, _.isNumber);

        if(filteredTeams.length !== $teams.length)
            return res.validation('Expecting "teams" to be an array of integers');

        CheckInRosterService.validateTeams($eventId, filteredTeams)
        .then(function (validationResults) {
            res.status(200).json({
                teams: validationResults
            })
        }).catch(res.customRespError.bind(res))
    },
    // get /api/club/event/:event/teams/assigned
    assignedTeamsList: function (req, res) {
        var $event_id = parseInt(req.options.event || req.params.event, 10),
            $master_club_id = parseInt(req.session.passport.user.master_club_id);

        if(!$event_id) return res.status(400).json({ validation: 'Invalid event identifier provided' });
        if(!$master_club_id) return res.status(400).json({ validation: 'No club created' });

        Db.query(
            `SELECT  
                 rt.ths_trav_coord_name, rt.ths_trav_coord_email, rt.ths_trav_coord_phone,
                 rt.master_team_id, rt.roster_team_id, rt.gender, 
                 rt.team_name "name", rt.organization_code "code"     
             FROM roster_team rt   
             INNER JOIN master_team mt  
                 ON mt.master_team_id = rt.master_team_id  
             WHERE rt.event_id = $1 
                 AND rt.deleted IS NULL   
                 AND mt.master_club_id = $2  
                 AND rt.status_entry <> 11
             ORDER BY rt.organization_code`,           
            [$event_id, $master_club_id]
        ).then(result => {
            res.status(200).json({ teams: result.rows });
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/club/event/:event/team/:team/members/unassigned
    unassignedMembersList: function () {

    },
    addMember: function () {

    }
}

function renderMembersContacts (members = []) {
   members.forEach(member => {
      member.contacts_html = _renderMemberContacts(member);
   })

   return members;
}

function _renderMemberContacts (member) {
    let res = ['<p>', member.first, ' ',  member.last , ' (', member.role, ')</p>'];

    if (member.email) {
        res.push('<p><a href="mailto:', member.email, '">', member.email, '</a></p>');
    }

    if (member.phoneh) {
        res.push('<p><b>Home:</b> <a href="tel:',  member.phoneh, '">', member.phoneh, '</a></p>');
    }

    if(member.phonem) {
        res.push('<p><b>Cell:</b> <a href="tel:', member.phonem, '">', member.phonem, '</a></p>');
    } 
    
    if(member.phonep && member.role === 'athlete') {
        res.push('<p><b>Parents:</b> <a href="tel:', member.phonep, '">', member.phonep, '</a></p>');
    }
    
    if (member.address) {
      res.push('<p>', member.address, '</p>');
    }

    res.push('<p>');

    if (member.city) {
        res.push(member.city, ' ');
    }

    if (member.state) {
        res.push(member.stat, ' ');
    }

    if (member.zip) {
       res.push(member.zip);
    }

    res.push('</p>')

    return res.join(''); 
}

var MEMBERS_LIST_SQL = 
`SELECT (
     SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("m"))), '[]'::JSON)
     FROM (
         SELECT    
              ma.first, ma.last, ma.gender, ma.gradyear, ma.phonem, ma.phoneh, ma.phonep, 
              ma.email, ma.age, ma.address, ma.state, ma.city, ma.zip, rt.team_name, 
              ra.jersey "jersey", ra.aau_jersey "aau_jersey",
              ma.jersey "default_jersey", ma.aau_jersey "default_aau_jersey",
              INITCAP(ar.name) athlete_role,
              COALESCE(spr.short_name) "sport_position_name", 
              COALESCE(spm.short_name, 'N/A') "default_position_name",
              spr.sport_position_id "position_id", spm.sport_position_id "default_position_id",
              (CASE WHEN ra.as_staff > 0 THEN 'staff' ELSE 'athlete' END) "role", '-' "cert", ra.roster_athlete_id "id",
              NULL "safesport_statusid", NULL "role_name", NULL "bkg", NULL "default_role_name",
              NULL "role_id", NULL "default_role_id", NULL "primary", TRUE "is_athlete", 
              TO_CHAR((ra."deleted_by_user" AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MI AM') 
                                                                                     "deleted_by_user"
          FROM roster_athlete ra 
          INNER JOIN master_athlete ma 
              ON ra.master_athlete_id = ma.master_athlete_id 
              AND ma.deleted IS NULL
          LEFT JOIN sport_position spr 
              ON ra.sport_position_id = spr.sport_position_id  
          LEFT JOIN "sport_position" spm 
              ON ma.sport_position_id = spm.sport_position_id 
          LEFT JOIN "athlete_role" ar
              ON ar.athlete_role_id = COALESCE(ra.role, ma.role)      
          WHERE ma.master_club_id = mt.master_club_id 
              AND ra.event_id = rt.event_id
              AND ra.deleted IS NULL 
              AND ra.roster_team_id = rt.roster_team_id
          UNION    
          SELECT ms.first, ms.last, ms.gender, -1 "gradyear", ms.phone "phonem", ms.phoneh,
                                                                                     NULL "phonep", 
             ms.email, NULL "age", ms.address, ms.state, ms.city, ms.zip, rt.team_name, 
             NULL "athlete_role",
             NULL "jersey",
             NULL "aau_jersey",
             NULL "default_jersey",
             NULL "default_aau_jersey",
             NULL "sport_position_name", 
             NULL "default_position_name",
             NULL "position_id",
             NULL "default_position_id",
             'staff' "role",
             (
                CASE 
                  WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                  WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                  ELSE NULL
                END  
             ) "cert",
             rsr.roster_staff_role_id "id", ( 
                  CASE 
                    WHEN (ms.safesport_statusid = '2') THEN 'OK' 
                    ELSE ''
                  END 
             ) safesport_statusid, (
                  CASE 
                      WHEN r.short_name IS NOT NULL 
                          THEN FORMAT('(%s)', r.short_name) 
                       ELSE NULL
                  END
             ) "role_name", (
               CASE                                 
                WHEN (
                    CASE
                        WHEN ms.bg_screening = 'foreign' THEN FALSE
                        WHEN ms.bg_expire_date IS NOT NULL
                            THEN (
                                    ms.bg_expire_date::DATE <= CURRENT_DATE 
                                    OR ms.bg_screening <> '2' 
                                    OR ms.bg_screening IS NULL
                                  )
                        WHEN ms.bg_expire_date IS NULL
                            THEN (
                                ms.bg_screening <> '2' 
                                OR ms.bg_screening IS NULL 
                            )
                    END
                    ) IS FALSE THEN 'YES'
                ELSE 'NO'
             END
             ) "bkg",
            FORMAT('(%s)', COALESCE(mr.short_name, 'N/A')) "default_role_name",
            r.role_id "role_id",
            mr.role_id "default_role_id",
            COALESCE(rsr.primary, msr.primary) "primary",
            FALSE "is_athlete", 
            TO_CHAR((rsr."deleted_by_user" AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MI AM') "deleted_by_user"
          FROM roster_staff_role rsr 
          LEFT JOIN "master_staff_role" msr
              ON msr.master_team_id = rsr.master_team_id
              AND msr.master_staff_id = rsr.master_staff_id
          INNER JOIN master_staff ms 
              ON ms.master_staff_id = rsr.master_staff_id 
              AND ms.deleted IS NULL   
          LEFT JOIN "role" r 
              ON r.role_id = rsr.role_id
          LEFT JOIN "role" mr 
              ON mr.role_id = msr.role_id
          WHERE rsr.roster_team_id = rt.roster_team_id
              AND rsr.deleted IS NULL 
          --ORDER BY "role", "last" DESC
     ) "m"
  ) "members", (
     COALESCE(d.roster_deadline, e.roster_deadline) < (NOW() AT TIME ZONE e.timezone)
 ) "deadline_passed", rt.locked, 
 TO_CHAR (rt.online_checkin_date::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') "online_checkin_date",
 TO_CHAR(COALESCE(d.roster_deadline, e.roster_deadline), 'Mon DD, YYYY, HH12:MI AM') "deadline"
 FROM "roster_team" rt 
 INNER JOIN master_team mt 
     ON mt.master_team_id = rt.master_team_id 
     AND mt.master_club_id = $1 
 INNER JOIN "event" e 
     on e.event_id = rt.event_id
 INNER JOIN "division" d 
     ON d.division_id = rt.division_id
 WHERE rt.roster_team_id = $3
     AND rt.deleted IS NULL
     AND rt.event_id = $2`;
