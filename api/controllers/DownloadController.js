'use strict';

const S3_PATH       = '/data/';
const knox          = require('knox');
const argv          = require('optimist').argv;

const S3_CONNECTION = argv.prod
                        ?sails.config.s3.ticketsBucket
                        :sails.config.s3.ticketsBucketDev;

module.exports = {
    // GET /api/download/:filepath
    getS3File: async function (req, res) {
        let filePath = req.params.filepath;

        let {filename: fileName, format: fileFormat } = req.query || {};

        if(!filePath || !fileName || !fileFormat) {
            return res.customRespError({ validation: 'Incorrect file name' });
        }

        let knoxClient = knox.createClient(S3_CONNECTION);

        knoxClient.getFile(`${S3_PATH}${filePath}/${fileName}.${fileFormat}`, (err, result) => {
            if(err) {
                return res.customRespError(err);
            }

            res.setHeader('Content-disposition', 'attachment; filename=' + `${fileName}.${fileFormat}`);
            res.setHeader('Content-type', 'application/octet-stream');

            result.pipe(res);
        });
    }
};
