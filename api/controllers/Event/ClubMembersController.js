'use strict';

const swUtils = require('../../lib/swUtils');

const { checkinSchema, checkinDeactivateSchema } = require('../../validation-schemas/online-checkin'),
    SportEngineUtilsService = require('../../lib/SEUtilsService'),
    { USAV_SANC_BODY } = require('../../constants/teams');
const {US_COUNTRY_CODE} = require("../../constants/common");

const MIN_AGE_FOR_SAFESPORT = 18;

const ROSTER_CLUB_EVENT_SQL =
    `SELECT
        e.sport_sanctioning_id,
        rc.country "team_club_country"
    FROM "roster_team" rt
        INNER JOIN "roster_club" rc ON rt.roster_club_id = rc.roster_club_id
        INNER JOIN "event" e ON rc.event_id = e.event_id AND e.deleted IS NULL
    WHERE e.event_id = $1
        AND rt.roster_team_id = $2`;

const ATHLETE_SANCTIONING_SQL =
    `SELECT ma.aau_membership_id, ma.usav_number
    FROM master_athlete ma
    WHERE ma.master_athlete_id = $1`;

const STAFF_SANCTIONING_SQL =
    `SELECT ms.aau_membership_id, ms.usav_number
    FROM master_staff ms
    WHERE ms.master_staff_id = $1`;

module.exports = {
    // get /api/event/:event/team/:team/members
    getTeamMembers: function (req, res) {
        var $teamId     = parseInt(req.params.team, 10),
            $eventId    = parseInt(req.params.event, 10);

        if(!$teamId) return res.validation('Invalid team identifier passed');

        Promise.all([
            Db.query(
                `SELECT 
                     FORMAT('%s %s', INITCAP(ma.first), INITCAP(ma.last)) "name", ma.gender, 
                     COALESCE(ra.jersey, ma.jersey) "jersey", 
                     COALESCE(ra.aau_jersey, ma.aau_jersey) "aau_jersey", 
                     COALESCE(spr.short_name, spm.short_name) "sport_position",
                     ma."safesport_statusid", ma.master_club_id,
                     COALESCE(ra.role, ma.role) "role", INITCAP(ma.ethnicity::TEXT) "ethnicity",
                     date_part('year', age(e.date_end::DATE, ma.birthdate))::INTEGER >= $3 "is_adult",
                     e.sport_sanctioning_id,
                     ma.gradyear, (ra."as_staff" > 0) "as_staff", 
                     ma.email, ma.age, ma.address, ma.state, ma.city,  
                     ma.zip, ma.organization_code, ma.aau_membership_id,
                     INITCAP(ma.seasonality) seasonality,
                     REGEXP_REPLACE(ma.phonem, '[^\d]+', '', 'g') phonem,  
                     REGEXP_REPLACE(ma.phoneh, '[^\d]+', '', 'g') phoneh,  
                     REGEXP_REPLACE(ma.phonep, '[^\d]+', '', 'g') phonep,    
                     ra.roster_athlete_id "id",
                     ma.master_athlete_id,
                     TO_CHAR(ma.webpoint_sync, 'Mon DD') "last_webpoint_sync"
                 FROM "roster_athlete" ra   
                 INNER JOIN "master_athlete" ma 
                     ON ma.master_athlete_id = ra.master_athlete_id 
                 LEFT JOIN sport_position spr 
                    ON ra.sport_position_id = spr.sport_position_id  
                 LEFT JOIN "sport_position" spm 
                    ON ma.sport_position_id = spm.sport_position_id
                 JOIN event e ON e.event_id = ra.event_id   
                 WHERE ra.roster_team_id = $1 
                     AND ra.event_id = $2 
                     AND ra.deleted IS NULL
                     AND ra.deleted_by_user IS NULL`,
                [$teamId, $eventId, MIN_AGE_FOR_SAFESPORT]
            ),
            Db.query(
                `SELECT  
                     FORMAT('%s %s', INITCAP(ms.first), INITCAP(ms.last)) "name", ms.gender,  
                     (
                        CASE 
                          WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                          WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                          ELSE NULL
                        END  
                     ) "cert", 
                     ms.phone "phonem", ms.phoneh, ms.phoneo, ms.phonew, ms.email, ms.is_impact, 
                     ms.address, ms.state, ms.city, ms.zip, ms.master_staff_id "id", ms.safesport_statusid,
                     COALESCE(rr.short_name, mr.short_name, 'N/A') "role_name", ms.checkin_barcode "barcode",
                     COALESCE(rr.role_id, mr.role_id) "role_id",
                     COALESCE(rsr.primary, msr.primary) "primary", (
                        CASE
                            WHEN  COALESCE(rsr.primary, msr.primary) IS TRUE 
                            THEN (
                                SELECT ROW_TO_JSON("t") 
                                FROM ( SELECT rt.roster_team_id "id", rt.organization_code "usav" ) "t"
                            )
                            ELSE (
                                SELECT ROW_TO_JSON("t")
                                FROM (
                                    SELECT rt.roster_team_id "id", rt.organization_code "usav"
                                    FROM "roster_staff_role" rsr 
                                    LEFT JOIN "master_staff_role" msr 
                                        ON msr.master_staff_id = rsr.master_staff_id
                                        AND msr.master_team_id = rsr.master_team_id
                                    INNER JOIN "roster_team" rt 
                                        ON rt.roster_team_id = rsr.roster_team_id
                                        AND rt.event_id = $2 
                                    WHERE rsr.deleted IS NULL 
                                        AND rsr.deleted_by_user IS NULL 
                                        AND rsr.master_staff_id = ms.master_staff_id
                                        AND COALESCE(rsr.primary, msr.primary) IS TRUE
                                        LIMIT 1
                                ) "t"
                            )
                        END
                     ) "primary_team",
                     (
                        e.online_team_checkin_mode = 'primary_staff_barcodes' 
                        AND COALESCE(rsr.primary, msr.primary)
                        AND rt.online_checkin_date IS NOT NULL
                     ) "primary_staff_barcodes_checkin",
                     (
                         SELECT 
                            etc.deactivated_at IS NOT NULL
                         FROM 
                            event_team_checkin AS etc
                         WHERE 
                            etc.master_staff_id = ms.master_staff_id
                            AND etc.event_id = e.event_id
                         LIMIT 1
                     ) AS is_deactivated,
                     (
                        SELECT 
                            comments
                        FROM 
                            event_change
                        WHERE 
                            master_staff_id = ms.master_staff_id
                            AND event_id = e.event_id
                            AND action IN ('team.roster.staff.checkin.activated', 'team.roster.staff.checkin.deactivated')
                        ORDER BY created DESC
                        LIMIT 1
                     ) AS deactivated_reason,
                     (
                        SELECT coalesce(array_to_json(array_agg(row_to_json(online_checkin_history))), '[]'::json)
                        FROM (
                            SELECT
                                barcode,
                                event_id,
                                action_type,
                                TO_CHAR(created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'YYYY/MM/DD HH12:MI:SS am') "created"
                            FROM "online_checkin_api_history"
                            WHERE barcode = ms.checkin_barcode
                                AND event_id = e.event_id
                        ) online_checkin_history
                     ) AS staff_online_checkin_history
                 FROM "roster_staff_role" rsr 
                LEFT JOIN "master_staff_role" msr
                    ON msr.master_staff_id = rsr.master_staff_id
                    AND msr.master_team_id = rsr.master_team_id
                INNER JOIN "roster_team" rt 
                    ON rt.roster_team_id = rsr.roster_team_id 
                    AND rt.event_id = $2 
                INNER JOIN "master_staff" ms
                    ON ms.master_staff_id = rsr.master_staff_id 
                LEFT JOIN "role" rr 
                    ON rr.role_id = rsr.role_id
                LEFT JOIN "role" mr 
                    ON mr.role_id = msr.role_id
                LEFT JOIN event e
                    ON e.event_id = rt.event_id   
                WHERE rsr.roster_team_id = $1 
                    AND rsr.deleted IS NULL
                    AND rsr.deleted_by_user IS NULL
                ORDER BY (COALESCE(rsr.primary, msr.primary) IS TRUE), COALESCE(rr.sort_order, mr.sort_order)`,
                [$teamId, $eventId]
            ),
            Db.query(
                `SELECT
                    COALESCE(rt.wristbands_count_athletes, 0) "athletes",
                    COALESCE(rt.wristbands_count_staff, 0) "staff"
                 FROM "roster_team" rt 
                 WHERE rt.roster_team_id = $2
                     AND rt.event_id = $1`,
                [$eventId, $teamId]
            ),
            CheckInRosterService.validateTeam($eventId, $teamId, 'EO'),
        ]).then(function (queryResults) {
            if(queryResults) {
                res.status(200).json({
                    athletes            : queryResults[0].rows || [],
                    staff               : CheckInRosterService.addDescriptionLinks(queryResults[1].rows || [], $eventId),
                    wristbands          : (_.first(queryResults[2].rows) || {}),
                    rosterValidation    : queryResults[3]
                })
            } else {
                res.status(200).json({
                    athletes            : [],
                    staff               : [],
                    wristbands          : {},
                    rosterValidation    : {}
                })
            }
        }).catch(function (err) {
            res.customRespError(err);
        })
    },
    // get /api/event/:event/club/:club/members?exclude_team
    getClubMembers: function (req, res) {
        let $rosterTeamId = parseInt(req.query.exclude_team, 10),
            $rosterClubId = parseInt(req.params.club, 10),
            $eventId = parseInt(req.params.event, 10),
            $search = swUtils.escapeStr(req.query.search || ''),
            season = sails.config.sw_season.current,
            params = [];

        if (!$rosterClubId) {
            return res.validation('Invalid club identifier passed');
        }

        params.push($rosterClubId, $eventId, season);

        if ($rosterTeamId) {
            params.push($rosterTeamId);
        }
        if ($search) {
            params.push('%' + $search + '%');
        }

        __getClubMembersSql($rosterTeamId, $eventId, $search).then((query) => {
            return Db.query(query, params);
        }).then(result => {
            res.status(200).json({members: result.rows});
        }).catch(res.customRespError.bind(res))
    },

    // post /api/event/:event/team/:team/add/member
    addMemberToRoster: function (req, res) {
        var $eventId            = parseInt(req.params.event, 10),
            $rosterTeamId       = parseInt(req.params.team, 10),
            $masterStaffId      = parseInt(req.body.staff, 10),
            $masterAthleteId    = parseInt(req.body.athlete, 10),
            $roleId             = parseInt(req.body.role, 10),
            $asStaff            = (req.body.asStaff === true),
            $userId             = parseInt(req.session.passport.user.user_id);

        if(!$rosterTeamId) 
            return res.validation('Invalid team identifier passed');
        if(!($masterStaffId || $masterAthleteId)) 
            return res.validation('No member identifier passed');
        if($masterStaffId && $masterAthleteId) 
            return res.validation('Both member types identifier passed');
        if($masterStaffId && !$roleId) 
            return res.validation('Invalid role passed for staff member');

        const errorHandler = async (err) => {
            try {
                if ($masterAthleteId && err.code === '23505' && err.constraint === 'unique_athlete_on_event') {
                    const { rows: [athlete] } = await Db.query(
                        squel.select().from('master_athlete', 'ma')
                            .join('roster_athlete', 'ra', 'ra.master_athlete_id = ma.master_athlete_id')
                            .where('ra.deleted IS NULL')
                            .where('ra.deleted_by_user IS NULL')
                            .where('ma.master_athlete_id = ?', $masterAthleteId)
                            .where('ra.as_staff = ?', $asStaff ? 1 : 0)
                            .where('ra.event_id = ?', $eventId)
                            .join('roster_team', 'rt', 'rt.roster_team_id = ra.roster_team_id')
                            .field('ma.first')
                            .field('ma.last')
                            .field('rt.team_name')
                            .field('rt.organization_code')
                    );
                    if (!athlete) {
                        throw new Error(`Athlete ${$masterAthleteId} with as_staff=${$asStaff} not found in roster_athlete`);
                    }
                    throw {
                        validation: `${athlete.first} ${athlete.last} is already added to "${athlete.team_name}" team (${athlete.organization_code}) as ${$asStaff ? 'staff' : 'player'}`,
                    }
                }

                throw err;
            }
            catch(err) {
                return res.customRespError(err);
            }
        };

        if($masterStaffId) {
            __createStaff({
                masterStaffId   : $masterStaffId,
                rosterTeamId    : $rosterTeamId,
                eventId         : $eventId,
                roleId          : $roleId,
                userId          : $userId
            }).then(() => {
                res.status(200).send('OK')
            }).catch(errorHandler);
        } else {
            __createAthlete({ 
                masterAthleteId : $masterAthleteId,
                rosterTeamId    : $rosterTeamId,
                eventId         : $eventId,
                asStaff         : $asStaff,
                userId          : $userId
            }).then(() => {
                res.status(200).send('OK')
            }).catch(errorHandler)
        }
    },
    // delete /api/event/:event/team/:team/member/:member/:type
    removeMemberFromRoster: async (req, res) => {
        const eventID          = parseInt(req.params.event, 10),
              rosterTeamID     = parseInt(req.params.team, 10),
              memberID         = parseInt(req.params.member, 10),
              type                      = req.params.type,
              userID           = parseInt(req.user.user_id, 10);

        if (!rosterTeamID) {
            return res.validation('Invalid team identifier passed');
        }
        if (!memberID) {
            return res.validation('Invalid member identifier passed');
        }     
        if (!type) {
            return res.validation('Invalid "type" parameter passed');
        }

        try {
            await EventTeamService.member.removeFromRoster(memberID, rosterTeamID, eventID, userID, type);

            return res.status(200).json({});
        } catch (err) {
            res.customRespError(err);
        }
    },

    // put /api/event/:event/team/:team/athlete/:athlete/update
    updateAthlete: async function (req, res) {
        try {
            const eventID       = Number(req.params.event);
            const rosterTeamID  = Number(req.params.team);
            const athleteID     = Number(req.params.athlete);
            const userID        = Number(req.session.passport.user.user_id);

            await EventTeamService.member.athlete.update(athleteID, eventID, rosterTeamID, req.body, userID);

            return res.status(200).json({});
        }
        catch(err) {
            return res.customRespError(err);
        }
    },

    // put /api/event/:event/team/:team/staffer/:staffer/update
    updateStaffer: async function (req, res) {
        const eventID = req.params.event,
            rosterTeamID = Number(req.params.team),
            stafferID = Number(req.params.staffer),
            stafferData = req.body.staffer,
            eventOwnerID  = eventOwnerService.findId(eventID, req.session.passport.user),
            userID        = Number(req.session.passport.user.user_id),
            season = sails.config.sw_season.current;

        if(!rosterTeamID) {
            return res.validation('Invalid Team Identifier');
        }

        if(!stafferID) {
            return res.validation('Invalid Staffer Identifier');
        }

        try {
            const result = await EventTeamService.member.staff.update(
                eventID,
                rosterTeamID,
                stafferID,
                eventOwnerID,
                season,
                stafferData,
                userID
            );

            return res.status(200).json(result);
        } catch (err) {
            return res.customRespError(err);
        }


    },
    // get /api/event/:event/team/:team/validate-roster
    validateTeamRoster: function (req, res) {
        let $eventId    = req.params.event,
            $teamId     = Number(req.params.team);

        if(!$teamId)
            return res.validation('Invalid Team Identifier');

        CheckInRosterService.validateTeam($eventId, $teamId, 'EO')
            .then((validationErrors) => {
            res.status(200).json({
                rosterValidation: validationErrors,
            })
        }).catch(err => {
            res.customRespError(err);
        })
    },

    // get /api/event/:event/teams/unlock
    unlockAllTeamsRosterOnEvent: function (req, res) {
        let $eventId        = Number(req.params.event),
            $teams          = req.query.teams || null,
            $eventOwnerId   = Number(req.session.passport.user.event_owner_id),
            $userID         = Number(req.session.passport.user.user_id);

        let getCheckinDataQuery = `SELECT online_checkin_date, roster_team_id FROM roster_team WHERE event_id = $1`;
        let setTeamsLockedQuery = `UPDATE "roster_team" SET locked = NULL, online_checkin_date = null WHERE event_id = $1`;
        let deleteCheckinQuery  = `DELETE FROM "event_team_checkin" etc WHERE etc.event_id = $1`;

        if($teams) {
            getCheckinDataQuery += ` AND roster_team_id IN (${swUtils.numArrayToString($teams)})`;
            setTeamsLockedQuery += ` AND roster_team_id IN (${swUtils.numArrayToString($teams)})`;
            deleteCheckinQuery  += ` AND roster_team_id IN (${swUtils.numArrayToString($teams)})`;
        }

        return Db.begin()
            .then( (tr) => {
                return Promise.all([
                    tr.query(getCheckinDataQuery, [$eventId]),
                    tr.query(setTeamsLockedQuery, [$eventId]),
                    tr.query(deleteCheckinQuery, [$eventId])
                ]).then((result) => {
                    if (!result[1].rowCount)
                        return res.validation('Teams not found');

                    let updatedTeams = result[0].rows;

                    let _query = squel.insert().into('event_change')
                        .set('event_id', $eventId)
                        .set('event_owner_id', $eventOwnerId)
                        .set('user_id', $userID);

                    return Promise.all(updatedTeams.map( team => {
                        _query.set('roster_team_id', team.roster_team_id);

                        if(team.online_checkin_date) {
                            _query.set('action', 'team.online-checkin.uncheckedin');
                        } else {
                            _query.set('action', 'team.roster.unlock.eo');
                        }

                        return tr.query(_query, [team.roster_team_id])
                    })).then(() => {
                        loggers.debug_log.verbose('Commiting transaction ...');
                        return tr.commit();
                    });
                }).catch(err => {
                    if(tr && !tr.isCommited) {
                        tr.rollback();
                    }
                    throw err;
                })
            })
            .then( () => res.status(200).json({}) )
            .catch(res.customRespError.bind(res));

    },

    // get /api/event/:event/teams/lock
    lockAllTeamsRosterOnEvent: function (req, res) {
        let $eventId        = Number(req.params.event),
            $teams          = req.query.teams || null,
            $eventOwnerId   = Number(req.session.passport.user.event_owner_id),
            $userID         = Number(req.session.passport.user.user_id),
            season          = sails.config.sw_season.current;

        let _query = 
            squel.update().table('roster_team')
                .set('locked', true)
            .where('event_id = ?', $eventId)
            .where('deleted IS NULL')
            .where('locked IS NOT TRUE')
            .returning('roster_team_id');

        if($teams) {
            _query.where(`roster_team_id IN (${swUtils.numArrayToString($teams)})`);
        }

        return Db.query(_query).then( result => {
            let lockedTeams = result.rows;

            if (!lockedTeams.length) {
                return Promise.reject(new Error('Teams not found or already locked'))
            }

            let teamsIds = lockedTeams.map(t => Number(t.roster_team_id));

            return Promise.all(teamsIds.map(id => {
                return Db.query(
                    squel.insert().into('event_change')
                        .set('event_id', $eventId)
                        .set('roster_team_id', id)
                        .set('event_owner_id', $eventOwnerId)
                        .set('user_id', $userID)
                        .set('action', 'team.roster.lock.eo')
                    .returning('roster_team_id')
                )
            }))
            .then(() => teamsIds)
            .catch(err => {
                loggers.errors_log.error(err);

                return teamsIds;
            })
        })
        .then(teamsIds => {
            return RosterSnapshotService.copyTeamsMembersValuesToRoster(teamsIds, season);
        })
        .then(() => res.status(200).json({}))
        .catch(res.customRespError.bind(res));
    },

    // get /api/event/:event/team/:team/unlock
    unlockTeamRoster: function (req, res) {
        let $eventId        = Number(req.params.event),
            $teamId         = Number(req.params.team),
            $eventOwnerId   = Number(req.session.passport.user.event_owner_id),
            $userID         = Number(req.session.passport.user.user_id);

        if(!$teamId)
            return res.validation('Invalid Team Identifier');

        return Db.begin()
            .then( (tr) => {
                return Promise.all([
                    tr.query(
                        `SELECT online_checkin_date FROM roster_team 
                        WHERE event_id = $1 AND roster_team_id = $2`,
                        [$eventId, $teamId]),
                    tr.query(
                        `UPDATE "roster_team" SET locked = false, online_checkin_date = null
                        WHERE event_id = $1 AND roster_team_id = $2`,
                        [$eventId, $teamId]),
                    tr.query(
                        `DELETE FROM "event_team_checkin" etc
                            WHERE etc.event_id = $1 
                            AND etc.roster_team_id = $2`,
                        [$eventId, $teamId])
                ]).then((result) => {
                    if (!result[1].rowCount)
                        return res.validation('Team not found');

                    let _query = squel.insert().into('event_change')
                        .set('event_id', $eventId)
                        .set('roster_team_id', $teamId)
                        .set('event_owner_id', $eventOwnerId)
                        .set('user_id', $userID);

                    if(result[0].rows[0].online_checkin_date) {
                        _query.set('action', 'team.online-checkin.uncheckedin');
                    } else {
                        _query.set('action', 'team.roster.unlock.eo');
                    }

                    return tr.query(_query).then(() => {
                        loggers.debug_log.verbose('Commiting transaction ...');
                        return tr.commit();
                    });
                }).catch(err => {
                    if(tr && !tr.isCommited) {
                        tr.rollback();
                    }
                    throw err;
                })
            })
            .then( () => res.status(200).json({}) )
            .catch( err => res.validation(err ) );
    },

    // get /api/event/:event/team/:team/set-valid
    markRosterValid: function (req, res) {
        let eventID         = Number(req.params.event);
        let rosterTeamID    = Number(req.params.team);
        let userID          = Number(req.user.user_id);

        return __checkTeamValidation(eventID, rosterTeamID, userID, true)
            .then(() => res.status(200).json({}))
            .catch(res.customRespError.bind(res));
    },

    // get /api/event/:event/team/:team/remove-validation
    removeRosterValidMark: function (req, res) {
        let eventID         = Number(req.params.event);
        let rosterTeamID    = Number(req.params.team);
        let userID          = Number(req.user.user_id);

        return __checkTeamValidation(eventID, rosterTeamID, userID, false)
            .then(() => res.status(200).json({}))
            .catch(res.customRespError.bind(res));
    },

    // get /api/event/:event/club/:club/:memberType(athlete|staff)/:member/sync
    syncWebpointData: function (req, res) {
        /**
         * "master_staff"."master_staff_id" or "master_athlete"."master_athlete_id"
         */
        let swMemberID      = Number(req.params.member);
        let memberType      = req.params.memberType;
        let masterClubID    = Number(req.params.club);

        return ClubService.webpoint.syncMember(masterClubID, swMemberID, memberType)
            .then(result => res.status(200).json(result))
            .catch(res.customRespError);
    },

    // get /api/event/:event/team/:team/lock
    lockTeamRoster: function (req, res) {
        let $eventId        = Number(req.params.event),
            $teamId         = Number(req.params.team),
            $eventOwnerId   = Number(req.session.passport.user.event_owner_id),
            $userID         = Number(req.session.passport.user.user_id),
            season          = sails.config.sw_season.current;

        Db.query(
            `UPDATE "roster_team" SET locked = true
            WHERE event_id = $1 AND roster_team_id = $2`,
            [$eventId, $teamId]
        ).then((result) => {
            if (!result.rowCount) {
                return res.validation('Team not found');
            }

            return Db.query(
                squel.insert().into('event_change')
                    .set('event_id', $eventId)
                    .set('roster_team_id', $teamId)
                    .set('event_owner_id', $eventOwnerId)
                    .set('user_id', $userID)
                    .set('action', 'team.roster.lock.eo')
            );
        }).then(() => {
            return RosterSnapshotService.copyTeamsMembersValuesToRoster([$teamId], season);
        })
        .then(() => res.status(200).json({}))
        .catch(res.customRespError.bind(res));
    },

    // POST /api/event/:event/team/:team/online-checkin
    onlineTeamCheckin: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const teamID = Number(req.params.team);
            const season = sails.config.sw_season.current;
            const userID = Number(req.session.passport.user.user_id);
            const staffers = req.body.staffers;

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }

            if (!teamID) {
                throw { validation: 'Invalid Team Identifier' };
            }

            const { error } = checkinSchema.validate(req.body);

            if (error) {
                throw { validation: error.details[0].message }
            }

            const masterClubID = await OnlineCheckinService.team.getMasterClubIDByRosterTeamID(teamID);

            if (!masterClubID) {
                throw { validation: 'Invalid Master Club Identifier' };
            }

            await OnlineCheckinService.team.checkin({ 
                eventID, 
                teamsList: [teamID], // TODO: refactor this 🤦‍♂️
                masterClubID, 
                season, 
                userID, 
                staffers,
            });

            res.ok();
        } catch(e) {
            res.customRespError(e);
        }
    },

    // GET /api/event/:event/team/:team/online-checkin/staffers
    getStaffers: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const teamID = Number(req.params.team);
            const season = sails.config.sw_season.current;

            if (!eventID) {
                throw { validation: 'Ivalid Event Identifier' };
            }

            if (!teamID) {
                throw { validation: 'Invalid Team Identifier' };
            }

            const masterClubID = await OnlineCheckinService.team.getMasterClubIDByRosterTeamID(teamID);
            const staffers = await OnlineCheckinService.team.getStaffers(eventID, masterClubID, season);

            res.json(staffers);
        } catch(e) {
            res.customRespError(e);
        }
    },

    // PUT /api/event/:event/staffer/:staffer/checkin/:action(activate|deactivate)
    toggleStaffCheckinActivation: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const stafferID = Number(req.params.staffer);
            const userID = Number(req.user.user_id);
            const action = req.params.action;

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }

            if (!stafferID) {
                throw { validation: 'Invalid Staff Identifier' };
            }

            const { error } = checkinDeactivateSchema.validate(req.body);

            if (error) {
                throw { validation: error.details[0].message };
            }

            await EventTeamService.member.staff.validateDeactivationStatus({ eventID, stafferID, action });

            const operationParams = {
                stafferID,
                eventID
            };

            if (action === EventTeamService.member.staff.ACTIONS.DEACTIVATE) {
                await EventTeamService.member.staff.deactivate(operationParams);
            } else {
                await EventTeamService.member.staff.activate(operationParams);
            }

            await EventTeamService.member.staff.createHistoryRow({
                eventID,
                stafferID,
                userID,
                action,
                reason: req.body.reason,
            });

            res.ok();
        } catch(err) {
            res.customRespError(err);
        }
    }

}

async function __createStaff (params) {
    const { eventId, masterStaffId, rosterTeamId } = params;

    await __checkMemberSanctioning(eventId, rosterTeamId, masterStaffId, STAFF_SANCTIONING_SQL);

    return Db.query(
        `WITH "staff" AS (
            SELECT  ms.master_staff_id, FALSE "primary", ($4)::INTEGER "role_id", 
                    rt.roster_team_id, rt.master_team_id, ms.first, ms.last, 
                    ms.organization_code, rt.team_name
            FROM "master_staff" ms 
            INNER JOIN "roster_team" rt 
                ON rt.roster_team_id = ($2)::INTEGER
                AND rt.event_id = ($3)::INTEGER
            WHERE ms.master_staff_id = ($1)::INTEGER
        ) 
         UPDATE "roster_staff_role" rsr 
         SET "deleted"           = NULL,
             "deleted_by_user"   = NULL,
             "role_id"           = $4
         WHERE rsr.master_staff_id = $1
             AND rsr.roster_team_id IN (
                 SELECT rt.roster_team_id
                 FROM "roster_team" rt 
                 WHERE rt.roster_team_id = $2 
                     AND rt.event_id = $3
             )
         RETURNING 'Moved'::TEXT "action", *, (SELECT "first" FROM "staff"), (SELECT "last" from "staff"),
            (SELECT "organization_code" from "staff"), (SELECT "team_name" from "staff")`,
        [params.masterStaffId, params.rosterTeamId, params.eventId, params.roleId]
    ).then(result => {
        let updatedRole = _.first(result.rows);

        if(!_.isEmpty(updatedRole)) {
            return Promise.resolve(updatedRole)
        } else {
            return Db.query(
                 `WITH "staff" AS (
                     SELECT ms.master_staff_id, FALSE "primary", ($2)::INTEGER "role_id", rt.roster_team_id,
                         rt.master_team_id, ms.first, ms.last, ms.organization_code, rt.team_name,
                         r.short_name "role_name"
                     FROM "master_staff" ms 
                     INNER JOIN "roster_team" rt 
                         ON rt.roster_team_id = ($3)::INTEGER
                         AND rt.event_id = ($4)::INTEGER
                     LEFT JOIN "role" r
                         ON r.role_id = ($2)::INTEGER
                     WHERE ms.master_staff_id = ($1)::INTEGER
                 )
                 INSERT INTO "roster_staff_role" ( 
                     "master_staff_id", "primary", "role_id", "roster_team_id" , "master_team_id" 
                 )
                 SELECT "master_staff_id", "primary", "role_id", "roster_team_id", "master_team_id"
                 FROM "staff"
                 RETURNING 'Added'::TEXT "action", *, (SELECT "first" FROM "staff"), (SELECT "last" from "staff"),
                    (SELECT "organization_code" from "staff"), (SELECT "team_name" from "staff"),
                    (SELECT "role_name" from "staff")`,
                 [params.masterStaffId, params.roleId, params.rosterTeamId, params.eventId]
            ).then(result => {
                if(result.rowCount === 0) {
                    throw {
                        validation: 'Team not found'
                    }
                } else {
                    loggers.debug_log.verbose('EO added', result.rowCount , 'roster staff');
                }
                return _.first(result.rows);
            })
        }
    }).then(member => {
        let comments = member.action === 'Added'
            
            ? `${member.action} Staff ${member.first} ${member.last} as ${member.role_name} [${member.team_name}] (${member.organization_code})`
            : `${member.action} Staff ${member.first} ${member.last} [${member.team_name}] (${member.organization_code})`
        
        return new Promise(resolve => {
            eventNotifications.add_notification(params.eventId, {
                action          : 'team.member.add',
                roster_team_id  : params.rosterTeamId,
                user_id         : params.userId,
                comments        : comments
            }, function (err) {
                if(err) loggers.errors_log.error(err);
                resolve();
            })
        })
    })
}

function __generateAthleteModificationQuery__ (data) {
    /*
        "as_staff" value Algorithm:
        1. Athlete is preset in the current roster team (deleted or not)
            1.1 Drop "deleted" and "deleted_by_user" fields
            1.2 Set "as_staff" value using 2.2 checks
        2. Athlete is not preset in the current roster team
            2.1 Insert a new "roster_athlete" row
            2.2 Check, if there is another "roster_team" row for the athlete in this event:
                YES: set "as_staff" nextval(sequence)
                NO: set 1 (is the value to mark primary staffer) 
    */

    const PRIMARY_STAFF_VALUE   = 1;

    let params = [data.masterAthleteId, data.eventId, data.rosterTeamId];

    let staffFlagCalcsBlock, 
        updRosterRowCheck;
    if (data.asStaff) {
        params.push(PRIMARY_STAFF_VALUE);
        staffFlagCalcsBlock = 
            `(
                CASE 
                    WHEN (
                        SELECT COALESCE(COUNT(*), 0)
                        FROM "roster_athlete" ra
                        WHERE ra."deleted" IS NULL 
                            AND ra."deleted_by_user" IS NULL 
                            AND ra."event_id" = $2 
                            AND ra."roster_team_id" <> $3
                    ) > 0
                        THEN NEXTVAL('roster_athlete_as_staff_sequence'::REGCLASS)
                    ELSE ($${params.length})::INTEGER
                END
             )`;

        // Check rows in current team only, cause athlete as staff can be added multiple times to
        //  event
        updRosterRowCheck = `AND ra."roster_team_id"     = ($3)::INTEGER`
    }

    let query = 
        `WITH 
         "athlete" AS (   
            SELECT  ($2)::INTEGER "event_id", ma.jersey, ma.aau_jersey, ma.master_athlete_id,  
                    ($3)::INTEGER "roster_team_id", ma.sport_position_id,  
                    ${staffFlagCalcsBlock ? staffFlagCalcsBlock : 0} "as_staff",
                    ma.first, ma.last, ma.organization_code, rt.team_name
            FROM "master_athlete" ma   
            INNER JOIN "roster_club" rc   
                ON rc.master_club_id    = ma.master_club_id   
                AND rc.event_id         = ($2)::INTEGER
            INNER JOIN "roster_team" rt
                ON rt.roster_team_id = ($3)::INTEGER
                AND rt.event_id = ($2)::INTEGER
            WHERE ma.master_athlete_id  = ($1)::INTEGER 
        ), 
         "update_athlete" AS (
            UPDATE "roster_athlete" ra 
            SET "deleted"         = NULL, 
                "deleted_by_user" = NULL, 
                "as_staff"        = (SELECT "as_staff" FROM "athlete"),
                "roster_team_id"  = ($3)::INTEGER
            WHERE ra."event_id"             = ($2)::INTEGER 
                AND ra."master_athlete_id"  = ($1)::INTEGER
                ${updRosterRowCheck ? updRosterRowCheck : ''}
            RETURNING 'Moved'::TEXT "action", ra.*,
                (SELECT "first" FROM "athlete"),   
                (SELECT "last" FROM "athlete"),   
                (SELECT "organization_code" FROM "athlete"),
                (SELECT "team_name" FROM "athlete")
        ), 
         "insert_athlete" AS (
            INSERT INTO "roster_athlete" (   
                "event_id", "jersey", "aau_jersey", "master_athlete_id", "roster_team_id",   
                "sport_position_id", "as_staff"    
            )  
            SELECT    
                a.event_id, a.jersey, a.aau_jersey, a.master_athlete_id, a.roster_team_id,   
                a.sport_position_id, a.as_staff    
            FROM "athlete" a 
            WHERE NOT EXISTS (
                SELECT * FROM "update_athlete"
            )
            RETURNING 'Added'::text "action", *,
            (SELECT "first" FROM "athlete"),   
            (SELECT "last" FROM "athlete"),   
            (SELECT "organization_code" FROM "athlete"),
            (SELECT "team_name" FROM "athlete")
        ) 
        SELECT * FROM "update_athlete" 
         UNION ALL 
        SELECT * FROM "insert_athlete"`;

    return {query, params};
}

async function __createAthlete (data) {
    let { masterAthleteId, rosterTeamId, eventId } = data;

    await __checkMemberSanctioning(eventId, rosterTeamId, masterAthleteId, ATHLETE_SANCTIONING_SQL);

    const res = await Db.query(`
        SELECT 1 FROM roster_team rt
        INNER JOIN master_athlete ma ON (
            ma.seasonality = rt.seasonality
            OR ( ma.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.SEASON}'
                AND rt.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.FULL}' )
            OR ( ma.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.REGIONAL}'
                AND rt.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.FULL}' )
        ) AND ma.master_athlete_id = ($1)::INTEGER
        WHERE rt.roster_team_id = ($2)::INTEGER;
    `, [masterAthleteId, rosterTeamId]);

    if (res.rowCount === 0) {
        throw { validation: "The team's and athlete's seasonality should be equal." };
    }

    let {query, params} = __generateAthleteModificationQuery__(data);

    return Db.query(query, params).then(result => {
        if (result.rowCount === 0) {
            return Promise.reject({ validation: 'Team not Found' });
        } else {
            return result.rows[0] || null;
        }
    }).then(athlete => {
        if (athlete === null) {
            return;
        }

        return new Promise(resolve => {
            let comments = 
                "{action} athlete {first} {last} [{team_name}] ({organization_code}) {asStaff}"
                .format(
                    _.extend({
                        asStaff: data.asStaff ? '(as Staff Member)' : ''
                    }, athlete)
                );

            eventNotifications.add_notification(data.eventId, {
                action              : 'team.member.add',
                roster_team_id      : data.rosterTeamId,
                user_id             : data.userId,
                comments
            }, function (err) {
                if(err) loggers.errors_log.error(err);
                resolve();
            })
        })
    });
}

function __checkTeamValidation (eventID, rosterTeamID, userID, setValid) {

    if(!eventID) {
        return Promise.reject({validation: 'Event ID required'});
    }

    if(!rosterTeamID) {
        return Promise.reject({validation: 'Team ID required'});
    }

    let updateValidationQuery = squel.update().table('roster_team', 'rt')
        .where('rt.roster_team_id = ?', rosterTeamID)
        .where('rt.event_id = ?', eventID);

    if(setValid) {
        updateValidationQuery
            .set('roster_validated_at'   , squel.str('NOW()'))
            .set('roster_validated_by'   , CheckInRosterService.ROSTER_VALIDATION_SOURCE.EO)
            .set('is_valid_roster'       , true)
    } else {
        updateValidationQuery
            .set('roster_validated_at'   , null)
            .set('roster_validated_by'   , null)
            .set('is_valid_roster'       , null)
    }

    return Db.begin().then(tr => {
        return tr.query(updateValidationQuery).then(result => result.rowCount)
            .then(updated => {

                if(!updated) {
                    tr.rollback();
                    throw { validation: 'Team not found' };
                }

                return saveTeamValidationChangeToHistory(tr, eventID, rosterTeamID, userID, setValid)
                    .then(() => tr.commit());

            }).catch(err => {
                tr.rollback();
                throw err;
            })
    })
}

function saveTeamValidationChangeToHistory (tr, eventID, rosterTeamID, userID, setValidMode) {
    let query = squel.insert()
        .into('event_change'  , 'ec')
        .set('roster_team_id' , rosterTeamID)
        .set('event_id'       , eventID)
        .set('user_id'        , userID);

    if(setValidMode) {
        query.set('action', 'team.roster.valid-mark.added')
    } else {
        query.set('action', 'team.roster.valid-mark.removed')
    }

    return tr.query(query).then(result => result.rowCount);
}

function __getClubMembersSql (excludeTeam, eventId, search) {
    return Db.query(
        `SELECT
            e.sport_sanctioning_id,
            rc.country "team_club_country"
        FROM "roster_team" rt
            INNER JOIN "roster_club" rc ON rt.roster_club_id = rc.roster_club_id
            INNER JOIN "event" e ON rc.event_id = e.event_id AND e.deleted IS NULL
        WHERE e.event_id = $1
            AND rt.roster_team_id = $2`,
        [eventId, excludeTeam]
    ).then(result => {
        let event = _.first(result.rows);

        if (_.isEmpty(event)) {
            throw {
                validation: 'Event or Team Not Found'
            };
        }

        return event;
    }).then(event => {
        const athleteTableShortName = 'ma';
        const staffTableShortName = 'ms';
        const sanctioningAthleteQuery =
            __getSanctioningMembersSql(event.sport_sanctioning_id, event.team_club_country, athleteTableShortName);
        const sanctioningStaffQuery =
            __getSanctioningMembersSql(event.sport_sanctioning_id, event.team_club_country, staffTableShortName);

        const sql =
            (`SELECT DISTINCT ON (ma."master_athlete_id")
                'Player'                           "type",
                ma.master_athlete_id,
                NULL                               "master_staff_id",
                FORMAT('%s %s', ma.first, ma.last) "name",
                ma.organization_code               "code",
                ma.gender,
                ma.jersey,
                ma.aau_jersey,
                sp.name                            "position",
                ma.age,
                NULL                               "cert",
                rt.team_name
            FROM master_athlete ma
            INNER JOIN "roster_club" rc
                ON rc.roster_club_id = $1 AND rc.event_id = $2
            INNER JOIN "master_club" mc
                ON mc.master_club_id = rc.master_club_id AND mc.master_club_id = ma.master_club_id
            LEFT JOIN "sport_position" sp
                ON sp.sport_position_id = ma.sport_position_id
            LEFT JOIN roster_athlete ra
                ON ra.master_athlete_id = ma.master_athlete_id
                AND ra.event_id = $2
                AND ra.deleted IS NULL
                AND ra.deleted_by_user IS NULL
            LEFT JOIN roster_team rt
                ON rt.roster_team_id = ra.roster_team_id AND rt.roster_club_id = rc.roster_club_id
            WHERE ma.season = $3
                AND ma.deleted IS NULL ` +
                ((excludeTeam)
                    ? `AND ma.master_athlete_id NOT IN ( 
                    SELECT ra.master_athlete_id 
                    FROM "roster_team" rt 
                    INNER JOIN "roster_athlete" ra 
                        ON ra.roster_team_id = rt.roster_team_id 
                        AND ra.deleted IS NULL 
                        AND ra.deleted_by_user IS NULL
                    WHERE rt.roster_team_id = $4 
                        AND rt.roster_club_id = $1 
                        AND rt.event_id = $2 
                    )`
                    : '') +
                ((search)
                    ? ` AND (
                        ma.organization_code ILIKE $5 OR
                        ma.first ILIKE $5 OR
                        ma.last ILIKE $5 OR
                        CONCAT(ma.first, ' ', ma.last) ILIKE $5
                    ) `
                    : ''
                ) +
                ((sanctioningAthleteQuery)
                    ? sanctioningAthleteQuery
                    : ''
                ) +
                ` UNION ALL 
                SELECT 'Staff' "type", NULL "master_athlete_id", ms.master_staff_id, 
                FORMAT('%s %s', ms.first, ms.last) "name", ms.organization_code "code", ms.gender, 
                NULL "jersey", NULL "aau_jersey", NULL  "position", NULL "age", 
                (
                    CASE 
                      WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                      WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                      ELSE NULL
                    END  
                ) "cert", 
                NULL "team_name" 
                FROM "master_staff" ms 
                INNER JOIN "roster_club" rc 
                    ON rc.roster_club_id = $1 
                    AND rc.event_id = $2 
                INNER JOIN "master_club" mc 
                    ON mc.master_club_id = rc.master_club_id 
                    AND mc.master_club_id = ms.master_club_id 
                WHERE ms.season = $3 
                AND ms.deleted IS NULL ` +
                ((excludeTeam)
                    ? `AND ms.master_staff_id NOT IN ( 
                    SELECT rsr.master_staff_id 
                    FROM "roster_staff_role" rsr 
                    INNER JOIN "roster_team" rt 
                        ON rt.roster_team_id = rsr.roster_team_id 
                        AND rt.event_id = $2 
                        AND rt.roster_club_id = $1 
                    WHERE rsr.roster_team_id = $4 
                        AND rsr.deleted IS NULL
                        AND rsr.deleted_by_user IS NULL )`
                    : '') +
                ((search)
                    ? ` AND (
                        ms.organization_code ILIKE $5 OR
                        ms.first ILIKE $5 OR
                        ms.last ILIKE $5 OR
                        CONCAT(ms.first, ' ', ms.last) ILIKE $5
                    ) `
                    : ''
                ) +
                ((sanctioningStaffQuery)
                    ? sanctioningStaffQuery
                    : ''
                )
            );

        return sql;
    });
}

function __getSanctioningMembersSql (sportSanctioningId, teamClubCountry, tableShortName) {
    let query = '';

    if (sportSanctioningId === USAV_SANC_BODY.USAV && teamClubCountry === US_COUNTRY_CODE) {
        query += ` AND ${tableShortName}.usav_number IS NOT NULL`;
    } else if (sportSanctioningId === USAV_SANC_BODY.JVA || sportSanctioningId === USAV_SANC_BODY.AAU) {
        query += ` AND (${tableShortName}.aau_membership_id IS NOT NULL OR ${tableShortName}.usav_number IS NULL)`;
    } else if (sportSanctioningId !== USAV_SANC_BODY.OTHER && sportSanctioningId !== USAV_SANC_BODY.USAV) {
        query += ` AND (${tableShortName}.usav_number IS NULL AND ${tableShortName}.aau_membership_id IS NULL)`;
    }

    return query;
}


async function __checkMemberSanctioning(eventId, rosterTeamId, memberId, memberSanctioningSql) {
    const [{rows: [event]}, {rows: [member]}] = await Promise.all([
        Db.query(ROSTER_CLUB_EVENT_SQL, [eventId, rosterTeamId]),
        Db.query(memberSanctioningSql, [memberId])
    ]);

    if (_.isEmpty(event)) {
        throw { validation: 'Event Not Found.' };
    }

    if (_.isEmpty(member)) {
        throw { validation: 'Member Not Found.' };
    }

    if (event.sport_sanctioning_id === USAV_SANC_BODY.USAV && event.team_club_country === US_COUNTRY_CODE) {
        if(!_.isNumber(member.usav_number)) {
            throw { validation: 'The member is not USAV sanctioned.' };
        }
    } else if (event.sport_sanctioning_id === USAV_SANC_BODY.JVA
        || event.sport_sanctioning_id === USAV_SANC_BODY.AAU) {
        if((!_.isString(member.aau_membership_id) || !member.aau_membership_id)
            && _.isNumber(member.usav_number)) {
            throw { validation: 'The member is not AAU sanctioned.' };
        }
    } else if (event.sport_sanctioning_id !== USAV_SANC_BODY.OTHER
        && event.sport_sanctioning_id !== USAV_SANC_BODY.USAV) {
        if(_.isNumber(member.usav_number)) {
            throw { validation: 'The member is USAV sanctioned.' };
        }
        if(_.isString(member.aau_membership_id) && member.aau_membership_id) {
            throw { validation: 'The member is AAU sanctioned.' };
        }
    }
}
