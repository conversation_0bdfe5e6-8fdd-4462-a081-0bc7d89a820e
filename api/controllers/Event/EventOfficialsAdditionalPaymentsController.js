const Joi = require('joi');

const { setDefaultSchema, updateSchema } = require('../../validation-schemas/official-additional-payment');

module.exports = {
    // GET api/event/:event/officials/additional-payment/categories
    getCategories: async function(req, res) {
        try {
            const categories = await OfficialsService.additionalPayment.getCategories();

            res.json({ categories });
        } catch(e) {
            res.customRespError(e, { status: 400 });
        }
    },

    // PUT api/event/:event/officials/additional-payment/set-defaults
    setDefaults: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const applyTo = req.body.applyTo;
            const categories = req.body.categories;
            const memberType = req.body.memberType;

            const { error } = setDefaultSchema.validate({categories});

            if (error) {
                throw { validationErrors: error.details };
            }

            if (!OfficialsService.additionalPayment.getAllowedApplyFlags().includes(applyTo)) {
                throw { validation: `'${applyTo}' query param is not allowed` };
            }

            OfficialsService.payout.validateMemberType(memberType);

            await OfficialsService.additionalPayment.setDefaults({ eventID, categories, applyTo, memberType });

            res.ok();
        } catch (e) {
            res.customRespError(e, { status: 400 });
        }
    },

    // PUT api/event/:event/type/:type/additional-payment/update
    update: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            
            const member_type = req.params.type;
            const { error } = updateSchema.validate(req.body);

            if (error) {
                throw { validationErrors: error.details }
            }
    
            OfficialsService.payout.validateMemberType(member_type);
            
            const additionalPayment = Object.assign(req.body, { member_type })
            await OfficialsService.additionalPayment.update({ additionalPayment, eventID });

            res.ok();
        } catch (e) {
            res.customRespError(e, { status: 400 });
        }
    }

}
