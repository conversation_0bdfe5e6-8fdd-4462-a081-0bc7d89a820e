'use strict';

const createSchema = require('json-gate').createSchema;
const moment = require('moment');
const QRUtils = require('../../lib/QRTicketsGenerator');
const QRTIcketsUtils = require('../../lib/QRTicketsGenerator');
const fs = require('fs');
const spawn = require('child_process').spawn;
const path = require('path');
const validationSchemas = require('../../validation-schemas/tickets');
const discountSchema = validationSchemas.discount;
const discountCreateSchema = validationSchemas.discountCreate;
const UPLOADS_DIR = path.resolve(__dirname, '..', '..', '..', '..', 'uploads');
const UPLOAD_DIR_DISCOUNTS = UPLOADS_DIR + '/discounts';
const DISCOUNTS_PARSER_PATH = path.resolve(__dirname, '..', '..', '..', 'sw-utils');
const stripeConnect = require('../../lib/StripeConnect');
const stripeService = require('../../services/StripeService.js');
const optimist = require('optimist');
const co = require('co');
const swUtils = require('../../lib/swUtils');
const CHECK_DATE_FORMAT = /2[0-9]{3}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}/g;
const BASE_URL = sails.config.urls.home_page.baseUrl;
const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

const { promisify } = require('util');

const { FEE_PAYER } = require('../../constants/payments');
const TilledService = require('../../services/TilledService.js');
const { POINT_OF_SALES_TYPE } = require('../../constants/sales-hub');

var PAYMENTS_ALIASES = {
    'code': 'p.ticket_barcode',
    'amount': 'p.amount',
    'purchased': 'p.created',
    'first': 'p.first',
    'last': 'p.last',
    'email': 'p.email',
    'zip': 'p.zip',
    'status': 'p.status',
    'scanned': 'p.scanned_at',
    'usav_age': 'usav_age',
    'buyer_first' : 'buyer_first',
    'buyer_last' : 'buyer_last'
};

const DISCOUNTS_ORDER = {
        email       : 'td.email',
        first       : 'td.first',
        last        : 'td.last',
        coupon      : 'td.code',
        quantity    : 'td.max_count',
        used        : 'td.used_count',
        type        : 'et.label',
        amount      : 'td.discount'
    }

const
    PG_CURRENCY_FORMAT = 'FM$999,999,999,990D00',
    WAITLIST_STATUSES = ['pending', 'pending_card', 'pending_check', 'canceled'],
    SWT_LINK = sails.config.urls.swt.baseUrl,
    WAITLIST_STATUS_DESCRIPTION = {
        pending: 'Waitlist registration status was changed to "pending". Waiting for paying.',
        pending_card: 'Waitlist registration status was changed to "pending". Waiting for paying by card.',
        pending_check: 'Waitlist registration status was changed to "pending". Waiting for paying by check.',
        canceled: 'Waitlist registration was canceled'
    };

module.exports = {
    // get /api/event/:event/tickets
    all: function (req, res) {
        let eventID = Number(req.params.event);

        SWTSettingsService.getAllTicketTypes(eventID)
            .then(data => {
                res.status(200).json(data);
            }).catch(res.customRespError.bind(res))
    },
    // put /api/event/:event/tickets/update
    updateTicket: async function (req, res) {
        const eventID = Number(req.params.event);
        const eventTicketID = Number(req.body.event_ticket_id);
        const updatedTicketData= _.omit(
            req.body,
            'current_price',
            'is_ticket_purchased',
            'event_ticket_id'
        );

        if (!eventTicketID) return res.validation('Event ticket id required');

        try {
            const priceChangeData = await TicketsService.eventTicket.updateTicketType(
                eventID,
                eventTicketID,
                updatedTicketData
            );

            await SalesHubService.sync.syncTicketProduct(eventID, eventTicketID);

            res.status(200).json(priceChangeData);
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/save
    saveTicket: async function (req, res) {
        const eventID = req.params.event;
        const ticketType = req.body;

        try {
            const createdEventTicket = await TicketsService.eventTicket.createTicketType(
                eventID,
                ticketType
            );

            await SalesHubService.sync.syncTicketProduct(eventID, createdEventTicket.event_ticket_id);

            res.status(200).json(createdEventTicket);
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/change/create
    savePriceChange: async function (req, res) {
        const eventID = +req.params.event;

        try {
            await TicketsService.eventTicket.createPriceChange(eventID, req.body);
            res.ok();
        } catch (err) {
            return res.customRespError(err);
        }
    },
    // put /api/event/:event/tickets/change/update
    updatePriceChange: async function (req, res) {
        const eventID = parseInt(req.params.event, 10);

        try {
            await TicketsService.eventTicket.updatePriceChange(eventID, req.body);
            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/change/remove
    removePriceChange: async function (req, res) {
        const eventID = +req.params.event;
        const changeId = +req.body.change;

        try {
            const result = await TicketsService.eventTicket.removePriceChange(eventID, changeId);
            return res.status(200).json(result);
        } catch (err) {
            res.customRespError(err);
        }
    },
    // get /api/event/:event/reports/tickets
    statistics: function (req, res) {
        var $event_id = +req.params.event,
            $from_date = req.query.from && moment.utc(req.query.from, 'MM/DD/YYYY HH:mm:ss').toDate(),
            $to_date = req.query.to && moment.utc(req.query.to, 'MM/DD/YYYY HH:mm:ss').toDate();

        if (!$event_id) return res.validation('event id required');
        if ($from_date && $to_date)
            if ($from_date > $to_date) return res.validation('Wrong date range');

        TicketsStatisticsService.getStatistics($event_id, req.query.from, req.query.to)
            .then(data => {
                res.status(200).json(data)
            }).catch(err => {
                res.customRespError(err)
            })
    },
    // get /api/event/:event/reports/tickets/export
    statisticsExport: function (req, res) {
        var $event_id = +req.params.event,
            $from_date = req.query.from && moment.utc(req.query.from, 'MM/DD/YYYY HH:mm:ss').toDate(),
            $to_date = req.query.to && moment.utc(req.query.to, 'MM/DD/YYYY HH:mm:ss').toDate();

        if (!$event_id) return res.validation('event id required');
        if ($from_date && $to_date)
            if ($from_date > $to_date) return res.validation('Wrong date range');

        TicketsStatisticsService.exportStatistics($event_id, req.query.from, req.query.to)
            .then(filePath => {
                res.download(filePath, (err) => {
                    if (err) {
                        loggers.errors_log.error(err);
                        if(err.code === 'ENOENT') {
                            return res.render('500', { error: 'File not found' });
                        } else {
                            return res.serverError();
                        }
                    }
                    fs.promises.unlink(filePath).catch((err) => loggers.errors_log.error(err));
                });
            }).catch(err => {
                res.customRespError(err)
            })
    },
    // get /api/event/:event/tickets/payments
    payments: async function (req, res) {
        let eventID             = Number(req.params.event);
        let additionalFields    = ['birthdate'];
        let filters             = PaymentService.tickets.list.extractFiltersFromQuery(req.query);
        let hidePayments        = Boolean(filters.camp || filters.type);

        // NOTE: if query request contains barcode - method should return only one ticket payment data
        let isSinglePaymentMode = !!filters.barcode;

        let total_rows              = 0;
        let inline_editing          = {};
        let all_additional_fields   = [];

        filters.event_id = eventID;

        try {
            let [payments, ticket_types, column_show, editableAdditionalFields] = await Promise.all([
                PaymentService.tickets.list.getTicketsList(filters, filters.is_camp, hidePayments),
                PaymentService.tickets.list.getTicketTypes(hidePayments, eventID),
                //checking columns' visability for the client
                CustomizeTicketShowService.isVisible(eventID, additionalFields),
                PaymentService.tickets.list.getEditableAdditionalFields(eventID)
            ]);

            if(editableAdditionalFields.all_additional_fields) {
                all_additional_fields   = _formatAdditionalFieldsObj(editableAdditionalFields.all_additional_fields);
            }

            if(editableAdditionalFields.edit_fields) {
                inline_editing          = _getEditableFields(editableAdditionalFields);
            }

            if (payments.length) {

                total_rows = payments[0].total_rows;

                payments.forEach(payment => {
                    payment.total_rows = undefined;
                    // clean payment if column is hidden for client
                    for(let key of Object.keys(_.omit(column_show,'status','type'))) {
                        if (! column_show[key] ) payment[key] = undefined;
                    }
                })
            }

            let resultData = {
                payments,
                ticket_types,
                total_rows,
                column_show,
                inline_editing,
                all_additional_fields
            };

            if(isSinglePaymentMode) {
                resultData = { payments };
            }

            res.status(200).json(resultData);
        } catch (err) {
            res.customRespError(err);
        }
    },
    // get /api/event/:event/ticket/:code/resend
    resendTicketReceipt: function (req, res) {
        let eventID = req.params.event,
            code = +req.params.code,
            type = req.query.type; // email/phone

        const isAppleDevice = /iPad|iPhone|iPod|Macintosh/gm.test(req.get('User-Agent'));

        if (!code) {
            return res.validation('Ticket Code required');
        }

        return SWTReceiptService.resendTickets({ eventID, code, type, isAppleDevice })
            .then(() => res.ok())
            .catch(res.customRespError.bind(res));
    },
    // get /api/event/:event/ticket/:code/history
    getTicketHistory: function (req, res) {
        let $event_id = +req.params.event,
            $code = +req.params.code,
            $available = (req.query.available === 'true');

        if (!$event_id) return res.validation('event id required');
        if (!$code) return res.validation('ticket code required');

        Promise.all([
            Db.query(
                'SELECT (REGEXP_REPLACE(p.tickets_scan, \'(?:\\r\\n|\\r|\\n)+\', \'<br/>\', \'g\')) history \
                 FROM purchase p \
                 WHERE p.ticket_barcode = $1 \
                    AND p.event_id = $2', [$code, $event_id]
            ),
            ($available) ?
            Db.query(
                'SELECT STRING_AGG( (et.label || \': \' || pt.available), \', \') available \
                    FROM purchase_ticket pt \
                    LEFT JOIN event_ticket et \
                        ON et.event_ticket_id = pt.event_ticket_id \
                    LEFT JOIN purchase p \
                        ON p.purchase_id = pt.purchase_id \
                    WHERE p.ticket_barcode = $1 \
                    AND p.event_id = $2', [$code, $event_id]
            ) :
            null
        ]).then(results => {
            let historyRow = results[0].rows[0],
                availableRow = results[1] && results[1].rows[0];

            let history = historyRow && historyRow.history,
                available = availableRow && availableRow.available;

            res.status(200).json({
                history,
                available
            })
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // post /api/event/:event/ticket/:code/scan
    updatePaymentScanner: async function (req, res) {
        const $event_id = parseInt(req.params.event, 10);
        const $code = parseInt(req.params.code, 10);
        const $scanner_id = req.body.scanner_id;
        const $scanned_at = moment(parseInt(req.body.scanned_at)).utc();
        const $scanner_location = req.body.scanner_location;
        const $purchase_id = parseInt(req.body.purchase_id, 10);
        const $reset = (req.body.reset === true || req.body.reset === 'true');
        const $update = req.query.mode === SWTAPIService.scan.MODES.UPDATE_INFO;

        if (!$event_id) {
            return res.validation('Invalid Event Identifier');
        }
        if (!$code) {
            return res.validation('Invalid Ticket Code');
        }
        if (!$purchase_id) {
            return res.validation('Invalid Purchase Identifier');
        }

        if (!$scanned_at.isValid()) {
            return res.validation('Invalid Scan Date');
        }

        let tr;
        try {
            tr = await Db.begin();
            const {rows: [{timezone: eventTimezone }]} = await tr.query(
                squel.select()
                    .from('event', 'e')
                    .field('timezone')
                    .where('event_id = ?', $event_id)
            );

            let mode = '';

            if ($update) {
                mode = SWTAPIService.scan.MODES.UPDATE_INFO;
            } else if ($reset) {
                mode = SWTAPIService.scan.MODES.RESET;
            } else if (!$reset) {
                mode = SWTAPIService.scan.MODES.SCAN;
            }

            const scannerData = {
                scannedAt: squel.str(`TIMESTAMP WITHOUT TIME ZONE 'epoch' + ? * INTERVAL '1 millisecond'`, $scanned_at.valueOf()),
                id: $scanner_id,
                location: $scanner_location,
            }

            const updatedPurchase = await SWTAPIService.scan.__updatePurchase__({
                tr,
                eventTimezone,
                mode,
                scannerData,
                eventID: $event_id,
                ticketBarcode: $code,
                fieldsToReturn: `
                    (regexp_replace(tickets_scan, \'(?:\\r\\n|\\r|\\n)+\', \'<br/>\', \'g\')) history,
                    (EXTRACT(EPOCH FROM scanned_at)*1000)::BIGINT "scanned_at",
                    purchase_id
                `
            });

            if (!updatedPurchase || !updatedPurchase.purchase_id) {
                throw { validation: `Ticket #${$code} already scanned` };
            }

            await SWTAPIService.scan.__updatePurchaseTicket__({
                tr,
                purchaseID: updatedPurchase.purchase_id,
                mode
            })

            await tr.commit();

            return res.status(200).json(updatedPurchase);
        }
        catch(err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            return res.customRespError(err);
        }
    },
    // post /api/event/:event/ticket/:code/redeem
    redeemTickets: function (req, res) {
        var $eventId = parseInt(req.params.event, 10),
            $barcode = parseInt(req.params.code, 10),
            $tickets = req.body.tickets;

        if (!$eventId)
            return res.validation('Invalid Event Identifier');
        if (!$barcode)
            return res.validation('Invalid Ticket Code');
        if (_.isEmpty($tickets))
            return res.validation('No Tickets passed');
        if (!_.isArray($tickets))
            return res.validation('Expecting tickets to be an Array')

        Db.query(
            `SELECT p.purchase_id, e.timezone
             FROM "purchase" p 
             INNER JOIN "event" e 
                ON e.event_id = p.event_id
             WHERE p.ticket_barcode = $1 
                AND p.event_id = $2
                AND p.payment_for = 'tickets'
                AND p.canceled_date IS NULL`, [$barcode, $eventId]
        ).then(function (result) {
            var purchase = _.first(result.rows);
            if (_.isEmpty(purchase)) {
                throw {
                    validation: 'No suitable Payment found'
                }
            }
            return purchase;
        }).then(function (purchase) {
            return Db.begin()
                .then(function (tr) {
                    return Promise.all(
                        $tickets.map(function (ticket) {
                            return tr.query(
                                `UPDATE "purchase_ticket" pt
                                SET "available" = (CASE WHEN $3 > quantity THEN quantity ELSE $3 END)
                             WHERE purchase_id = $1 
                                AND purchase_ticket_id = $2
                             RETURNING (
                                SELECT et.label FROM "event_ticket" et
                                WHERE et.event_ticket_id = pt.event_ticket_id
                             ) "label", pt."available"`, [purchase.purchase_id, ticket.purchase_ticket_id, ticket.count]
                            ).then(function (result) {
                                var pt = _.first(result.rows);
                                if (_.isEmpty(pt)) {
                                    throw {
                                        validation: 'Invalid Ticket Passed (Not found for the Purchase)'
                                    }
                                }
                                return pt;
                            })
                        })
                    ).then(function (updatedTickets) {
                        var description = '';
                        updatedTickets.forEach(function (t) {
                            description += `"${t.label}" count ${t.available}; `
                        })
                        return description;
                    }).then(function (description) {
                        return tr.query(
                            `UPDATE "purchase"
                         SET "scanned_at" = NULL,
                             "scanner_id" = 'Admin Account',
                             "scanner_location" = 'SW Event Owner''s Account',
                             "tickets_scan" = 
                                CONCAT_WS(
                                    CHR(10), 
                                    tickets_scan, 
                                    FORMAT(
                                        'Redeem by "Admin Account" at "SW Event Owner''s Account" on "%s". Details: %s',
                                        TO_CHAR((NOW() AT TIME ZONE $2), 'YYYY/MM/DD HH12:MI AM'),
                                        ($3)::TEXT
                                    )
                             )
                         WHERE "purchase_id" = $1`, [purchase.purchase_id, purchase.timezone, description]
                        )
                    }).then(function () {
                        return tr.commit()
                    }).catch(function (err) {
                        if (tr && !tr.isCommited) {
                            tr.rollback()
                        }
                        throw err;
                    })
                }).then(function () {
                    return purchase.purchase_id;
                })
        }).then(function () {
            res.ok();
        }, function (err) {
            res.customRespError(err)
        })
    },
    // get /api/event/:event/ticket/:code/charge
    retreiveChargeInfo: function (req, res) {
        var $event_id = +req.params.event,
            $code = +req.params.code;

        if (!$event_id) return res.status(400).json({
            validation: 'event id required'
        });
        if (!$code) return res.status(400).json({
            validation: 'No ticket barcode provided'
        });

        var query =
            'SELECT  \
                p.stripe_charge_id charge_id, \
                to_char(p.date_refunded, \'Mon DD, YYYY HH12:MI, AM\') refunded, \
                to_char(p.canceled_date, \'Mon DD, YYYY HH12:MI, AM\') canceled, \
                to_char(p.dispute_created, \'Mon DD, YYYY HH12:MI, AM\') disputed, \
                p.card_last_4 last4, \
                p.card_name \
            FROM purchase p \
            WHERE p.event_id = $1 \
            AND p.ticket_barcode = $2 \
            AND p.payment_for = \'tickets\' \
            AND p.type = \'card\'';

        Db.query(query, [$event_id, $code]).then(function (result) {
            let row = _.first(result.rows);
            if (_.isEmpty(row)) {
                res.status(200).json({
                    validation: 'No payment found'
                })
            } else {
                res.status(200).json({
                    payment: row
                });
            }
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/event/:event/tickets/payments/export
    paymentsExport: function (req, res) {
        let filters = PaymentService.tickets.list.extractFiltersFromQuery(req.query, { isExport: true});
        let $event_id = req.params.event;

        let aliases = {
            barcode: 'Ticket Barcode',
            amount: 'Total Amount',
            purchased_date: 'Purchase Date',
            purchased_time: 'Purchase Time',
            first: 'First',
            last: 'Last',
            email: 'Email',
            zip: 'Zip',
            phone: 'Phone',
            status: 'Payment Status',
            scanned_at_date: 'Scanned Date',
            scanner_id: 'Scanner Id',
            scanned_at_time: 'Scanned Time',
            scanner_location: 'Scanner Location'
        }

        generatePaymentsQuery($event_id, filters, aliases).then(sqlQuery => {
            return Buffer.from(sqlQuery).toString('base64')
        }).then(encodedQuery => {
            return new Promise((resolve, reject) => {
                let filepath = UPLOADS_DIR + `/payments_exp_${Date.now()}${req.user.user_id}.xlsx`,
                    params = [],
                    xlsExportProcess,
                    error = '';

                if (optimist.argv.dev) {
                    params.push('--dev');
                }

                const cs = Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64');

                params.push('--res_file', filepath, '--base64_sql_query', encodedQuery, '--cs', cs);

                xlsExportProcess = spawn(path.resolve(__dirname, '..', '..', '..', 'sw-utils', 'sql2xlsx.js'), params, {
                    detached: true,
                    stdio: 'pipe'
                });

                xlsExportProcess.stderr.on('data', function (err) {
                    error = err.toString();
                });

                xlsExportProcess.on('error', function (err) {
                    loggers.errors_log.error('Payments xlsx error', err);
                    reject(err);
                });

                xlsExportProcess.on('close', function (code) {
                    if (code !== 0) {
                        loggers.errors_log.error(error);
                    }
                    resolve(filepath);
                });
                xlsExportProcess.unref();
            })
        }).then(path => {
            res.download(path, 'payments.xlsx', function (err) {
                if (err) {
                    loggers.errors_log.error(`Payments xlsx download error. Event ID: ${$event_id}`, err);

                    ErrorSender.defaultError(err);

                    return res.serverError('No file created ' + path);
                } else {
                    fs.unlinkSync(path);
                }
            })
        }).catch(err => {
            res.customRespError(err);
        });
    },

    // get /api/event/:event/ticket/:code/payment/info
    paymentInfo: function (req, res) {
        let $event_id = parseInt(req.params.event, 10),
            $code = parseInt(req.params.code, 10);

        if (!$event_id) return res.validation('event id required');
        if (!$code) return res.validation('No ticket barcode provided');

        let query =
            `SELECT 
                e.event_tickets_code,
                p.is_ticket,
                p.is_payment,
                p.wristband_serial,
                p.linked_purchase_id,
                p.net_profit,
                p.registration_status,
                (e.tickets_settings ->> 'require_covid_test_for_each_ticket')::BOOLEAN IS TRUE require_covid_test_for_each_ticket,
                e.long_name "event_name", TO_CHAR(e.date_start, 'Dy, Mon DD') "event_date",
                el.name "event_location", e.name "event_short_name", e.email "event_email",
                TO_CHAR( (p.created::timestamptz AT TIME ZONE e.timezone), 'Dy, Mon DD, YYYY HH12:MI:SS AM') purchased, 
                TO_CHAR(p.ticket_barcode, '999-999-999') ticket_barcode, p.ticket_barcode "barcode",
                TO_CHAR( 
                    (p.scanned_at::timestamptz AT TIME ZONE e.timezone), 
                    'HH12:MI AM on Mon DD'
                ) scanned_at_timezone,
                TO_CHAR( 
                    (p.date_refunded::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) date_refunded, 
                TO_CHAR( 
                    (p.canceled_date::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) canceled_date, 
                TO_CHAR( 
                    (COALESCE(p.dispute_created, p2.dispute_created)::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) dispute_created, 
                TO_CHAR( 
                    (p.received_date::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) received_date,
                (   
                    CASE
                        WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                            AND COALESCE((e.tickets_settings ->> 'require_coupon')::BOOLEAN, false) IS TRUE
                        THEN
                        (SELECT ROW_TO_JSON("c_row")
                            FROM (
                                SELECT UPPER(tc.code) "code", rt.team_name, 
                                ARRAY_TO_JSON(
                                    ARRAY_AGG(tcr.first || ' ' || tcr.last || ' <' || tcr.email || '>')
                                        FILTER ( WHERE tcr.email IS NOT NULL )
                                ) receivers
                                FROM purchase_ticket pt
                                    JOIN ticket_coupon_receiver tcr ON pt.ticket_coupon_id = tcr.ticket_coupon_id
                                    JOIN ticket_coupon tc ON tc.ticket_coupon_id = tcr.ticket_coupon_id
                                    LEFT JOIN roster_team rt 
                                        ON rt.roster_team_id = tcr.roster_team_id 
                                        AND rt.event_id = p.event_id                                   
                                WHERE pt.purchase_id = p.purchase_id
                                GROUP BY tc.code, rt.team_name
                            ) "c_row"
                        )
                        WHEN  (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                            AND (
                                SELECT eec.is_active 
                                FROM event_ticket_buy_entry_code_settings eec 
                                WHERE eec.event_id = p.event_id
                            )::BOOLEAN IS TRUE
                        THEN
                        (
                            SELECT ROW_TO_JSON("c_row")
                            FROM (
                                SELECT UPPER(pt.ticket_buy_entry_code) "code", rt.team_name
                                FROM purchase_ticket pt
                                 LEFT JOIN roster_team rt 
                                    ON LOWER(rt.organization_code) = LOWER(pt.ticket_buy_entry_code)
                                    AND rt.event_id = p.event_id
                                    AND rt.deleted IS NULL
                                WHERE pt.purchase_id = p.purchase_id 
                            ) "c_row"
                        )    
                        ELSE '{}'::JSON
                    END                         
                ) "coupon",
                (
                    CASE
                        WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS NOT TRUE
                            THEN '{}'::JSON
                        ELSE 
                        (SELECT ROW_TO_JSON("p_row")
                            FROM (
                                   SELECT
                                     payment_row.purchase_id,
                                     (payment_row.stripe_payment_type = 'connect') "use_connect",
                                     payment_row.stripe_charge_id                  "charge_id",
                                     payment_row.amount,
                                     payment_row.type,
                                     payment_row.stripe_fee,
                                     payment_row.tilled_fee,
                                     payment_row.payment_provider,
                                     payment_row.collected_sw_fee,
                                     COALESCE(payment_row.amount_refunded, 0)      "amount_refunded",
                                     payment_row.user_id,
                                     payment_row.additional_fee_amount,
                                     e.stripe_tickets_fee_payer "stripe_fee_payer",
                                     e.tickets_sw_fee_payer "sw_fee_payer",
                                     e.tilled_tickets_fee_payer "tilled_fee_payer"
                                   FROM purchase "payment_row"
                                   WHERE payment_row.purchase_id = p.linked_purchase_id
                                    AND payment_row.is_payment IS TRUE
                                 ) "p_row") 
                    END             
                                                  
                ) "payment_row",
                ( CASE WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE THEN
                    (
                        SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t")))
                          FROM (
                              SELECT FORMAT('%s %s', p1.first, p1.last) "name", p1.ticket_barcode "barcode", et.label,
                              pt.purchase_ticket_id, et.event_ticket_id "id",
                                pt.available, pt.quantity, ticket_purchase.amount "current_total_amount", (ticket_purchase.amount + COALESCE(ticket_purchase.amount_refunded, 0)) "initial_total_amount",
                                pt.amount, pt.ticket_price, pt.discount, 
                                COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee, 0)::REAL "app_fee",
                                (
                                    CASE 
                                        WHEN (pt.quantity = 0) AND (pt.amount > 0)
                                            THEN pt.amount
                                        ELSE 0
                                    END
                                ) "cancellation",
                                p1.status = 'canceled' "is_canceled",
                                pt.ticket_discount_id "discount_id",
                                ticket_purchase.purchase_id
                          FROM purchase p1
                            LEFT JOIN purchase_ticket pt
                              ON p1.purchase_id = pt.purchase_id
                            INNER JOIN "purchase" as ticket_purchase 
                                ON ticket_purchase.purchase_id = pt.purchase_id
                            LEFT JOIN event_ticket et
                              ON et.event_ticket_id = pt.event_ticket_id
                          WHERE p1.linked_purchase_id = p.linked_purchase_id
                            AND p1.is_ticket IS TRUE
                          ORDER BY p1.first, p1.last
                          ) "t"
                    )
                    ELSE null
                    END
                ) "tickets_in_payment",
                p.first, p.last, p.status, p.type, p.email, p.zip, p.phone, p.source, 
                COALESCE(p.dispute_status, p2.dispute_status) "dispute_status",
                COALESCE(p.debt, 0)::NUMERIC "debt", p.scanner_id, p.scanner_location, 
                (EXTRACT(EPOCH FROM (p.scanned_at)) * 1000)::BIGINT "scanned_at",
                (REGEXP_REPLACE(p.tickets_scan, '(?:\r\n|\r|\n)+', '<br/>', 'g'))  history, 
                p.amount, p.tickets_additional additional_fields, p.purchase_id, p.user_id, p.event_id, 
                COALESCE(e.tickets_sw_fee, 0)::REAL "app_fee", p.amount "current_total_amount",
                TO_CHAR(p.amount::NUMERIC, '${PG_CURRENCY_FORMAT}') amount_formatted, ( 
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))) 
                    FROM ( 
                        SELECT  pt.purchase_ticket_id, et.label, et.event_ticket_id "id",
                                pt.available, pt.quantity, et.short_label, et.ticket_type,
                                TO_CHAR(
                                    pt.has_covid_test::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY HH12:MI AM'
                                ) has_covid_test,
                                pt.amount, pt.ticket_price, pt.discount, pt.registration_status,
                                ticket_purchase.amount "current_total_amount", (ticket_purchase.amount + COALESCE(ticket_purchase.amount_refunded, 0)) "initial_total_amount",
                                to_char(pt.ticket_price::NUMERIC, '${PG_CURRENCY_FORMAT}') price_formatted,
                                COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee, 0)::REAL "app_fee",
                                ec.event_camp_id "camp_id", ec.name "camp_name", (
                                    CASE 
                                        WHEN (pt.quantity = 0) AND (pt.amount > 0)
                                            THEN pt.amount
                                        ELSE 0
                                    END
                                ) "cancellation",
                                pt.ticket_discount_id "discount_id"
                        FROM purchase_ticket pt 
                        LEFT JOIN event_ticket et 
                            ON et.event_ticket_id = pt.event_ticket_id 
                        INNER JOIN "purchase" as ticket_purchase 
                            ON ticket_purchase.purchase_id = pt.purchase_id
                        LEFT JOIN "event_camp" ec 
                            ON ec.event_camp_id = et.event_camp_id
                        WHERE pt.purchase_id = p.purchase_id 
                        ORDER BY et.sort_order, et.event_ticket_id 
                    ) t 
                ) tickets, (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(h))) 
                    FROM (
                        SELECT 
                            TO_CHAR((ph.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI:SS, AM')
                                                                                                             "created",
                            TO_CHAR((ph.operation_date::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI:SS, AM')
                                                                                                             "op_date",
                            ph.description, ph.notes, ph.amount, ph.action, ph.check_num, (
                                CASE
                                    WHEN ph.action = 'debt.paid' THEN TRUE
                                    ELSE FALSE
                                END
                            ) "is_income"
                        FROM "purchase_history" ph 
                        WHERE ph.purchase_id = p.purchase_id 
                            AND ph.action IN ('debt.paid', 'debt.refunded')
                        ORDER BY ph.created DESC
                    ) "h"
                ) "debt_history",
                e.tickets_purchase_additional_fields event_fields, (
                    CASE 
                        WHEN e.ticket_camps_registration IS TRUE THEN 'camps'
                        ELSE 'tickets'
                    END
                ) "sales_type",
                e.tickets_has_barcodes "scan_available", (
                    CASE
                        WHEN (p.is_payment IS TRUE AND p.stripe_percent > 0)
                            THEN ROUND(p.stripe_percent / 100, 4)
                        WHEN (p.is_payment IS NOT TRUE AND p2.stripe_percent > 0)
                            THEN ROUND(p2.stripe_percent / 100, 4)    
                        ELSE 0
                    END
                 )::REAL "stripe_percent",
                COALESCE(e.stripe_tickets_fixed, 0)::REAL "stripe_fixed",
                (
                    CASE
                        WHEN (p.is_payment IS TRUE AND p.tilled_percentage > 0)
                            THEN ROUND(p.tilled_percentage / 100, 4)
                        WHEN (p.is_payment IS NOT TRUE AND p2.tilled_percentage > 0)
                            THEN ROUND(p2.tilled_percentage / 100, 4)    
                        ELSE 0
                    END
                 )::REAL "tilled_percentage",
                COALESCE(e.tilled_tickets_fixed, '${TilledService.__DEFAULT_FIXED_CARD_FEE__}')::REAL "tilled_fixed",
                e.stripe_tickets_fee_payer "stripe_fee_payer",
                e.tilled_tickets_fee_payer "tilled_fee_payer",
                COALESCE((p.payment_provider), p2.payment_provider, '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "payment_provider",
                e.tickets_sw_fee_payer "sw_fee_payer",
                COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) AS assigned_tickets_mode,
                p.deactivated_at IS NOT NULL AS is_ticket_deactivated,
                (
                    SELECT notes
                    FROM purchase_history
                    WHERE action IN ('ticket.deactivated', 'ticket.activated')
                    AND purchase_id = p.purchase_id
                    ORDER BY created DESC
                    LIMIT 1
                ) AS deactivate_reason,
                p.collected_sw_fee,
                p.stripe_fee,
                CONCAT(TO_CHAR((arr.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MIam'),
                FORMAT(' (%s)', (SELECT abbrev FROM pg_timezone_names WHERE name = e.timezone))
              ) as refund_request_at,
              arr.notes refund_request_reason
            FROM purchase p 
            LEFT JOIN "event" e 
                ON e.event_id = p.event_id
            LEFT JOIN "event_location" el 
                ON e.event_id = el.event_id 
                AND el.number = 1
            LEFT JOIN purchase p2 ON p.linked_purchase_id = p2.purchase_id
            LEFT JOIN admission_refund_request arr on arr.purchase_id = p.purchase_id
            WHERE p.ticket_barcode = $1 
            AND p.event_id = $2 
            AND p.payment_for = 'tickets'`;
        Db.query(query, [$code, $event_id])
            .then(result => {
                let payment = _.first(result.rows);
                if (_.isEmpty(payment)) {
                    res.status(200).json({
                        payment: {}
                    });
                } else {

                    if (payment.source !== 'api') {
                        generateHash(payment, req, $code);
                    } else {
                        payment.receipt_hash = null
                    }

                    payment.amount = __parse(payment.amount);
                    payment.collected_sw_fee = __parse(payment.collected_sw_fee);

                    if (payment.type !=='waitlist') {
                        return findPurchaserInfo(res, payment, req, $code);
                    }

                    res.status(200).json({
                        payment: _.omit(payment, 'user_id')
                    });
                }
            }).catch(res.customRespError.bind(res));
    },
    //PUT /api/event/:event/ticket/:code/covid-test
    updateCovidTestValidation: function (req, res) {
        let eventID = Number(req.params.event);
        let ticketBarcode = req.params.code;
        let reset = req.body.reset;

        if (!eventID) {
            return res.validation('Event ID required');
        }

        if (!ticketBarcode) {
            return res.validation('No ticket barcode provided');
        }

        return PaymentService.tickets.updateCovidTestValidation(eventID, ticketBarcode, reset)
            .then(hasCovidTest => res.status(200).json({ value: hasCovidTest }))
            .catch(res.customRespError.bind(res));
    },

    // post /api/event/:event/ticket/:code/payment/update
    updatePaymentInfo: async function (req, res) {
        let eventID         = Number(req.params.event);
        let ticketBarcode   = req.params.code;

        if (!eventID) {
            return res.customRespError({validation: 'Event ID required'})
        }

        if (!ticketBarcode) {
            return res.customRespError({validation: 'No ticket barcode provided'})
        }

        try {
            const validationResult = validationSchemas.editPayerInfo.validate(req.body);
            if (validationResult.error) {
                throw { validationErrors: validationResult.error.details };
            }
            const payerInfo = validationResult.value;

            const purchaseID = await PaymentService.tickets.editPayerInfo(eventID, ticketBarcode, payerInfo, req.user, true);

            try{
                let paymentData = await PaymentService.getPaymentDataForQRContent(purchaseID, eventID);
                await ApplePassService.updateApplePassForTicket(paymentData)
            }catch(err){
                loggers.errors_log.error('Error updating apple pass err:', err)
            }


            res.status(204).send();
        } catch (err) {
            res.customRespError(err);
        }
    },
    // get /api/event/:event/tickets/payments/map
    paymentsOnMap: function (req, res) {
        var $event_id = +req.params.event;
        if (!$event_id) return res.status(400).json({
            validation: 'No event id provided'
        });

        var query =
            'SELECT  \
                zips.*, zl.location \
            FROM( \
                SELECT DISTINCT \
                    (CASE WHEN p.zip IS NOT NULL THEN p.zip ELSE u.zip END) zip, \
                    COALESCE(sum(pt.quantity), 0)::INT tickets_count \
                FROM purchase p \
                LEFT JOIN "user" u \
                    ON u.user_id = p.user_id \
                LEFT JOIN purchase_ticket pt \
                    ON pt.purchase_id = p.purchase_id \
                    AND pt.canceled IS NULL \
                WHERE p.event_id = $1 \
                AND p.payment_for = \'tickets\' \
                AND p.status <> \'canceled\' \
                AND p.canceled_date IS NULL \
                AND p.amount > 0 \
                GROUP BY (CASE WHEN p.zip IS NOT NULL THEN p.zip ELSE u.zip END) \
            ) zips \
            INNER JOIN zip_location zl \
                ON zl.zip = zips.zip';
        Db.query(query, [$event_id]).then(function (result) {
            res.status(200).json({
                payments: result.rows || []
            });
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/event/:event/tickets/email/text
    getEmailText: function (req, res) {
        var $event_id = +req.params.event;
        if (!$event_id) return res.validation('No event id provided');

        var query =
            `SELECT  
                e.tickets_description event_description, 
                e.tickets_receipt_descr disclaimer, 
                COALESCE(e.event_kiosk_description, '[]')::JSON "kiosk_description",
                COALESCE(e.tickets_locations, '[]'::JSON) locations 
            FROM "event" e 
            WHERE e.event_id = $1`;
        Db.query(query, [$event_id]).then(function (result) {
            if (result.rowCount === 0) {
                res.validation('No Event found')
            } else {
                res.status(200).json((_.first(result.rows) || {}))
            }
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // post /api/event/:event/tickets/update/text
    saveTicketsDescriptionFields: async function (req, res) {
        const eventID = Number(req.params.event);
        const fields = req.body;

        if (!eventID) {
            return res.validation('No event id provided');
        }

        if(_.isEmpty(fields)) {
            return res.validation('No data provided');
        }

        try {
            await TicketsService.updateTicketsDescriptionFields(eventID, fields);
            await SalesHubService.sync.syncPointOfSales(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.status(200).json({});
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/event/:event/ticket/:code/emails
    getPurchaseSendedEmails: function (req, res) {
        const eventId = parseInt(req.params.event, 10),
            purchaseId = parseInt(req.params.code, 10);

        return Db.query(
            `SELECT ee.email_from, ee.email_to, ee.email_text, 
                ee.email_subject, ee.created, ee.email_cc, ee.email_bcc
            FROM event_email ee
            LEFT JOIN event_change ec
                ON ee.event_email_id = ec.event_email_id
            WHERE ee.event_id = $1
              AND ec.purchase_id = $2
            UNION
            SELECT e.email email_from, p.email email_to, ph.notes email_text, 
                ph.description email_subject, ph.created, null, null
            FROM purchase_history ph
            LEFT JOIN purchase p
              ON p.purchase_id = ph.purchase_id
            LEFT JOIN event e
              ON e.event_id = p.event_id
            WHERE ph.notes IS NOT NULL
              AND p.purchase_id = $2
              AND e.event_id = $1
              AND ph.action = 'ticket.email-sent'
              AND p.email IS NOT NULL
            ORDER BY created`, [eventId, purchaseId]
        ).then((result) => {
            res.status(200).json({
                emails: result.rows
            });
        }).catch(err => {
            res.customRespError(err);
        })
    },

    // post /api/event/:event/ticket/:code/send-email
    sendMailToCustomer: function (req, res) {
        const data = req.body.email,
            purchaseId = parseInt(req.params.code, 10),
            eventId = parseInt(req.params.event, 10);

        let reciever = data.email,
            replyto = data.replyto,
            cc = data.cc || null,
            bcc = data.bcc || null,
            subject = data.subject,
            text = data.text;

        if (!reciever) {
            return res.validation('Email is empty');
        }
        if (!subject) {
            return res.validation('Subject is empty');
        }
        if (!text) {
            return res.validation('Text is empty');
        }

        EmailService.renderAndSend({
            template: 'stripe/purchaserEmail',
            layout: 'official/layout',
            data: _.defaults({
                baseUrl: BASE_URL
            }, {
                message: text
            }),
            from: '"SportWrench" <<EMAIL>>',
            to: reciever,
            replyto: replyto,
            cc: cc,
            bcc: bcc,
            subject: subject,
        }).then((result) => {

            return Db.query(
                squel.insert().into('event_email')
                .setFields({
                    email_from: replyto,
                    email_html: result.html,
                    email_subject: subject,
                    email_text: result.text,
                    email_id: result.email_id,
                    email_to: reciever,
                    event_id: eventId,
                    email_cc: cc,
                    email_bcc: bcc,
                    reason_type: 'purchase dispute',
                    recipient_type: 'ticket buyer'
                })
                .returning('event_email_id')
            ).then((data) => {
                let eventEmailId = data.rows[0].event_email_id;

                return Db.query(
                        squel.insert().into('event_change')
                        .set('event_email_id', eventEmailId)
                        .set('event_id', eventId)
                        .set('action', 'purchase.email.sent')
                        .set('purchase_id', purchaseId)

                    ).then(() => res.status(200).json({}))
                    .catch(err => loggers.errors_log.error(err));
            })
        }).catch(err => {
            res.customRespError(err);
        })
    },

    // post /api/event/:event/tickets/passcodes
    updatePasscodes: async function (req, res) {
        const eventID = +req.params.event;
        const data = req.body;

        try {
            await TicketsService.eventTicketGuruSettings.updateTicketsPasscodes(eventID, data);
            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/sales-dates
    changePurchaseDates: async function (req, res) {
        const eventID = Number(req.params.event);

        if (!eventID) return res.validation('No event id provided');

        const {
            date_start,
            date_end
        } = req.body;

        if(!date_start || !date_end) return res.validation('Invalid dates passed');

        try {
            await updateInitialDates(eventID, date_start, date_end);

            await SalesHubService.sync.syncPointOfSales(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.status(200).json({});
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/:ticket/discounts/upload
    importDiscountsList: function (req, res) {
        let $eventId = parseInt(req.params.event, 10),
            $eventTicketId = parseInt(req.params.ticket, 10);

        let $amount = parseFloat(req.body.amount, 10),
            $type = req.body.type,
            $skipFirstLine = req.body.skip_first;

        if ($type !== 'COUPON' && $type !== 'EM2_FREE') {
            try {
                req.file('discounts-list')._files[0].stream.end();
            } catch (e) {
                loggers.errors_log.error(e);
            }
            return res.validation('Invalid Type')
        }

        if (($type === 'COUPON') && (!$amount || $amount < 0)) {
            try {
                req.file('discounts-list')._files[0].stream.end();
            } catch (e) {
                loggers.errors_log.error(e);
            }
            return res.validation('Invalid Amount')
        }

        new Promise(function (resolve, reject) {
            let errorMsg = '';

            let proc = spawn('node', [
                'parse-discounts-list.js',
                `--type=${$type}`,
                `--path=${UPLOAD_DIR_DISCOUNTS}`,
                `--event=${$eventId}`,
                `--ticket=${$eventTicketId}`,
                `--amount=${$amount}`,
                `--skip_first=${$skipFirstLine}`,
                `--conn=${Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64')}`
            ], {
                cwd: DISCOUNTS_PARSER_PATH,
                stdio: 'pipe'
            }).on('error', err => {
                reject({
                    validation: (err && err.toString()) || errorMsg
                });
            }).on('close', exitCode => {
                switch (exitCode) {
                    case 0:
                        resolve();
                        break;
                    case 2:
                        reject({
                            validation: 'Empty File'
                        });
                        break;
                    default:
                        reject(!!errorMsg ? {
                            validation: errorMsg
                        } : new Error('Internal Error'));
                }
            });

            proc.stderr.on('data', function (error) {
                errorMsg += error;
            })
            // That's why I do not like Sails
            // TODO: remove Skipper usage
            try {
                req.file('discounts-list')._files[0].stream.pipe(proc.stdin);
            } catch (e) {
                reject({
                    validation: e.message
                })
            }
        }).then(() => {
            res.ok();
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/event/:event/tickets/short
    getEventTicketsShort: function (req, res) {
        var $event_id = parseInt(req.params.event, 10);

        if (!$event_id) return res.validation('Invalid event identifier')

        Db.query(
            `SELECT et.event_ticket_id "id", et.label 
             FROM event_ticket et 
             WHERE et.event_id = $1 
             ORDER BY et.sort_order, et.event_ticket_id`, [$event_id]
        ).then(result => {
            res.status(200).json({
                tickets: result.rows
            });
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/event/:event/tickets/discounts
    getDiscountsList: function (req, res) {

        let $limit = parseInt(req.query.limit, 10);
        let $offset = parseInt(req.query.offset, 10);

        if(!$limit || swUtils.isNumberLessThanZero($limit)) {
            $limit = 100;
        }
        if(!$offset || swUtils.isNumberLessThanZero($offset)) {
            $offset = 0;
        }

        let $eventId = parseInt(req.params.event, 10),
            $search = swUtils.escapeStr(req.query.search || ''),
            $order = DISCOUNTS_ORDER[req.query.order],
            $reverse = (req.query.reverse === 'true'),
            $unused = (req.query.unused === 'true'),
            $notNotified = (req.query.not_notified === 'true'),
            baseUrl = sails.config.urls.swt.baseUrl,
            counterSQLQuery,
            resultsSQLQuery

        if (!$eventId) {
            return res.validation('Invalid event identifier');
        }

        resultsSQLQuery = squel.select()
            .from('ticket_discount', 'td')
            .left_join('event_ticket', 'et', 'et.event_ticket_id = td.event_ticket_id and et.event_id = td.event_id')
            .left_join('event', 'e', 'e.event_id = td.event_id')
            .field('td.ticket_discount_id', 'id')
            .field('td.email')
            .field('td.first')
            .field('td.last')
            .field('td.max_count "quantity"')
            .field('td.used_count "used"')
            .field('et.label "type_name"')
            .field('code', 'coupon')
            .field(
                `CASE WHEN (td.discount < 0) THEN 'free' ELSE TO_CHAR(td.discount, '${PG_CURRENCY_FORMAT}') END`,
                'amount'
            )
            .field(
                `FORMAT('${baseUrl}/#/events/%s?discount=%s', e.event_tickets_code, td.code)`,
                'coupon_link'
            )
            .where('td.event_id = ?', $eventId)

        counterSQLQuery = squel.select()
            .from('ticket_discount', 'td')
            .field('COUNT(td.*)::INT', '"count"')
            .where('td.event_id = ?', $eventId);

        if ($order)
            resultsSQLQuery.order($order, $reverse)
        if ($search) {
            var formattedSearch = ('%' + $search + '%');
            resultsSQLQuery.where(
                squel.expr()
                .and('td.first ILIKE ?', formattedSearch)
                .or('td.last ILIKE ?', formattedSearch)
                .or('td.email ILIKE ?', formattedSearch)
                .or('td.code ILIKE ?', formattedSearch)
            );
            counterSQLQuery.where(
                squel.expr()
                .and('td.first ILIKE ?', formattedSearch)
                .or('td.last ILIKE ?', formattedSearch)
                .or('td.email ILIKE ?', formattedSearch)
                .or('td.code ILIKE ?', formattedSearch)
            );
        }

        if ($unused) {
            resultsSQLQuery.where('td.used_count = 0');
            counterSQLQuery.where('td.used_count = 0');
        }

        if ($notNotified) {
            resultsSQLQuery.where('td.notified_at IS NULL');
            counterSQLQuery.where('td.notified_at IS NULL');
        }

        resultsSQLQuery.limit($limit).offset($offset);

        Db.query(
                `WITH counted_rows AS (${counterSQLQuery})
             SELECT d.discounts, (SELECT "count" FROM counted_rows LIMIT 1) "count"
             FROM (
                 SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(d))), '[]'::JSON) "discounts"
                 FROM (
                     ${resultsSQLQuery.toString()}
                 ) "d"
             ) "d"`
            )
            .then(function (result) {
                res.status(200).json(_.first(result.rows) || {})
            }, function (err) {
                res.customRespError(err);
            })
    },
    // get /api/event/:event/tickets/discounts/:discount/info
    getDiscountInfo: function (req, res) {
        var $eventId = parseInt(req.params.event, 10),
            $discountId = parseInt(req.params.discount, 10);

        if (!$eventId) return res.validation('Invalid event identifier');
        if (!$discountId) return res.validation('Invalid discount identifier');

        Db.query(
            `SELECT
                td.code, td.email, td.first, td.last, (td.discount = -1) "is_free",
                td.max_count, td.used_count, td.discount "amount", (
                    CASE
                        WHEN td.notified_at IS NOT NULL 
                        THEN TO_CHAR((td.notified_at::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI AM') 
                        ELSE 'N/A'
                    END
                ) "notified_at", (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(payments_list))), '[]'::JSON)
                    FROM (
                        SELECT p.purchase_id "id", pt.amount, pt.discount "discounted", pt.quantity, 
                            TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI AM') 
                                                                                                            "created",
                            FORMAT('%s %s', p.first, p.last) "payer_name", p.email, p.ticket_barcode "barcode"
                        FROM "purchase" p   
                        INNER JOIN "purchase_ticket" pt
                            ON pt.purchase_id = p.purchase_id
                            AND pt.ticket_discount_id = td.ticket_discount_id
                        WHERE p.event_id = td.event_id
                            AND p.payment_for = 'tickets'
                    ) "payments_list"
                ) "payments"
            FROM "ticket_discount" td
            LEFT JOIN "event" e
                ON e.event_id = td.event_id
            WHERE td.ticket_discount_id = $1
                AND td.event_id = $2`, [$discountId, $eventId]
        ).then(function (result) {
            let discount = result.rows[0] || {};
            discount.amount = +discount.amount;
            res.status(200).json({
                discount
            });
        }, function (err) {
            res.customRespError(err)
        })
    },
    // put /api/event/:event/tickets/discounts/:discount/update
    updateDiscountInfo: function (req, res) {
        var $eventId = parseInt(req.params.event, 10),
            $discountId = parseInt(req.params.discount, 10),
            $discountInfo = req.body.discount;

        if (!$eventId) return res.validation('Invalid event identifier');
        if (!$discountId) return res.validation('Invalid discount identifier');
        if (_.isEmpty($discountInfo)) return res.validation('Empty discount info');

        new Promise(function (resolve, reject) {
            const validationResult = discountSchema.validate($discountInfo);
            if(validationResult.error) {
                reject({
                    validationErrors: validationResult.error.details
                });
            }
            else {
                resolve();
            }
        }).then(function () {
            return checkCouponCodeUniqueness($eventId, $discountInfo.code, $discountId);
        }).then(function () {
            return Db.query(
                squel.update().table('ticket_discount')
                .setFields($discountInfo)
                .where('ticket_discount_id = ?', $discountId)
                .where('event_id = ?', $eventId)
                .returning('ticket_discount_id')
            )
        }).then(function (result) {
            if (result.rowCount === 1) {
                res.ok();
            } else {
                res.validation('No discount row found');
            }
        }, function (err) {
            res.customRespError(err)
        })
    },
    // get /api/event/:event/tickets/discounts/templates
    getDiscountsEmailTmpls: function (req, res) {
        var $eventId = parseInt(req.params.event, 10),
            $eventOwnerId;

        if (!$eventId) return res.validation('Invalid event identifier passed')

        $eventOwnerId = eventOwnerService.findId($eventId, req.session.passport.user);

        if (!$eventOwnerId) return res.forbidden('No Event Owner role found');

        Db.query(
            `SELECT 
                et.email_html "html",
                et.email_subject "subject",
                et.title,
                et.email_template_id "id"
            FROM email_template et 
            WHERE (et.event_owner_id = $1 OR et.event_owner_id = 0)
            ORDER BY et.email_template_id DESC
            `, [$eventOwnerId]
        ).then(function (result) {
            res.status(200).json({
                templates: result.rows || []
            })
        }, function (err) {
            res.customRespError(err)
        })
    },
    // post /api/event/:event/tickets/discounts/:discount/remove
    removeDiscount: function (req, res) {
        let $eventId = req.params.event,
            $discountId = req.params.discount;

        if (!$discountId) {
            return res.validation('Invalid Discount Identifier');
        }

        co(function* () {
            let result = yield(Db.query(
                `SELECT DISTINCT ON (p.purchase_id) p.purchase_id, p.amount, p.type
                 FROM "purchase_ticket" pt 
                 INNER JOIN "purchase" p 
                     ON p.purchase_id = pt.purchase_id 
                 INNER JOIN "ticket_discount" td     
                     ON pt.ticket_discount_id = td.ticket_discount_id 
                     AND td.event_id = $2
                 WHERE pt.ticket_discount_id = $1
                    AND p.event_id = $2`, [$discountId, $eventId]
            ));

            if (result.rows.length > 0) {
                throw {
                    validation: `Cannot remove discount, becaused it is applied to ${result.rows.length} payment(s)`
                }
            }

            result = yield(Db.query(
                `DELETE FROM "ticket_discount" WHERE "ticket_discount_id" = $1 AND "event_id" = $2 RETURNING *`, [$discountId, $eventId]
            ));

            if (result.rows.length === 0) {
                throw {
                    validation: 'Discount not found'
                }
            }
        }).then(() => {
            res.ok()
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/event/:event/tickets/camps-list
    campsList: function (req, res) {
        let $eventId = req.params.event,
            $campsOnly = (req.query.camps === 'true');

        (
            ($campsOnly) ?
            Db.query(
                `SELECT
                         ec.event_camp_id "id", ec.name "name", ec.types_order "order", (
                             ARRAY_TO_JSON(ARRAY_AGG((
                                 SELECT ROW_TO_JSON("t") 
                                 FROM ( 
                                     SELECT 
                                         et.event_ticket_id "id", et.label, et.short_label,
                                         TO_CHAR(et.current_price::NUMERIC, '${PG_CURRENCY_FORMAT}') "price"
                                 ) "t"
                             )))
                         ) "types",
                         TO_CHAR(ec.date_start, '(Mon DD)') "date_start"
                     FROM "event_camp" ec
                     INNER JOIN "event_ticket" et 
                         ON et.event_camp_id = ec.event_camp_id
                         AND et.event_id = ec.event_id
                     INNER JOIN "event" e 
                         ON e.event_id = ec.event_id
                     WHERE ec.event_id = $1
                        AND ec."deleted" IS NULL AND ec.visibility IN ('eo', 'published')
                     GROUP BY ec.event_camp_id, ec.name, ec.types_order, ec.date_start
                     ORDER BY ec.event_camp_id`, [$eventId]
            ).then(result => {
                return result.rows;
            }) :
            Db.query(
                `SELECT
                        et.event_ticket_id, et.label "type_label", et.current_price,
                        TO_CHAR(et.current_price::NUMERIC, '${PG_CURRENCY_FORMAT}') price_formatted,
                        ec.event_camp_id, ec.name "camp_name", 
                        COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee, 0) "app_fee"
                    FROM "event_ticket" et
                    INNER JOIN "event_camp" ec
                        ON ec.event_camp_id = et.event_camp_id
                        AND ec.visibility IN ('eo', 'published')
                    INNER JOIN "event" e 
                        ON e.event_id = ec.event_id
                    WHERE et.event_id = $1
                    ORDER BY ec.event_camp_id, ec.types_order DESC`, [$eventId]
            ).then(function (result) {
                return result.rows.map(function (campType) {
                    campType.app_fee = __parse(campType.app_fee);
                    campType.current_price = __parse(campType.current_price);
                    return campType;
                })
            })
        ).then(data => {
            res.status(200).json({
                camps: data
            })
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/event/:event/tickets/
    eventPaymentOptionsDetails: function (req, res) {
        res.ok();
    },
    // post /api/event/:event/tickets/receive-check/:type
    receiveCheck: function (req, res) {
        var $receivedAt = req.body.received_at,
            $checkNum = req.body.check_num,
            $notes = req.body.notes,
            $purchaseId = parseInt(req.body.purchase_id, 10),
            $eventId = parseInt(req.params.event, 10),
            $type = req.params.type,
            receiverPromise,
            matchRes;

        if (!$receivedAt)
            return res.validation('Invalid Received Date');
        if (!$checkNum)
            return res.validation('Invalid Check Num');
        if (!$purchaseId)
            return res.validation('Invalid Purchase Identifier');
        if (!$eventId)
            return res.validation('Invalid Event Identifier');
        if (!$type)
            return res.validation('Invalid Check Receiving Type');

        matchRes = $receivedAt.match(CHECK_DATE_FORMAT);
        if (!(matchRes && matchRes.length)) {
            return res.validation('Invalid Received Date Format');
        }

        if ($type === 'debt') {
            receiverPromise = payOffDebt($eventId, $purchaseId, $receivedAt, $checkNum, $notes, req.user.user_id);
        } else if ($type === 'payment') {
            receiverPromise
                = receiveCheckPayment($eventId, $purchaseId, $receivedAt, $checkNum, $notes, req.user.user_id);
        } else {
            return res.validation('Unrecognized Check Receiving Type')
        }

        receiverPromise.then(function () {
            res.ok()
        }).catch(function (err) {
            res.customRespError(err)
        })
    },
    // get /api/event/:event/tickets/:code/history
    paymentHistory: async function (req, res) {
        try {
            var $event_id = parseInt(req.params.event, 10),
            $code = parseInt(req.params.code, 10);

            if (!$event_id) return res.validation('event id required');
            if (!$code) return res.validation('No ticket barcode provided');

            const history = await PaymentService.tickets.history.getHistory({
                eventID: $event_id,
                ticketBarcode: $code,
            });

            res.json({ history });
        } catch(e) {
            res.customRespError(e);
        }
    },
    // post /api/event/:event/tickets/:code/add-note
    addHistoryNote: function (req, res) {
        var $event_id = parseInt(req.params.event, 10),
            $code = parseInt(req.params.code, 10),
            $notes = req.body.notes;

        if (!$event_id) return res.validation('event id required');
        if (!$code) return res.validation('No ticket barcode provided');
        if (!$notes) return res.validation('Invalid notes passed');

        Db.query(
            `WITH p AS (
                SELECT p.purchase_id
                FROM "purchase" p
                WHERE p.ticket_barcode = $1 
                    AND p.event_id = $2 
                    AND p.payment_for = 'tickets'
            )
            INSERT INTO "purchase_history" (
                "purchase_id", "notes", "action"
            )
            SELECT p.purchase_id, $3 "notes", 'notes.created' "action"
            FROM p`, [$code, $event_id, $notes]
        ).then(function () {
            res.ok();
        }, function (err) {
            res.customRespError(err)
        })
    },
    // post /api/:event/tickets/:code/change-type/:type
    changeTicketType: function (req, res) {
        var $event_id = parseInt(req.params.event, 10),
            $code = parseInt(req.params.code, 10),
            $type = req.params.type,
            $changes = req.body.changes,
            $debt = req.body.debt;

        if (!$event_id) return res.validation('event id required');
        if (!$code) return res.validation('No ticket barcode provided');
        if ($type !== 'camps')
            return res.validation('Invalid Type passed');
        if (_.isEmpty($changes))
            return res.validation('No changes provided');

        changeCampsType($event_id, $code, $changes, req.user.user_id, $debt)
            .then(function () {
                res.ok();
            }, function (err) {
                res.customRespError(err);
            })
    },
    // get /api/event/:event/reports/camps
    campsStatistics: function (req, res) {
        const $eventId = req.params.event;

        campsStatisticsService.getStatistics($eventId).then(function (stats) {
            res.status(200).json(stats)
        }).catch(function (err) {
            res.customRespError(err)
        })
    },
    // post /api/event/:event/ticket/:code/refund-debt
    refundDebt: function (req, res) { // todo: implement tilled
        const
            $eventId = req.params.event,
            $ticketBarcode = req.params.code,
            settingsRow = optimist.argv.prod ? 'stripe_connect' : 'stripe_connect_dev';

        if (!$ticketBarcode)
            return res.validation('Invalid Ticket Barcode');

        Db.query(
            `SELECT 
                 p.stripe_charge_id charge_id, p.debt, p.amount, (
                     CASE
                         WHEN e.tickets_use_connect IS TRUE 
                             THEN (SELECT "value"->>'secret_key' FROM "settings" 
                                                                     WHERE "key" = '${settingsRow}')
                         ELSE COALESCE(
                                 e.stripe_tickets_private_key, 
                                 (SELECT "value"->>'secret_key' 
                                  FROM "settings" 
                                  WHERE "key" = '${settingsRow}')
                             )
                     END
                 ) "private_key", p.purchase_id, p.amount::real,
                ((e.tickets_use_connect IS TRUE) OR (p.stripe_payment_type = 'connect')) "use_connect", (
                    CASE
                        WHEN (e.stripe_tickets_percent > 0)
                            THEN (e.stripe_tickets_percent / 100)
                        ELSE 0
                    END
                 )::NUMERIC "stripe_percent",
                COALESCE(e.stripe_tickets_fixed, 0)::NUMERIC "stripe_fixed"
             FROM purchase p 
             INNER JOIN "event" e 
                 ON e.event_id = p.event_id 
             LEFT JOIN stripe_account sa 
                 ON sa.secret_key = e.stripe_tickets_private_key 
             WHERE p.ticket_barcode = $1 
                 AND p.stripe_charge_id IS NOT NULL 
                 AND p.payment_for = 'tickets' 
                 AND p.type = 'card'
                 AND p.canceled_date IS NULL 
                 AND p.status <> 'canceled' 
                 AND p.event_id = $2 
                 AND p.debt < 0`, [$ticketBarcode, $eventId]
        ).then(function (result) {
            var purchase = _.first(result.rows);

            if (_.isEmpty(purchase)) {
                throw {
                    validation: 'Now suitable purchase found'
                }
            }

            if (!purchase.private_key) {
                throw {
                    validation: 'Stripe Secret Key not found'
                }
            }

            purchase.debt = Math.abs(__parse(purchase.debt));
            purchase.amount = __parse(purchase.amount);

            return purchase;
        }).then(function (purchase) {
            return Db.begin()
                .then(function (tr) {
                    return Promise.all([
                        tr.query(
                            `INSERT INTO "purchase_history" (
                            "purchase_id", "action", "description", "user_id", "amount", "operation_date")
                         VALUES($1, 'debt.refunded', $2, $3, $4, NOW())`, [
                                purchase.purchase_id,
                                `Debt of $${purchase.debt} was refunded`,
                                req.user.user_id || null,
                                purchase.debt
                            ]
                        ), tr.query(
                            'UPDATE "purchase" SET "debt" = 0 WHERE "purchase_id" = $1', [purchase.purchase_id]
                        )
                    ]).then(function () {
                        return tr
                    })
                }).then(function (tr) {
                    return new Promise(function (resolve, reject) {
                        var onChargeRefunded = function (err) {
                            if (err) {
                                reject(err)
                            } else {
                                resolve()
                            }
                        }

                        if (purchase.use_connect) {
                            let oldFee = swUtils.normalizeNumber(
                                (purchase.amount + purchase.debt) -
                                (
                                    (purchase.amount + purchase.debt) *
                                    (1 - __parse(purchase.stripe_percent)) -
                                    __parse(purchase.stripe_fixed)
                                )
                            );
                            let newFee = swUtils.normalizeNumber(
                                purchase.amount -
                                (
                                    purchase.amount * (1 - __parse(purchase.stripe_percent)) -
                                    __parse(purchase.stripe_fixed)
                                )
                            );
                            let stripeFee = swUtils.normalizeNumber(oldFee - newFee)

                            stripeConnect.refundClientPayment({
                                charge_id: purchase.charge_id,
                                amount: purchase.debt,
                                stripe_secret: purchase.private_key,
                                fee: stripeFee
                            }, onChargeRefunded)
                        } else {
                            StripeService.refund({
                                charge_id: purchase.charge_id,
                                amount: purchase.debt,
                                stripe_secret: purchase.private_key
                            }, onChargeRefunded)
                        }
                    }).then(function () {
                        return tr.commit()
                    }).catch(function (err) {
                        if (tr && !tr.isCommited) {
                            tr.rollback();
                        }
                        throw err;
                    })
                })
        }).then(function () {
            res.ok();
        }).catch(function (err) {
            res.customRespError(err);
        })
    },
    // post /api/event/:event/tickets/stripe-statement
    saveStripeStatement: async function (req, res) {
        const eventID = Number(req.params.event);
        const statementDescriptor = req.body.statement;

        if(!statementDescriptor) {
            return res.validation('Statement Descriptor is empty');
        }

        try {
            await TicketsService.updateStripeStatementDescriptor(eventID, statementDescriptor);
            await SalesHubService.sync.syncPOSPaymentProviderAccount(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },
    // put /api/event/:event/tickets/stripe-key
    saveStripeTicketsKey: async function (req, res) {
        const eventID = Number(req.params.event);
        const accountID = req.body.account_id || null;
        let eventOwnerID = req.user.event_owner_id || null;

        if(!eventOwnerID) {
            eventOwnerID = req.user?.shared_events?.[eventID]?.event_owner_id;
        }

        if(!eventOwnerID) {
            return res.validation('Event Owner ID is empty');
        }

        try {
            await TicketsService.updateTicketsStripeAccount(eventID, accountID, eventOwnerID);
            await SalesHubService.sync.syncPOSPaymentProviderAccount(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/pass/:event/:purchase
    getPass: async function (req, res) {
        let eventID     = Number(req.params.event);
        let purchaseID  = Number(req.params.purchase);

        try {
            if (!eventID) {
                return res.status(400).json({ error: 'Event ID required' });
            }

            if (!purchaseID) {
                return res.status(400).json({ error: 'Purchase ID required' });
            }

            let paymentData = await PaymentService.getPaymentDataForQRContent(purchaseID, eventID);

            if (_.isEmpty(paymentData) || paymentData.status === 'canceled') {
                return res.status(404).json({ error: 'Ticket Not Found' });
            }

            let passBuffer = await ApplePassService.getApplePassForTicket(paymentData);

            res.type("application/vnd.apple.pkpass");
            res.setHeader("Content-Disposition", `attachment; filename="${purchaseID}.pkpass"`);
            res.end(passBuffer, 'binary');
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/event/:event/tickets/settings-validate
    validateSettings: async function (req, res) {
        const eventID = req.params.event;

        try {
            const settingsErrors = await TicketsService.validateEventSettings(eventID);

            await SalesHubService.sync.process(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.status(200).json({ errors: settingsErrors });
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/publish
    setTicketsPublished: async function (req, res) {
        const eventID = Number(req.params.event);

        try {
            await TicketsService.publishTickets(eventID, req.body);
            await SalesHubService.sync.syncPointOfSales(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.status(200).json({});
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/ticket/:code/payment/void
    voidPurchase: function (req, res) {
        const eventID           = Number(req.params.event);
        const ticketBarcode     = Number(req.params.code);
        const restoreDiscounts  = req.body.restore;
        const userID            = req.user.user_id;

        return PaymentService.tickets.cancellation.voidPurchase(eventID, ticketBarcode, restoreDiscounts, userID)
            .then(() => res.status(200).json({}))
            .catch(res.customRespError.bind(res));
    },
    // post /api/event/:event/ticket/:code/waitlist/:status
    changeWaitlistStatus: function (req, res) {
        let $eventId = req.params.event,
            $code = Number(req.params.code),
            $status = req.params.status;

        let tr;

        (async () => {
            if (!$code) {
                throw {
                    validation: 'Invalid payment barcode'
                }
            }

            if (WAITLIST_STATUSES.indexOf($status) < 0) {
                throw {
                    validation: 'Invalid status for waitlisted payment'
                }
            }

            tr = await Db.begin();

            let result = await tr.query(
                `WITH "waitlisted_payment" AS (
                    SELECT 
                        p.purchase_id, p.ticket_barcode, p.user_id, p.event_id, (
                            SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("pt")))
                            FROM (
                                SELECT 
                                    ec.name "camp_name", et.label, pt.quantity, 
                                    pt.amount, pt.ticket_price
                                FROM "purchase_ticket" pt 
                                INNER JOIN "event_ticket" et 
                                    ON et.event_ticket_id = pt.event_ticket_id
                                LEFT JOIN "event_camp" ec 
                                    ON ec.event_camp_id = et.event_camp_id
                                where pt.purchase_id = p.purchase_id 
                                    AND pt.canceled IS NULL 
                                    AND pt.quantity > 0
                            ) "pt"
                        ) "items",
                        FORMAT('"%s %s" <%s>', p.first, p.last, p.email) "receiver",
                        e.long_name "event_name",
                        e.email "event_email",
                        e.event_tickets_code "event_barcode"
                    FROM "purchase" p 
                    INNER JOIN "event" e 
                        ON e.event_id = p.event_id
                    WHERE p.type = 'waitlist'
                        AND p.ticket_barcode = $1
                        AND p.event_id = $2
                        AND (p.status <> $3 OR p.status IS NULL)
                ), "update_payment" AS (
                    UPDATE "purchase" p 
                    SET "status" = $3
                    WHERE p.purchase_id = (SELECT purchase_id FROM "waitlisted_payment")
                    RETURNING (SELECT ROW_TO_JSON("waitlisted_payment") "p" FROM "waitlisted_payment")
                )
                SELECT "p" FROM "update_payment"`, [$code, $eventId, $status]
            );

            let purchaseRow = result.rows[0] && result.rows[0].p;

            if (_.isEmpty(purchaseRow)) {
                throw {
                    validation: 'Suitable Payment not found'
                }
            }

            await tr.query(
                `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id") 
                   VALUES ($1, $2, $3, $4)`, [
                    purchaseRow.purchase_id,
                    'purchase.status-changed',
                    WAITLIST_STATUS_DESCRIPTION[$status],
                    req.user.user_id
                ]
            );

            if ($status === 'pending') {
                let invoiceIdentifier = QRUtils.generateHash(purchaseRow, true);

                if (!invoiceIdentifier) {
                    throw {
                        validation: 'Error while generating a payment link'
                    }
                }

                let invoiceLink = `${SWT_LINK}/#/events/${purchaseRow.event_barcode}/invoice/${invoiceIdentifier}`;

                await EmailService.renderAndSend({
                    template: 'tickets/waitlist/pending',
                    data: _.extend({
                        link: invoiceLink
                    }, purchaseRow),
                    from: 'SportWrench <<EMAIL>>',
                    to: purchaseRow.receiver,
                    replyto: `"${purchaseRow.event_email}" <${purchaseRow.event_email}>`,
                    subject: `Camp Opening. Pay now`
                });
            }

            await tr.commit();
        })().then(function () {
            res.ok();
        }).catch(err => {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            res.customRespError(err);
        });
    },
    // post /api/event/:event/tickets/discounts/create
    createDiscount: function (req, res) {
        let $eventId = req.params.event,
            $discount = req.body.discount;

        let validationResult = discountCreateSchema.validate($discount);

        if (validationResult.error) {
            return res.customRespError({
                validationErrors: validationResult.error.details
            });
        }

        co(function* () {
            yield(Promise.all([
                checkCouponCodeUniqueness($eventId, $discount.code),
                checkDiscountLastUniqueness($eventId, $discount.last),
                checkDiscountEmailUniqueness($eventId, $discount.email)
            ]));

            yield(Db.query(
                `INSERT INTO "ticket_discount" (  
                     "email", "first", "last", "event_id", "event_ticket_id", "max_count", "discount", "code"
                 ) 
                 SELECT LOWER(TRIM(($1)::TEXT)), LOWER(TRIM(($2)::TEXT)), LOWER(TRIM(($3)::TEXT)), $4, $5, $6, $7, $8 
                 RETURNING "ticket_discount_id"`, [
                    $discount.email,
                    $discount.first,
                    $discount.last,
                    $eventId,
                    $discount.ticket_id,
                    $discount.max_count,
                    $discount.amount,
                    $discount.code
                ]
            ));
        }).then(() => {
            res.ok();
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // put /api/event/:event/tickets/checkin
    checkin: function(req,res) {
        const ACTION = {
            created: 'camp.checkin.created',
            canceled: 'camp.ckeckin.canceled'
        };

        const DESCR = {
            done: 'Checkin: Done: ',
            canceled: 'Ckeckin: Canceled: '
        };

        let $eventId = req.params.event;

        if (!$eventId) return res.validation('Invalid event id specified');

        let params = req.body.params;

        if (!params) return res.validation('No checkin params specified');

        let purchase_ticket_id = parseInt(params.purchase_ticket_id, 10);
        let checked = parseInt(params.checked, 10);

        let sqlCheckin = `
                UPDATE purchase_ticket
                SET available = (CASE WHEN $2 = 1 THEN 0 ELSE quantity END)
                FROM purchase 
                WHERE purchase.purchase_id = purchase_ticket.purchase_id
                      AND canceled IS NULL 
                      AND purchase.status = 'paid' 
                      AND purchase_ticket_id = $1
                RETURNING purchase_ticket_id
                `;
        let sqlHistory = `
                INSERT INTO purchase_history (purchase_id, action, description,user_id)
                SELECT p.purchase_id,
                        CASE 
                            WHEN $2 = 1 THEN '${ACTION.created}'
                            ELSE             '${ACTION.canceled}'
                        END,
                        CASE 
                            WHEN $2 = 1 THEN '${DESCR.done}'   || ec.name || ' - ' || et.label
                            ELSE             '${DESCR.canceled}' || ec.name || ' - ' || et.label
                        END,
                        $3
                FROM purchase p JOIN purchase_ticket pt ON p.purchase_id = pt.purchase_id
                                JOIN event_ticket et on et.event_ticket_id = pt.event_ticket_id
                                JOIN event_camp ec on ec.event_id = p.event_id and et.event_camp_id = ec.event_camp_id    
                WHERE purchase_ticket_id = $1
                `;

        Db.begin()
            .then(tr => {
                return Promise.all([
                    tr.query(sqlCheckin, [purchase_ticket_id, checked]),
                    tr.query(sqlHistory, [purchase_ticket_id, checked, req.user.user_id]),
                ])
                .then((results) => {
                    if (results[0].rowCount === 0) {
                        throw new Error('Error checkining');
                    } else {
                        return tr.commit();
                    }
                })
                .catch(() => {
                    if (tr && !tr.isCommited) {
                        tr.rollback();
                    }
                    throw new Error('Error checkining');
                })
            }).then(() => {
                res.ok();
            }).catch(err => {
                res.customRespError(err);
            });

    },

    //    get /api/event/:event/tickets/checkininfo?purchaseid=
    getTicketsCheckinInfo: function(req, res) {
        let $eventId = req.params.event;

        if (!$eventId) return res.validation('Invalid event id specified');

        let $purchaseId = parseInt(req.query.purchaseid, 10);

        if (!$purchaseId) return res.validation('Invalid purchase id specified');

        let sql = `
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(w))) as "tickets"
                FROM
                ( 
                SELECT pt.purchase_ticket_id, ec.name as "camp_name", 
                        et.label, p.status as "purchase_status", pt.quantity, pt.available, pt.canceled,
                        CASE 
                            WHEN pt.quantity = pt.available AND pt.available <> 0 THEN false
                            ELSE true
                        END as "checkVal",
                        CASE 
                            WHEN p.status = 'paid' 
                                 AND pt.registration_status = $2 
                                 AND pt.quantity > 0 
                                 AND pt.canceled IS NULL THEN true
                            ELSE false
                        END as "canBeChecked"
                FROM purchase p join purchase_ticket pt ON p.purchase_id = pt.purchase_id
                    JOIN event_ticket et ON et.event_ticket_id = pt.event_ticket_id
                    JOIN event_camp ec 
                        ON ec.event_id = p.event_id 
                        AND et.event_camp_id = ec.event_camp_id 
                        AND ec.visibility IN ('eo', 'published')
                WHERE p.purchase_id = $1
                ORDER by pt.purchase_ticket_id
                ) w
            `;

            Db.query(sql, [$purchaseId, PaymentService.tickets.participation.REGISTRATION_STATUS.ACTIVE])
                .then(result => {
                    res.status(200).json({
                        tickets : result.rows[0].tickets
                    })
                })
                .catch(err => {
                    res.customRespError(err);
                });
    }
};


function changeCampsType(eventId, barcode, changes, userId, debt) {
    let changesKeys = Object.keys(changes),
        purchaseTypes = changesKeys.filter(function (id) {
            return Number(id);
        })
    if (!purchaseTypes.length || purchaseTypes.length !== changesKeys.length) {
        return Promise.reject({
            validation: 'Invalid Purchase Types passed'
        })
    }
    return Db.query(
        `SELECT * FROM (
            SELECT p.purchase_id, (
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t)))
                FROM (
                    SELECT 
                        pt.purchase_ticket_id, 
                        COALESCE(et.current_price, et.initial_price, 0) "price", 
                        pt.ticket_price
                    FROM "purchase_ticket" pt
                    INNER JOIN "event_ticket" et 
                        ON et.event_ticket_id = pt.event_ticket_id
                    WHERE pt.purchase_id = p.purchase_id
                        AND pt.quantity > 0
                        AND pt.purchase_ticket_id IN (${purchaseTypes.join(', ')})
                ) "t"
            ) "types"
            FROM "purchase" p
            WHERE p.ticket_barcode = $1 
                AND p.event_id = $2 
                AND p.payment_for = 'tickets'
                and p.status <> 'canceled'
        ) "p"
        WHERE p.types IS NOT NULL`, [barcode, eventId]
    ).then(function (result) {
        var purchase = _.first(result.rows);
        if (_.isEmpty(purchase)) {
            throw {
                validation: 'No suitable purchase found'
            }
        }
        return purchase;
    }).then(function (purchase) {
        return Db.begin()
            .then(function (tr) {
                return Promise.all(
                        purchaseTypes.map(purchaseType => {
                            return purchaseCampChange(tr, purchase.purchase_id, purchaseType, changes[purchaseType], userId)
                        })
                    )
                    .then(function () {
                        if (_.isNumber(debt)) {
                            return tr.query(
                                `UPDATE "purchase" SET "debt" = $2, "amount" = ("amount" + $2) WHERE "purchase_id" = $1`, [purchase.purchase_id, debt]
                            )
                        }
                    })
                    .then(function () {
                        return tr.commit();
                    }, function (err) {
                        if (tr && !tr.isCommited) {
                            tr.rollback();
                        }
                        throw err;
                    })
            })
    })
}

function purchaseCampChange(tr, purchaseId, purchaseTicketId, eventTicketId, userId) {
    return tr.query(
        `WITH "old_type" AS (
             SELECT
                FORMAT('"%s" - %s', ec.name, et.label) "name",
                pt.amount
             FROM "purchase_ticket" pt
             INNER JOIN "event_ticket" et
                 ON et.event_ticket_id = pt.event_ticket_id
             INNER JOIN "event_camp" ec 
                 ON ec.event_camp_id = et.event_camp_id
             WHERE pt.purchase_ticket_id = $2
                 AND pt.purchase_id = $1
         )
         UPDATE "purchase_ticket" pt 
         SET "event_ticket_id"  = d.event_ticket_id,
             "amount"           = (d."price" * pt."quantity"),
             "ticket_price"     = d."price" 
         FROM (
             SELECT 
                 et.event_ticket_id, 
                 COALESCE(et.current_price, et.initial_price, 0) "price", 
                 FORMAT('"%s" - %s', ec.name, et.label) "name"
             FROM "event_ticket" et
             INNER JOIN "event_camp" ec 
                 ON ec.event_camp_id = et.event_camp_id
             WHERE et.event_ticket_id = $3
         ) "d"
         WHERE pt.purchase_id = $1
             AND pt.purchase_ticket_id = $2
             AND pt.event_ticket_id <> d.event_ticket_id
             AND EXISTS (SELECT * FROM "old_type")
         RETURNING
            d.name "new_type", pt.amount, 
            (SELECT "name" FROM "old_type") "old_type", 
            (SELECT "amount" FROM "old_type") "old_amount"`, [purchaseId, parseInt(purchaseTicketId, 10), eventTicketId]
    ).then(function (result) {
        var purchaseTicket = _.first(result.rows);

        if (_.isEmpty(purchaseTicket)) {
            throw {
                validation: 'Not equivalent Types Change'
            }
        }
        return purchaseTicket;
    }).then(function (changeData) {
        return tr.query(
            `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id")
             VALUES($1, 'ticket-type.change', $2, $3)`, [
                purchaseId,
                `Camp Type changed from ${changeData.old_type} to ${changeData.new_type}`,
                userId
            ]
        );
    })
}

function payOffDebt(eventId, purchaseId, receivedAt, checkNum, notes, userId) {
    return Db.query(
        `SELECT p.purchase_id, p.debt
         FROM "purchase" p 
         WHERE p.purchase_id = $1
            AND p.event_id = $2
            AND p.debt > 0
            AND p.payment_for = 'tickets'`, [purchaseId, eventId]
    ).then(function (result) {
        var purchase = _.first(result.rows);
        if (_.isEmpty(purchase)) {
            throw {
                validation: 'No Purchase found'
            }
        }
        return purchase;
    }).then(function (purchase) {
        return Db.begin()
            .then(function (tr) {
                return Promise.all([
                    tr.query(
                        `INSERT INTO "purchase_history" 
                                        ("purchase_id", "action", "description", "notes", "user_id", "amount",
                                                                                        "operation_date", "check_num") 
                     VALUES ($1, 'debt.paid', $2, $3, $4, $5, $6, $7) `, [
                            purchase.purchase_id,
                            `Purchaser's Debt of $${purchase.debt} was payed by check #${checkNum} at ${receivedAt}`,
                            notes || null,
                            userId,
                            purchase.debt,
                            receivedAt,
                            checkNum
                        ]
                    ),
                    tr.query(
                        `UPDATE "purchase" SET debt = 0 WHERE "purchase_id" = $1`, [purchase.purchase_id]
                    )
                ]).then(function () {
                    return tr.commit();
                })
            })
    })
}

function receiveCheckPayment(eventId, purchaseId, receivedAt, checkNum, notes, userId) {
    return Db.query(
        `SELECT p.purchase_id, p.amount
         FROM "purchase" p 
         WHERE p.purchase_id = $1
            AND p.event_id = $2
            AND p.payment_for = 'tickets'
            AND p.check_num IS NULL 
            AND p.received_date IS NULL`, [purchaseId, eventId]
    ).then(function (result) {
        var purchase = _.first(result.rows);
        if (_.isEmpty(purchase)) {
            throw {
                validation: 'No Purchase found'
            }
        }
        return purchase;
    }).then(function (purchase) {
        return Db.begin()
            .then(function (tr) {
                return Promise.all([
                    tr.query(
                        `INSERT INTO "purchase_history" 
                                    ("purchase_id", "action", "description", "notes", "user_id", "amount", 
                                                                                        "operation_date", "check_num") 
                     VALUES ($1, 'check.received', $2, $3, $4, $5, $6, $7) `, [
                            purchase.purchase_id,
                            `Check received #${checkNum} at ${receivedAt}`,
                            notes || null,
                            userId,
                            purchase.amount,
                            receivedAt,
                            checkNum
                        ]
                    ),
                    tr.query(
                        `UPDATE "purchase" SET "check_num" = $1, "received_date" = $2, "status" = 'paid'
                     WHERE "purchase_id" = $3`, [
                            checkNum,
                            receivedAt,
                            purchase.purchase_id
                        ]
                    )
                ]).then(function () {
                    return tr.commit();
                })
            })
    })
}

function getEventTicketOptions(event_id, cb) {
    return Db.query(
        squel.select().from('event')
        .where('event_id = ?', event_id)
        .field('COALESCE(tickets_options, \'{}\'::json)', 'tickets_options')
    ).then(result => {
        let event = result.rows[0];
        if (_.isEmpty(event)) {
            cb({
                validation: 'Event not found'
            });
        } else {
            cb(null, event.tickets_options);
        }
    }).catch(err => {
        cb(err);
    });
}

function updateTicketOptions(event_id, options, cb) {
    Db.query(
        squel.update().table('event')
        .set('tickets_options', options)
        .where('event_id = ?', event_id)
    ).then(() => {
        cb();
    }).catch(err => {
        cb(err);
    });
}

async function updateInitialDates(eventID, dateStart, dateEnd) {
    const query = knex('event')
        .update({
            tickets_purchase_date_start: knex.raw(`(to_timestamp(?, 'MM/DD/YYYY HH24:MI'))`, [dateStart]),
            tickets_purchase_date_end:  knex.raw(`(to_timestamp(?, 'MM/DD/YYYY HH24:MI'))`, [dateEnd])
        })
        .where('event_id', eventID);

    const { rowCount } = await Db.query(query);

    if(!rowCount) {
        throw { validation: 'Event not found' };
    }
}

async function generatePaymentsQuery(event_id, params, aliases) {
    let [[ttResult], customFields] = await Promise.all([
        getEventTicketTypes(event_id),
        getEventCustomFields(event_id)
    ]);

    let ticketTypes     = ttResult.tickets,
        tz              = ttResult.timezone,
        is_camps        = ttResult.is_camps;

    let _aliases = _.isEmpty(aliases) ? {} : aliases;

    let query = squel.select().from('purchase', 'p')
        .field(`to_char(p.ticket_barcode, '999-999-999')`           , `"${_aliases.barcode     || 'ticket_barcode'}"`)
        .field(`p.amount::NUMERIC`                                  , `"${_aliases.amount      || 'amount'}"`)
        .field(`p.first`                                            , `"${_aliases.first       || 'first'}"`)
        .field(`p.last`                                             , `"${_aliases.last        || 'last'}"`)
        .field(`p.email`                                            , `"${_aliases.email       || 'email'}"`)
        .field(`p.zip`                                              , `"${_aliases.zip         || 'zip'}"`)
        .field(`p.phone`                                            , `"${_aliases.phone       || 'phone'}"`)
        .field(`p.status`                                           , `"${_aliases.status      || 'status'}"`)
        .field(`p.scanner_id`                                       , `"${_aliases.scanner_id  || 'scanner_id'}"`)
        .field(`p.scanner_location`                            , `"${_aliases.scanner_location || 'scanner_location'}"`)
        .join('event', 'e', 'e.event_id = p.event_id')
        .left_join('purchase', 'p2', 'p.linked_purchase_id = p2.purchase_id')
        .where('p.event_id = ?', event_id)
        .where(`p.payment_for = 'tickets'`)
        .where('p.is_ticket IS TRUE');

    if (!_.isEmpty(params)) {
        SQLQueryBuilder.ticketsCustomer._addFiltersToQuery(query, params, is_camps);
    }

    if(is_camps) {
        query
            .join('purchase_ticket', 'pt'  , `pt.purchase_id = p.purchase_id`)
            .join('event_ticket'   , 'et'  , 'et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id')
    }

    if (ticketTypes && ticketTypes.length) {
        ticketTypes.forEach((tt, index) => {
            let alias = `pt${index}`;

            query
                .left_join(
                    'purchase_ticket',
                    `${alias}`,
                    `"${alias}".purchase_id = p.purchase_id AND "${alias}".event_ticket_id = ${tt.event_ticket_id}`
                )
                .field(`COALESCE("${alias}".quantity, 0)`, `"${(is_camps)?tt.short_label:tt.label}"`)

            if (params.unclaimed) {
                query.group(`"${alias}".quantity`);
            }
        })
    }

    if (customFields && customFields.length) {
        customFields.forEach(cf => {
            // add USAV age column
            if(cf.field === 'birthdate') {

                let agesBlockSubquery = squel.select().from('v_swt_participant_age', 'v_spa')
                    .field('v_spa.age')
                    .where('v_spa.purchase_id = p.purchase_id')
                    .where(`v_spa.age_type = 'camp'`)
                    .order('v_spa.age');

                let agesSelectSubquery = squel.select()
                    .field(`STRING_AGG(DISTINCT ages_block.age::TEXT, ', ')`)
                    .from(agesBlockSubquery, 'ages_block');

                if (params.type) {
                    let ticketTypeSubquery = squel.select().from('event_ticket', 'et')
                        .where(`et.event_ticket_id = ?`, params.type)
                        .field('et.event_camp_id');

                    agesBlockSubquery.where(`v_spa.event_camp_id IN (${ticketTypeSubquery.toString()})`);
                } else if (params.camp) {
                    agesBlockSubquery.where('v_spa.event_camp_id = ?', params.camp);
                }

                query.field(agesSelectSubquery, "Ages");
            }

            let formattedLabel = swUtils.escapeStr(cf.label, { duplicateDoubleQuote: true });
            query.field(`p.tickets_additional->>'${cf.field}'`, `"${formattedLabel}"`);
        })
    }

    if(tz) {
        query
            .field(
                `TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE '${tz}'), 'Mon DD, YYYY')`,
                `"${(_aliases.purchased_date || 'purchased date')}"`
            )
            .field(
                `TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE '${tz}'), 'HH12:MI AM')`,
                `"${(_aliases.purchased_time || 'purchased time')}"`
            )
            .field(
                `TO_CHAR((p.scanned_at::TIMESTAMPTZ AT TIME ZONE '${tz}'), 'Mon DD, YYYY')`,
                `"${(_aliases.scanned_at_date || 'scanned_at date')}"`
            )
            .field(
                `TO_CHAR((p.scanned_at::TIMESTAMPTZ AT TIME ZONE '${tz}'), 'HH12:MI AM')`,
                `"${(_aliases.scanned_at_time || 'scanned_at time')}"`
            )
    }

    return query.toString();
}

function __parse(n) {
    return (parseFloat(n) || 0);
}

function getEventTicketTypes (eventID) {
    return Db.query(
        `SELECT  
                (SELECT ARRAY_TO_JSON(ARRAY_AGG(t)) 
                 FROM ( 
                    SELECT et.label, et.short_label, et.event_ticket_id 
                    FROM event_ticket et 
                    WHERE et.event_id = e.event_id 
                 ) t 
                ) tickets, 
                e.timezone, 
                e.ticket_camps_registration "is_camps" 
            FROM "event" e 
            WHERE e.event_id = $1`, [eventID]
    ).then(result => result.rows);
}

function getEventCustomFields (eventID) {
    return Db.query(
        `SELECT d.f->>'field' "field", d.f->>'label' "label" 
             FROM ( 
                 SELECT  
                     JSONB_ARRAY_ELEMENTS(e.tickets_purchase_additional_fields) f, e.event_id 
                 FROM "event" e 
                 WHERE e.event_id = $1 
             ) d`, [eventID]
    ).then(result => result.rows);
}

function checkCouponCodeUniqueness(eventId, coupon, discountId) {
    let params = [eventId, coupon],
        sql =
        `SELECT td.ticket_discount_id 
         FROM "ticket_discount" td
         WHERE td.event_id = $1 
            AND LOWER(TRIM(td.code)) = LOWER(TRIM($2))`;

    if (discountId) {
        params.push(discountId);
        sql += ' AND td.ticket_discount_id <> $3';
    }

    return Db.query(sql, params).then(function (result) {
        if (result.rowCount > 0) {
            throw {
                validation: 'Specified Coupon Code already exists'
            }
        }
    });
}

function checkDiscountLastUniqueness(eventId, lastName, discountId) {
    let params = [eventId, lastName],
        sql =
        `SELECT 
             td.ticket_discount_id
         FROM "ticket_discount" td 
         WHERE td.event_id = $1
             AND LOWER(TRIM(td.last)) = LOWER(TRIM($2))`;

    if (discountId) {
        params.push(discountId);
        sql += ' AND td.ticket_discount_id <> $3';
    }

    return Db.query(sql, params).then(function (result) {
        if (result.rowCount > 0) {
            throw {
                validation: 'Discount for specified Last Name already exists'
            }
        }
    });
}

function checkDiscountEmailUniqueness(eventId, email, discountId) {
    let params = [eventId, email],
        sql =
        `SELECT 
             td.ticket_discount_id
         FROM "ticket_discount" td 
         WHERE td.event_id = $1
             AND LOWER(TRIM(td.email)) = LOWER(TRIM($2))`;

    if (discountId) {
        params.push(discountId);
        sql += ' AND td.ticket_discount_id <> $3';
    }

    return Db.query(sql, params).then(function (result) {
        if (result.rowCount > 0) {
            throw {
                validation: 'Discount for specified email already exists'
            }
        }
    });
}

function findPurchaserInfo(res, payment, req, $code) {
    const query = squel.select()
        .from('purchase', 'p')
        .field('p.first')
        .field('p.last')
        .field('p.card_name')
        .field('p.email')
        .field('p.phone')
        .field(`
            CASE
              WHEN
                kiosk IS NOT NULL
                AND kiosk <> '{}'::JSONB
                AND status = 'paid'
                THEN TRUE
              ELSE FALSE
            END
        `, 'is_pending_payment')
        .field(`
            CASE
              WHEN
                kiosk IS NOT NULL
                AND kiosk <> '{}'::JSONB
                THEN TRUE
              ELSE FALSE
            END
        `, 'is_kiosk_payment')
        .field(`json_build_object(
            'charge_id', p.stripe_charge_id,
            'card_id', p.stripe_card_id,
            'fingerprint', p.stripe_card_fingerprint
        )`, 'stripe_info')
        .where('p.purchase_id = ?', payment.linked_purchase_id || payment.purchase_id);

    Db.query(query).then(response => {
        const purchaserInfo = _.first(response.rows);
        const omitFields = ['user_id'];

        if (purchaserInfo.is_pending_payment) {
            generateHash(payment, req, $code);
        }

        Object.assign(payment, { purchaser_info:  purchaserInfo });

        if (!needSendFees(payment)) {
            omitFields.push(...['collected_sw_fee', 'stripe_fee']);
        }

        res.status(200).json({
            payment: _.omit(payment, omitFields)
        });
    })
}

function generateHash(payment, req, $code) {
    payment.receipt_hash = QRTIcketsUtils.generateHash({
        ticket_barcode: $code,
        purchase_id: payment.purchase_id,
        user_id: payment.user_id,
        event_id: payment.event_id
    }, true);
    payment.typeChangeLink = SWTReceiptService.getTypeChangeLink(
        req, payment.event_tickets_code, payment.receipt_hash);
    delete payment.event_tickets_code;
}

function _getEditableFields (editableAdditionalFields) {
    let { edit_fields, all_additional_fields } = editableAdditionalFields;
    let inline_editing = {};

    if(!edit_fields || !edit_fields.length || !all_additional_fields || !all_additional_fields.length) {
        return {};
    }

    all_additional_fields.forEach(item => {
        if(edit_fields.includes(item.field)) {
            let field = { type: item.type };

            if(item.options) {
                field.options = item.options;
            }

            inline_editing[item.field] = field;
        }
    });

    return inline_editing;
}

function _formatAdditionalFieldsObj (all_additional_fields) {
    return all_additional_fields.reduce((fields, item) => {
        fields[item.field] = item;
        return fields;
    }, {})
}

function needSendFees({ sw_fee_payer, stripe_fee_payer, assigned_tickets_mode }) {
    const rules = [
        sw_fee_payer === FEE_PAYER.BUYER,
        stripe_fee_payer === FEE_PAYER.BUYER,
        !assigned_tickets_mode
    ];

    return rules.every(rule => rule);
}
