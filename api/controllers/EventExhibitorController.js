const { exhibitorRegistrationInfoSchema } = require("../validation-schemas/exhibitor");
const {RECEIVER_TYPES} = require("../constants/common");

module.exports = {
    // GET api/event/:event/exhibitors
    getExhibitors: async function(req, res) {
        const eventID = Number(req.params.event);

        if (!eventID) {
            return res.validation('Invalid Event Identifier');
        }

        try {
            const exhibitors = await EventExhibitorService.getExhibitors(eventID);

            res.json(exhibitors);
        } catch (e) {
            res.customRespError(e);
        }
    },

    // GET api/event/:event/exhibitor/:exhibitor/info
    getExhibitorProfileInfo: async function(req, res) {
        try {
            const exhibitorID = Number(req.params.exhibitor);

            if (!exhibitorID) {
                throw { validation: 'Invalid Exhibitor Identifier' };
            }

            const info = await EventExhibitorService.getExhibitorProfileInfo(exhibitorID);

            res.json(info);
        } catch (e) {
            res.customRespError(e);
        }
    },

    // GET api/event/:event/exhibitor/:exhibitor
    getExhibitorRegistrationInfo: async function(req, res) {
        const eventId = Number(req.params.event);
        const exhibitorId = Number(req.params.exhibitor);

        if (!eventId) {
            return res.validation('Invalid Event Identifier');
        }

        if (!exhibitorId) {
            return res.validation('Invalid Exhibitor Identifier');
        }

        try {
            const registrationInfo = await EventExhibitorService.getExhibitorRegistrationInfo(eventId, exhibitorId);

            res.json(registrationInfo);
        } catch (e) {
            res.customRespError(e);
        }
    },

    // PUT api/event/:event/exhibitor/:exhibitor
    updateExhibitorRegistrationInfo: async function(req, res) {
        const eventId = Number(req.params.event);
        const exhibitorId = Number(req.params.exhibitor);
        const registrationInfo = req.body;

        if (!eventId) {
            return res.validation('Invalid Event Identifier');
        }

        if (!exhibitorId) {
            return res.validation('Invalid Exhibitor Identifier');
        }

        const { error } = exhibitorRegistrationInfoSchema.validate(registrationInfo);

        if (error) {
            return res.validation(error.details[0].message);
        }

        try {
            const statusChanged = await EventExhibitorService.updateRegistrationInfo(eventId, exhibitorId, registrationInfo);

            if(statusChanged) {
                await SponsorService.notifications.sendStatusChangeNotification(
                    eventId, exhibitorId, registrationInfo.status
                );
            }

            res.ok();
        } catch (e) {
            res.customRespError(e);
        }
    },

    // POST api/event/:event/exhibitor/:exhibitor
    createExhibitorRegistrationInfo: async function(req, res) {
        const eventId = Number(req.params.event);
        const exhibitorId = Number(req.params.exhibitor);
        const registrationInfo = req.body;

        if (!eventId) {
            return res.validation('Invalid Event Identifier');
        }

        if (!exhibitorId) {
            return res.validation('Invalid Exhibitor Identifier');
        }

        const userAccessIds = {
            sales_manager_id: Number(req.session.passport.user.sales_manager_id),
            event_owner_id: Number(req.session.passport.user.event_owner_id),
            user_id: Number(req.session.passport.user.user_id),
            sponsor_id: exhibitorId
        };

        try {
            await EventExhibitorService.createRegistrationInfo(eventId, exhibitorId, userAccessIds, registrationInfo);

            await SponsorService.notifications.sendAppliedNotification(
                eventId, exhibitorId
            );

            if(registrationInfo.status && registrationInfo.status !== EventExhibitorService.APPLICATION_STATUS.PENDING) {
                await SponsorService.notifications.sendStatusChangeNotification(
                    eventId, exhibitorId, registrationInfo.status
                );
            }

            res.ok();
        } catch (e) {
            res.customRespError(e);
        }
    },

    // POST /api/event/:event/exhibitor
    createExhibitor: async function(req, res) {
        let tr = await Db.begin();

        try {
            const salesManagerID = Number(req.user.sales_manager_id);
            const userID = Number(req.user.user_id);
            const eventID = Number(req.params.event);

            let exhibitorData = req.body;
            let creatorData = _.pick(req.user, ['first', 'last', 'email']);

            exhibitorData.email = exhibitorData.email.toLowerCase();

            const isExhibitorAlreadyRegisteredAtEvent =  await SponsorService.profile.findRegisteredExhibitor({ email: exhibitorData.email, eventID });

            if (isExhibitorAlreadyRegisteredAtEvent) {
                throw {
                    message: 'This Exhibitor has already been added to current event.',
                    type: 'exhibitor_added',
                }
            }

            const { exhibitor_id, company_name } = await SponsorService.profile.findExhibitorFromList({
                email: exhibitorData.email,
                addedByUserId: userID,
                addedBySalesId: salesManagerID
            });

            if (exhibitor_id) {
                throw {
                    message: 'You have already created this exhibitor. Add it to the event?',
                    type: 'exhibitor_in_list',
                    exhibitor_id,
                    company_name,
                }
            }
            
            const exhibitorUserProfile = await SponsorService.profile.findExhibitor(exhibitorData.email);

            const sponsorCreationData = {
                data: _.omit(exhibitorData, ['gender']),
                addedByUserId: userID,
                addedBySalesId: salesManagerID,
                tr: tr,
            }

            if (exhibitorUserProfile === null) {
                const userProfile = await SponsorService.profile.createUserProfile({ data: exhibitorData, tr: tr});

                await SponsorService.profile.create({ ...sponsorCreationData, userProfileId: userProfile.user_id });
                
                await SponsorService.profile.sendEmail({
                    exhibitorData: exhibitorData,
                    creatorData: creatorData,
                    password: userProfile.password,
                });
            }else{
                await SponsorService.profile.create({ ...sponsorCreationData, userProfileId: exhibitorUserProfile.user_id });
            }
            

            await tr.commit();

            res.ok();
        } catch (e) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }
            const statusCode = e.type ? 400 : 500;
            res.status(statusCode).json(e);
        }
    },

    // GET /api/event/:event/exhibitors/list
    getExhibitorsList: async function(req, res) {
        try {
            const userID = Number(req.user.user_id);
            const salesManagerID = Number(req.user.sales_manager_id);
            const eventID = Number(req.params.event);

            const exhibitors = await SponsorService.profile.getExhibitors(userID, salesManagerID, eventID);

            res.json(exhibitors);
        } catch (e) {
            res.customRespError(e);
        }
    },

    // GET /api/event/:event/exhibitors/tickets
    tickets: async function (req, res) {
        const eventID = Number(req.params.event),
            additionalFields = ['birthdate'],
            filters = PaymentService.tickets.list.extractFiltersFromQuery(req.query),
            hidePayments = Boolean(filters.camp || filters.type),
            receiverType = RECEIVER_TYPES.EXHIBITORS;

        let total_rows= 0;
        filters.event_id = eventID;

        try {
            let [payments, ticket_types, column_show] = await Promise.all([
                PaymentService.tickets.list.getTicketsList(filters, filters.is_camp, hidePayments, receiverType),
                PaymentService.tickets.list.getTicketTypes(hidePayments, eventID),
                //checking columns visibility for the client
                CustomizeTicketShowService.isVisible(eventID, additionalFields),
            ]);

            if (payments.length) {
                total_rows = payments[0].total_rows;

                payments.forEach(payment => {
                    payment.total_rows = undefined;
                    // clean payment if column is hidden for client
                    for(let key of Object.keys(_.omit(column_show,'status','type'))) {
                        if (! column_show[key] ) payment[key] = undefined;
                    }
                })
            }

            res.status(200).json({
                payments,
                ticket_types,
                total_rows,
                column_show,
            });
        } catch (err) {
            res.customRespError(err);
        }
    },
}
