'use strict';

const moment = require('moment');
const createSchema = require('json-gate').createSchema;
require('date-utils');

function buildStaffersHTMLData (staffers) {
    return staffers.map(staffer => {

        staffer.contacts            = _buildContactsHTML(staffer);
        staffer.minorTeamsHtml      = _buildTeamsListHtml(staffer);
        staffer.minorTeamsCount     = (staffer.primary_team)
                                        ?((staffer.teams.length || 1) - 1)
                                        :staffer.teams.length

        return staffer;
    })
};

module.exports = {
    // get /api/master_staff
    index: function(req, res) {
        var master_club_id = parseInt(req.session.passport.user.master_club_id, 10),
            $currentSeason = sails.config.sw_season.current;

        if(!master_club_id) return res.status(403).json({error: 'Access denied.'});

        async.parallel({                
            queue: function (done) {
                Promise.all([
                    AauMemberService.import.queue.getClubImport(master_club_id),
                    SportEngineMemberService.import.queue.getClubImport(master_club_id)
                ]).then(([aauDate, seDate]) => {
                    const createdDate = aauDate || seDate;

                    if(createdDate) {
                        return done(null, { queue_data: { requested: createdDate } });
                    }

                    return done(null, null);
                }).catch(err => done(err));
            },
                staff: function (done) {
                    var query = 
                        `SELECT staff.*, (
                            CASE  
                                WHEN ((teams#>'{0}'->>'primary')::BOOLEAN IS TRUE)  
                                THEN teams#>'{0}'  
                                ELSE NULL  
                             END 
                          ) primary_team 
                         FROM ( 
                            SELECT 
                               ms.gender, ms.organization_code, ms.first, ms.last,  
                               ms.email, ms.phone phonem, ms.phoneh, ms.city, ms.state, ms.zip,  
                               ms.master_staff_id, ms.usav_number, ms.aau_membership_id,
                               (
                                 CASE 
                                   WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                                   WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                                   ELSE NULL
                                 END  
                               ) cert, ( 
                                 CASE 
                                   WHEN (ms.safesport_statusid = '2') THEN 'OK' 
                                   ELSE ''
                                 END 
                               ) safesport_statusid, (
                                 CASE                                 
                                    WHEN (
                                        CASE
                                            WHEN ms.bg_screening = 'foreign' THEN FALSE
                                            WHEN ms.bg_expire_date IS NOT NULL
                                                THEN (
                                                        ms.bg_expire_date::DATE <= CURRENT_DATE 
                                                        OR ms.bg_screening <> '2' 
                                                        OR ms.bg_screening IS NULL
                                                      )
                                            WHEN ms.bg_expire_date IS NULL
                                                THEN (
                                                    ms.bg_screening <> '2' 
                                                    OR ms.bg_screening IS NULL 
                                                )
                                        END
                                        ) IS FALSE THEN 'YES'
                                    ELSE 'NO'
                                 END
                               ) "bkg", ( 
                                   SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))), '[]'::JSON) 
                                   FROM ( 
                                     SELECT mt.team_name, mt.organization_code,  
                                            mt.master_team_id, msr.primary, 
                                            r.name role_name 
                                     FROM master_staff_role msr  
                                     INNER JOIN master_team mt  
                                        ON mt.master_team_id = msr.master_team_id  
                                        and mt.season = $2 
                                     LEFT JOIN "role" r 
                                       ON r.role_id = msr.role_id 
                                     WHERE msr.master_staff_id = ms.master_staff_id 
                                     AND mt.deleted IS NULL 
                                     ORDER BY msr.primary DESC NULLS LAST, mt.organization_code DESC  
                                   ) t 
                               ) teams 
                            FROM master_staff ms  
                            WHERE ms.master_club_id = $1  
                                 AND ms.deleted IS NULL  
                                 and ms.season = $2 
                        ) staff`;

                    Db.query(query, [master_club_id, $currentSeason])
                    .then(result => {
                        done(null, result.rows);
                    }).catch(err => {
                        done(err);
                    });
                },
                certifications: function (done) {
                    Db.query(
                        `SELECT ARRAY_AGG(DISTINCT (
                            CASE 
                              WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                              WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                              ELSE NULL
                            END  
                         )) certifications 
                        FROM master_staff ms 
                        WHERE ms.master_club_id = $1 
                        AND ms.season = $2 
                        AND ms.deleted is NULL`,
                        [master_club_id, $currentSeason]
                    ).then(result => {
                        let row     = result.rows[0],
                            certs   = row && row.certifications || [];
                        done(null, certs);
                    }).catch(err => {
                        done(err);
                    });
                }
            },
            function (err, data) {
                if(err) return res.serverError();
                if (_.isEmpty(data.queue)) {

                    data.staff = buildStaffersHTMLData(data.staff);
                    
                    return res.status(200).json({ staff: data.staff, certs: data.certifications });
                } else {
                    return res.status(200).json(data.queue);
                }                
            }
        );
    },
    // get /api/master_staff/:master_staff
    find: function(req, res) {
        let staff_id        = parseInt(req.params.master_staff, 10),
            master_club_id  = parseInt(req.session.passport.user.master_club_id, 10),
            $currentSeason  = sails.config.sw_season.current;

        if(!staff_id) {
            return res.validation('Invalid Staff Indetifier');
        }
        if(!master_club_id) {
            return res.forbidden('No Club Found')
        }

        let sql = 
            `SELECT ( 
                SELECT ROW_TO_JSON("staff") 
                FROM ( 
                    ( 
                        SELECT ms.gender, ms.organization_code, ms.first, ms.last, 
                        ms.email, ms.phone, ms.phoneh, ms.city, ms.state, ms.zip, 
                        ms.nick, ms.address, ms.address2, ms.phonew, ms.phoneo, ms.membership_status, 
                        ms.bg_screening, ms.bg_expire_date, ms.chaperone_status, ms.coach_status, 
                        ms.birthdate, ms.usav_number, ms.aau_membership_id,
                        (
                            CASE 
                              WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                              WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                              ELSE NULL
                            END  
                        ) "cert"
                    ) 
                ) "staff" 
            ) "staff", ( 
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("states"))) 
                FROM ( 
                    SELECT s.state, s.name 
                    FROM state s 
                    WHERE s.country = $1 
                ) "states" 
            ) "states", ( 
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("teams"))) 
                FROM ( 
                    SELECT mt.team_name, msr.role_id, mt.master_team_id "id", 
                            msr.primary, msr.role_id "initial_role_id" 
                    FROM master_staff_role msr
                    INNER JOIN master_team mt
                        ON mt.master_team_id = msr.master_team_id 
                        AND mt.season = $4 
                    WHERE msr.master_staff_id = ms.master_staff_id 
                    ORDER BY mt.organization_code 
                ) "teams" 
            ) "teams", (
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("roles"))) 
                FROM (
                    SELECT r.role_id AS id, r.name 
                    FROM ROLE r 
                    WHERE r.role_id IN (4, 5, 6, 15, 7)
                ) "roles"
            ) "roles"
            FROM master_staff ms
            WHERE ms.master_club_id = $2 
                AND ms.master_staff_id = $3`;

        Promise.all([
            Db.query(sql, ['US', master_club_id, staff_id, $currentSeason]),
            RosterSnapshotService.findBlockedEvents(master_club_id, null, staff_id)
        ]).then(results => {
            let staffData       = results[0].rows[0],
                blocked_events  = results[1];

            if(_.isEmpty(staffData)) {
                res.status(200).json({});
            } else {
                res.status(200).json(_.extend({ blocked_events }, staffData));
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },

    findInTeam: function (req, res) {
        var team_id = parseInt(req.param('team'), 10);
        if(!team_id) return res.status(400).json({error: 'No team selected'});
        var master_club_id = req.session.passport.user.master_club_id;
        if(!master_club_id) return res.status(403).json({error: 'Access denied'});

        Promise.all([
            Db.query(
                `SELECT  
                    ms.gender, ms.organization_code, ms.first, ms.last, 
                    ms.email, ms.phone AS phonem, ms.phoneh, ms.city, ms.state, ms.zip,  
                    msr.role_id, ms.master_staff_id, msr.master_team_id,  
                    msr.primary, ms.usav_number, ms.aau_membership_id,
                    (
                        CASE 
                          WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                          WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                          ELSE NULL
                        END  
                    ) "cert",
                    ( 
                        SELECT mt.team_name 
                        FROM master_team mt 
                        LEFT JOIN master_staff_role msr1 ON msr1.master_team_id = mt.master_team_id 
                        WHERE msr1.master_staff_id = ms.master_staff_id 
                          AND msr1.primary = TRUE 
                        ORDER BY master_staff_role_id DESC 
                        LIMIT 1 
                    ) AS primary_team_name 
                FROM master_staff_role msr  
                LEFT JOIN master_staff ms ON msr.master_staff_id = ms.master_staff_id  
                WHERE msr.master_team_id = $1 
                  AND ms.master_club_id = $2 AND ms.deleted IS NULL`,
                [team_id, master_club_id]
            ),
            Db.query(
                `SELECT r.role_id AS id, r.name 
                 FROM role r 
                 WHERE r.role_id IN (4, 5, 6, 15)`
            )
        ]).then(results => {
            let staff = results[0].rows, 
                roles = results[1].rows;

            res.status(200).json({staff, roles});
        }).catch(err => {
            res.customRespError(err);
        });
    },

    create: function(req, res) {
        if (!req.is('json')) {
            return res[400]([new Error('Request should be json')]);
        }
        var schema = createSchema({
            type: 'object',
            additionalProperties: false,
            properties: {
                birthdate: {
                    type: ['date', 'string', 'null'],
                    required: false
                },
                email: {
                    type: ['string', 'null'],
                    required: true
                },
                first: {
                    type: ['string', 'null'],
                    minLength: 1,
                    maxLength: 100,
                    required: true
                },
                last: {
                    type: ['string', 'null'],
                    minLength: 1,
                    maxLength: 100,
                    required: true
                },
                gender: {
                    type: 'string',
                    required: true,
                    enum: ['male', 'female', 'coed']
                },
                phone: {
                    type: ['string', 'null'],
                    required: true
                },
                nick: {
                    type: ['string', 'null'],
                    required: true
                },
                address2: {
                    type: ['string', 'null'],
                    required: true
                },
                phonew: {
                    type: ['string', 'null'],
                    required: true
                },
                phoneo: {
                    type: ['string', 'null'],
                    required: true
                }
            }
        });

        try {
            schema.validate(req.body);
        } catch (err) {
            return res.status(400).json(err);
        }

        var source = _.clone(req.body);
        source.master_club_id = req.session.passport.user.master_club_id;
        _.each(_.keys(source), function(key) {
            if (_.isNull(source[key])) {
                delete source[key];
            }
        });

        if(source.birthdate) {
            source.birthdate = new Date(source.birthdate);
        }

        var q = squel.insert()
            .into('master_staff')
            .setFields(source)
            .returning('*');

        Db.query(q)
        .then(result => {
            if(result.rowCount === 0) {
                res[500]([new Error('Server internal error')]);
            } else {
                res[201]({
                    master_staff: _.omit(_.first(result.rows), 'created', 'modified')
                });
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // put /api/master_staff/:master_staff
    update: function(req, res) {
        var master_staff_id = req.param('master_staff');
        var master_club_id = req.session.passport.user.master_club_id;
        if(!master_staff_id) return res.status(400).json({error: 'No staff selected'});
        if(!master_club_id) return res.status(403).json({error: 'Access denied'});

        var schema = createSchema({
            type: 'object',
            additionalProperties: false,
            properties: {
                email: {
                    type: ['string', 'null']
                },
                first: {
                    type: ['string', 'null'],
                    minLength: 1,
                    maxLength: 100,
                    required: false
                },
                birthdate: {
                    type: ['string', 'null']
                },
                nick: {
                    type: ['string', 'null']
                },
                phoneo: {
                    type: ['string', 'null']
                },
                phonew: {
                    type: ['string', 'null']
                },
                phone: {
                    type: ['string', 'null']
                },
                phoneh: {
                    type: ['string', 'null']
                },
                address: {
                    type: ['string', 'null']
                },
                address2: {
                    type: ['string', 'null']
                },
                city: {
                    type: ['string', 'null']
                }, 
                state: {
                    type: ['string', 'null']
                },
                zip: {
                    type: ['string', 'null'],
                    maxLength: 20,
                }
            }
        });

        try {
            schema.validate(req.body);
        } catch (error) {
            loggers.errors_log.error(error);
            return res.status(400).json({ validationErrors: error });
        }

        if(req.body.birthdate){
            if(!moment(req.body.birthdate, 'MM/DD/YYYY', true).isValid()) {
                throw { validation: 'Invalid Birthdate' }
            }
        }

        var keys = _.keys(req.body);
     
        var query = squel.update().table('master_staff')
                        .setFields(req.body)
                        .where('master_staff_id = ?', master_staff_id)
                        .where('master_club_id = ? ', master_club_id)
                        .returning(keys.join(', '));


        Db.query(query)
        .then(result => {
            res.status(200).json({ master_staff: result.rows[0] || {} });
        }).catch(err => {
            res.customRespError(err);
        });
    },

    destroy: async function(req, res) {
        const masterStaffId = req.param('master_staff');

        if (_.isNaN(Number(masterStaffId)) || (Number(masterStaffId) <= 0)) {
            return res.customRespError({ validation: 'Master Staff Id invalid' });
        }

        var q = squel.delete()
            .from('master_staff')
            .where('master_staff_id = ?', masterStaffId)
            .returning('master_staff_id');

        Db.query(q)
        .then(result => {
            if (result.rowCount === 0) {
                res[404](new Error('Resource with that id does not exists'));
            } else {
                res[204]();
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },
    //post /api/master_staff/move
    move_to_team: function (req, res) {
        var $team_id        = parseInt(req.body.team, 10),
            $staff          = req.body.staff,
            $master_club_id = parseInt(req.session.passport.user.master_club_id, 10),
            $season         = sails.config.sw_season.current;

        if(!$team_id) {
            return res.validation('No team selected');
        }
        if(!_.isArray($staff)) {
            return res.validation('Invalid staff list passed');
        }
        if ($staff.length === 0) {
            return res.validation('No staffers passed');
        }
        if(!$master_club_id) {
            return res.validation('No Club Found');
        }

        // TODO: move to service
        function insertStaffRole (tr, masterStaffID) {
            let numID = parseInt(masterStaffID, 10);

            if (!Number.isInteger(numID)) {
                return Promise.reject({ validation: 'Staff identifiers should be integers' });
            }

            return tr.query(
                `INSERT INTO "master_staff_role" (
                    master_staff_id, master_team_id, role_id
                 ) SELECT ms.master_staff_id, $3, 0 
                 FROM "master_staff" ms 
                 WHERE ms.master_staff_id = $1
                    AND ms.master_club_id = $2
                    AND ms.season = $4
                    AND NOT EXISTS ( 
                     SELECT msr.master_staff_role_id  
                     FROM "master_staff_role" msr 
                     WHERE msr.master_staff_id = ms.master_staff_id
                         AND master_team_id = $3 
                 ) RETURNING *`,
                [numID, $master_club_id, $team_id, $season]
            ).then(result => result.rows[0] || null)
        }

        // TODO: move to service
        function getMasterStaffIDFromRows (rows) {
            return rows.reduce((arr, item) => {
                if (item !== null && item.master_staff_id) {
                    arr.push(item.master_staff_id);
                }

                return arr;
            }, []);
        }


        Db.begin().then(tr => {
            const masterStaffers = [];

            return $staff.reduce((prev, id) => 
                prev.then(() => 
                    insertStaffRole(tr, id).then(staffers => {
                        masterStaffers.push(staffers);
                    })
                ), Promise.resolve()
            )
            .then(() => {

                let staffIdentifiers = getMasterStaffIDFromRows(masterStaffers);

                loggers.debug_log.verbose(
                    'Inserted', staffIdentifiers.length, '"master_staff_role" rows');

                if (staffIdentifiers.length === 0) {
                    return staffIdentifiers;
                }

                return tr.query(
                    `UPDATE "master_staff_role"
                     SET "primary" = TRUE 
                     WHERE master_staff_role_id IN ( 
                         SELECT  
                             STRING_AGG(msr.master_staff_role_id::TEXT, '')::INTEGER 
                         FROM master_staff_role msr 
                         WHERE msr.master_staff_id IN (${staffIdentifiers.join(', ')}) 
                             AND msr.master_team_id IS NOT NULL 
                         GROUP BY msr.master_staff_id 
                         HAVING COUNT(msr.master_staff_id) = 1
                     )`
                ).then(() => staffIdentifiers)
            })
            .then(processedStaffersIDs => tr.commit().then(() => processedStaffersIDs))
            .catch(err => {

                tr.rollback();

                return Promise.reject(err);
            })
        }).then(processedStaffersIDs => {
            let upd = RosterSnapshotService.upsertRosterStaffRoles.bind(RosterSnapshotService, $master_club_id);

            return processedStaffersIDs.reduce((prev, masterStaffID) => 
                prev.then(() => 
                    upd(masterStaffID)
                ), 
                Promise.resolve()
            )
        }).then(() => {
            res.ok();
        })
        .catch(res.customRespError.bind(res));      
    },
    // post /api/master_staff/remove
    remove_from_team: function (req, res) {
        var $staff              = req.body.staff,
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10),
            filteredStaff       = [];

        if(!_.isArray($staff)) 
            return res.validation('Invalid staff list passed');        
        if(!$master_club_id) 
            return res.validation('No club found');

        filteredStaff = _.filter($staff, _.isNumber);

        if(!filteredStaff.length) 
            return res.validation('Empty staff list passed');

        Db.query(
            `DELETE FROM "master_staff_role"
             WHERE "master_staff_id" IN ( 
                SELECT master_staff_id 
                FROM "master_staff" 
                WHERE master_staff_id IN (${
                    filteredStaff.join(', ')
                }) 
                    AND master_club_id = $1 
            )`,
            [$master_club_id]
        ).then(() => {
            return Promise.all(
                filteredStaff.map(master_staff_id =>
                    RosterSnapshotService.upsertRosterStaffRoles($master_club_id, master_staff_id)
                )
            )
        }).then(function () {
            res.ok();
        }).catch(res.customRespError.bind(res))
    }
};

function _buildContactsHTML(staff) {
    let res = ['<p>', staff.first, ' ', staff.last];

    if (staff.email) {
        res.push('<br/><a href="mailto:', staff.email, '">', staff.email, '</a>')
    }

    res.push('</p>');

    if (staff.phoneh) {
        res.push('<p><b>Home:</b> <a href="tel:', staff.phoneh, '">', staff.phoneh, '</a></p>');
    }

    if(staff.phone) {
        res.push('<p><b>Cell:</b> <a href="tel:', staff.phone, '">', staff.phone, '</a></p>');
    }
    
    if(staff.phonem) {
        res.push('<p><b>Mobile:</b> <a href="tel:', staff.phonem, '">', staff.phonem, '</a></p>');
    }

    if(staff.phonep) {
        res.push('<p><b>Parents:</b> <a href="tel:', staff.phonep, '">', staff.phonep, '</a></p>');
    }

    res.push('<p>');

    if (staff.address) {
        res.push(staff.address, '<br/>');
    }

    if (staff.city) {
        res.push(staff.city);
    }

    if (staff.state) {
        res.push(' ', staff.state);
    }

    if (staff.zip) {
        res.push(', ', staff.zip);
    }

    res.push('</p>');

    return res.join('');
}

function _buildTeamsListHtml(staff) {
    if(staff.teams.length === 0) {
        return '';
    }

    return staff.teams.map(team => {
        if (!team.primary) {
            let teamRes = ['<div style="text-overflow: ellipsis; white-space: nowrap;">'];
            
            if (team.organization_code) {
                teamRes.push(team.organization_code);
            }

            if (team.team_name) {
                teamRes.push('"' + team.team_name + '"');
            }

            if (team.role_name) {
                teamRes.push(' - ' + team.role_name);
            }

            teamRes.push('</div>')


            return teamRes.join(' ');
        }
    }).join('');
}
