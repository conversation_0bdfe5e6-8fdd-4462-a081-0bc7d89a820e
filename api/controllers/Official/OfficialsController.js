'use strict';

const
    fs                        = require('fs'),
    /* NOTE: why is this imported via "require"? (This is available in global) */
    OfficialsService          = require('../../services/OfficialsService'),
    AdditionalRestrictionSchema = require('../../validation-schemas/official').update_additional_restrictions;

module.exports = {
    // get /api/official/event/:event/officials
    index: function (req, res) {
        let eventID         = parseInt(req.params.event, 10);
        let showOfficials   = true;

        OfficialsService.getAll(eventID, {role: OfficialsService.checkin.ROLE.OFFICIAL})
        .then(data => {
            res.status(200).json({
                link        : OfficialsService.SCHEDULE_APP_LINK + eventID,
                officials   : data.role_data,
                event       : data.event
            });
        })
        .catch(res.customRespError.bind(res));
    },
    // get /api/official/event/:event/official/:official
    find: function (req, res) {
        let officialID  = parseInt(req.params.official, 10),
            eventID     = parseInt(req.params.event, 10);
        
        OfficialsService.findOne(eventID, officialID, function (err, data) {
            if(err) {
                return res.customRespError(err);
            }

            if(data && data.official) {
                delete data.official.bank_account_routing;
                delete data.official.bank_account_number;
            }

            return res.status(200).json(data);
        });         
    },

    // post /api/event/:event/officials/export
    export_to_excel: function (req, res) {
        let eventID         = parseInt(req.params.event, 10),
            officialsList   = req.query.officials && req.query.officials.map(id => parseInt(id, 10));

        if (!Number.isInteger(eventID)) {
            return res.validation('Invalid event identifier');
        }

        let query = OfficialsService.get_export_query(officialsList, eventID);

        XLSXService.export(query, 'officials').then(filePath => {
            res.download(filePath, err => {
                if (err) {
                    loggers.errors_log.error(err);
                    if(err.code === 'ENOENT') {
                        res.render('500', { error: 'File not found' });
                    } else {
                        res.serverError();
                    }
                } else {
                    fs.unlinkSync(filePath);
                }
            });
        }).catch(res.customRespError.bind(res));
    },
    // put /api/official/event/:event/official/:official/update
    update: function (req, res) {
        let $eventID    = Number(req.params.event),
            $officialID = req.params.official,
            $data       = req.body;

        let personName  = `${req.user.first} ${req.user.last}`;

        OfficialsService.updateRegData($eventID, $officialID, personName, $data)
        .then(() => {
            res.ok();
        }).catch(res.customRespError.bind(res));
    },

    // put /api/official/event/:event/officials/update
    updateGroup: function (req, res) {
        let $eventID    = parseInt(req.params.event, 10),
            $officials  = req.body.officials,
            $data       = req.body.data;

        let personName  = `${req.user.first} ${req.user.last}`;

        OfficialsService.updateRegDataGroup($eventID, $officials, personName, $data)
        .then(() => {
            res.ok();
        }).catch(res.customRespError.bind(res));
    },

    // put /api/official/event/:event/official/:official/additional-restrictions
    updateAdditionalRestrictions: function (req, res) {
        let $eventID    = Number(req.params.event),
            $officialID = req.params.official,
            $data       = req.body;

        const validationResult = AdditionalRestrictionSchema.validate($data);

        if (validationResult.error) {
            return res.validation(validationResult.error.details);
        }

        OfficialsService.updateAdditionalRestrictions($eventID, $officialID, $data.additional_restrictions)
        .then(() => {
            res.ok();
        }).catch(res.customRespError.bind(res));
    },
}
