'use strict';

const co = require('co');

module.exports = {
    // 🔧 TODO: move to profile service
    //get /api/official/find
    find: function (req, res) {
        const official_id = req.session.passport.user.official_id;
        if(!official_id) return res.status(403).json({error: 'Access denied. You are not an official'});

        const daysCountAfterEventEnd = OfficialsService.assignments.DAYS_COUNT_AFTER_EVENT_END;

        const ciRequiredQuery = squel.select()
            .field('TRUE')
            .from('event', 'ev')
            .join('event_official', 'eof', 'eof.event_id = ev.event_id')
            .join('event_clothes', 'ec', 'ec.event_id = ev.event_id AND ec.deleted IS NULL')
            .where('ev.date_end>(NOW() AT TIME ZONE ev.timezone) AND eof.official_id = o.official_id AND ec.common_item_id = ci.common_item_id')
            .where(`(u.gender = 'unspecified' AND ec.gender = 'male') OR ec.gender = u.gender::TEXT`)
            .limit(1);

        const hasApprovedStaffRegsQuery = squel.select()
            .field('1')
            .from('event_official', 'eo')
            .join('event', 'e', 'e.event_id = eo.event_id')
            .where('eo.staff_deleted IS NULL')
            .where('eo.is_staff IS TRUE')
            .where(`eo.staff_work_status = 'approved'`)
            .where('eo.official_id = ?', official_id)
            .where('EXTRACT(DAYS from (NOW() AT TIME ZONE e.timezone - e.date_end)) <= ?', daysCountAfterEventEnd);

        const query = squel.select().from('official', 'o')
                    .field('o.is_official')
                    .field('o.is_staff')
                    .field('o.region')
                    .field('o.arbiter_pay_username')
                    .field('o.arbiter_pay_account_number')
                    .field('o.rq_pay_username')
                    .field('o.country')
                    .field('o.background_screening')
                    .field('o.city')
                    .field('o.state')
                    .field('u.email')
                    .field('o.zip')
                    .field('o.address')
                    .field('o.usav_num')
                    .field('o.advancement')
                    .field('o.rank')
                    .field('o.profile_completed_at IS NULL', 'not_completed')
                    .field('o.aau_profile_completed_at IS NULL', 'aau_not_completed')
                    .field('o.aau_region')
                    .field('o.aau_number')
                    .field('o.special_sizing_requests')
                    .field(`TO_CHAR(o.birthdate, 'YYYY-MM-DD')`, 'birthdate')
                    .field('o.emergency_contact_name')
                    .field('o.emergency_phone')
                    .field('o.emergency_contact_relationship')
                    .field(`TO_CHAR(o.mbr_expire_date, 'MM/DD/YYYY')`, 'mbr_expire_date')
                    .field(
                        `CASE 
                            WHEN NULLIF(o.safesport_end_date, '') IS NOT NULL
                                THEN TO_CHAR(o.safesport_end_date::TIMESTAMP, 'MM/DD/YYYY')
                            ELSE NULL
                         END`
                    , 'ss_expire_date')
                    .field(`TO_CHAR(o.bg_expire_date, 'MM/DD/YYYY')`, 'bg_expire_date')
                    .field(`TO_CHAR(o.webpoint_modified, 'MM/DD/YYYY')`, 'webpoint_modified')
                    .field(
                        `CASE 
                            WHEN o.safesport_statusid::TEXT = ${WebpointService.VALID_SAFESPORT_STATUS}::TEXT THEN 'OK' 
                            ELSE 'NO'
                        END`
                        , 'ss_status'
                    )
                    .field(
                        `CASE 
                            WHEN 
                                o.background_screening::TEXT 
                                    = ${WebpointService.VALID_BACKGROUND_SCREENING_STATUS}::TEXT 
                            THEN 'OK'
                            ELSE 'NO'
                        END`
                        , 'bg_status'
                    )
                    .field(
                        squel.select()
                            .field('array_to_json(array_agg(cl))')
                            .from(squel.select()
                                .from('common_item', 'ci')
                                .field('ci.common_item_id')
                                .field('ci.title')
                                .field(`ci.details->>'size_type'`, 'size_type')
                                .field('ocs.size')
                                .field(squel.str('EXISTS(?)', ciRequiredQuery), 'required')
                                .left_join(
                                    'official_clothes_size', 'ocs',
                                    `ocs.common_item_id = ci.common_item_id
                                     AND ocs.official_id = o.official_id`
                                )
                                .where(`ci.details::jsonb->>'gender' = 'any' 
                                    OR ((u.gender = 'unspecified' AND ci.details::jsonb->>'gender' = 'male') 
                                        OR ci.details::jsonb->>'gender' = u.gender::TEXT)`)
                                .where(`ci.item_type = 'event_clothes'`)
                                .order(`ci.details::jsonb->>'order'`)
                        , 'cl')
                    , 'clothes')
                    .field(squel.str('EXISTS(?)', hasApprovedStaffRegsQuery), 'has_approved_staff_regs')
                    .field(`TO_CHAR(o.modified, 'YYYY-MM-DD hh:mm:ss')`, 'modified')
                    .left_join('user', 'u', 'u.user_id = o.user_id')
                    .where('official_id = ?', official_id);

        Db.query(query).then(function (result) {
            res.status(200).json({ official: result.rows, season: sails.config.sw_season.current });
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // post /api/official/create
    create: function(req, res) {
        let userID = +req.session.passport.user.user_id;

        OfficialsService.profile.create(userID, req.user, req.body)
            .then(profile => {
                return new Promise(resolve => {
                    req.session.passport.user.official_id = profile.officialId;

                    req.session.save(err => {
                        if(err) {
                            loggers.errors_log.error(err);
                        }

                        resolve(profile);
                    })
                })
            })
            .then((profile) => res.status(200).json(profile))
            .catch(res.customRespError.bind(res));
    },
    // put /api/official/update
    update: function(req, res) {
        let official_id             = req.session.passport.user.official_id;
        let { gender, first, last } = req.user;

        if(!official_id) {
            return res.status(403).json({error: 'Access denied. You are not an official'});
        }

        return OfficialsService.profile.update(official_id, {gender, first, last}, req.body)
            .then((profile) => res.status(200).json(profile))
            .catch(res.customRespError.bind(res));
    },

    //GET /api/official/clothes
    getClothes: co.wrap(function * (req, res) {
        let userID = Number(req.session.passport.user.user_id);

        const result = yield OfficialsService.profile.getClothes(userID);

        res.status(200).json({ clothes: result });
    }),

    //GET /api/official/update-webpoint
    updateWebpointInfo: function (req, res) {
        let officialID              = req.session.passport.user.official_id;
        let { gender, first, last } = req.user;

        if(!officialID) {
            return res.status(403).json({error: 'Access denied. You are not an official'});
        }

        return OfficialsService.profile.updateWebpointInfo(officialID, { gender, first, last })
            .then(() => res.status(200).json({}))
            .catch(res.customRespError.bind(res));
    }
};
