'use strict';

const
    Joi             = require('joi'),
    userSchema      = require('../validation-schemas/user').create;

require('date-utils');

module.exports = {
    signup: function (req, res) {
        let validationRes = userSchema.validate(req.body);

        if (validationRes.error) {
            return res.customRespError({ validationErrors: validationRes.error.details });
        }

        const user = _.omit(req.body, ['password_confirmation']);

        UserService.reg.register(user, req.protocol, req.headers.host)
        .then(user => {
            res[201]({ user });
        }).catch(error => {
            res.status(400).json({ validationErrors: error });
        });

    },
    activate: function (req, res) {
        let activationCode = req.query.code;

        if (!activationCode) {
            return res.forbidden('No activation code.');
        }

        UserService.reg.activateUser(activationCode, req.protocol, req.headers.host)
        .then(() => {
            res.send('OK');
        }).catch(res.customRespError.bind(res));
    }
};
