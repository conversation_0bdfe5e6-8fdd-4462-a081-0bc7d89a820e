'use strict';

const co            = require("co");

// POST /api/sms/report
function smsReport (req, res) {
    return co(function* () {
        let body = req.body;

        if(!Object.keys(body).length) {
            res.status(200).json({});
        } else {

            let webhookData = SmsService.prepareTwilioEventForSaving(body);

            // NOTE: <PERSON><PERSON><PERSON> expect 204 as empty response for Inbound sms
            // https://www.twilio.com/docs/api/twiml/sms/your_response
            if(webhookData.isReceivedSms) {
                res.status(204).json({});
            } else {
                yield SmsService.saveWebhook(body, webhookData);

                yield SmsService.updateSms(webhookData.smsID, webhookData);

                res.status(200).json({});
            }
        }
    }).catch(err => {
        loggers.errors_log.error('sms webhook handler error: ', err);
        res.status(200).json({});
    })
}

module.exports = { smsReport };
