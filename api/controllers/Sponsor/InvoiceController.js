module.exports = {
    //POST /api/event/:event/exhibitor/pay/:type
    async payInvoice (req, res) {
        try {
            let type    = req.params.type;
            let payment = req.body.payment;

            const eventID = Number(payment.event_id);
            const sponsorID = Number(payment.sponsor_id);
            const userID = Number(req.user.user_id);

            const applicationStatus =  await EventExhibitorService.getApplicationStatus(eventID, sponsorID);

            if (applicationStatus !== EventExhibitorService.APPLICATION_STATUS.APPROVED) {
                throw { validation: `Operation Denied: Application status is not "Approved"` };
            }

            await BoothsService.exhibitorPayment.pay(type, payment, userID);

            await BoothsService.notifications.sendPaymentNotification(eventID, payment.purchase_id, type);

            return res.status(200).json({});
        } catch (e) {
            res.customRespError(e);
        }
    }
};
