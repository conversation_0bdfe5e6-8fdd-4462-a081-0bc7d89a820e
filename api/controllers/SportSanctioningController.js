'use strict';

module.exports = {
    find: function (req, res) {
        var sportId = parseInt(req.query.sport, 10);
        var q = squel.select()
            .from('sport_sanctioning')
            .field('sport_sanctioning_id', 'id')
            .field('name');

        if (sportId)  {
            q.where('sport_id = ?', sportId);
        }

        Db.query(q)
        .then(result => {
            res.status(200).json({
                sport_sanctioning: result.rows
            });
        }).catch(err => {
            res.customRespError(err);
        });
    }
};
