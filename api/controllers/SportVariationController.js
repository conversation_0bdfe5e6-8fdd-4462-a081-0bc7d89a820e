'use strict';

module.exports = {
    find: function (req, res) {
        var sportId = parseInt(req.query.sport, 10);
        var q = squel.select()
            .from('sport_variation')
            .field('sport_variation_id', 'id')
            .field('name')
            .field('sort_order');

        if (sportId)  {
            q.where('sport_id = ?', sportId);
        }

        Db.query(q)
        .then(result => {
            res.status(200).json({
                sport_variation: result.rows
            })
        }).catch(err => {
            res.customRespError(err);
        });
    }
};
