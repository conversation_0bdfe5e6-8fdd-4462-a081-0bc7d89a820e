'use strict';

module.exports = {
    //POST /api/public/support
    send: async (req, res) => {
        const session =
            JSON.stringify(_.omit(req.user, 'pwd_hash', 'pwd_salt', 'remember_me'), null, '\t');

        try {
            await SupportMessageService.sendEmail(req.body, session);

            return res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    }
};
