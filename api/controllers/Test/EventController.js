'use strict';

var crypto = require('crypto');

const fs = require('fs');
const path = require('path');
const ejs = require('ejs');

const StripeConnect = require('../../lib/StripeConnect');

module.exports = {
    index: function(req, res) {
        return res.status(200).json({
            value: 1
        })
    },
    barcode: function(req, res) {
        var count = 0;
        var codes = [];
        var time = new Date().getTime();
        async.whilst(
            function() {
                return count < 200; },
            function(callback) {
                SWTReceiptService.generateUniqueBarcode()
                    .then(code => {
                        count++;
                        codes.push(code);
                        callback();
                    }).catch(err => {
                        callback(err)
                    })
            },
            function(err) {
                if (err) return res.send(err);
                console.log('Total, secs', time - new Date().getTime())
                return res.send(codes.join('\n'));
            }
        );
    },
    transfersCron: function(req, res) {
        EventUtils.updatePaymentsTransferStatus(function(err, count) {
            if (err) return res.send(err);
            return res.send('Number of affected rows:' + count);
        })
    },
    // get /test/events/esw-id-gen
    eswIdGen: function(req, res) {
        Db.query(
            `SELECT e.event_id FROM "event" e WHERE e.esw_id IS NULL ORDER BY e.created DESC`
        ).then(function(result) {
            return result.rows || []
        }).then(function(eventsList) {
            loggers.debug_log.verbose('Got', eventsList.length, 'events to update esw id')
            return __processEvents(eventsList)
        }).then(function() {
            res.ok();
        }, function(err) {
            res.customRespError(err)
        })
    },
    banDisputes: function(req, res) {
        banService.banLostDisputes()
            .then(() => {
                res.ok()
            }).catch(err => {
                res.customRespError(err);
            })
    },
    s2: function(req, res) {
        Db.query(
            `update "event_official" set "security_pin" = '33583' where"event_official_id" = 2100;`
        ).then(() => {res.ok()}).catch(res.customRespError.bind(this));
        // TicketsStatisticsService2.getStatistics((+req.query.e) || 42) // , '02/01/2016 10:25', '03/01/2016 10:25'
        //     .then(result => { res.status(200).json(result) })
        //     .catch(err => { res.serverError(err) });
    },
    // get /api/test-render
    testRender: function(req, res) {
        res.render(path.resolve(__dirname, '..', '..', '..', 'views/official/checkin_update.ejs'), {
            eventDates: ['Sat, May 28', 'Sun, May 29', 'Mon, May 30'],
            eventName: '2016 Music City Volleyball Championships',
            difference: [{
                fieldName: 'Departure Time',
                before: '06:01 pm',
                after: '06:00 pm'
            }, {
                fieldName: 'Additional Restrictions',
                before: null,
                after: '123'
            }, {
                fieldName: 'Schedule Availability',
                before: {
                    "Sat, May 28": false,
                    "Sun, May 29": true,
                    "Mon, May 30": true
                },
                "after": {
                    "Sat, May 28": true,
                    "Sun, May 29": false,
                    "Mon, May 30": true
                },
                is_schedule: true
            }],
            official: {
                usav_num: 'SO1201153MOA15',
                rank: 'International',
                work_status: 'approved',
                address: '708 Planters Row Lilburn GA SO US, 30047',
                name: 'Paul Albright',
                first: 'Paul',
                last: 'Albright',
                email: '<EMAIL>'
            },
            detailsLink: 'http://localhost/#/official/event/52/manage/174/info',
            _layoutFile: 'layout.ejs',
            formatValue: function(val) {
                if (val === (void 0) || val === null || val === '') {
                    return 'N/A';
                }

                if (typeof val == 'boolean' || val instanceof Boolean) {
                    return val ? 'Yes' : 'No';
                }

                return val;
            }
        }, function (err, content) {
            if (err) {
                res.customRespError(err)
            } else {
                res.set('content-type', 'text/html');
                res.send(content);
            }
        })
    },

    // GET /api/test/housing-upd
    housing: function (req, res) {
        Db.query('SELECT e."event_id" FROM "event" e WHERE e."event_id" > 17000 AND e."has_status_housing" IS TRUE')
        .then(result => result.rows)
        .then(eventsList => {
            return eventsList.reduce((prev, event) => {
                return prev.then(sum => {
                    return HousingService.update_event_clubs_housing(event.event_id)
                    .then(clubsQty => sum + clubsQty);
                })
            }, Promise.resolve(0))
        })
        .then(qty => {
            res.status(200).json({ qty });
        })
        .catch(res.customRespError.bind(res));
    },
}

function __processEvents(eventsList) {
    return new Promise(function(resolve, reject) {
        async.eachSeries(eventsList, function(event, next) {
            __generateESWId()
                .then(function(eswId) {
                    return Db.query(
                        `UPDATE "event" SET "esw_id" = $2 WHERE "event_id" = $1`, [event.event_id, eswId]
                    )
                })
                .then(function() {
                    next();
                }, function(err) {
                    next(err)
                })
        }, function(err) {
            if (err) {
                reject(err)
            } else {
                resolve();
            }
        })
    })
}

function __generateESWId() {
    return new Promise(function(resolve, reject) {
        var idLength = 9,
            run = true,
            iterations = 0,
            eswId;
        async.whilst(
            function() {
                return run; },
            function(done) {
                if (iterations >= 100)
                    return done('Exceed iterations limit on ESW ID Gen');
                ++iterations;
                eswId = crypto.randomBytes(Math.ceil(idLength / 2)).toString('hex').slice(0, idLength);
                Db.query(
                    `SELECT e.event_id FROM "event" e WHERE e.esw_id = $1`, [eswId]
                ).then(function(result) {
                    if (result.rowCount === 0) {
                        run = false;
                    }
                    done();
                }, function(err) {
                    done(err);
                })
            },
            function(err) {
                if (err) {
                    reject(err)
                } else {
                    resolve(eswId)
                }
            }
        )
    })
}
