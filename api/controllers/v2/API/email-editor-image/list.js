// GET /api/email-editor-images/v2
module.exports = {
    friendlyName: 'Get Email Editor Images',
    description: 'Get Email Editor Images',

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        try {
            const eventOwnerId = Number(
                this.req.session.passport.user.event_owner_id
            );

            const eventOwnerIds = [];

            if (eventOwnerId) {
                eventOwnerIds.push(eventOwnerId)
            }

            const sharedEventOwnerIds = this.req.user.shared_events && Object.values(this.req.user.shared_events)
                .filter(({ permissions }) => permissions.email_module_tab)
                .map(({ event_owner_id })=>event_owner_id);
            
            if(sharedEventOwnerIds) {
                eventOwnerIds.push(..._.uniq(sharedEventOwnerIds))
            }

            const images = await EmailEditorImageService.list({
                event_owner_ids: eventOwnerIds,
            });

            exits.success(images);
        } catch (err) {
            loggers.errors_log.error(err);
            this.res.customRespError(err);
        }
    },
};
