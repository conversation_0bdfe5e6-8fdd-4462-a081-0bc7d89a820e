module.exports = {
    friendlyName: 'Get email event history',
    description: 'Get email event history',

    inputs: {
        email: {
            type: 'string',
            description: 'Email ID',
            required: true
        },
    },

    exits: {
        success: {
            statusCode: 200
        },
    },

    fn: async function (inputs, exits) {
        try {
            const emailEvents = await EmailService.getEmailEvents(inputs.email);

            exits.success({ emailEvents })

        } catch (err) {
            loggers.errors_log.error(err);
            this.res.customRespError(err);
        }
    }
};