module.exports = {
    friendlyName: 'Events with schedule for SWT App',
    description: "Returns events for user's purchased tickets",
    
    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const userId = Number(this.req.session.passport.user.user_id);

        try {
            const events = await getPurchaseEvents(userId);

            exits.success(events);
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

async function getPurchaseEvents(user_id) {
    const query = `
        WITH user_tickets AS (
            SELECT p.purchase_id 
            FROM purchase p
            INNER JOIN purchase AS pr
                  ON pr.user_id = $1
                      AND pr.is_payment IS TRUE
                      AND pr.purchase_id = p.linked_purchase_id
                      AND (pr.kiosk IS NULL OR pr.status <> 'pending')
            UNION ALL
            SELECT p.purchase_id 
            FROM purchase p
            JOIN ticket_wallet tw ON tw.holder_user_id = $1
            WHERE p.ticket_barcode = tw.ticket_barcode
        ),
        user_purchase_events AS (
            SELECT  e.event_id,
                    e.long_name,  
                    e.name AS "short_name",
                    e.city,
                    e.state,
                    e.timezone,
                    e.date_start,
                    e.date_end,
                    (
                        CASE WHEN NOW() AT TIME ZONE e.timezone < e.date_start
                            THEN 'upcoming'
                            ELSE 'current'
                        END
                    ) AS status 
                FROM event AS e 
                INNER JOIN purchase AS p 
                    ON p.event_id = e.event_id AND p.purchase_id IN (SELECT * FROM user_tickets)
                WHERE e.deleted IS NULL 
                    AND e.live_to_public IS TRUE 
                    AND e.schedule_published IS TRUE 
                    AND (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                    AND NOW() AT TIME ZONE e.timezone < e.date_end
                    AND (p.dispute_status <> 'lost' OR p.dispute_status IS NULL)
                    AND p.is_ticket IS TRUE
                    AND p.payment_for='tickets'
                GROUP BY e.event_id
                ORDER BY e.date_start
        )
        SELECT upe.status, JSON_AGG(upe) AS events FROM user_purchase_events AS upe
        GROUP BY upe.status
        ORDER BY (CASE WHEN upe.status = 'upcoming' THEN 1 ELSE 2 END) ASC
    `;

    return Db.query(query, [user_id])
        .then(({ rows }) => rows)
}
