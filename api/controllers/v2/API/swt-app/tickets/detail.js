module.exports = {
    friendlyName: "Events with user's tickets for SWT App",
    description: 'Returns events with tickets for SWT App',

    inputs: {
        barcode: {
            type: 'string',
            example: '99999999',
            description: 'Barcode of the ticket',
            required: true,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function ({ barcode }, exits) {
        const userId = Number(this.req.session.passport.user.user_id);
        
        try {
            const ticket = await getTicketByBarcode(userId, barcode);

            if(!ticket) {
                return this.res.status(404).end('Ticket not found');
            }

            exits.success(ticket);
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

async function getTicketByBarcode(userId, barcode) {
    const query = knex('purchase as p')
        .select({
            ticket_barcode: 'p.ticket_barcode',
            first: 'p.first',
            last: 'p.last',
            label: 'et.label',
            short_label: 'et.short_label',
            ticket_type: 'et.ticket_type',
            scannable: 'et.can_be_scanned',
            is_scanned: knex.raw('pt.available = 0 AND p.scanned_at IS NOT NULL'),
            valid_dates: knex.raw(`(
                SELECT
                    COALESCE(
                        JSONB_OBJECT_AGG(
                            TO_CHAR(TO_TIMESTAMP(vd::TEXT, 'YYYY-MM-DD'), 'Dy, Mon DD'),
                            TRUE
                        ),
                        '{}'::JSONB
                    )
                FROM JSONB_OBJECT_KEYS(et.valid_dates) vd
            )`),
            scanned_at: knex.raw(`(p.scanned_at::TIMESTAMPTZ AT TIME ZONE e.timezone)::DATE`),
            is_purchased_ticket: knex.raw('lp.purchase_id IS NOT NULL')
        })
        .leftJoin('purchase AS lp', (join) => {
            join.on('lp.purchase_id', 'p.linked_purchase_id')
                .andOn('lp.user_id', userId)
        })
        .leftJoin('ticket_wallet AS tw', (join) => {
            join.on('tw.ticket_barcode', 'p.ticket_barcode')
                .andOn('tw.holder_user_id', userId)
        })
        .innerJoin('purchase_ticket AS pt','pt.purchase_id', 'p.purchase_id')
        .leftJoin('event_ticket AS et', 'et.event_ticket_id', 'pt.event_ticket_id')
        .join('event AS e', 'e.event_id', 'p.event_id')
        .where('p.is_ticket', true)
        .where('p.payment_for', 'tickets')
        .whereRaw(`(p.dispute_status <> 'lost' OR p.dispute_status IS NULL)`)
        .whereNull('p.canceled_date')
        .whereNull('p.deactivated_at')
        .whereNull('pt.canceled')
        .where('p.ticket_barcode', barcode);

    return Db.query(query).then(({ rows }) => rows[0] || null);
}
