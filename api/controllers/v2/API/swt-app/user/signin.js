const passport = require("passport");
const crypto = require("crypto");
const UserService = require('../../../../../services/UserService');
const RedisService = require('../../../../../services/RedisService');
const ErrorSender = require('../../../../../services/ErrorSender');

module.exports = {
    friendlyName: 'User Signup',
    description: 'Signup user',

    inputs: {
        email: {
            type: 'ref',
            example: '<EMAIL>',
            description: 'User email',
        },
        password: {
            type: 'ref',
            example: '*********',
            description: 'User Password',
        }
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        try {
            const user = await auth(this.req, this.res);

            if (!user.activated) {
                throw {
                    validationErrors: {
                        message: 'Your account is not activated',
                        path: 'email',
                    },
                    code: 400,
                };
            }

            if (user.deleted_at) {
                throw {
                    validationErrors: {
                        message: 'This account has already been deleted',
                        path: 'email',
                    },
                    code: 400,
                };
            }

            await login(user, this.req);

            await setRememberMeToken(this.req, this.res, user);

            if (!(
                user.clubDirector || user.eventOwner || user.sponsor ||
                user.staff || user.sales || user.owner || user.housing)
            ) {
                user.has_tickets = await findTickets(user);
            }

            exits.success({
                user: {
                    country: user.country,
                    first: user.first,
                    last: user.last,
                    clubDirector: user.role_club_director,
                    eventOwner: user.role_event_owner || user.has_shared_events,
                    hasEORole: user.role_event_owner,
                    isUSAVAdmin: user.has_usav_admin_role,
                    eoid: user.event_owner_id,
                    sponsor: user.role_sponsor,
                    staff: user.role_staff,
                    sales: user.role_sales_manager,
                    owner: user.is_sw_owner,
                    housing: user.housing_company_id,
                    has_tickets: user.has_tickets,
                    hasGodRole: user.has_god_role,
                    is_admin: (UserService.reg.ADMIN_EMAILS.indexOf(user.email.trim().toLowerCase()) !== -1),
                    email: user.email,
                    recognition_image: user.recognition_image,
                    recognition_verification_status: user.recognition_verification_status,
                    zip: user.zip,
                    phone: user.phone_mob,
                    user_id: user.user_id
                }
            });
        } catch (err) {
            if (err) {
                loggers.errors_log.error(err);
                if (this.res.code === 401) {
                    return res.status(401).json({error: err.error});
                } else if (this.res.code !== 500) {
                    return this.res[err.code]
                        ? this.res.status(400).json({validationErrors: [err.validationErrors]})
                        : this.res.serverError();
                }
            }
            return this.res.serverError();
        }
    }
};

function auth (req, res) {
    return new Promise((resolve, reject) => {
        passport.authenticate('user-local', (err, user, info) => {
            if (err) {
                reject(err);
            } else if (_.isEmpty(user)) {
                reject({ validationErrors: info, code: 400 });
            } else {
                RedisService.setUserDataMonitorKey(user.user_id, req.sessionID)
                    .catch(ErrorSender.defaultError.bind(ErrorSender));
                resolve(user);
            }
        })(req, res);
    });
}

function login (user, req) {
    return new Promise((resolve, reject) => {
        req.logIn(user, (err) => {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        })
    })
}

function setRememberMeToken (req, res, user) {
    let token = crypto.randomBytes(32).toString('hex');

    let sql =
        squel.update().table('user')
            .set('remember_me', token)
            .where('user_id = ?', user.user_id);

    return Db.query(sql)
        .then(() => {
            res.cookie(sails.config.sessionKeys.rememberKey, token, {
                path        : '/',
                httpOnly    : true,
                maxAge      : 30 * 24 * 60 * 60 * 1000
                // domain: __setDomain(req.host)
            });
        });
}

function findTickets (user) {
    return Db.query(
        `SELECT p.purchase_id 
             FROM "purchase" p 
             WHERE p.payment_for = 'tickets' 
                AND p.user_id = $1
             LIMIT 1`,
        [user.user_id]
    ).then(result => (result.rows.length > 0))
}
