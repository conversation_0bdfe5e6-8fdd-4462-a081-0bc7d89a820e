const { PHONE_REGEX } = require('../../../../../lib/joi-constants');
const validationSchemas = require('../../../../../validation-schemas/user');

module.exports = {
    friendlyName: 'User Signup',
    description: 'Signup user',

    inputs: {
        first: {
            type: 'string',
            example: '<PERSON>',
            description: 'First Name',
        },
        last: {
            type: 'string',
            example: 'John',
            description: 'Last Name',
        },
        email: {
            type: 'string',
            example: '<EMAIL>',
            description: 'Email',
        },
        phone_mob: {
            type: 'string',
            example: '10000000000',
            description: 'Phone number',
            regex: PHONE_REGEX,
        },
        country: {
            type: 'string',
            example: 'US',
            description: 'Country',
        },
        zip: {
            type: 'string',
            example: '19703',
            description: 'Zip',
        },
        password: {
            type: 'string',
            example: 'password',
            description: 'password',
        },
    },

    exits: {
        created: {
            statusCode: 201,
        },
    },

    fn: async function (inputs, exits) {
        let validationRes = validationSchemas.swtAppSignup.validate(inputs);

        if (validationRes.error) {
            return this.res.customRespError({
                validationErrors: validationRes.error.details,
            });
        }

        const defaultValues = {
            role_club_director: false,
            role_staff: false,
        };

        const userData = {
            ...defaultValues,
            ...validationRes.value,
        };

        try {
            const user = await UserService.reg.register(
                userData,
                this.req.protocol,
                this.req.headers.host
            );

            exits.created({ user });
        } catch (err) {
            if (_.isArray(err) && err[0]?.message) {
                return this.res.status(400).json({ validationErrors: err });
            }

            this.res.customRespError(err);
        }
    },
};
