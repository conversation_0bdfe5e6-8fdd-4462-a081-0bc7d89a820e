module.exports = {
    friendlyName: 'Get SW dispute details',
    description: 'Returns dispute details',

    inputs: {
        purchase: {
            type: 'number',
            description: 'Purchase ID'
        },
    },

    exits: {
        success: {
            statusCode: 200
        },
        notFound: {
            statusCode: 404,
        },
    },

    fn: async function ({ purchase }, exits) {
        try {
            const dispute = await __getDisputeDetails(purchase);

            if (!dispute) {
                return exits.notFound('Purchase does not exist');
            }

            exits.success({ dispute });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
};

async function __getDisputeDetails(purchase_id) {
    const underReviewCondition = `
        (de.has_evidence OR de.submission_count > 0) AND
        d.status IN ('under_review', 'warning_under_review')
    `
    const lostCondition = `
        d.status IN ('lost', 'warning_closed') 
        OR ((de.past_due IS TRUE OR DATE_PART('day', AGE(to_timestamp(de.due_by), NOW())) < 0)
            AND de.has_evidence IS FALSE) 
    `
    const wonCondition = `d.status = 'won'`
    const needsResponseCondition = `
        d.status IN ('needs_response', 'warning_needs_response') 
        AND DATE_PART('day', AGE(to_timestamp(de.due_by), NOW())) >= 0
    `

    const query = `SELECT 
        ROUND(d.amount / 100, 2)::NUMERIC amount,
        d.status,
        d.reason,
        TO_CHAR((p.dispute_created::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY, hh12:mi AM') dispute_created,
        TO_CHAR((de.created_at::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY, hh12:mi AM') evidence_created,
        TO_CHAR((sde.submitted_at::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY, hh12:mi AM') evidence_submitted,
        TO_CHAR((to_timestamp(de.due_by) AT TIME ZONE e.timezone), 'Mon DD, YYYY, hh12:mi AM') evidence_due,
        (
            SELECT TO_CHAR((ph.created::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY, hh12:mi AM') 
                FROM purchase_history ph 
                WHERE ph.purchase_id = p.purchase_id 
                AND ph.action = 'dispute.won' 
                ORDER BY ph.created DESC 
                LIMIT 1
        ) dispute_won,
        (
            CASE
                WHEN ${underReviewCondition} THEN 'submitted'
                WHEN ${lostCondition} THEN 'lost'
                WHEN ${wonCondition} THEN 'won'
                WHEN ${needsResponseCondition} THEN 'pending'
                ELSE 'other'
            END
        ) dispute_status,
        JSON_BUILD_OBJECT(
            'purchase_id', p.purchase_id,
            'amount', p.amount,
            'payment_type', p.type,
            'created', TO_CHAR((p.created::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY, hh12:mi AM'),
            'payment_for', p.payment_for,
            'collected_sw_fee', p.collected_sw_fee
        ) purchase,
        JSON_BUILD_OBJECT(
            'first', COALESCE(p.first, u.first),
            'last', COALESCE(p.last, u.last),
            'email', COALESCE(p.email, u.email),
            'phone', COALESCE(p.phone, u.phone_mob),
            'zip', COALESCE(p.zip, u.zip)
        ) purchaser
        FROM purchase p
            JOIN stripe.dispute d on d.charge_id = p.stripe_charge_id
            JOIN stripe.dispute_evidence de on de.stripe_dispute_id = d.id
            LEFT JOIN stripe_dispute_evidence sde on sde.stripe_dispute_id = d.id
            JOIN event e on p.event_id = e.event_id
            JOIN "user" u on u.user_id = p.user_id
        WHERE p.dispute_created IS NOT NULL AND p.purchase_id = $1
    `;

    return Db.query(query, [purchase_id]).then(({ rows }) => rows[0]);
}
