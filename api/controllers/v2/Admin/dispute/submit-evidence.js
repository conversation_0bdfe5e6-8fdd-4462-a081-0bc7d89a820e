const {
    disputeEvidenceSchema,
} = require('../../../../validation-schemas/stripe');

module.exports = {
    friendlyName: 'Get SW dispute evidence details',
    description: 'Returns dispute evidence details',

    inputs: {
        purchase: {
            type: 'string',
            required: true,
            description: 'The Stripe dispute id.',
        },

        receipt: {
            type: 'ref',
            required: false,
            description:
                'Any receipt or message sent to the customer notifying them of the charge.',
        },

        service_date: {
            type: 'ref',
            required: false,
            description:
                'The date on which the customer received or began receiving the purchased service, in a clear human-readable format.',
        },

        customer_name: {
            type: 'ref',
            required: false,
            description: 'The name of the customer.',
        },

        refund_policy: {
            type: 'ref',
            required: false,
            description: 'Your refund policy, as shown to the customer.',
        },

        shipping_date: {
            type: 'ref',
            required: false,
            description:
                'The date on which a physical product began its route to the shipping address, in a clear human-readable format.',
        },

        billing_address: {
            type: 'ref',
            required: false,
            description: 'The billing address provided by the customer.',
        },

        shipping_address: {
            type: 'ref',
            required: false,
            description: 'The address to which a physical product was shipped.',
        },

        shipping_carrier: {
            type: 'ref',
            required: false,
            description:
                'The delivery service that shipped a physical product.',
        },

        customer_signature: {
            type: 'ref',
            required: false,
            description:
                'A relevant document or contract showing the customer’s signature.',
        },

        uncategorized_file: {
            type: 'ref',
            required: false,
            description: 'Any additional evidence or statements.',
        },

        uncategorized_text: {
            type: 'ref',
            required: false,
            description: 'Any additional evidence or statements.',
        },

        access_activity_log: {
            type: 'ref',
            required: false,
            description:
                'Any server or activity logs showing proof that the customer accessed or downloaded the purchased digital product.',
        },

        cancellation_policy: {
            type: 'ref',
            required: false,
            description:
                'Your subscription cancellation policy, as shown to the customer.',
        },

        product_description: {
            type: 'ref',
            required: false,
            description:
                'A description of the product or service that was sold.',
        },

        customer_purchase_ip: {
            type: 'ref',
            required: false,
            description:
                'The IP address that the customer used when making the purchase.',
        },

        cancellation_rebuttal: {
            type: 'ref',
            required: false,
            description:
                'A justification for why the customer’s subscription was not canceled.',
        },

        service_documentation: {
            type: 'ref',
            required: false,
            description:
                'Documentation showing proof that a service was provided to the customer.',
        },

        customer_communication: {
            type: 'ref',
            required: false,
            description:
                'Any communication with the customer that you feel is relevant to your case.',
        },

        customer_email_address: {
            type: 'ref',
            required: false,
            description: 'The email address of the customer.',
        },

        shipping_documentation: {
            type: 'ref',
            required: false,
            description:
                'Documentation showing proof that a product was shipped to the customer at the same address the customer provided to you.',
        },

        refund_policy_disclosure: {
            type: 'ref',
            required: false,
            description:
                'Documentation demonstrating that the customer was shown your refund policy prior to purchase.',
        },

        shipping_tracking_number: {
            type: 'ref',
            required: false,
            description:
                'The tracking number for a physical product, obtained from the delivery service.',
        },

        refund_refusal_explanation: {
            type: 'ref',
            required: false,
            description:
                'A justification for why the customer is not entitled to a refund.',
        },

        duplicate_charge_explanation: {
            type: 'ref',
            required: false,
            description:
                'An explanation of the difference between the disputed charge versus the prior charge that appears to be a duplicate.',
        },

        cancellation_policy_disclosure: {
            type: 'ref',
            required: false,
            description:
                'An explanation of how and when the customer was shown your refund policy prior to purchase.',
        },

        duplicate_charge_documentation: {
            type: 'ref',
            required: false,
            description:
                'Documentation for the prior charge that can uniquely identify the charge.',
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
        badRequest: {
            statusCode: 400,
        },
    },

    fn: async function ({ purchase, ...data }, exits) {
        const { error, value: validatedData } = disputeEvidenceSchema.validate(
            data,
            { allowUnknown: true    }
        );

        if (error) {
            return exits.badRequest({ validationErrors: error.details });
        }

        try {
            const disputeEvidence = await StripeService.disputeEvidence.submit(
                purchase,
                validatedData,
            );


            exits.success({ disputeEvidence });
        } catch (err) {

            this.res.customRespError(err);
        }
    },
};
