module.exports = {
    friendlyName: 'Changes ticket type',
    description:
        'Change ticket type for given ticket',

    inputs: {
        barcode: {
            type: 'number',
            example: 22,
            description: 'Ticket Barcode',
            required: true,
        },
        eventTicketId: {
            type: 'number',
            example: 12,
            description: 'Id of event ticket type',
            required: true,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const { barcode, eventTicketId } = inputs;
        const user = this.req.user

        try {
            await TicketsService.eventTicket.changeEventTicketType({ ticketBarcode: barcode, eventTicketID: eventTicketId, user });

            exits.success({})
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};
