const { PHONE_REGEX } = require('../../../../lib/joi-constants');

module.exports = {
    friendlyName: 'Update ticket ',
    description: 'Updates user\'s ticket and resends it',

    inputs: {
        barcode: {
            type: 'number',
            example: 22,
            description: 'Ticket Barcode',
            required: true,
        },
        first: {
            type: 'string',
            example: '<PERSON>',
            description: 'First Name',
            required: true,
        },
        last: {
            type: 'string',
            example: '<PERSON><PERSON><PERSON><PERSON>',
            description: 'Last Name',
            required: true,
        },
    },

    exits: {
        updated: {
            statusCode: 204,
        },
    },

    fn: async function (inputs, exits) {
        const user = this.req.session.passport.user;
        const { barcode, type, ...paymentInfo } = inputs;

        try {
            await PaymentService.tickets.editPaymentInfo(
                barcode,
                paymentInfo,
                user
            );
            exits.updated();
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};
