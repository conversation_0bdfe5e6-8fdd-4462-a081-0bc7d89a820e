
//GET /api/club/club-invoice
module.exports = {
    friendlyName: 'Clubs Club Invoices List',
    description: 'Returns club invoices for a specific club',

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const masterClubId = Number(this.req.session.passport.user.master_club_id);
        const filters = this.req.query;

        try {
            const clubInvoices = await ClubInvoiceService.list.club.get(masterClubId, filters);

            exits.success({ clubInvoices });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
