

//POST /api/custom-form-builder/event/:event/form/:form_id/field
module.exports = {
    friendlyName: 'Custom Event Form Field Creation',
    description: 'Create custom form field for a specific event',

    inputs: {
        event: {
            type: 'number',
            description: 'Event ID'
        },
        form_id: {
            type: 'number',
            description: 'Form ID'
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const {
            form_id: formID
        } = inputs;
        const data = this.req.body || {};

        try {
            const fieldID = await EventService.eventCustomForm.editing.fields.create(formID, data);

            exits.success({ field: fieldID });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
