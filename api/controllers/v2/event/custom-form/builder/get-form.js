

//GET /api/custom-form-builder/event/:event/form/:form_id
module.exports = {
    friendlyName: 'Custom Event Form Data',
    description: 'Returns specific custom form for a specific event for editing',

    inputs: {
        event: {
            type: 'number',
            description: 'Event ID'
        },
        form_id: {
            type: 'number',
            description: 'Form ID'
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventID, form_id: formID } = inputs;

        try {
            const form = await EventService.eventCustomForm.editing.getData(eventID, formID);

            exits.success({ form });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
