

//GET /api/custom-form/event/:event/form
module.exports = {
    friendlyName: 'Custom Event Forms List',
    description: 'Returns custom forms for a specific event',

    inputs: {
        event: {
            type: 'number',
            description: 'Event ID'
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventID } = inputs;
        const filters = this.req.query;

        try {
            const forms = await EventService.eventCustomForm.list.get(eventID, filters);

            exits.success({forms});
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
