

//PUT /api/custom-form-builder/event/:event/form/:from_id
module.exports = {
    friendlyName: 'Custom Event Form Editing',
    description: 'Edit specific custom form for a specific event',

    inputs: {
        event: {
            type: 'number',
            description: 'Event ID'
        },
        form_id: {
            type: 'number',
            description: 'Form ID'
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventID, form_id: formID } = inputs;
        const data = this.req.body || {};

        try {
            await EventService.eventCustomForm.editing.update(eventID, formID, data);

            exits.success();
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
