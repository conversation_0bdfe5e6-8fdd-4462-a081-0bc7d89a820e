
const {
    VERIFICATION_DECISION,
} = require('../../../../constants/tickets-app');

//PUT /api/tickets-app/event/:event/verification/users/:user_id/:decision
module.exports = {
    friendlyName: 'User Verification Status Change',
    description: `Approve or decline user's Verification Ask`,

    inputs: {
        event: {
            type: 'number',
            example: 22,
            description: 'Event ID',
            required: true
        },
        user_id: {
            type: 'number',
            example: 22,
            description: 'User ID',
            required: true
        },
        decision: {
            type: 'string',
            isIn: [VERIFICATION_DECISION.VERIFIED, VERIFICATION_DECISION.DECLINED],
            description: 'Verification decision'
        }
    },

    exits: {
        success: {
            statusCode: 204
        }
    },

    fn: async function(inputs, exits) {
        const {
            user_id: userID,
            event: eventID,
            decision
        } = inputs;

        try {
            await EventService.ticketsAppUserVerification.processUserVerificationDecision(eventID, userID, decision);

            exits.success();
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
