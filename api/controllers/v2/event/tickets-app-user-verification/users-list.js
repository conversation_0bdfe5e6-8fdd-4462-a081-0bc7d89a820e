

//GET /api/tickets-app/event/:event/verification/users
module.exports = {
    friendlyName: 'Users List waiting Verification Approve',
    description: 'Returns list of users who are waiting for verification approve by EO.',

    inputs: {
        event: {
            type: 'number',
            example: 22,
            description: 'Event ID',
            required: true
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function(inputs, exits) {
        const params = this.req.query || {};

        try {
            const users = await EventService.ticketsAppUserVerification.getUsersList(inputs.event, params);

            exits.success({ users });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
