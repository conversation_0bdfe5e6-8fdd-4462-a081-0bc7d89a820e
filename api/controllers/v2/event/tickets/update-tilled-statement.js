const TilledService = require('../../../../services/TilledService');

// post /api/event/:event/tickets/tilled-statement
module.exports = {
    friendlyName: 'Update Tilled statement descriptor',
    description: 'Update Tilled statement descriptor',

    inputs: {
        event: {
            type: 'number',
            example: 22,
            description: 'Event ID',
            required: true,
        },
        statement: {
            type: 'string',
            example: 'Statement',
            description: 'Statement for tilled payment',
            required: true,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
        unauthorized: {
            statusCode: 401,
        },
    },

    fn: async function ({ statement, event }, exits) {
        try {
            validateStatement(statement);

            await Db.query(
                `UPDATE "event" SET "tickets_tilled_statement" = $2 WHERE "event_id" = $1`,
                [event, statement]
            );

            exits.success();
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};

const NUMBER_REG_EXP = '^[0-9]+$';
const STATEMENT_ALLOWED_LETTERS = '^[a-zA-Z0-9()_\\-!#$%^&,.|\\][]+$';

function validateStatement(statement) {
    if (statement.length > TilledService.__STATEMENT_MAX_LENGTH__) {
        throw {
            validation: `Statement length must be less than ${TilledService.__STATEMENT_MAX_LENGTH__} characters`,
        };
    }

    const doesNotContainsForbiddenLetters = new RegExp(STATEMENT_ALLOWED_LETTERS).test(
        statement
    );
    const containsOnlyNumbers = new RegExp(NUMBER_REG_EXP).test(statement);

    if (!doesNotContainsForbiddenLetters || containsOnlyNumbers) {
        throw {
            validation: `Should not use forbidden letters < > " ' \\ * or numbers only`,
        };
    }
}
