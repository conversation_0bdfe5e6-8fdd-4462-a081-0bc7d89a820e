const config = sails.config.paymentHub;

module.exports = {
    friendlyName: 'Payment Hub Webhook',
    description: 'Payment Hub Webhook',

    inputs: {

    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const data = this.req.body;

        validateWebhookRequest(this.req)

        
        try {
            if(await isPaymentHubEventSaved(data)){
                return exits.success()
            };
            
            await savePaymentHubEvent(data);

            await processPaymentHubWebhook(data);

            return exits.success()
        } catch (err) {
            return this.res.customRespError(err);
        }
    }
};

async function processPaymentHubWebhook(webhookData) {
    switch (webhookData.type) {
        case 'payment.settled':
            await PaymentService.teams.processPaymentHubWebhook(webhookData);
        default:
            break;
    }
}

function validateWebhookRequest(req) {
    const token = req.headers['X-AUTH-TOKEN'] || req.headers['x-auth-token'];

    const isAuthorized = token && token === config.webhookSecret;

    if (!isAuthorized) {
        throw new Error('Request not authorized')
    }
}

async function savePaymentHubEvent(webhook) {
    return Db.query(knex('payment_hub.event').insert({
        event_id: webhook.id,
        payload: webhook.data,
        object: webhook.object
    }));
}

async function isPaymentHubEventSaved(webhook) {
    return Db.query(
        knex('payment_hub.event').select('event_id').where({
            event_id: webhook.id,
        })
    ).then(({ rowCount }) => rowCount !== 0);
}