const { PHONE_REGEX } = require('../../../lib/joi-constants');
const validationSchemas = require('../../../validation-schemas/user');

module.exports = {
    friendlyName: 'Updates user info',
    description: 'Update user profile info',

    inputs: {
        type: {
            type: 'string',
            example: 'details',
            description: 'Update type',
            isIn: ['details', 'password', 'role'],
        },
        first: {
            type: 'string',
            example: '<PERSON>',
            description: 'First Name',
        },
        last: {
            type: 'string',
            example: '<PERSON>',
            description: 'Last Name',
        },
        email: {
            type: 'string',
            example: '<EMAIL>',
            description: 'Email',
        },
        gender: {
            type: 'string',
            example: 'male',
            description: 'Gender',
            isIn: ['male', 'female', 'unspecified', 'non-binary'],
        },
        phone_mob: {
            type: 'string',
            example: '10000000000',
            description: 'Phone number',
            regex: PHONE_REGEX,
        },
        country: {
            type: 'string',
            example: 'US',
            description: 'Country',
        },
        zip: {
            type: 'string',
            example: '19703',
            description: 'Zip',
        },
        password: {
            type: 'string',
            example: 'password',
            description: 'password',
        },
        new_password: {
            type: 'string',
            example: 'password',
            description: 'password',
        },
        role_club_director: {
            type: 'boolean',
            example: false,
            description: 'Role Club Director',
        },
        role_staff: {
            type: 'boolean',
            example: false,
            description: 'Role Staff',
        },
        role_sponsor: {
            type: 'boolean',
            example: false,
            description: 'Role Sponsor',
        },
        role_event_owner: {
            type: 'boolean',
            example: false,
            description: 'Role Event Owner',
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const userId = Number(this.req.session.passport.user.user_id);
        const { pwd_hash, pwd_salt } = this.req.session.passport.user;

        let validationRes = validationSchemas.updateV2.validate(inputs);

        if (validationRes.error) {
            _.forEach(validationRes.error.details, (error) => {
                if (
                    error.path[0] === 'zip' &&
                    error.type === 'string.regex.base'
                ) {
                    error.message = `"Zip Code" invalid format`;
                }
            });

            return this.res.customRespError({
                validationErrors: validationRes.error.details,
            });
        }


        const data = validationRes.value;

        try {
            await UserService.reg.updateUserV2({
                user: {
                    user_id: userId,
                    pwd_hash,
                    pwd_salt,
                },
                data,
                protocol: this.req.protocol,
                host: this.req.headers.host,
            });
    
            exits.success({ message: 'OK' });
        } catch (err) {
            this.res.customRespError(err);
        }
       
    },
};
