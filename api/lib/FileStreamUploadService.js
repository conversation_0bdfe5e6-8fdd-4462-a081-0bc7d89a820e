const fs = require('mz/fs');
const path = require('path');
const knox = require('knox');
const optimist = require('optimist');
const { PassThrough, Readable } = require('stream');
const utils = require('./swUtils');

const TEMP_PATH = path.resolve(__dirname, '..', '..', '.tmp/uploads');
const s3Config = optimist.argv.prod
    ? sails.config.s3.ticketsBucket
    : sails.config.s3.ticketsBucketDev;

const s3Client = knox.createClient(s3Config);

const MAX_FILE_SIZE_KB = 5 * 1024;
const ALLOWED_FILE_TYPES = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'];
const ALLOWED_FILE_XML = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

class FileStreamUploadService {
    constructor(serverFilePath, s3FilePath) {
        this.serverFilePath = serverFilePath;
        this.s3FilePath = s3FilePath;
    }

    /**
     * @param fileStream
     * @param getFileName
     * @param S3Folder
     * @returns {Promise<FileStreamUploadService>}
     */
    static async uploadFile(fileStream, getFileName, S3Folder) {
        if(!fileStream) {
            throw { validation: 'File is required' };
        }

        FileStreamUploadService.fileUploadValidation(fileStream);
    
        // clone origin stream to 2 separate streams
        let streamCopyForServer = new PassThrough();
        let streamCopyForS3 = new PassThrough();
    
        fileStream.pipe(streamCopyForServer);
        fileStream.pipe(streamCopyForS3);
    
        const S3FilePath = await FileStreamUploadService.__saveFileOnS3(
            streamCopyForS3, fileStream, getFileName, S3Folder
        );
        const serverFilePath = await FileStreamUploadService.__saveFileOnServer(streamCopyForServer, getFileName);
    
        return new FileStreamUploadService(serverFilePath, S3FilePath);
    }
    
    static async uploadFileForVipTickets(fileStream, fileName, S3Folder) {
        if(!fileStream) {
            throw { validation: 'File is required' };
        }

        FileStreamUploadService.fileUploadValidation(fileStream);

        // clone origin stream to 2 separate streams
        const { streamCopyForServer, streamCopyForS3 } = await this.cloneReadableStream(fileStream);
        
        const serverFilePath = await FileStreamUploadService.__saveFileOnServer(streamCopyForServer, fileName);
        const S3FilePath = await FileStreamUploadService.__saveFileOnS3(
            streamCopyForS3, fileStream, fileName, S3Folder
        );
        
        return new FileStreamUploadService(serverFilePath, S3FilePath);
    }
    
    static cloneReadableStream(origin) {
        return new Promise((resolve, reject) => {
            
            let streamCopyForServer = new Readable({
                read() {}
            });
            let streamCopyForS3 = new Readable({
                read() {}
            });
            
            origin.on("data", function(chunk) {
                streamCopyForServer.push(chunk);
                streamCopyForS3.push(chunk);
            });
            
            origin.on('end', function() {
                streamCopyForServer.push(null);
                streamCopyForS3.push(null);
                resolve({ streamCopyForServer, streamCopyForS3 });
            });
            
            origin.on("error", function(err) {
                streamCopyForServer.destroy();
                streamCopyForS3.destroy();
                reject(err);
            });
        })
    }
    
    async removeServerFile() {
        await fs.unlink(this.serverFilePath);
    }

    static fileUploadValidation(fileStream) {
        if (utils.getNextBinaryPrefixValue(fileStream.byteCount) > MAX_FILE_SIZE_KB) {
            throw {
                validation: `Maximum size for this file type is ${utils.getNextBinaryPrefixValue(MAX_FILE_SIZE_KB)}MB`
            };
        }

        if (!ALLOWED_FILE_TYPES.includes(fileStream.headers['content-type'])) {
            throw { validation: 'Allowable XLSX or CSV types' };
        }
    }

    static xmlFileTypeUploadValidation(fileStream) {
        if (utils.getNextBinaryPrefixValue(fileStream.byteCount) > MAX_FILE_SIZE_KB) {
            throw {
                validation: `Maximum size for this file type is ${utils.getNextBinaryPrefixValue(MAX_FILE_SIZE_KB)}MB`
            };
        }

        if (ALLOWED_FILE_XML !== fileStream.headers['content-type']) {
            throw { validation: 'Allowable only XLSX type' };
        }
    }

    static __saveFileOnServer(stream, getFilePath) {
        return new Promise((resolve, reject) => {
            let filePath = getFilePath(TEMP_PATH);
            let writeStream = fs.createWriteStream(filePath);
            
            stream.on('error', (err) => {
                reject(err);
            });
            
            stream.on('end', () => {
                resolve(filePath);
            });
            
            stream.pipe(writeStream);
        })
    }
    
    static __saveFileOnS3(stream, originStream, getFilePath, S3Folder) {
        return new Promise((resolve, reject) => {
            const header = {
                'Content-Length': originStream.byteCount,
                'Content-Type': originStream.headers['content-type'],
            };
            
            let filePath = getFilePath(S3Folder);
            let putFileToS3 = s3Client.put(filePath, header);
            
            putFileToS3.on('response', (data) => {
                if(data.statusCode === 200) {
                    resolve(filePath);
                }
                
                reject(new Error(`Error storing file to s3: ${data.statusMessage}`));
            }).on('error', error => {
                
                loggers.errors_log.error(error);
                
                reject(error || new Error('Internal S3 Error'));
            });
            
            stream.pipe(putFileToS3);
        })
    }

}

module.exports = FileStreamUploadService;
