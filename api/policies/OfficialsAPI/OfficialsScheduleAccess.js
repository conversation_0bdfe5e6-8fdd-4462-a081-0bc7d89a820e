'use strict';

module.exports = function (req, res, next) {
    var $event_id 		= parseInt(req.params.event, 10),
    	$eventOwnerId 	= parseInt(req.session.passport.user.event_owner_id, 10) || -1,
    	$officialId 	= parseInt(req.session.passport.user.official_id, 10) || -1,
        sharedEvents    = req.user.shared_events || {},
        sharedOwnerId;   

    if(!$event_id) return res.validation('Invalid event identifier');

    sharedOwnerId = sharedEvents[$event_id] && sharedEvents[$event_id].event_owner_id

    Db.query(
    	`with ho_access as (
		    select eo.event_official_id "id"
		    from "event_official" eo
		    where eo.official_id = $3
		        and eo.work_status = 'approved'
		        and eo.head_official is true
		        and eo.event_id = $1
		), eo_access as (
		    select eo.event_owner_id "id"
		    from "event" e
		    inner join "event_owner" eo
		        on eo.event_owner_id = e.event_owner_id
		        and eo.event_owner_id = $2
		    where e.event_id = $1
		)
		select id from ho_access union all select id from eo_access`,
    	[$event_id, (sharedOwnerId || $eventOwnerId), $officialId]
    ).then(function (result) {
    	if(result.rowCount > 0) {
    		req.params.event = $event_id;
    		return next();
    	} else {
    		res.forbidden('Only Event Owners and Head Officials allowed')
    	}
    }, function (err) {
    	res.customRespError(err)
    })
}
