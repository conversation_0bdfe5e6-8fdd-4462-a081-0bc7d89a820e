'use strict';

module.exports = function (req, res, next) {
    var purchase_id = parseInt(req.params.purchase, 10),
        sharedEventsKeys = (req.user.shared_events && Object.keys(req.user.shared_events)) || [],
        allowedEvents = req.user.events || [];

    if(!purchase_id) return res.accessDenied();    

    for(var i = 0, l = sharedEventsKeys.length, eventObj; i < l; ++i) {
        eventObj = req.user.shared_events[sharedEventsKeys[i]];
        if(eventObj.role_co_owner) allowedEvents.push(sharedEventsKeys[i])
    }  

    if(!allowedEvents.length) return res.accessDenied();

    Db.query(
        `SELECT p.purchase_id, p.type FROM purchase p 
         WHERE p.purchase_id = $1 AND p.event_id IN (${allowedEvents.join(', ')})`,
        [purchase_id]
    ).then(result => {
        let row = result.rows[0];
        if(row && (row.type === 'check')) {
            next();
        } else {
            res.accessDenied();
        }
    }).catch(() => {
        res.serverError();
    });
}