module.exports = function (req, res, next) {
    var $master_club_id = parseInt(req.session.passport.user.master_club_id, 10),
        $club_owner_id = parseInt(req.session.passport.user.club_owner_id, 10),
        $id = parseInt(req.params.id);
    if(!$master_club_id) return res.status(403).send('No club created. Access denied')
    if(!$club_owner_id) return res.status(403).send('Not event owner. Access denied')
    if(!$id) return res.status(403).send('Invalid invoice id. Access denied')

    Db.query(
        'select p.purchase_id \
        from purchase p \
        inner join roster_club rc \
            on rc.roster_club_id = p.roster_club_id \
        inner join master_club mc \
            on rc.master_club_id = mc.master_club_id \
        where p.purchase_id = $1  \
        and rc.master_club_id = $2 \
        and mc.club_owner_id = $3',
        [$id, $master_club_id, $club_owner_id]
    ).then(result => {
        if(result.rowCount > 0) return next();
        return res.status(403).send('Access denied');
    }).catch(() => {
        res.status(500).send('Internal error');
    })
}
