'use strict';

function doesProfileCompleted (profileType) {
    return async function (req, res, next) {
        let userID          = Number(req.user.user_id);
        let masterClubID    = Number(req.user.master_club_id);
        let isCompleted     = false;
        let errorMessage    = '';

        if(profileType === UserService.reg.ROLE.CD) {
            if(!masterClubID) {
                req.validation(`Club ID required`);
            }

            isCompleted = await __CDProfileIsCompleted(masterClubID);
            errorMessage = 'Club director profile needs update';

        } else if(profileType === UserService.reg.ROLE.OFFICIAL) {
            if(!userID) {
                req.validation(`User ID required`);
            }

            isCompleted = await __officialProfileIsCompleted(userID);
            errorMessage = 'Official/Staff profile needs update';

        }

        if(!isCompleted) {
            return res.status(403).send({validation: errorMessage});
        }

        await next();

    }
}

function __CDProfileIsCompleted (masterClubID) {
    let query = squel.select().from('master_club', 'mc')
        .field('(mc.profile_completed_at IS NOT NULL) :: BOOLEAN', 'is_completed')
        .where('mc.master_club_id = ?', masterClubID);

    return Db.query(query).then(result => result.rows[0] && result.rows[0].is_completed);
}

function __officialProfileIsCompleted (userID) {
    let query = squel.select().from('official', 'o')
        .field('(o.profile_completed_at IS NOT NULL) :: BOOLEAN', 'is_completed')
        .where('o.user_id = ?', userID);

    return Db.query(query).then(result => result.rows[0] && result.rows[0].is_completed);
}

module.exports = doesProfileCompleted;
