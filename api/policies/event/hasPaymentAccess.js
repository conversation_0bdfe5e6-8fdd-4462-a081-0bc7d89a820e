'use strict';

let eventAccess = require('../eventAccess');
let Permissions = require('../../services/event/operations');

module.exports = async function (req, res, next) {
    // purchase ID can be in params.id or in params.purchase
    let purchaseID = Number(req.params.id || req.params.purchase);

    if(!purchaseID) {
        return res.status(403).send('Invalid invoice id. Access denied.');
    }

    try {
        let eventID = await __getEventIDFromPurchase(purchaseID);

        if(!eventID) {
            return res.status(403).send('Access denied');
        }

        let permissionCheck = eventAccess(Permissions.TEAMS_TAB);
        req.params.event    = eventID;

        return permissionCheck(req, res, next);
    } catch (err) {
        res.status(500).send('Internal error');
    }
};

function __getEventIDFromPurchase (purchaseID) {
    return Db.query(
        'SELECT p.event_id::INTEGER FROM purchase p WHERE p.purchase_id = $1',
        [purchaseID]
    ).then(result => result.rows[0] && result.rows[0].event_id);
}

    

     
