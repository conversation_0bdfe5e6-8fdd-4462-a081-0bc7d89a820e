module.exports = function (req, res, next) {
    if(!req.user || !req.user.housing_company_id) {
        return res.forbidden('Not a housing manager');
    }
    if(req.user.housing_company_id === 1) {
        return res.forbidden('Access denied');
    }
    let $event_id = Number(req.params.event);

    if(!$event_id) {
        return res.validation('No event identifier provided');
    }

    Db.query(
        squel.select()
            .field('housing_company_id')
            .from('event', 'e')
            .where('e.event_id = ?', $event_id)
    )
        .then(({rows: [event]}) => {
            if(event.housing_company_id !== req.user.housing_company_id) {
                return res.forbidden(`You don't have access to this event`);
            }
            return next();
        })
        .catch(res.customRespError);
};
