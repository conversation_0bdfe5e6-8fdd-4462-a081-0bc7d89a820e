const knex = require('knex')({client: 'pg'});
const eventAccess = require('./eventAccess');
const Permissions = require('../services/event/operations');

module.exports = async function (req, res, next) {
    const purchaseID = Number(req.params.purchase);
    const sponsorIDS = req.user.sponsor_ids || [];
    const salesManagerID = req.user.sales_manager_id;

    try {
        if(!purchaseID) {
            throw { validation: 'Invalid invoice id' };
        }
        const purchase = await Db.query(
            knex('purchase as p')
                .join('event as e', 'e.event_id', 'p.event_id')
                .select(['e.event_id','e.sales_manager_id','p.sponsor_id'])
                .where('p.purchase_id', purchaseID)
        ).then(r => r.rows[0]);

        if(purchase) {
            const accessCheckResults = [
                sponsorIDS.includes(purchase.sponsor_id),
                salesManagerID === purchase.sales_manager_id,
            ];
            if(accessCheckResults.includes(true)) {
                return next();
            }
            else {
                return eventAccess(Permissions.EXHIBITORS_TAB, purchase.event_id)(req, res, next);
            }
        }

        return res.notFound();
    }
    catch(err) {
        res.customRespError(err);
    }
};
