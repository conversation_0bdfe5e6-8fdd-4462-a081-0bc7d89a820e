'use strict';

module.exports = async function (req, res, next) {
    const { barcode, purchase, code } = req.params;

    let purchaseId = parseInt(purchase, 10);
    let ticketBarcode = parseInt(barcode, 10) || parseInt(code, 10);
    let userId = req.user.user_id;

    const hasValidParams = purchaseId || ticketBarcode;

    if (!hasValidParams || !userId) return res.accessDenied();

    const query = knex('purchase as p')
        .select('lp.purchase_id')
        .leftJoin('purchase AS lp', (join) => {
            join.on('lp.purchase_id', 'p.linked_purchase_id')
                .orOn('lp.purchase_id', 'p.purchase_id')
        })
        .where('p.is_ticket', true)
        .andWhere('p.payment_for', 'tickets')
        .andWhere('lp.user_id', userId);

    if (purchaseId) {
        query.andWhere('p.purchase_id', purchaseId);
    } else {
        query.andWhere('p.ticket_barcode', ticketBarcode);
    }

    const hasPurchase = await Db.query(query)
        .then(({ rows }) => rows.length > 0)
        .catch(() => res.serverError());

    if (hasPurchase) {
        next();
    } else {
        res.accessDenied();
    }
};
