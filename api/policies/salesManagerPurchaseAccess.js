const knex = require('knex')({client: 'pg'});

module.exports = async function (req, res, next) {
    const eventID       = Number(req.body.payment.event_id);
    const purchaseID    = Number(req.body.payment.purchase_id);
    const smID          = req.session.passport.user.sales_manager_id;

    if(!eventID || !purchaseID || !smID) {
        return res.forbidden();
    }

    try {
        const purchase = await Db.query(
            knex('purchase as p')
                .where('p.event_id', eventID)
                .where('p.purchase_id', purchaseID)
                .where('p.sales_manager_id', smID)
        ).then(r => r.rows[0]);

        if(purchase) {
            return next();
        }

        return res.forbidden();
    }
    catch(err) {
        res.customRespError(err);
    }
};
