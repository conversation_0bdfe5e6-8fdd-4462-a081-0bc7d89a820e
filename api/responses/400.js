const nodeutil = require('util');

module.exports = function(errors, redirectTo) {
  var statusCode = 400,
    i, errorToLog, errorToJSON, res = this.res,
    req = res.req;

  var result = {
    status: statusCode
  };

  if (errors) {
    var validationErrors = [];
    for (var i in errors) {
      if (errors[i].message || errors[i].msg) {
        validationErrors.push({
          msg: errors[i].message || errors[i].msg
        });
      }
    }
    result.validationErrors = validationErrors;
  }

  var errorsToDisplay = SailsUtilService.normalizeErrors(errors);
  for (i in errorsToDisplay) {

    if (errorsToDisplay[i].original) {
      errorToLog = nodeutil.inspect(errorsToDisplay[i].original);
    } else {
      errorToLog = errorsToDisplay[i].stack;
    }

    errorToJSON = errorsToDisplay[i].original || errorsToDisplay[i].message;
    errorsToDisplay[i] = errorToJSON;
  }

  if (sails.config.environment === 'development') {
    result.errorsRaw = errorsToDisplay;
  }

  if (req.wantsJSON) {
    return res.status(result.status).json(result);
  }

  if (redirectTo) {
    req.flash('errors', errors);

    return res.redirect(redirectTo);
  }

  return res.status(result.status).json(result);
}
