'use strict';

/**
 * 403 (Forbidden) Handler
 *
 * Usage:
 * return res.forbidden();
 * return res.forbidden(err);
 * return res.forbidden(err, 'some/specific/forbidden/view');
 * return res.forbidden(err, {
 *  Headers, e.g. Location: '/login'
 *})
 *
 * e.g.:
 * ```
 * return res.forbidden('Access denied.');
 * ```
 */

module.exports = function forbidden (data, options) {
  let req   = this.req,
      res   = this.res,
      sails = req._sails;

  res.status(403);

  loggers.debug_log.verbose('Sending 403 ("Forbidden") response', data || '');

  // http://sailsjs.org/documentation/reference/request-req/req-wants-json
  if (req.wantsJSON) {
    if (_.isObject(options) && _.isObject(options.headers)) {
      Object.keys(options.headers).forEach(header => {
        res.setHeader(header, options.headers[header]);
      });
    }

    return res.json(data);
  }

  options = (typeof options === 'string') ? { view: options } : options || {};

  if (options.view) {
    return res.view(options.view, { data: data });
  } else {
    return res.view('403', { data: data }, function (err, html) {
      if (err) {
        // If the view was missing, ignore the error but provide a verbose log.
        if (err.code === 'E_VIEW_FAILED') {
          sails.log.verbose(
            'res.forbidden() :: Could not locate view for error page (sending JSON instead).  Details: ',err);
        } else {
          sails.log.warn(
            'res.forbidden() :: When attempting to render error page view, an error occured (sending JSON instead).  Details: ', err);
        }

        return res.json(data);
      }

      return res.send(html);
    })
  }
};

