class AdminDisputeService {
    get __MAX_LIMIT__() {
        return 50;
    }

    get __DISPUTE_EMAILS_QUERY__() {
        return `
            SELECT ee.email_id, ee.email_from, ee.email_to, ee.email_text, 
            ee.email_subject, TO_CHAR((ee.created::timestamptz AT TIME ZONE e.timezone), 'Mon DD') as created, ee.email_cc, ee.email_bcc
            FROM event_email ee
            LEFT JOIN event_change ec
            ON ee.event_email_id = ec.event_email_id
            WHERE ee.event_id = e.event_id
            AND ee.reason_type = 'purchase dispute'
            AND ec.purchase_id = lp.purchase_id
        `
    }

    async getDisputeList({ limit = this.__MAX_LIMIT__, offset = 0, status }, fetchAll = false) {
        if (!fetchAll && limit > this.__MAX_LIMIT__) {
            throw { validation: 'Limit can\'t be more ' + DEFAULT_LIMIT };
        }

        const underReviewCondition = `
            (de.has_evidence OR de.submission_count > 0) AND
            d.status IN ('under_review', 'warning_under_review')
        `
        const lostCondition = `
            d.status IN ('lost', 'warning_closed') 
            OR ((de.past_due IS TRUE OR DATE_PART('day', AGE(to_timestamp(de.due_by), NOW())) < 0)
                AND de.has_evidence IS FALSE) 
        `
        const wonCondition = `d.status = 'won'`
        const needsResponseCondition = `
            d.status IN ('needs_response', 'warning_needs_response') 
            AND DATE_PART('day', AGE(to_timestamp(de.due_by), NOW())) >= 0
        `
        const deadlineMissedCondition = `
            (de.past_due IS TRUE AND de.has_evidence IS FALSE) 
            OR DATE_PART('day', AGE(to_timestamp(de.due_by), NOW())) < 0
        `

        const whereClause = `
            ${status === 'needs_response' ? `AND ${needsResponseCondition}` : ''}
            ${status === 'lost' ? `AND ${lostCondition}` : ''}
            ${status === 'won' ? `AND ${wonCondition}` : ''}
            ${status === 'under_review' ? `AND ${underReviewCondition}` : ''}
        `

        const limitOffsetClause = `
            ${limit ? `LIMIT ${limit}` : ''}
            ${offset > 0 ? `OFFSET ${offset}` : ''}
        `


        const query = `SELECT de.past_due,
                e.event_id,
                e.name event_name,
               (COUNT(*) OVER ())::INT total,
               de.has_evidence,
               p.payment_for,
               p.purchase_id,
               pt.roster_team_id,
               TO_CHAR((p.created::timestamptz AT TIME ZONE e.timezone), 'Dy, Mon DD, YYYY HH12:MI:SS AM') purchased,
                COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) AS assigned_tickets_mode,
               el.name "event_location", e.name "event_short_name", e.email "event_email",
               p.email purchaser_email,
               lp.ticket_barcode,
               lp.purchase_id as ticket_purchase_id,
               TO_CHAR(
                    (lp.scanned_at::timestamptz AT TIME ZONE e.timezone),
                    'HH12:MI AM on Mon DD'
               ) ticket_scanned_at,
               d.status,
               d.reason,
               TO_CHAR((p.dispute_created::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY') dispute_created,
               (
                    CASE
                        WHEN d.status = 'lost' THEN 'Lost'
                        WHEN d.status = 'warning_closed' THEN 'Enquiry closed'
                        WHEN d.status = 'won' THEN 'Won'
                        WHEN ${underReviewCondition} THEN 'Under review'
                        WHEN ${deadlineMissedCondition} THEN 'Deadline missed'
                        WHEN ${needsResponseCondition}
                           THEN DATE_PART('day', AGE(to_timestamp(de.due_by), NOW())) || ' days to respond'
                       END
                ) evidence_status,
               ROUND(d.amount / 100, 2)::NUMERIC amount,
                (
                    SELECT COALESCE(JSON_AGG(ee), '[]'::JSON) FROM (${this.__DISPUTE_EMAILS_QUERY__}) AS ee
                ) AS emails
            FROM purchase p
            JOIN stripe.dispute d on d.charge_id = p.stripe_charge_id
            JOIN stripe.dispute_evidence de on de.stripe_dispute_id = d.id
            JOIN event e on p.event_id = e.event_id
            LEFT JOIN "event_location" el 
                ON e.event_id = el.event_id 
                AND el.number = 1
            LEFT JOIN LATERAL (
                SELECT lp.purchase_id,
                       lp.linked_purchase_id,
                       lp.ticket_barcode,
                       lp.scanned_at
                FROM purchase lp
                WHERE (lp.purchase_id = p.purchase_id
                    OR lp.linked_purchase_id = p.purchase_id)
                  AND lp.is_ticket is TRUE
                ORDER BY scanned_at DESC
                LIMIT 1
            ) lp ON TRUE
            LEFT JOIN LATERAL (
                SELECT 
                    pt.roster_team_id,
                    pt.purchase_id
                FROM purchase_team pt
                WHERE pt.purchase_id = p.purchase_id
                ORDER BY pt.roster_team_id DESC
                LIMIT 1
            ) pt ON TRUE
            WHERE p.dispute_created IS NOT NULL
                ${whereClause}
            ORDER BY 
                CASE
                    WHEN d.status IN ('warning_needs_response', 'needs_response') AND DATE_PART('day', AGE(to_timestamp(de.due_by), NOW())) >= 0 THEN 1
                    WHEN d.status IN ('warning_under_review', 'under_review') THEN 2
                    ELSE 3
                END,
                CASE 
                    WHEN d.status IN ('warning_needs_response', 'needs_response') THEN to_timestamp(de.due_by)
                END ASC,
                d.created DESC
            ${fetchAll ? '' : limitOffsetClause}
        `;

        const { rows: disputes } = await Db.query(query);

        return disputes;
    }
}


module.exports = new AdminDisputeService()
