const {POINT_OF_SALES_TYPE} = require("../constants/sales-hub");

class AdminEventTicketsService {
    async updateEventSWFeeForTickets (eventId, eventAppFee = null) {
        await this.#updateEventTicketSWFee(eventId, eventAppFee);
        await SalesHubService.sync.syncPointOfSales(eventId, POINT_OF_SALES_TYPE.TICKETS);
    }

    updateEventTicketsSWFee (eventId, tickets) {
        return Promise.all(tickets.map(async ({application_fee = null, event_ticket_id}) => {
            await this.#updateEventTicketRow(eventId, event_ticket_id, application_fee);
            await SalesHubService.sync.syncTicketProduct(eventId, event_ticket_id);
        }));
    }

    #updateEventTicketSWFee (eventId, eventAppFee = null) {
        const query = knex('event')
            .update({tickets_sw_fee: eventAppFee})
            .where('event_id', eventId);

        return Db.query(query);
    }

    #updateEventTicketRow (eventId, event_ticket_id, application_fee) {
        const query = knex('event_ticket')
            .update({application_fee})
            .where('event_id', eventId)
            .where('event_ticket_id', event_ticket_id);

        return Db.query(query);
    }
}

module.exports = new AdminEventTicketsService();
