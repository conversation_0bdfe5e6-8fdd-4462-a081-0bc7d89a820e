const {PAYMENT_STATUSES } = require('../constants/teams');
const {PAYMENT_TYPE} = require("../constants/payments");
const StripeConnect = require('../lib/StripeConnect');

class AdminPaymentsService {
    constructor (StripeConnect) {
        this.StripeConnect = StripeConnect;
        this.stripe = null;
    }

    get PAID_STATUSES() {
        return [
            PAYMENT_STATUSES.PAID,
            PAYMENT_STATUSES.PARTIAL,
            PAYMENT_STATUSES.PENDING
        ];
    }

    async checkTeamPayment(rosterTeamId) {
        const responseData = {};

        const teamPaymentData = await this.#getTeamPaymentData(rosterTeamId);

        let errors = this.#checkPaidStatuses(teamPaymentData);

        if (teamPaymentData.payments) {
            await this.#checkPayments(teamPaymentData, errors);
        }

        if (!_.isEmpty(errors)) {
            responseData.errors = errors;
        }

        if (teamPaymentData) {
            teamPaymentData.stripe_data = undefined;

            responseData.data = teamPaymentData;
        }

        return responseData;
    }

    async checkPaymentRefunded(paymentId) {
        const payment = await this.#getPaymentData(paymentId);

        return this.#checkChargeRefunded(payment);
    }

    async checkCardPayment(paymentId) {
        const payment = {
            purchase_id: paymentId
        }

        const errors = [];
        const purchaseTeamData = await this.#getPurchaseTeamData(payment, errors);

        if(!purchaseTeamData.stripe_charge_id) {
            errors.push({
                type        : 'payment',
                message     : 'Payment has no stripe charge id',
                data        : {
                    payment_amount              : purchaseTeamData.payment_amount,
                    payment_refunded_amount     : purchaseTeamData.payment_refunded_amount,
                    valid_sum                   : purchaseTeamData.valid_sum,
                    canceled_sum                : purchaseTeamData.canceled_sum
                }
            });

            return errors;
        }

        payment.stripe_charge_id = purchaseTeamData.stripe_charge_id;
        payment.amount = purchaseTeamData.amount;
        payment.amount_refunded = purchaseTeamData.amount_refunded;

        await this.#checkStripeAmount(purchaseTeamData, payment, errors);

        await this.#checkRefundedAmount(payment, errors);

        return errors;
    }

    async #getPaymentData(paymentId) {
        const query =
            `SELECT  
                p.purchase_id "id", 
                p.amount, 
                p.stripe_charge_id "charge", 
                sa.secret_key stripe_secret, 
                e.stripe_statement 
            FROM purchase p  
            LEFT JOIN "event" e 
                ON e.event_id = p.event_id 
            LEFT JOIN "stripe_account" sa 
                ON sa.secret_key = e.stripe_teams_private_key
            WHERE  p.purchase_id = $1`;

        const {rows: [paymentData]} = await Db.query(query, [paymentId])

        return paymentData;
    }

    async #checkChargeRefunded(payment) {
        try {
            await this.#initStripeInstance();

            const charge = await this.stripe.charges.retrieve(payment.charge);

            const is_refunded = (charge?.refunds?.data?.length > 0),
                refund_data = {};

            if (is_refunded) {
                refund_data.refunded_amount = charge.refunds.data[0].amount / 100;
                refund_data.refund_created = charge.refunds.data[0].created * 1000;
            }

            return refund_data;
        } catch (e) {
            loggers.purchase_log.error('[Stripe error]', e.toString());

            throw {
                text: e.toString()
            };
        }
    }

    async #getTeamPaymentData(rosterTeamId) {
        const query = `
            SELECT (SELECT ROW_TO_JSON("d")
                    FROM (SELECT (COALESCE(d.reg_fee, e.reg_fee)::INTEGER +
                                  COALESCE(d.credit_surcharge, e.credit_surcharge, 0) -
                                  COALESCE(rt.discount, 0))                                           AS due_card,
                                 (COALESCE(d.reg_fee, e.reg_fee)::INTEGER - COALESCE(rt.discount, 0)) AS due_check,
                                 rt.status_paid::INTEGER,
                                 COALESCE(rt.discount, 0)                                             AS discount) "d") "team_data",
                   (SELECT ROW_TO_JSON("d")
                    FROM (SELECT sa.secret_key "stripe_secret") "d") AS                                                 stripe_data,
                   SUM(pt.amount - COALESCE(rt.discount, 0))::INTEGER                                                   "paid",
                   (SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(payments)))
                    FROM (SELECT p.purchase_id,
                                 p.amount,
                                 COALESCE(p.amount_refunded, 0)::INTEGER AS amount_refunded,
                                 p.type,
                                 p.payment_for,
                                 p.stripe_charge_id,
                                 p.date_refunded,
                                 p.date_paid,
                                 p.status
                          FROM purchase p
                          WHERE p.purchase_id = pt.purchase_id
                            AND p.event_id = e.event_id
                            AND p.canceled_date IS NULL
                            AND p.amount > 0) payments)                                                                 "payments"
            FROM roster_team rt
                     LEFT JOIN purchase_team pt
                               ON rt.roster_team_id = pt.roster_team_id
                                   AND pt.canceled IS NULL
                     LEFT JOIN purchase p
                               ON p.purchase_id = pt.purchase_id
                                   AND p.amount > 0
                     LEFT JOIN "event" e
                               ON e.event_id = rt.event_id
                     LEFT JOIN division d
                               ON d.division_id = rt.division_id
                     LEFT JOIN "stripe_account" sa
                               ON e.stripe_teams_private_key = sa.secret_key
            WHERE rt.roster_team_id = $1
            GROUP BY pt.purchase_id, p.amount, e.reg_fee, e.credit_surcharge, rt.discount,
                     sa.secret_key, rt.status_paid, e.event_id, d.reg_fee, d.credit_surcharge;`;

        const {rows: [data]} = await Db.query(query, [rosterTeamId]);

        if (_.isEmpty(data)) {
            throw new Error('No data');
        }

        return data;
    }

    #checkPaidStatuses(teamPaymentData) {
        const errors = [];
        const {team_data} = teamPaymentData;

        if (!team_data) {
            throw new Error('No team found');
        }

        if (!teamPaymentData.paid && this.PAID_STATUSES.indexOf(team_data.status_paid) >= 0) {
            errors.push({
                type: 'team',
                message: 'Team marked as PAID, but there is no valid payment for the team',
                descr: `Team status is ${team_data.status_paid} but valid payment not found`,
                data: teamPaymentData
            });

            return errors;
        } else if (teamPaymentData.paid && this.PAID_STATUSES.indexOf(team_data.status_paid) < 0) {
            errors.push({
                type: 'team',
                message: 'Team marked as UNPAID, but there is a valid payment for it',
                descr: `Team status is ${team_data.status_paid} there is a valid payment for this team`,
                data: teamPaymentData
            })
        }

        if (teamPaymentData.paid && team_data.due_card !== teamPaymentData.paid && team_data.due_check !== teamPaymentData.paid) {
            errors.push({
                type: 'payment',
                message: 'Paid wrong amount',
                descr: 'Paid value does not match due value',
                data: {
                    due_card: team_data.due_card,
                    due_check: team_data.due_check,
                    paid: teamPaymentData.paid,
                    payments: teamPaymentData.payments
                }
            });
        }

        if (teamPaymentData.paid && teamPaymentData.payments.length === 0) {
            errors.push({
                type: 'payment',
                message: 'Invalid data constraint for payments',
                descr: 'Valid purchase_team records do not match valid purchase records',
                hint: 'Maybe some of purchase_team rows have not been marked as canceled',
                data: {
                    paid: teamPaymentData.paid,
                    payments: []
                }
            });
        }

        return errors;
    }

    async #checkPayments(teamPaymentData, errors) {
        const {payments, team_data} = teamPaymentData;

        await Promise.all(
            payments.map(async (payment) => {
                if (payment.type === PAYMENT_TYPE.CHECK) {
                    if (payment.date_paid && team_data.status_paid !== PAYMENT_STATUSES.PAID) {
                        errors.push({
                            type: 'payment',
                            message: 'Payment has date_paid but team does not mark as paid',
                            data: payments
                        })
                    }

                    if (!payment.date_paid && team_data.status_paid === PAYMENT_STATUSES.PAID) {
                        errors.push({
                            type: 'payment',
                            message: 'Payment has NO date_paid but team is marked as paid',
                            data: payments
                        });
                    }
                } else if (payment.type === PAYMENT_TYPE.CARD) {
                    await this.#checkStripeAmount(teamPaymentData, payment, errors);

                    await this.#checkRefundedAmount(payment, errors);
                }

                return payment;
            })
        );

        return errors;
    }

    async #checkStripeAmount(teamPaymentData, payment, errors) {
        try {
            await this.#initStripeInstance();

            const charge = await this.stripe.charges.retrieve(payment.stripe_charge_id);

            if(_.isEmpty(charge)) {
                errors.push({
                    type: 'stripe',
                    message: 'Stripe retrieve payment error',
                    descr: 'Charge not found',
                    data: payment
                });

                return null;
            }

            const stripe_amount = charge.amount / 100;

            if ((payment.amount + payment.amount_refunded) !== stripe_amount) {
                payment.error = {
                    type: 'stripe',
                    message: 'Stripe payment amount do not match amount in db',
                    data: {
                        stripe_charge: charge,
                        payment: _.cloneDeep(payment)
                    }
                }
            }

            return charge;
        } catch (err) {
            errors.push({
                type: 'stripe',
                message: 'Stripe error',
                descr: err.message,
                data: {
                    stripe_data: teamPaymentData.stripe_data,
                    payment: payment
                }
            });
        }
    }

    async #checkRefundedAmount(payment, errors) {
        const purchaseTeamData = await this.#getPurchaseTeamData(payment, errors);

        if(purchaseTeamData.payment_amount !== purchaseTeamData.valid_sum) {
            errors.push({
                type: 'payment',
                message: 'Payment constraint error',
                descr: 'Purchase amount does not match purchase_team amounts ( '
                    + purchaseTeamData.payment_amount
                    + ' !== ' + purchaseTeamData.valid_sum + ' )',
                data: {
                    payment: payment,
                    query_data: purchaseTeamData
                }
            });
        }

        if(purchaseTeamData.payment_refunded_amount !== purchaseTeamData.canceled_sum) {
            errors.push({
                type: 'payment',
                message: 'Payment constraint error',
                descr: 'Purchase refunded amount does not match purchase_team '
                    + ' canceled amounts ( '
                    + purchaseTeamData.payment_refunded_amount
                    + ' !== ' + purchaseTeamData.canceled_sum + ' )',
                data: {
                    payment: payment,
                    query_data: purchaseTeamData
                }
            });
        }
    }

    async #getPurchaseTeamData(payment, errors) {
        const query =
            `SELECT
                p.amount::INTEGER                AS payment_amount,
                p.amount_refunded::INTEGER       AS payment_refunded_amount,
                sum(pt_valid.amount)::INTEGER    AS "valid_sum",
                sum(pt_canceled.amount)::INTEGER AS "canceled_sum",
                p.stripe_charge_id
             FROM purchase p
            LEFT JOIN purchase_team pt_valid
                ON pt_valid.purchase_id = p.purchase_id
                    AND pt_valid.canceled IS NULL
            LEFT JOIN purchase_team pt_canceled
                ON pt_canceled.purchase_id = p.purchase_id
                    AND pt_canceled.canceled IS NOT NULL
            WHERE p.purchase_id = $1
            GROUP BY p.amount, p.amount_refunded, p.stripe_charge_id`;

        const {rows: [purchaseTeamData]} = await Db.query(query, [payment.purchase_id]);

        if(_.isEmpty(purchaseTeamData)) {
            errors.push({
                type: 'db',
                message: 'No data or invalid query',
                data: {
                    query: query,
                    payment: payment
                }
            });

            return {};
        }

        return purchaseTeamData;
    }

    async #initStripeInstance() {
        if(!this.stripe) {
            this.stripe = await this.StripeConnect.getInstanceV2();
        }
    }
}

module.exports = new AdminPaymentsService(StripeConnect);
