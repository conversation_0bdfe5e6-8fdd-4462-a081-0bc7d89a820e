'use strict';

const co 				= require('co');
const swUtils 			= require('../lib/swUtils');
const argv 				= require('optimist').argv;
const STATS_BASE_URL 	= `${sails.config.urls.home_page.baseUrl}/#/event/`;

const StripeAccStatistics       = require('./admin-statistics/_StripeAccStatistics');
const FinancialReportService    = require('./admin-statistics/__FinancialReportService');

const { FEE_PAYER } = require('../constants/payments');

class AdminStatisticsService {
	constructor () {}

	get STRIPE_CONNECT_SETTINGS_KEY () {
	    return sails.config.environment === 'production'
                ? 'stripe_connect'
                : 'stripe_connect_dev';
    }

	get __EVENTS_LIST_SQL__ () {
		return (
			`SELECT 
			     e."event_id", 
			     e."long_name" "name", 
			     (EXTRACT(EPOCH FROM e."date_start") * 1000)::BIGINT "date_start",

			    (teams_settings IS NOT NULL) "teams_sw_acc",
				(tickets_settings IS NOT NULL) "tickets_sw_acc"
			 FROM "event" e
			 INNER JOIN "purchase" p 
			 	ON p."event_id" = e."event_id"
			 	AND p."created" >= ($1)::DATE
			 	AND p."created" <= ($2)::DATE
			 	AND p."status" <> 'canceled'
			 LEFT JOIN "settings" teams_settings 
			 	ON teams_settings."key" = 'stripe_connect'
			 	AND teams_settings."value"->>'secret_key' = e."stripe_teams_private_key"
			 LEFT JOIN "settings" tickets_settings 
			 	ON tickets_settings."key" = 'stripe_connect'
			 	AND tickets_settings."value"->>'secret_key' = e."stripe_tickets_private_key"
			 WHERE e."is_test" IS NOT TRUE
			 	AND (
			 		e."tickets_use_connect" IS TRUE OR 
			 		e."teams_use_connect" IS TRUE OR 
			 		teams_settings IS NOT NULL OR 
			 		tickets_settings IS NOT NULL
			 	)
			 GROUP BY e."event_id", e."long_name", teams_settings.*, tickets_settings.*
			 ORDER BY e."date_start" ASC`
		);
	}

	get __DEFAULT_STATS__ () {
		return {
			stripe 	 : {
                taxed_from_sw_real  : 0,
                taxed_from_sw       : 0,
				taxed_from_eo       : 0,
				delta 			    : 0,
                delta_real          : 0
			},
			sw_fee 	 : {
				target 				: 0,
				collected 			: 0,
                collected_limited   : 0,
				balance 			: 0
			},
			disputes  				: 0,
			transfers 				: 0,
			payments_qty 			: 0,
			collected_fee 			: 0
		}
	}

	get financialReport () {
	    return FinancialReportService;
    }

    get stripe () {
        return StripeAccStatistics;
    }

	getTicketStatisticsLink (eventID) {
		return STATS_BASE_URL + eventID + '/tickets-stats'
	}

	get DATE_REG_EXP () {
		return /^(20[\d]{2})-(\d{1,2})-(\d{1,2})$/g
	}

	__validateDate__ (date) {
		if (!this.DATE_REG_EXP.test(date)) {
			throw new Error('Invalid date format!');
		}

		let [, year, month, day] = this.DATE_REG_EXP.exec(date);

		year 	= parseInt(year, 10);
		month 	= parseInt(month, 10);
		day 	= parseInt(day, 10);

		if (!year || !month || !day) {
			throw new Error('Invalid date parts!');
		}

		if (month < 1 || month > 12) {
			throw new Error('Invalid month!');
		}

		if (day < 1 || day > 31) {
			throw new Error('Invalid day!');
		}
	}

	/*
	* Return events that have purchase rows in the passed year, 
	* are Connected via stripe, or have payments to Marc's account
	*/
	eventsList (dateFrom, dateTo) {
		return Promise.resolve().then(() => {
			this.__validateDate__(dateFrom);
			this.__validateDate__(dateTo);

			let ticketsDefaultStats = Object.assign(this.__DEFAULT_STATS__.sw_fee, { sw_delta: 0, ua_delta: 0 });

			let boothsDefaultStats = _.omit(this.__DEFAULT_STATS__.sw_fee, ['collected_limited']);

			return Db.query(this.__EVENTS_LIST_SQL__, [dateFrom, dateTo])
			.then(result => result.rows.map(event => {	

				event.date_start 	= +event.date_start;
				event.link 		 	= this.getTicketStatisticsLink(event.event_id);
				event.teams 		= this.__DEFAULT_STATS__;
				event.tickets 		= ticketsDefaultStats;
				event.booths 		= boothsDefaultStats;
				event.income 		= 0;	

				return event;
			}));
		})
	}

	eventStatistics (eventID, dateFrom, dateTo) {
		return co(function* () {
			if ((toString.call(eventID) !== '[object Number]') || (eventID % 1 !== 0) || (eventID <= 0)) {
				return Promise.reject({ validation: 'Event ID should be a positive Integer' });
			}

			this.__validateDate__(dateFrom);
			this.__validateDate__(dateTo);

			let df = dateFrom;
			let dt = dateTo;

			const approx = swUtils.normalizeNumber.bind(swUtils);

			let eventData = yield (this.__getEventData__(eventID));

			/* NOTE: booths' payments are collected to the same account as teams' payments */
			let teamsEOAccUsed 		= !(eventData.teams_connected || eventData.teams_sw_acc),
				ticketsEOAccUsed 	= !(eventData.tickets_connected || eventData.tickets_sw_acc);

			let teams 	= {}, 
				tickets = {}, 
				booths 	= {};

			if (!teamsEOAccUsed) {
				teams = yield ({
					collected_fee: 	 this.__getCollectedFee__(eventID, 'teams', df, dt),
					stripe: {
                        taxed_from_sw_real  : this.__getRealStripeFeeForSWAcc__(eventID, 'teams', df, dt),
						taxed_from_eo       : this.__getStripeFeeForEOAcc__(eventID, 'teams', df, dt),
                        taxed_from_sw       : this.__getStripeFeeForSWAcc__(eventID, eventData, 'teams', df, dt)
					},
					sw_fee: {
						_escrow  			: TeamsPaymentService.getSWFeeEscrowTarget(eventID, false /* count disputes*/, true /* get summands */),
						target 				: 0 /* See details below */,
						collected 			: TeamsPaymentService.getCollectedSWFee(eventID, 'all', df, dt)
					},
					disputes 		: this.__getDisputes__(eventID, 'teams', df, dt),
					transfers 		: eventData.teams_sw_acc?this.__getTransfersRest__(eventID, 'teams', df, dt):{ qty: 0 },
					payments_qty 	: 0 /* See details below */
				});

				teams.sw_fee.target = approx(teams.sw_fee._escrow.summands.teams_qty * teams.sw_fee._escrow.summands.sw_fee); //teams.sw_fee._escrow.escrow;
				teams.payments_qty 	= teams.sw_fee._escrow.summands.teams_qty;

				delete teams.sw_fee._escrow;

				teams.stripe.delta_real = approx(this.__getStripeDeltaReal__(eventData.season, teams.stripe));
                teams.stripe.delta      = approx(this.__getStripeDelta__(eventData.season, teams.stripe));

				teams.sw_fee.balance 	        = approx(teams.sw_fee.collected - teams.sw_fee.target);
                teams.sw_fee.collected_limited  = this.__getCollectedLimited__(teams.sw_fee);

				/* Add balance, because we have only collected fee in "transfers", not target (required) */
				if (teams.transfers.qty === 0) {
					teams.transfers = 0;
				} else {
					teams.transfers = approx(teams.transfers.rest + teams.sw_fee.balance);
				}				

				booths = yield ({
					collected_fee: 	 this.__getCollectedFee__(eventID, 'booths', df, dt),
					stripe: {
                        taxed_from_sw_real  : this.__getRealStripeFeeForSWAcc__(eventID, 'booths', df, dt),
						taxed_from_eo       : this.__getStripeFeeForEOAcc__(eventID, 'booths', df, dt),
                        taxed_from_sw       : this.__getStripeFeeForSWAcc__(eventID, eventData, 'booths', df, dt)
					},
					sw_fee: {
						target 				: 0,
						collected_cards 	: 0,
						collected_checks 	: 0,
					},
					disputes 		: this.__getDisputes__(eventID, 'booths', df, dt),
					transfers 		: eventData.teams_sw_acc?this.__getTransfersRest__(eventID, 'booths', df, dt):{ qty: 0 },
					payments_qty 	: this.__getPaymentItemsQty__(eventID, 'booths', df, dt)
				});

				booths.stripe.delta_real = approx(this.__getStripeDeltaReal__(eventData.season, booths.stripe));
                booths.stripe.delta      = approx(this.__getStripeDelta__(eventData.season, booths.stripe));
				booths.sw_fee.balance 	 = 0;
				booths.transfers 		 = (booths.transfers.qty === 0)?0:booths.transfers.rest;

			}

			if (!ticketsEOAccUsed) {
				tickets = yield ({
					collected_fee: 	 this.__getCollectedFee__(eventID, 'tickets', df, dt),
					stripe: {
                        taxed_from_sw_real: this.__getRealStripeFeeForSWAcc__(eventID, 'tickets', df, dt),
						taxed_from_eo     : this.__getStripeFeeForEOAcc__(eventID, 'tickets', df, dt),
                        taxed_from_sw     : this.__getStripeFeeForSWAcc__(eventID, eventData, 'tickets', df, dt)
					},
					sw_fee: {
						target 				: this.__getTargetSWFee__(eventID, 'tickets', df, dt),
						collected 			: this.__getCollectedSWFee__(eventID, 'tickets', df, dt)
					},
					disputes 		: this.__getDisputes__(eventID, 'tickets', df, dt),
					transfers 		: eventData.tickets_sw_acc?this.__getTransfersRest__(eventID, 'tickets', df, dt):{ qty: 0 },
					payments_qty 	: this.__getPaymentItemsQty__(eventID, 'tickets', df, dt)
				});

				tickets.stripe.delta_real   = approx(this.__getStripeDeltaReal__(eventData.season, tickets.stripe));
                tickets.stripe.delta        = approx(this.__getStripeDelta__(eventData.season, tickets.stripe));
				tickets.sw_fee.balance 		= approx(tickets.sw_fee.collected - tickets.sw_fee.target);

                tickets.sw_fee.collected_limited    = this.__getCollectedLimited__(tickets.sw_fee);
                tickets.sw_fee.ua_delta             = this.__getSWFeeUADelta__(eventData, tickets.sw_fee);
                tickets.sw_fee.sw_delta             = this.__getSWFeeSWDelta__(eventData, tickets.sw_fee);

				/* Add balance, because we have only collected fee in "transfers", not target (required) */
				if (tickets.transfers.qty === 0) {
					tickets.transfers = 0;
				} else {
					tickets.transfers = approx(tickets.transfers.rest + tickets.sw_fee.balance);
				}

				yield this.__validateSWFeeDeltas__(eventID, eventData, tickets.sw_fee);
			}

			return { teams, tickets, booths };

		}.bind(this))
	}

	__getSWFeeSWPercent__ (eventData) {
	    return Math.round((1 - Number(eventData.ua_percent)) * 1000000) / 1000000;
    }

    __getSWFeeSWDelta__ (eventData, swFee) {
        if(eventData.ua_percent === 0) {
            return 0;
        }

        let percent = this.__getSWFeeSWPercent__(eventData);

        return Math.round((swFee.collected_limited * percent) * 100) / 100;
    }

    __getSWFeeUADelta__ (eventData, swFee) {
	    if(eventData.ua_percent === 0) {
            return 0;
        }

        return Math.round((swFee.collected_limited * Number(eventData.ua_percent)) * 100) / 100;
    }

	__getCollectedLimited__ (swFee) {
	    return swFee.collected > swFee.target ? swFee.target : swFee.collected;
    }

    __getStripeDelta__ (eventSeason, stripeData) {
        if(eventSeason >= 2019) {
            return stripeData.taxed_from_eo - stripeData.taxed_from_sw;
        } else {
            return 0;
        }
    }

	__getStripeDeltaReal__ (eventSeason, stripeData) {
	    if(eventSeason >= 2019) {
	        return stripeData.taxed_from_sw - stripeData.taxed_from_sw_real;
        } else {
            return stripeData.taxed_from_eo - stripeData.taxed_from_sw_real;
        }
    }

	/* === NEW STATISTICS === */
	__getCollectedFee__ (eventID, paymentFor, dateFrom, dateTo) {
		return Db.query(
			`SELECT COALESCE(SUM(sc."collected_fee"), 0) "fee"
			 FROM "purchase" p 
			 INNER JOIN "stripe_charge" sc ON sc.stripe_charge_id = p.stripe_charge_id
			 WHERE p."event_id" = $1
			 	AND p."payment_for" = $2 
			 	AND p."status" <> 'canceled'
			 	AND p."type" <> 'waitlist'
			 	AND p."created" >= ($3)::DATE
			 	AND p."created" <= ($4)::DATE`,
			[eventID, paymentFor, dateFrom, dateTo]
		).then(result => result.rows[0] && Number(result.rows[0].fee) || 0)
	}

	__getDisputes__ (eventID, paymentFor, dateFrom, dateTo) {
		let disputeFee = StripeService.DISPUTE_FEE;

		return Db.query(
			`SELECT 
			    COALESCE(COUNT(p.*), 0) "qty"
			FROM "purchase" p 
			WHERE p."event_id" = $1 
				AND p."payment_for" = $2
				AND p."dispute_created" IS NOT NULL
			    AND p."dispute_status" IN ('lost', 'pending')
			    AND p."type" <> 'waitlist'
			    AND p."dispute_created" >= ($3)::DATE
			 	AND p."dispute_created" <= ($4)::DATE`,
			[eventID, paymentFor, dateFrom, dateTo]
		).then(result => {
			let data = result.rows[0] || {};

			let qty = parseInt(data.qty, 10);

			return swUtils.normalizeNumber(disputeFee * qty) * (-1);
		})
	}

	__getRealStripeFeeForSWAcc__ (eventID, paymentFor, dateFrom, dateTo) {
	    let query = squel.select().from('purchase', 'p')
            .field('COALESCE(SUM(sch."fee"), 0)', 'fee')
            .join('stripe_charge', 'sch', 'sch."stripe_charge_id" = p."stripe_charge_id"')
            .where('p."event_id" = ?', eventID)
            .where(`p."status" = 'paid'`)
            .where(`p."type" = 'card'`)
            .where('p."payment_for" = ?', paymentFor)
            .where('p."created" >= (?)::DATE', dateFrom)
            .where('p."created" <= (?)::DATE', dateTo);

	    if(paymentFor === 'tickets') {
	        query.where('p."is_payment" IS TRUE');
        }

		return Db.query(query).then(result => result.rows[0] && Number(result.rows[0].fee) || 0)
	}

    async __getStripeFeeForSWAcc__ (eventID, eventData, paymentFor, dateFrom, dateTo) {
	    if(eventData.season < 2019) {
	        return 0;
        }

        const FIXED = StripeService.DEFAULT_STRIPE_FIXED;
	    const PERCENT_BEFORE_2021_01_01 = StripeService.STRIPE_PERCENT_MIN_BEFORE_2021_01_01;
	    const PERCENT = StripeService.STRIPE_PERCENT_MIN_DEFAULT
	    const PERCENT_CHANGE_DATE = StripeService.STRIPE_PERCENT_CHANGE_DATE_2020_12_31;

        let defaultFormula = 
            squel.str(
                `COALESCE(SUM(ROUND(p."amount" * (CASE WHEN p."created" > ?::DATE THEN ? ELSE ? END)::NUMERIC + ?, 2)), 0)`,
                PERCENT_CHANGE_DATE,
                PERCENT,
                PERCENT_BEFORE_2021_01_01,
                FIXED
            );

        let query = squel.select().from('purchase', 'p')
            .where(`p."status" = 'paid'`)
            .where('p."event_id" = ?', eventID)
            .where(`p."type" = 'card'`)
            .where('p."payment_for" = ?', paymentFor)
            .where('p."created" >= (?)::DATE', dateFrom)
            .where('p."created" <= (?)::DATE', dateTo);

        if(paymentFor === 'teams' || paymentFor === 'booths') {
            query.field(defaultFormula, 'fee');
        } else if(paymentFor === 'tickets') {  
            let {stripe_fee_payer: stripePayer} = eventData;

            let formula = null;

            if (stripePayer === FEE_PAYER.BUYER) {
                formula = squel.str(
                    `COALESCE(SUM(
                        ROUND((p."amount" - p."stripe_fee" + ?) / (1.0 - (CASE WHEN p."created" > ?::DATE THEN ? ELSE ? END)::NUMERIC) - (p."amount" - p."stripe_fee"), 2)
                    ), 0)`, FIXED, PERCENT_CHANGE_DATE, PERCENT, PERCENT_BEFORE_2021_01_01);
            } else {
                formula = defaultFormula;
            }
            
            query.field(formula, 'fee')
                 .where(`p."is_payment" IS TRUE`)
        } else {
            throw new Error(`Unknown item type "${paymentFor}"`);
        }

        return Db.query(query).then(result => result.rows[0] && Number(result.rows[0].fee) || 0);
    }

	__getStripeFeeForEOAcc__ (eventID, paymentFor, dateFrom, dateTo) {
	    let query = squel.select().from('purchase', 'p')
            .field('COALESCE(SUM(p."stripe_fee"), 0)', 'fee')
            .where('p."event_id" = ?', eventID)
            .where(`p."status" <> 'canceled'`)
            .where(`p."type" <> 'waitlist'`)
            .where('p."payment_for" = ?', paymentFor)
            .where('p."created" >= (?)::DATE', dateFrom)
            .where('p."created" <= (?)::DATE', dateTo);

        if(paymentFor === 'tickets') {
            query.where('p."is_payment" IS TRUE');
        }

		return Db.query(query).then(result => result.rows[0] && Number(result.rows[0].fee) || 0)
	}

	__getTargetSWFee__ (eventID, paymentFor, dateFrom, dateTo) {
		let selectBlock, joinBlock;

		/* No in use for teams right now */
		if (paymentFor === 'teams') {
			selectBlock = 
				`COALESCE(COUNT(pt.*), 0) * COALESCE(e."teams_entry_sw_fee", 0)`;
			joinBlock = 
				`INNER JOIN "purchase_team" pt 
					ON pt."purchase_id" = p."purchase_id"
					AND pt."canceled" IS NULL`;
		} else if (paymentFor === 'tickets') {

            /**
             * NOTE: we do not collect sw fee for camps' check payments
             */
            
            /**
             * ∑ items * fee
             * items -> "purchase_ticket"."quantity"
             * fee -> "event_ticket"."application_fee" OR "event"."tickets_sw_fee" (the default one)
             */
			selectBlock = 
                `COALESCE(
                    SUM(
                        (CASE WHEN (e."ticket_camps_registration" IS TRUE AND p."type" = 'check') THEN 0 ELSE pt."quantity" END)
                        *
                        COALESCE(NULLIF(et."application_fee", 0), e."tickets_sw_fee")
                    ), 
                    0
                )`;

			joinBlock = 
				`INNER JOIN "purchase_ticket" pt 
					ON pt."purchase_id" = p."purchase_id" 
					AND pt."canceled" IS NULL
				 INNER JOIN "event_ticket" et 
				 	ON et."event_ticket_id" = pt."event_ticket_id"`;
		}

		let query = 
			`SELECT 
				(${selectBlock}) "fee"
			 FROM "purchase" p 
			 INNER JOIN "event" e 
			 	ON e."event_id" = p."event_id"
			 ${joinBlock}
			 WHERE p."event_id" = $1
			 	AND p."payment_for" = $2 
			 	AND p."status" <> 'canceled'
			 	AND p."type" <> 'waitlist'
			 	AND p."created" >= ($3)::DATE
			 	AND p."created" <= ($4)::DATE
			 GROUP BY e."event_id"`;

		return Db.query(query, [eventID, paymentFor, dateFrom, dateTo])
		.then(result => result.rows[0] && Number(result.rows[0].fee) || 0)
	}

	__getCollectedSWFee__ (eventID, paymentFor, dateFrom, dateTo) {
		const COLLECTED = 'COALESCE(SUM(p."additional_fee_amount"), 0)';
		const ADDITIONAL = 'COALESCE(SUM(p."collected_sw_fee"), 0)';

		let query = squel.select().from('purchase', 'p')
            .field(`COALESCE(${COLLECTED} + ${ADDITIONAL}, 0)`, 'fee')
            .where('p."event_id" = ?', eventID)
            .where('p."status" <> \'canceled\'')
            .where('p."payment_for" = ?', paymentFor)
            .where('p."type" = \'card\'')
            .where('p."created" >= (?)::DATE', dateFrom)
            .where('p."created" <= (?)::DATE', dateTo);

		if(paymentFor === 'tickets') {
		    query.where('p."is_payment" IS TRUE');
        }

		return Db.query(query).then(result => result.rows[0] && Number(result.rows[0].fee) || 0);
	}

	__getEventData__ (eventID) {
		return Db.query(
			`SELECT 
				e."teams_use_connect" "teams_connected",
				e."tickets_use_connect" "tickets_connected",

				(e."teams_settings" IS NOT NULL) "teams_sw_acc",
				(e."tickets_settings" IS NOT NULL) "tickets_sw_acc",

				NULLIF(e."stripe_teams_fixed", 0) "stripe_teams_fixed",
				NULLIF(e."stripe_tickets_fixed", 0) "stripe_tickets_fixed",

				(NULLIF((s."value"->>'percent')::NUMERIC, 0) / 100) "sw_percent",
				e."season",
				e."teams_sw_balance",
				e."tickets_sw_balance",
				
				CASE 
				    WHEN e."tickets_sw_fee" = 0 THEN 0
				    ELSE ROUND((e."tickets_sw_fee_internal" * 100 / e."tickets_sw_fee") / 100, 4)
				END "ua_percent",
							
				e."tickets_sw_fee_payer" "stripe_fee_payer",
				e."stripe_tickets_fee_payer" "sw_fee_payer",
				(e."stripe_sw_percent" / 100) "stripe_sw_percent"
			 FROM "event" e 
			 LEFT JOIN "settings" teams_settings 
			 	ON teams_settings."key" = 'stripe_connect'
			 	AND teams_settings."value"->>'secret_key' = e."stripe_teams_private_key"
			 LEFT JOIN "settings" tickets_settings 
			 	ON tickets_settings."key" = 'stripe_connect'
			 	AND tickets_settings."value"->>'secret_key' = e."stripe_tickets_private_key"
			 INNER JOIN "settings" s 
			 	ON s."key" = $2
			 WHERE e."event_id" = $1`,
			[eventID, this.STRIPE_CONNECT_SETTINGS_KEY]
		).then(result => result.rows[0] || null)
		.then(eventData => {
			if (eventData !== null) {
				// TODO: move to constant
				['stripe_teams_fixed', 'stripe_tickets_fixed', 'sw_percent'].forEach(field => {
					let val = eventData[field];

					eventData[field] = (val === null)?val:+val;
				})
			}

			return eventData
		})
	}

	/**
	* Counts the amount of funds that has been left (not transfered to EO) on the SW Stripe Acc.
	* The components of the formula:
	* 	"amount" 			- amount of funds that is received on the SW Acc. (Consider "purchase"."amount" value as trusty)
	* 	"eo_stripe_fee" 	- Stripe Fee that EO should pay (usually 2.9% + $0.3 per charge) (Consider "purchase"."stripe_fee" value as trusty)
	* 	"collected_sw_fee"  - SW tax per team registraion/ticket purchase that was collected from card/ach charge
	* 	"transfers" 		- the total amount of funds transfered to EO from the SW Acc.
	* The final formula is: 
	*	"rest" = "amount" - "eo_stripe_fee" - "collected_sw_fee" - "transfers";
	*/
	__getTransfersRest__ (eventID, paymentFor, dateFrom, dateTo) {
		/*
			"non_card_sw_fee" - we did not have "balance" logic for old events, so we just populated
				cash payments rows with sw fee value that we might have collected
		*/

		return Db.query(
			`SELECT 
				(e."payment_subtotal" - e."non_card_sw_fee") "subtotal",
				e."transfer"->>'sum' "transfered_amount",
				e."transfer"->>'qty' "transfers_qty"
			FROM (
				SELECT (
					SELECT COALESCE(
						SUM(p."amount" - COALESCE(p."stripe_fee", 0) - COALESCE(p."collected_sw_fee", 0)), 0
					 )
					FROM "purchase" p 
					WHERE p."event_id" = e."event_id"
					 	AND p."type" IN ('card')
					 	AND p."status" <> 'canceled'
					 	AND p."type" <> 'waitlist'
					 	AND p."created" >= ($3)::DATE
			 			AND p."created" <= ($4)::DATE
					 	AND p."payment_for" = $2
				) "payment_subtotal", (
					SELECT ROW_TO_JSON("d")
					FROM (
						SELECT 
							COALESCE(SUM(st."amount"), 0) "sum",
							COUNT(st.*) "qty"
						FROM "stripe_transfer" st 
				    	WHERE st."event_id" = e."event_id"
				    		AND st."transfer_type" IN ('stripe', 'check', 'internal')
				    		AND st."date_canceled" IS NULL
				    		AND st."created" >= ($3)::DATE
			 				AND st."created" <= ($4)::DATE
				    		AND st."payment_for" = $2
				    ) "d"
				) "transfer", (
					SELECT COALESCE(SUM(p."collected_sw_fee"), 0)
					FROM "purchase" p 
					WHERE p."event_id" = e."event_id"
					 	AND p."type" NOT IN ('card')
					 	AND p."status" <> 'canceled'
					 	AND p."type" <> 'waitlist'
					 	AND p."created" >= ($3)::DATE
			 			AND p."created" <= ($4)::DATE
					 	AND p."payment_for" = $2
				) "non_card_sw_fee"
				FROM "event" e 
				WHERE e."event_id" = $1
			) "e"`,
			[eventID, paymentFor, dateFrom, dateTo]
		).then(result => result.rows[0] || null)
		.then(data => {
			if (data === null) {
				return Promise.reject(new Error('No data found for specified event ID'));
			} 

			let subtotal = Number(data.subtotal) || 0,
				trAmount = Number(data.transfered_amount) || 0,
				qty 	 = Number(data.transfers_qty) || 0,
				rest 	 = 0;
 
			if (qty > 0) {
				rest = swUtils.normalizeNumber(subtotal - trAmount);
			}

			return { rest, qty };
		})
	}

	/* This works only for Tickets and Booths */
	__getPaymentItemsQty__ (eventID, paymentFor, dateFrom, dateTo) {
		let joinBlock;

		if (paymentFor === 'tickets') {
			joinBlock = 
				`INNER JOIN "purchase_ticket" "item" 
					ON "item"."purchase_id" = "p"."purchase_id"
					AND "item"."canceled" IS NULL`
		} else if (paymentFor === 'booths') {
			joinBlock = 
				`INNER JOIN "purchase_booth" "item"
					ON "item"."purchase_id" = "p"."purchase_id"`
		} else {
			return Promise.reject(new Error(`Unknows items "${paymentFor}"`));
		}


		return Db.query(
			`SELECT COALESCE(SUM("item"."quantity"), 0) "qty"
			 FROM "purchase" p 
			 ${joinBlock}
			 WHERE p."event_id" = $1
			 	AND p."payment_for" = $2 
			 	AND p."status" <> 'canceled'
			 	AND p."type" <> 'waitlist'
			 	AND p."created" >= ($3)::DATE
			 	AND p."created" <= ($4)::DATE`,
			[eventID, paymentFor, dateFrom, dateTo]
		).then(res => (res.rows[0] && Number(res.rows[0].qty)) || 0)
	}

	__validateSWFeeDeltas__ (eventID, eventData, swFee) {
	    let deltasSum = Math.round((swFee.sw_delta + swFee.ua_delta) * 100) / 100;
	    let swPercent = this.__getSWFeeSWPercent__(eventData);

	    if(deltasSum !== swFee.collected_limited) {
            return EmailService.sendEmail({
                from        : '"SportWrench" <<EMAIL>>',
                to          : '"sw debug" <<EMAIL>>',
                subject     : 'ΔUA and ΔSW validation error',
                text        : `
                    Event ID: ${eventID}. 
                    UA%: ${Number(eventData.ua_percent)}. 
                    ΔUA: ${Number(swFee.ua_delta)}.
                    %SW: ${Number(swPercent)}.
                    ΔSW: ${Number(swFee.sw_delta)}. `
            }).catch(ErrorSender.defaultError.bind(ErrorSender));
        }

	    return Promise.resolve();
	}
}

module.exports = new AdminStatisticsService();
