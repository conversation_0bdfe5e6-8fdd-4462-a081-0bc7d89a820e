'use strict';

const PaymentCommonService = require('./booths/__PaymentCommonService');
const PaymentService = require('./booths/__PaymentService');
const PaymentTypeChangeService = require('./booths/__PaymentTypeChangeService');
const ExhibitorPaymentService = require('./booths/__ExhibitorPaymentService');
const PaymentsNotificationsService = require('./booths/__PaymentNotificationsService');
const RefundService = require('./booths/__RefundService');
const PaymentStatisticService = require('./booths/__PaymentStatisticService');
const BoothsStripeMetadataService = require('./booths/__BoothsStripeMetadataService');

const swUtils = require('../lib/swUtils');

class BoothsService {

    constructor () {}

    get paymentUtils () {
        return PaymentCommonService;
    }

    get payment () {
        return new PaymentService(PaymentCommonService, swUtils);
    }

    get paymentTypeChange () {
        return new PaymentTypeChangeService(PaymentCommonService, swUtils)
    }

    get exhibitorPayment () {
        return new ExhibitorPaymentService(PaymentCommonService, swUtils);
    }

    get notifications () {
        return new PaymentsNotificationsService(PaymentCommonService);
    }

    get refund () {
        return new RefundService(PaymentCommonService);
    }

    get metadata () {
        return BoothsStripeMetadataService;
    }

    get paymentStatistic () {
        return new PaymentStatisticService(swUtils);
    }
}

module.exports = new BoothsService();
