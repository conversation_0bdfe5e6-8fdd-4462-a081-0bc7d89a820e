'use strict';

const ClubInvoiceEditingService = require('./club-invoice/_ClubInvoiceEditingService');
const ClubInvoiceListService = require('./club-invoice/_ClubInvoiceListService');
const ClubInvoicePaymentService = require('./club-invoice/_ClubInvoicePaymentService');
const ClubInvoiceNotificationService = require('./club-invoice/_ClubInvoiceNotificationService');
const ClubInvoiceDetailsService = require('./club-invoice/_ClubInvoiceDetailsService');

class ClubInvoiceService {

    get editing() {
        return ClubInvoiceEditingService;
    }

    get list() {
        return ClubInvoiceListService;
    }

    get details() {
        return ClubInvoiceDetailsService;
    }

    get payment() {
        return ClubInvoicePaymentService;
    }

    get notification() {
        return ClubInvoiceNotificationService;
    }

    clubsList(eventID) {
        if (!eventID) {
            throw new Error('Event ID required');
        }

        let query = knex('roster_club AS rc')
            .select(['rc.roster_club_id','rc.club_name'])
            .where('rc.event_id', eventID)
            .whereRaw('rc.deleted IS NULL')
            .whereRaw('rc.is_virtual IS NOT TRUE');

        return Db.query(query).then(result => result.rows || []);
    }

}

module.exports = new ClubInvoiceService();
