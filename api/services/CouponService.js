const CouponListService = require('./coupon/_ListService');
const PurchaseService = require('./coupon/_PurchaseService');
const CouponUpdateService = require('./coupon/_CouponUpdateService');
const CouponSendingService = require('./coupon/_SendingService');
const CouponGenerationService = require('./coupon/_GenerationService');
const CouponReceiverService = require('./coupon/_ReceiversService');
const CouponHistoryService = require('./coupon/_HistoryService');

class CouponService {

    get purchase () {
        return PurchaseService;
    }

    get couponUpdate () {
        return CouponUpdateService;
    }

    get sending () {
        return CouponSendingService;
    }

    get generation () {
        return CouponGenerationService;
    }

    get list () {
        return CouponListService;
    }

    get receiver () {
        return CouponReceiverService;
    }

    get history () {
        return CouponHistoryService;
    }

    get VALID_TICKET_TYPES() {
        return [
            TicketsService.EVENT_TICKET_TYPE.DAILY,
            TicketsService.EVENT_TICKET_TYPE.WEEKEND,
        ];
    }

    get GENERATION_MODES() {
        return {
            APPEND: 'append',
        };
    }

    get COUPON_RECEIVER_TYPES() {
        return {
            TEAMS: 'teams',
            CUSTOM: 'custom'
        };
    }

    async getTicketTypes(eventID) {
        const query = knex('event_ticket as et')
            .select(
                'event_ticket_id', 'label', 'short_label',
                knex.raw(`
                    (SELECT ARRAY_AGG(
                        TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'Dy, Mon DD')
                    ) FROM JSONB_OBJECT_KEYS(et.valid_dates) vd) "valid_dates"
                `)
            )
            .where('event_id', eventID)
            .whereIn('ticket_type', this.VALID_TICKET_TYPES)
            .orderBy('sort_order')
        const result = await Db.query(query);
        return result.rows;
    }
}

module.exports = new CouponService();
