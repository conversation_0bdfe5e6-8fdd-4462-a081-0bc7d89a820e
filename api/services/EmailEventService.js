'use strict';

class EmailEventService {
  constructor () {}

  getEmailHtml (eventEmailID) {
      if(!eventEmailID) {
          return Promise.reject({ validation: 'Event Email ID required' });
      }

      let query = squel.select().from('event_email', 'ee')
          .field('ee.email_html', 'html')
          .field('ee.email_text', 'text')
          .where('ee.event_email_id = ?', eventEmailID);

      return Db.query(query).then(result => {
          return (result.rows[0] && (result.rows[0].html || result.rows[0].text) || null);
      });
  }

}

module.exports = new EmailEventService();
