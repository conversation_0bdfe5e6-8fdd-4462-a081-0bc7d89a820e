'use strict';

/* A temporary solution for event's funds pay outs */

const swUtils 	= require('../lib/swUtils');
const co 		= require('co');
const assert 	= require('assert');
const numeral   = require('numeral');
const { PAYMENT_PROVIDER } = require('../constants/payments');
const knex      = require('knex')({client: 'pg'});

const transferSchema = require('../validation-schemas/event-schemas').create_transfer;

const FIELDS_SKIP_NOT_NULL = {
	event_id 		: true,
	payment_for 	: true
};

const {
    DISPUTE_PENALTY_CHANGE_2024_09_01,
} = require('../constants/payments');

const TICKET_TRANSFER_TYPE  = 'tickets';
const TEAM_TRANSFER_TYPE    = 'teams';
const BOOTH_TRANSFER_TYPE   = 'booths';

const TEAMS_STATS_RENAMING_TABLE = {
    paid_teams_qty 			 	: 'paid_items_qty',
    teams_qty                   : 'sw_fee_items_qty',
	paid_teams_amount 		 	: 'paid_items_amount',
	paid_teams_cards_qty 	 	: 'paid_items_cards_qty',
	paid_teams_cards_amount  	: 'paid_items_cards_amount',
    paid_teams_ach_qty          : 'paid_items_ach_qty',
    paid_teams_ach_amount  	    : 'paid_items_ach_amount',
	paid_teams_checks_qty 		: 'paid_items_checks_qty',
	paid_teams_checks_amount 	: 'paid_items_checks_amount',
	accepted_or_paid_teams_fee 	: 'sw_fee',
	net_team_entry_profit  		: 'net_profit',
    teams_stripe_fee_card       : 'stripe_fee_card',
    teams_stripe_fee_ach        : 'teams_stripe_fee_ach'
}

const BOOTHS_STATS_RENAMING_TABLE = {
    paid_booths_qty 			 	: 'paid_items_qty',
    paid_or_accepted_exhibitors     : 'sw_fee_items_qty',
    paid_booths_amount 		 	    : 'paid_items_amount',
    paid_booths_cards_qty 	 	    : 'paid_items_cards_qty',
    paid_booths_cards_amount  	    : 'paid_items_cards_amount',
    paid_booths_checks_qty 		    : 'paid_items_checks_qty',
    paid_booths_checks_amount 	    : 'paid_items_checks_amount',
    accepted_or_paid_booths_fee 	: 'sw_fee',
    net_booths_entry_profit  		: 'net_profit'
}

const DISPUTE_CREATED_TYPE = 'charge.dispute.created';

class FundsTransferService {
	constructor () {}

    get TRANSFER_TYPES() {
        return {
            TICKETS: 'tickets',
            TEAMS: 'teams',
            BOOTHS: 'booths',
        };
    }

    isCorrectTransferType(type) {
        return _.values(this.TRANSFER_TYPES)
            .includes(type);
    }

    getMerchantAccountData (apiVer, eventID, type) {
        if (!Number.isInteger(eventID) || (eventID <= 0)) {
            return Promise.reject({ validation: 'Invalid Event ID' });
        }
        if(!this.isCorrectTransferType(type)) {
            return Promise.reject({ validation: 'Unknown account type' });
        }

        return this._getMerchantKeys(eventID, type).then(acc => {
            if (acc === null) {
                return Promise.reject({validation: 'Event Stripe Keys not found'});
            }

            if (
                acc.tickets_payment_provider === PaymentService.__PAYMENT_PROVIDERS__.TILLED &&
                acc.account_id &&
                type === this.TRANSFER_TYPES.TICKETS
            ) {
                return TilledService.getAccountInformation(acc.account_id).then((account) => {
                    if (!account) {
                        return { account: null, balance: null };
                    }

                    return {
                        account: {
                            id: account.id,
                            display_name: account.name,
                            business_name: account.business_profile.legal_name,
                            payment_provider: PaymentService.__PAYMENT_PROVIDERS__.TILLED,
                        },
                        balance: null,
                    };
                });
            }

            if(acc.secret_key && acc.account_id) {
                return StripeService.account.getAccount(acc.secret_key, apiVer, true)
                    .then(async (data) => {

                        if(!data) {
                            return { account: null, balance: null }
                        }

                        delete data.account.currencies_supported;

                        const [
                            events,
                            { transfers, last_days_ago },
                        ] = await Promise.all([
                            StripeService.account.getStripeAccountEvents(acc.account_id),
                            this._getAutoTransfers(acc.account_id),
                        ]);
                        data.events = events;
                        data.transfers = transfers;
                        data.account.last_transfer_days_ago = last_days_ago;

                        return data;
                    })
            } else {
                return Promise.resolve({ account: null, balance: null });
            }
        })
    }

	async getEventAccountsData (eventID, apiVer) {
		if (!Number.isInteger(eventID) || (eventID <= 0)) {
			throw { validation: 'Invalid Event ID' };
		}

		let accounts = await this._getMerchantKeys(eventID);

        if (accounts === null) {
            return Promise.reject({ validation: 'Event Stripe Keys not found' });
        }

        let formattedAccounts = this.__formatAccounts(accounts);

        let result = [];

        let emptyAccount = { account: null, balance: null, transfers: null, events: null };

        for (const account of formattedAccounts) {
            if(account.secret_key && account.account_id) {
                let [data, transfers, events, current] = await Promise.all([
                    StripeService.account.getAccount(account.secret_key, apiVer, true),
                    this._getAutoTransfers(account.account_id),
                    StripeService.account.getStripeAccountEvents(account.account_id),
                    this._getCurrentEventBalance(eventID, account.usage)
                ]);

                data.account.public_key 	= account.public_key;
                data.account.secret_key 	= account.secret_key ? swUtils.maskString(account.secret_key) : null;
                data.account.usage          = account.usage;
                data.transfers 				= transfers.transfers;
                data.last_transfer_days_ago = transfers.last_days_ago;
                data.events                 = events;
                data.currentEventBalance    = current;

                delete data.account.currencies_supported;

                result.push(data);
            }
        }

        if(!result.length) {
            result.push(emptyAccount);
        }

        return result
    }

    async _getCurrentEventBalance (eventID, usage) {
	    let available = await this._getAvailableStripeNetProfit(eventID, usage);
        let pending = await this._getPendingStripe(eventID, usage);

        return {
            available   : [{ currency: 'usd', amount: available }],
            pending     : [{ currency: 'usd', amount: pending }]
        }
    }

    _getMerchantKeys (eventID, type = null) {
	    if(type && ![TICKET_TRANSFER_TYPE, TEAM_TRANSFER_TYPE, BOOTH_TRANSFER_TYPE].includes(type)) {
	        return Promise.reject(new Error('Unknown account type'));
        }

	    let query = knex('event AS e').where('e.event_id', eventID);

	    if(type === TICKET_TRANSFER_TYPE || !type) {
	        query.leftJoin('stripe_account AS sa_tickets', 'sa_tickets.secret_key', 'e.stripe_tickets_private_key')
                .select([
                    `sa_tickets.secret_key AS ${type ? 'secret_key' : 'tickets_secret_key'}`,
                    `sa_tickets.public_key AS ${type ? 'public_key' : 'tickets_public_key'}`,
                    'e.tickets_payment_provider AS tickets_payment_provider',
                ]);
            query.leftJoin('tilled.account as ta', 'ta.tilled_account_id', 'e.tilled_tickets_account_id');

            if (type) {
                query.select([
                    knex.raw(`
                        CASE 
                        WHEN e.tickets_payment_provider = '${PAYMENT_PROVIDER.TILLED}' THEN ta.tilled_account_id
                        ELSE sa_tickets.stripe_account_id 
                        END AS account_id
                    `)
                ]);
            } else {
                query.select([
                    'sa_tickets.stripe_account_id AS tickets_stripe_account_id',
                    'ta.tilled_account_id AS tilled_tickets_account_id',
                ]);
            }
        }

        if(type === TEAM_TRANSFER_TYPE || !type) {
            query.leftJoin('stripe_account AS sa_teams', 'sa_teams.secret_key', 'e.stripe_teams_private_key')
                .select([
                    `sa_teams.secret_key AS ${type ? 'secret_key' : 'teams_secret_key'}`,
                    `sa_teams.public_key AS ${type ? 'public_key' : 'teams_public_key'}`,
                    `sa_teams.stripe_account_id AS ${type ? 'account_id' : 'teams_stripe_account_id'}`
                ])
        }

        if(type === BOOTH_TRANSFER_TYPE || !type) {
            query.leftJoin('stripe_account AS sa_booths', 'sa_booths.secret_key', 'e.stripe_exhibitors_private_key')
                .select([
                    `sa_booths.secret_key AS ${type ? 'secret_key' : 'exhibitors_secret_key'}`,
                    `sa_booths.public_key AS ${type ? 'public_key' : 'exhibitors_public_key'}`,
                    `sa_booths.stripe_account_id AS ${type ? 'account_id' : 'exhibitors_stripe_account_id'}`
                ])
        }

        return Db.query(query.toString()).then(result => result.rows[0] || null)
    }

    __formatAccounts (accounts) {
        const ticketsKeys   = ['tickets_secret_key', 'tickets_public_key', 'tickets_stripe_account_id'];
        const teamsKeys     = ['teams_secret_key', 'teams_public_key', 'teams_stripe_account_id'];
        const boothsKeys    = ['exhibitors_secret_key', 'exhibitors_public_key', 'exhibitors_stripe_account_id'];

        let ticketsAccount  = _.pick(accounts, ticketsKeys);
        let teamsAccount    = _.pick(accounts, teamsKeys);
        let boothsAccount   = _.pick(accounts, boothsKeys);

        let accountsData = [];

        let boothsAdded     = false;
        let ticketsAdded    = false;

        let usage = [];

        if(accounts.teams_secret_key) {
            usage.push(TEAM_TRANSFER_TYPE);

            if(accounts.tickets_secret_key && accounts.tickets_secret_key === accounts.teams_secret_key) {
                usage.push(TICKET_TRANSFER_TYPE);

                ticketsAdded = true;
            }

            if(accounts.exhibitors_secret_key && accounts.exhibitors_secret_key === accounts.teams_secret_key) {
                usage.push(BOOTH_TRANSFER_TYPE);

                boothsAdded = true;
            }

            teamsAccount.usage = usage;

            accountsData.push(this.__formatAccountKeys(teamsAccount));
        }

        if(accounts.tickets_secret_key && !ticketsAdded) {
            usage = [TICKET_TRANSFER_TYPE];

            if(accounts.exhibitors_secret_key && accounts.exhibitors_secret_key === accounts.tickets_secret_key) {
                usage.push(BOOTH_TRANSFER_TYPE);

                boothsAdded = true;
            }

            ticketsAccount.usage = usage;

            accountsData.push(this.__formatAccountKeys(ticketsAccount));
        }

        if(accounts.exhibitors_secret_key && !boothsAdded) {
            usage = [BOOTH_TRANSFER_TYPE];

            boothsAccount.usage = usage;

            accountsData.push(this.__formatAccountKeys(boothsAccount));
        }

        return accountsData;
    }

    __formatAccountKeys (account) {
        let newObject = {
            secret_key          : null,
            public_key          : null,
            account_id          : null
        };

        Object.keys(account).forEach(key => {
            if(key.toString().includes('secret_key')) {
                newObject.secret_key = account[key];
            } else if(key.toString().includes('public_key')) {
                newObject.public_key = account[key];
            } else if(key.toString().includes('account_id')) {
                newObject.account_id = account[key];
            } else {
                newObject[key] = account[key];
            }
        });

        return newObject;
    }

	_getAutoTransfers (accountID, limit = 10) {
		return Promise.resolve().then(() => {
			assert(_.isString(accountID), 'Account id should be a string');
			assert(Number.isInteger(limit) && (limit > 0), 'Limit should be a positive integer');

			return Db.query(
				`SELECT 
					"d"."transfers", COALESCE(
						DATE_PART('day', (NOW()::TIMESTAMP - TO_DATE("d"."transfers"->1->>'date_sent', 'Mon DD, YYYY')::TIMESTAMP)),
						0
					) "last_days_ago"
				 FROM (
				 	SELECT 
				 		COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("transfer"))), '[]'::JSON) "transfers"
				 	FROM (
				 		SELECT
							st."amount",
							TO_CHAR(st."date_sent", 'Mon DD, YYYY') "date_sent"
				 		FROM "stripe_transfer" st 
				 		WHERE st."stripe_account_id" = $1
				 			AND st."transfer_type" = 'auto'
				 			AND st."amount" > 0
				 		ORDER BY st."date_sent" DESC, st."created" DESC
				 		LIMIT ${limit}
				 	) "transfer"
				 ) "d"`,
				[accountID]
			)
		})
		.then(result => result.rows[0] || { transfers: [], last_days_ago: 0 });
	}

	_getEventSecretKey (eventID, accountID) {
		return Db.query(
			`SELECT 
				sa."secret_key"
			 FROM "event" e 
			 INNER JOIN "stripe_account" sa 
			 	ON sa."stripe_account_id" = $2
			 WHERE e."event_id" = $1
			 	AND (
			 		sa."secret_key" = e."stripe_teams_private_key" OR
			 		sa."secret_key" = e."stripe_tickets_private_key" OR
			 		sa."secret_key" = e."stripe_exhibitors_private_key"
			 	)`,
			[eventID, accountID]
		).then(result => result.rows[0] || null);
	}

	_genTransferSaveSQLQuery (data) {
		let columns = Object.keys(data);

		let updQuery = 
			squel.update().table('stripe_transfer')
			.where('stripe_transfer_id = ?', data.stripe_transfer_id)
			.returning(`'update'::TEXT`, 'op')

		columns.forEach(col => {
			/* Can't update this field, 'cause we have a unique constraint on it */
			if (col !== 'stripe_transfer_id') {
				if (FIELDS_SKIP_NOT_NULL[col]) {
					updQuery.set(
						col, 
						squel.str(
							`CASE WHEN "${col}" IS NOT NULL THEN "${col}" ELSE ? END`, data[col])
					);
				} else {
					updQuery.set(col, squel.str('?', data[col]));
				}
				
			}
		});

		let insQuery = 
			squel.insert().into('stripe_transfer')
			.fromQuery(
				columns,
				columns.reduce((_q, _col) => {

					_q.field(squel.str('?', data[_col]));

					return _q;
				}, squel.select().where('NOT EXISTS (SELECT * FROM "upd")'))
			)
			.returning(`'insert'::TEXT`, 'op')

		let resultQuery = 
			squel.select().field('*').from('ins')
			.union(
				squel.select().field('*').from('upd')
			)
			.with('upd', updQuery)
			.with('ins', insQuery)

		return resultQuery;
	}

	_saveStripeTransferRow (data) {
		return Promise.resolve().then(() => {
			if (!_.isObject(data)) {
				return Promise.reject(new Error('Expecting data to be an object'));
			}

			let {
				event_id = null,
				amount,
				stripe_account_id,
				stripe_transfer_id,
				description,
				payment_for = null,
				/* Could be: check, internal, pay_out, stripe, auto */
				transfer_type = 'pay_out',
				date_sent = 'NOW()'
			} = data;

			let sqlPrepared = {
    			event_id, amount, stripe_account_id, stripe_transfer_id, 
    			description, payment_for, transfer_type, date_sent
    		};

			let sqlQuery = this._genTransferSaveSQLQuery(sqlPrepared);

			return Db.query(sqlQuery)
		})
		.then(res => res.rows[0] || null)
		.then(row => {
			if (row === null) {
				return null
			} else {
				/* "false" means that there was no row for the transfer in the db */
				return (row === 'insert') ? false : true;
			}
		})
	}

	// covered 😄👍
	createTransfer (data) {
		return co(function* () {
			let validation = transferSchema.validate(data);

            if (validation.error) {
                return Promise.reject({ validationErrors: validation.error.details });
            }

            let [
                accountData, 
                availableAmount
            ] = yield Promise.all([ /* jshint ignore:line */
                this._getEventSecretKey(data.event_id, data.account_id), 
                this.getAvailableToPayout(data.event_id, data.payment_for)
            ]);

            if (accountData === null) {
            	return Promise.reject({ validation: 'Stripe Account not found' });
            }

            const prepared = validation.value;

            if (availableAmount < prepared.amount) {
                /**
                 * See http://numeraljs.com/ #Format Currency
                 * @type {String}
                 */
                let available = numeral(availableAmount).format('$0,0.00');
                return Promise.reject({ 
                    validation: `Amount is too large! You have ${available} available` 
                });
            }

            let transfer = yield (
            	StripeService.account.payOutFunds(
            		accountData.secret_key, data.apiVer, 
            		prepared.amount, prepared.currency, prepared.description));


            prepared.stripe_transfer_id = transfer.id;

            yield (this._saveStripeTransferRow(prepared));

            return transfer;
		}.bind(this));
	}

	getTransfersList (eventID, type) {
		if (!Number.isInteger(eventID) || (eventID <= 0)) {
			return Promise.reject({ validation: 'Invalid Event ID' });
		}

		if(!this.isCorrectTransferType(type)) {
            return Promise.reject({ validation: 'Unknown transfer type' });
        }

		let findTransfers = paymentFor => {
			return Db.query(
				`SELECT 
					st.amount, 
					st.description,
					TO_CHAR(st.date_sent, 'Mon DD, YYYY') "sent",
					TO_CHAR(st.date_canceled, 'Mon DD, YYYY') "date_canceled"
				 FROM "stripe_transfer" st 
				 INNER JOIN "event" e 
				 	ON e."event_id" = st."event_id"
				 WHERE st."event_id" = $1
				 	AND st."payment_for" = $2
				 	AND st."transfer_type" IN ('pay_out', 'internal', 'virtual_auto')
				 ORDER BY st."date_sent" DESC`,
				[eventID, paymentFor] 
			).then(result => result.rows)
		}

        return findTransfers(type);
	}

	_getStripeFee (eventID, paymentFor = 'teams', paymentType = 'card') {
		return Db.query(
			`SELECT SUM( 
                CASE
                    WHEN p.payment_provider IS NULL OR p.payment_provider = '${PAYMENT_PROVIDER.STRIPE}' THEN p.stripe_fee
                    ELSE p.provider_fee
                END
             ) "fee"
			 FROM "purchase" p 
			 WHERE p."event_id" = $1 
			 	AND p."payment_for" = $2
			 	AND p."type" = $3 
			 	AND p."status" = 'paid'`,
			[eventID, paymentFor, paymentType]
		).then(result => (result.rows[0] && parseFloat(result.rows[0].fee, 10)) || 0)
	}

	_getStripeTransfers (eventID, paymentFor = 'teams') {
		return Db.query(
			`SELECT SUM(st."amount") "amount"
			 FROM "stripe_transfer" st
			 WHERE st."event_id" = $1
			 	AND st."payment_for" = $2
			 	AND st."transfer_type" IN ('pay_out', 'internal', 'virtual_auto')
				AND st.date_canceled IS NULL
			`,
			[eventID, paymentFor]
		).then(result => (result.rows[0] && parseFloat(result.rows[0].amount, 10)) || 0)
	}

	_getAutoTransferedStripe (eventID, paymentFor = 'teams') {
		return Db.query(
			`SELECT 
				COALESCE(SUM(p."net_profit"), 0) - 
				COALESCE(SUM(p."additional_fee_amount"), 0) "amount"
			 FROM "purchase" p 
			 WHERE p."event_id" = $1
			 	AND p."payment_for" = $2
			 	AND p."status" = 'paid'
			 	AND p."stripe_transfer_status" = 'available'
                ${
                    (paymentFor === this.TRANSFER_TYPES.TICKETS)
                        ? 'AND (p."is_payment" IS TRUE)'
                        : ''
                }`,
			[eventID, paymentFor]
		).then(result => (result.rows[0] && parseFloat(result.rows[0].amount, 10)) || 0)
	} 

	_getPendingStripe (eventID, paymentFor = 'teams') {
        paymentFor = _.isArray(paymentFor) ? paymentFor : [paymentFor];

	    let query = knex('purchase AS p')
            .select(knex.raw(
                'COALESCE(SUM(p."net_profit"), 0) - COALESCE(SUM(p.additional_fee_amount), 0) AS "amount"'
            ))
            .where('p.event_id', eventID)
            .where(builder =>
                builder
                    .whereNull('p.stripe_balance_available')
                    .orWhereRaw('p.stripe_balance_available > NOW()')
            )
            .whereIn('p.type', ['card', 'ach'])
            .where('p.status', '<>', 'canceled')
            .whereIn('p.payment_for', paymentFor);

        if(paymentFor.includes(this.TRANSFER_TYPES.TICKETS)) {
            query.whereRaw(`p.is_payment IS TRUE`);
        }

		return Db.query(query).then(result => (result.rows[0] && Number(result.rows[0].amount)) || 0)
	}

	/**
	* We Disable "Make Payout" button, if there were auto transfers during last 3 days
	* 
	**/
	_disableManualTransfers (eventID, type = 'teams') {
		return Promise.resolve().then(() => {
			assert(Number.isInteger(eventID), 'Event id should be an integer');

            let field = {
                teams       : 'stripe_teams_private_key',
                tickets     : 'stripe_tickets_private_key',
                booths      : 'stripe_exhibitors_private_key',
            }[type];

			if (!field) {
				return Promise.reject(new Error(`Type "${type}" is not supported`));
			}

			return Db.query(
				`SELECT COALESCE(COUNT(*), 0) "qty"
				 FROM "stripe_transfer" str 
				 INNER JOIN "stripe_account" sa 
				     ON sa."stripe_account_id" = str."stripe_account_id"
				 INNER JOIN "event" e 
				     ON e."${field}" = sa."secret_key"
				 WHERE str."transfer_type" = 'auto'
				    AND e."event_id" = $1
				    AND str."amount" > 0
				  	AND str."date_sent"::DATE >= (NOW() - INTERVAL '3 days')::DATE`,
				[eventID]
			).then(res => res.rows[0] && (Number(res.rows[0].qty) > 0))
		})

	}

    /**
     * Returns the amount that can be payed out from the EO's Stripe Account to the bank 
     *
     * Formula details: https://docs.google.com/spreadsheets/d/1MAq_8Gg3v7a2_9Wy23t8fGUmcOsJRdak6aXM-JjuVes/edit
     */
    getAvailableToPayout (eventID, paymentFor) {
        return co(function* () {
            let [
                availableNetProfitAmount, 
                autoPayedOutAmount, 
                manuallyPayedOutAmount,
                returnedEscrow
            ] = yield Promise.all([ /* jshint ignore:line */
                this._getAvailableStripeNetProfit(eventID, paymentFor),
                this._getAutoTransferedStripe(eventID, paymentFor),
                this._getStripeTransfers(eventID, paymentFor),
                this.getReturnedEscrow(eventID, paymentFor)
            ]);

            let availableToPayOutAmount = (
                availableNetProfitAmount - autoPayedOutAmount - 
                                                        manuallyPayedOutAmount + returnedEscrow);

            return swUtils.normalizeNumber(availableToPayOutAmount);
        }.bind(this));
    }

    /* !!! */
	getStripeStatistics (eventID, paymentFor) {
		return Promise.all([
			this._getNetProfit(eventID, paymentFor),
			this._getAvailableStripeNetProfit(eventID, paymentFor),
			this._getNotAvailableStripeNetProfit(eventID, paymentFor),

			this._getAutoTransferedStripe(eventID, paymentFor),
			this._getStripeTransfers(eventID, paymentFor),
			this._getPendingStripe(eventID, paymentFor),

			this._disableManualTransfers(eventID, paymentFor),

            this._countLostDisputesPenalty(eventID, paymentFor),
            this._getFailedACHPaymentsPenalty(eventID, paymentFor)
		]).then(data => {

			let [
				net_profit,
				available_net_profit,
				not_available_net_profit,
				auto_transfered, 
				bank_transfered, 
				balance_pending,
				diable_transfers,
                lost_disputes,
                lost_ach_payments
			] = data;

			let available_to_transfer = (available_net_profit - auto_transfered - bank_transfered);
				available_to_transfer = swUtils.normalizeNumber(available_to_transfer);

			return { 
				net_profit, not_available_net_profit, 
				auto_transfered, bank_transfered, balance_pending, 
				available_to_transfer, diable_transfers, lost_disputes,
                lost_ach_payments
			};
		})
	}

    getTilledStatistics (eventID, paymentFor) {
        return Promise.all([
            this._getNetProfit(eventID, paymentFor),
            Promise.resolve(0), // available_net_profit will be added later
            Promise.resolve(0), // not_available_net_profit will be added later

            Promise.resolve(0), // auto_transfered will be added later
            Promise.resolve(0), // bank_transfered will be added later
            Promise.resolve(0), // balance_pending will be added later

            Promise.resolve(true), // diable_transfers will be added later

            Promise.resolve({ qty: 0, penalty: 0, fee: 0, stripe_fee_sum: 0 }), // lost_disputes will be added later
            Promise.resolve({ qty: 0, penalty: 0, fee: 0 }) // lost_ach_payments will be added later
        ]).then(data => {
            const [
                net_profit,
                available_net_profit, // TODO: add this field
                not_available_net_profit, // TODO: add this field
                auto_transfered, // TODO: add this field
                bank_transfered, // TODO: add this field
                balance_pending, // TODO: add this field
                diable_transfers, // TODO: add this field
                lost_disputes, // TODO: add this field
                lost_ach_payments// TODO: add this field
            ] = data;

            let available_to_transfer = (available_net_profit - auto_transfered - bank_transfered);
            available_to_transfer = swUtils.normalizeNumber(available_to_transfer);

            const payment_provider =  PaymentService.__PAYMENT_PROVIDERS__.TILLED;

            return {
                net_profit, not_available_net_profit,
                auto_transfered, bank_transfered, balance_pending,
                available_to_transfer, diable_transfers, lost_disputes,
                lost_ach_payments, payment_provider
            };
        });
    }

    async _getFailedACHPaymentsPenalty (eventID, paymentFor) {
        let query = knex('purchase AS p')
            .select({
                penalty: knex.raw(`COALESCE(COUNT(*) * ?, 0)`, [StripeService.FAILED_ACH_PAYMENT_FEE]),
                qty: knex.raw(`COALESCE(COUNT(*), 0)`),
                fee: knex.raw('?', [StripeService.FAILED_ACH_PAYMENT_FEE])
            })
            .whereRaw(`p.status = 'canceled'`)
            .whereRaw(`p.type = 'ach'`)
            .where('p.event_id', eventID)
            .whereNotNull('p.canceled_date')
            .whereNull('p.date_refunded')
            .where((builder) => {
                builder.where('p.amount_refunded', 0)
                    .orWhereNull('p.amount_refunded')
            })
            .whereNull('p.dispute_created')
            .where('p.payment_for', paymentFor);

        if(paymentFor === this.TRANSFER_TYPES.TICKETS) {
            query.whereRaw(`p.is_payment IS TRUE`);
        }

        const data = await Db.query(query).then(result => result?.rows?.[0]);

        return {
            penalty: Number(data.penalty),
            qty: Number(data.qty),
            fee: Number(data.fee)
        }
     }

	_getNetProfit (eventID, paymentFor) {
		return Db.query(
			`SELECT 
				COALESCE(SUM(p."net_profit"), 0) - 
				COALESCE(SUM(p."additional_fee_amount"), 0) "profit"
			 FROM "purchase" p
			 WHERE p."event_id" = $1
			 	AND p."payment_for" = $2
			 	AND p."type" IN ('card', 'ach')
			 	AND p."status" = 'paid'
                ${
                    (paymentFor === this.TRANSFER_TYPES.TICKETS)
                        ? 'AND (p."is_payment" IS TRUE)'
                        : ''
                }`,
			[eventID, paymentFor]
		).then(res => res.rows[0] && Number(res.rows[0].profit) || 0);
	}

	_getAvailableStripeNetProfit (eventID, paymentFor) {
	    paymentFor = _.isArray(paymentFor) ? paymentFor : [paymentFor];

	    let query = knex('purchase AS p')
            .select(knex.raw(`
                COALESCE(SUM(p.net_profit), 0) - COALESCE(SUM(p.additional_fee_amount), 0) AS profit`
            ))
            .where('p.event_id', eventID)
            .whereIn(`p.type`, ['card', 'ach'])
            .whereRaw(`p.status = 'paid'`)
            .whereNotNull('p.stripe_balance_available')
            .whereRaw(`p.stripe_balance_available <= NOW()::TIMESTAMP`)
            .whereIn('p.payment_for', paymentFor);

        if(paymentFor.includes(this.TRANSFER_TYPES.TICKETS)) {
            query.whereRaw(`p.is_payment IS TRUE`)
        }

		return Db.query(query).then(res => res.rows[0] && Number(res.rows[0].profit) || 0);
	}

	_getNotAvailableStripeNetProfit (eventID, paymentFor) {
		return Db.query(
			`SELECT COALESCE(SUM(p."net_profit"), 0) "profit"
			 FROM "purchase" p
			 WHERE p."event_id" = $1
			 	AND p."payment_for" = $2
			 	AND p."type" IN ('card', 'ach')
			 	AND p."status" = 'paid'
			 	AND (
			 		p."stripe_balance_available" IS NULL OR
			 		p."stripe_balance_available" > NOW()::TIMESTAMP
			 	)
                ${
                    (paymentFor === this.TRANSFER_TYPES.TICKETS)
                        ? 'AND (p."is_payment" IS TRUE)'
                        : ''
                }`,
			[eventID, paymentFor]
		).then(res => res.rows[0] && Number(res.rows[0].profit) || 0);
	}

	_getPaidTeamsAmount (eventID, paymentType) {
		let paymentTypeBlock = '';
		let paymentStatusBlock = '';

		if (paymentType === 'card') {
			paymentTypeBlock = `AND p."type" = 'card'`;
            paymentStatusBlock = `OR (rt."status_paid" = 26 AND p.dispute_status <> 'lost')`;
		} else if (paymentType === 'check') {
			paymentTypeBlock = `AND p."type" = 'check'`
		} else if (paymentType === 'ach') {
            paymentTypeBlock = `AND p."type" = 'ach'`;
            paymentStatusBlock = `OR (rt."status_paid" = 26 AND p.dispute_status <> 'lost')`;
        }

		return Db.query(
			`SELECT 
				COALESCE(SUM(p."amount"), 0) "amount"
			 FROM "purchase" p 
			 WHERE p."status" = 'paid'
			 	${paymentTypeBlock}
			 	AND p."event_id" = $1
			 	AND p."payment_for" = 'teams'
			 	AND EXISTS (
					SELECT pt."purchase_id"
					FROM "purchase_team" pt 
					INNER JOIN "roster_team" rt 
						ON rt."roster_team_id" = pt."roster_team_id"
						AND rt."deleted" IS NULL 
						AND (rt."status_paid" = 22 ${paymentStatusBlock})
					WHERE pt."purchase_id" = p."purchase_id"
						AND pt."canceled" IS NULL 
			 	)`,
			[eventID]
		).then(result => result.rows[0] && parseFloat(result.rows[0].amount, 10) || 0)
	}

    async _countLostDisputesPenalty (eventID, paymentFor) {
        let paymentForBlock;

        if (paymentFor) {

            paymentForBlock = `AND p."payment_for" = '${paymentFor}'`;

            if (paymentFor === this.TRANSFER_TYPES.TICKETS) {
                paymentForBlock = 
                    `${paymentForBlock}
                    AND (p."is_payment" IS TRUE)`;
            }
        }

        const query = `WITH dispute_counts AS (
                SELECT
                    p.dispute_status,
                    -- Calculate penalty based on dispute status and creation date
                    CASE
                        WHEN p.dispute_created BETWEEN $3 AND $4 THEN $7  -- Penalty for all statuses within this date range
                        WHEN p.dispute_status = $2 AND p.dispute_created >= $5 THEN $6  -- Increased penalty for lost disputes after 2024-09-01
                        WHEN p.dispute_status = $2 THEN $7  -- $15 penalty for other lost disputes
                        ELSE 0  -- No penalty for other statuses outside the specified ranges
                    END AS penalty,
                    p.stripe_fee
                FROM "purchase" p
                WHERE
                    p.event_id = $1
                     ${paymentForBlock}
            )
            SELECT
                (COUNT(*) FILTER (WHERE penalty = $7))::INT AS "qty_before_change",
                (COUNT(*) FILTER (WHERE penalty = $6))::INT AS "qty_after_change",
                (COALESCE(SUM(stripe_fee) FILTER (WHERE dispute_status = $2), 0))::FLOAT AS "stripe_fee_sum",
                (COALESCE(SUM(penalty), 0))::FLOAT AS penalty
            FROM dispute_counts;
            `

        const { rows: [disputes] } = await Db.query(
            query,
            [
                eventID,
                StripeService.DISPUTE_STATUS.LOST,
                StripeService.DISPUTE_FEE_COLLECTION_CHANGE_DATE,
                StripeService.DISPUTE_FEE_COLLECTION_EXCLUDE_WON_DATE,
                DISPUTE_PENALTY_CHANGE_2024_09_01.DATE,
                DISPUTE_PENALTY_CHANGE_2024_09_01.FEE,
                StripeService.DISPUTE_FEE

            ]
        );

        return {
            ...disputes,
            qty: disputes.qty_after_change + disputes.qty_before_change,
            fee_before_change: StripeService.DISPUTE_FEE,
            fee_after_change: DISPUTE_PENALTY_CHANGE_2024_09_01.FEE,
        };

    }

    getDefaultPaymentsSWFeeForTickets (eventID) {
        return Db.query(
            `SELECT
                COALESCE(SUM(pt."ticket_fee" * pt."quantity"), 0) "fee"
            FROM "purchase_ticket" pt 
            INNER JOIN "purchase" ticketRow 
                ON ticketRow."purchase_id" = pt."purchase_id"
                AND ticketRow."type" IN ('card', 'ach') 
                AND ticketRow."payment_for" = 'tickets'
                AND ticketRow."is_ticket" IS TRUE
            INNER JOIN "purchase" paymentRow 
                ON (
                    paymentRow."purchase_id" = ticketRow."linked_purchase_id"
                    OR paymentRow."purchase_id" = ticketRow."purchase_id"
                )
                AND paymentRow."event_id" = ticketRow."event_id"
                AND paymentRow."stripe_payment_type" = 'default'
                AND paymentRow."is_payment" IS TRUE
            WHERE pt."canceled" IS NULL 
                AND ticketRow."event_id" = $1`,
            [eventID]
        ).then(result => Number(result.rows[0].fee))
    }

    getDefaultPaymentsSWFeeForTeams (eventID) {
        return Db.query(
            `SELECT 
                COALESCE(SUM(pt."sw_fee"), 0) "fee" 
            FROM "purchase" p 
            INNER JOIN "purchase_team" pt 
                ON pt."purchase_id" = p."purchase_id"
                AND pt."canceled" IS NULL
            WHERE p."status" <> 'canceled'
                AND p."type" IN ('card', 'ach')
                AND p."stripe_payment_type" = 'default'
                AND p."payment_for" = 'teams'
                AND p."event_id" = $1`,
            [eventID]
        ).then(result => Number(result.rows[0].fee))
    }

    getReturnedEscrow (eventID, paymentFor = 'teams') {
        return Db.query(
            `SELECT SUM("amount") "amount"
             FROM "stripe_transfer"
             WHERE "event_id" = $1
                 AND "payment_for" =  $2
                 AND "transfer_type" = 'escrow_refund'
                 AND "date_canceled" IS NULL`,
            [eventID, paymentFor]
        ).then(result => Number(result.rows[0].amount))
    }

    /**
     * Amount of mutual credit transfer for events (virtual type)
     */
    getTransfersAmount (eventID, paymentFor = 'teams', transferType = 'virtual') {
        let transferTypesToRetrieve = [transferType];

        if(transferType === 'virtual') {
            transferTypesToRetrieve.push('escrow_refund_by_check');
        }

        let query = squel.select().from('stripe_transfer', 'st')
            .field('SUM(st.amount)', 'amount')
            .where('st.event_id = ?', eventID)
            .where('st.payment_for = ?', paymentFor)
            .where(`st.transfer_type IN ?`, transferTypesToRetrieve)
            .where('st.date_canceled IS NULL');

        return Db.query(query).then(result => Number(result.rows[0].amount));
    }

    getVirtualTransfersList (eventID, paymentFor = 'teams', transferType = 'virtual') {
        let transferTypesToRetrieve = [transferType];

        if(transferType === 'virtual') {
            transferTypesToRetrieve.push('escrow_refund_by_check');
        }

        let query = squel.select().from('stripe_transfer', 'st')
            .field('st.amount')
            .field('st.description')
            .where('st.event_id = ?', eventID)
            .where('st.payment_for = ?', paymentFor)
            .where(`st.transfer_type IN ?`, transferTypesToRetrieve)
            .where('st.date_canceled IS NULL')
            .order('created', false);

        return Db.query(query).then(res => res.rows);
    }

    /**
     * Note - virtual transfers not included in formulas
     *
     * @param eventID
     * @returns {Promise<{general: *, event_fees: {per_item_fee: *}}>}
     */
    async getBoothsGeneralStats (eventID) {
        const approx = swUtils.normalizeNumber.bind(swUtils);
        let eventFees = await BoothsService.paymentStatistic.getEventFees(eventID);

        let [
            paid_booths_qty, paid_booths_cards_amount, paid_booths_cards_qty, paid_booths_checks_amount,
            paid_booths_checks_qty, stripe_fee_card, stripe_fee_ach,
            { escrow: accepted_or_paid_booths_fee, purchasesCount: paid_or_accepted_exhibitors },
            collected_sw_fee, paid_escrow, returned_escrow
        ] = await Promise.all([
            BoothsService.paymentStatistic.getPaidBoothsQty(eventID),
            BoothsService.paymentStatistic.getPaidBoothsAmount(eventID, 'card'),
            BoothsService.paymentStatistic.getPaidBoothsQty(eventID, 'card'),
            BoothsService.paymentStatistic.getPaidBoothsAmount(eventID, 'check'),
            BoothsService.paymentStatistic.getPaidBoothsQty(eventID, 'check'),
            this._getStripeFee(eventID, 'booths'),
            this._getStripeFee(eventID, 'booths', 'ach'),
            BoothsService.paymentUtils.getTargetSWFee(eventID),
            BoothsService.paymentStatistic.getCollectedSWFee(eventID),
            this.getPaidSWFee(eventID, 'booths'),
            this.getReturnedEscrow(eventID, 'booths')
        ])

        let statistics = {
            paid_booths_qty,
            paid_or_accepted_exhibitors,
            paid_booths_cards_amount,
            paid_booths_cards_qty,
            paid_booths_checks_amount,
            paid_booths_checks_qty,
            stripe_fee_card,
            stripe_fee_ach,
            accepted_or_paid_booths_fee,
            collected_sw_fee,
            paid_escrow,
            returned_escrow
        };

        statistics.stripe_fee = approx(statistics.stripe_fee_card + statistics.stripe_fee_ach);

        statistics.paid_booths_amount =
            approx(statistics.paid_booths_cards_amount + statistics.paid_booths_checks_amount);

        statistics.escrow_still_owned = approx(
            statistics.collected_sw_fee - statistics.accepted_or_paid_booths_fee + paid_escrow.total
        );

        statistics.net_booths_entry_profit = approx(
            statistics.paid_booths_amount -
            statistics.stripe_fee -
            statistics.accepted_or_paid_booths_fee
        );

        statistics.disputes_collected = approx(
            statistics.collected_sw_fee - statistics.accepted_or_paid_booths_fee - paid_escrow.total
        );

        statistics.disputes_collected  = (statistics.disputes_collected < 0)
            ? 0
            : statistics.disputes_collected

        statistics.uncollected_sw_fee = approx(
            statistics.accepted_or_paid_booths_fee - statistics.collected_sw_fee - paid_escrow.total
        );

        statistics.uncollected_sw_fee = (statistics.uncollected_sw_fee < 0)
            ? 0
            : statistics.uncollected_sw_fee;

        let general = statistics;

        return { general, event_fees: eventFees };
    }

    /** !!! */
	getTeamsGeneralStats (eventID) {
		return co(function* () {
			const approx = swUtils.normalizeNumber.bind(swUtils);

			let statistics = yield ({
				event_fees  	   		 	: getEventFees(eventID),

				paid_teams_qty 			 	: getPaidTeamsQty(eventID),

				paid_teams_cards_amount  	: this._getPaidTeamsAmount(eventID, 'card'),
				paid_teams_cards_qty 	 	: getPaidTeamsQty(eventID, 'card'),

                paid_teams_ach_amount  	    : this._getPaidTeamsAmount(eventID, 'ach'),
                paid_teams_ach_qty 	 	    : getPaidTeamsQty(eventID, 'ach'),

				paid_teams_checks_amount 	: this._getPaidTeamsAmount(eventID, 'check'),
				paid_teams_checks_qty 	 	: getPaidTeamsQty(eventID, 'check'),

				stripe_fee_card 			: this._getStripeFee(eventID, 'teams'),
                stripe_fee_ach              : this._getStripeFee(eventID, 'teams', 'ach'),

				accepted_or_paid_teams_fee 	: TeamsPaymentService.getSWFeeEscrowTarget(eventID, true /* skip disputes */, true),
				collected_sw_fee  			: TeamsPaymentService.getCollectedSWFee(eventID),

                /* This fee is not collected as SW can collect fees only via the Stripe Connect */
                default_payments_sw_fee     : this.getDefaultPaymentsSWFeeForTeams(eventID),

                returned_escrow             : this.getReturnedEscrow(eventID),

                virtual_transfers_amount    : this.getTransfersAmount(eventID),

                virtual_transfers_list      : this.getVirtualTransfersList(eventID),

                escrow_transfers_list       : this.getVirtualTransfersList(eventID, 'teams', 'escrow_refund'),

                paid_escrow                 : this.getPaidSWFee(eventID, 'teams')
			});

            statistics.stripe_fee = approx(statistics.stripe_fee_card + statistics.stripe_fee_ach);

			statistics.teams_qty                    = statistics.accepted_or_paid_teams_fee.summands.teams_qty;
			statistics.accepted_or_paid_teams_fee   = statistics.accepted_or_paid_teams_fee.escrow;

			statistics.paid_teams_amount = approx(
                    statistics.paid_teams_cards_amount + statistics.paid_teams_checks_amount + statistics.paid_teams_ach_amount
            );

			statistics.possible_disputes = 
					TeamsPaymentService.countPossibleDisputesEscrow(statistics.event_fees.reg_fee);

			// Not in use
			statistics.current_net_revenue = approx(
				statistics.paid_teams_amount - 
				(
					statistics.stripe_fee +
					statistics.accepted_or_paid_teams_fee +
					statistics.possible_disputes
				)
			);

			statistics.escrow_still_owned = approx(
				statistics.collected_sw_fee
				- statistics.accepted_or_paid_teams_fee
                - statistics.default_payments_sw_fee
                - statistics.returned_escrow
                - statistics.virtual_transfers_amount
                + statistics.paid_escrow.total
			);

			statistics.net_team_entry_profit = approx(
				statistics.paid_teams_amount - 
				statistics.stripe_fee - 
				statistics.accepted_or_paid_teams_fee
			);

            /**
             * Actually, is the amount that we collected beyond the SW Fee that was supposed 
             * to be collected for the entry of teams
             *
             * X = A + B 
             *
             * A =  ∑(purchase.collected_sw_fee + purchase.additional_fee_amount)
             * B = (∑ accepted or paid team) * "sw fee" + "target balance"
             *
             * NOTE: this is "Escrow" on the UI in the "Stripe Balance ..." panel
             */
			statistics.disputes_collected = approx(
				statistics.collected_sw_fee
                - statistics.accepted_or_paid_teams_fee
                - statistics.default_payments_sw_fee
                + statistics.paid_escrow.total
                // commented by Eugene - we should not subtract returned escrow now
                // - statistics.returned_escrow
			);

			statistics.disputes_collected  = (statistics.disputes_collected < 0) 
				? 0 
				: statistics.disputes_collected



			statistics.uncollected_sw_fee = approx(
				statistics.accepted_or_paid_teams_fee - statistics.collected_sw_fee - statistics.paid_escrow.total
			);
			statistics.uncollected_sw_fee = (statistics.uncollected_sw_fee < 0) 
				? 0 
				: statistics.uncollected_sw_fee



			statistics.uncollected_escrow = approx(
				statistics.possible_disputes - statistics.disputes_collected
			);
			statistics.uncollected_escrow = (statistics.uncollected_escrow < 0)
				? 0 
				: statistics.uncollected_escrow

			let general = _.omit(statistics, 'event_fees');

			return { general, event_fees: statistics.event_fees };
		}.bind(this))

		function getPaidTeamsQty (eventID, paymentType) {
			let paymentTypeBlock = '';
			let paidStatusBlock = '';

			if (paymentType === 'card') {
				paymentTypeBlock = `AND p."type" = 'card'`;
				paidStatusBlock = `OR (rt."status_paid" = 26 AND p.dispute_status <> 'lost')`;
			} else if (paymentType === 'check') {
				paymentTypeBlock = `AND p."type" = 'check'`
			} else if (paymentType === 'ach') {
                paymentTypeBlock = `AND p."type" = 'ach'`;
                paidStatusBlock = `OR (rt."status_paid" = 26 AND p.dispute_status <> 'lost')`;
            }

			return Db.query(
				`SELECT 
					COALESCE(COUNT(DISTINCT rt."roster_team_id"), 0) "qty"
				 FROM "roster_team" rt
				 INNER JOIN "purchase_team" pt
				 	ON pt."roster_team_id" = rt."roster_team_id"
				 	AND pt."canceled" IS NULL 
				 INNER JOIN "purchase" p 
				 	ON p."purchase_id" = pt."purchase_id"
				 	AND p."status" = 'paid'
				 	${paymentTypeBlock}
				 WHERE rt."event_id" = $1
				 	AND (rt."status_paid" = 22 ${paidStatusBlock})
				 	AND rt."deleted" IS NULL`,
				[eventID]
			).then(result => result.rows[0] && parseInt(result.rows[0].qty, 10) || 0)		
		}

		// TODO: add here a range of tickets' application fees 
		function getEventFees (eventID) {
			return Db.query(
				`SELECT
				 	COALESCE(e."teams_entry_sw_fee", 0) "teams_entry_sw_fee",
				 	COALESCE(e."reg_fee", 0) "reg_fee",
				 	COALESCE(e."stripe_teams_percent", 0) "stripe_teams_percent"
				 FROM "event" e 
				 WHERE e."event_id" = $1`,
				[eventID]
			).then(result => result.rows[0] || {})
			.then(data => {
				Object.keys(data).forEach(key => {
					data[key] = parseFloat(data[key]);
				});

				if (data.stripe_teams_percent === 0) {
					data.stripe_teams_percent = swUtils.normalizeNumber(StripeService.DEFAULT_STRIPE_PERCENT * 100);
				}

				return data;
			})
		}
	}
    /** !!! */
	getTicketsGeneralStats (eventID) {
		let __getPaidItemsQty__ = (statistics, props) => {
            let paymentTypes = props || ['card', 'cash', 'check'];

			return paymentTypes.reduce((res, prop) => {
				let typeStats 	       = statistics[prop];

				let itemsQty 	       = typeStats.items_qty;
				let amount 		       = typeStats.amount;

				let swFee 		       = typeStats.sw;
				let paymentMethodFee   = typeStats.payment_method_fee;

				res.qty 		       = swUtils.normalizeNumber(res.qty + itemsQty);
				res.amount 		       = swUtils.normalizeNumber(res.amount + amount);
				res.sw 			       = swUtils.normalizeNumber(res.sw + swFee);
				res.payment_method_fee = swUtils.normalizeNumber(res.payment_method_fee + paymentMethodFee);

				return res;
			}, { qty: 0, amount: 0, sw: 0, payment_method_fee: 0 })
		}

		return co(function* () {
			let statistics = yield ({
				per_item_fee 	: TicketsStatisticsService.getSWFees(eventID),

				event_balance 	: TicketsStatisticsService.getEventBalance(eventID),

				card 			: TicketsStatisticsService.getCardStatistics(eventID),
				cash 			: TicketsStatisticsService.getCashStatistics(eventID),
				check 			: TicketsStatisticsService.getCheckStatistics(eventID),

                /* This fee is not collected as SW can collect fees only via the Stripe Connect */
                default_payments_sw_fee : this.getDefaultPaymentsSWFeeForTickets(eventID),

                returned_escrow: this.getReturnedEscrow(eventID, 'tickets'),

                virtual_transfers_amount    : this.getTransfersAmount(eventID, 'tickets'),
                virtual_transfers_list      : this.getVirtualTransfersList(eventID, 'tickets'),
                escrow_transfers_list       : this.getVirtualTransfersList(eventID, 'tickets', 'escrow_refund'),

                paid_escrow                 : this.getPaidSWFee(eventID, 'tickets')
			});

			let {event_balance: balance, per_item_fee} = statistics;

            const statisticsByMethod = __getPaidItemsQty__.bind(null, statistics);

            let { sw: sw_fee        , qty: paid_items_qty        , amount: paid_items_amount        }   = statisticsByMethod(null /* all */),
                {                     qty: paid_items_checks_qty , amount: paid_items_checks_amount }   = statisticsByMethod(['check']),
                {                     qty: paid_items_cash_qty   , amount: paid_items_cash_amount   }   = statisticsByMethod(['cash']),
                { payment_method_fee }                                                                  = statisticsByMethod(['card']);


			let net_profit = swUtils.normalizeNumber(paid_items_amount - payment_method_fee - sw_fee);

			/* "Collected Escrow" field */
			let possible_disputes 	= balance.target;
			let disputes_collected 	= swUtils.normalizeNumber(
                                            balance.collected_amount
                                            - statistics.default_payments_sw_fee
											- sw_fee
                                            + statistics.paid_escrow.total
											);
				disputes_collected  = (disputes_collected < 0) ? 0 : disputes_collected;

			/* Yes, generally it is the same formula as "disputes_collected" */
			let escrow_still_owned = swUtils.normalizeNumber(
                                            balance.collected_amount - 
                                            statistics.default_payments_sw_fee - 
                                                sw_fee - statistics.returned_escrow + statistics.paid_escrow.total -
                                                statistics.virtual_transfers_amount);

			let uncollected_sw_fee = swUtils.normalizeNumber(
			    sw_fee - balance.collected_amount - statistics.paid_escrow.total
            );

			uncollected_sw_fee = (uncollected_sw_fee < 0) ? 0 : uncollected_sw_fee;

			let uncollected_escrow = swUtils.normalizeNumber(possible_disputes - disputes_collected);
				uncollected_escrow = (uncollected_escrow < 0 ) ? 0 : uncollected_escrow;


			let general = {
				paid_items_qty,
				paid_items_amount,

				paid_items_cards_amount : statistics.card.amount,
				paid_items_cards_qty 	: statistics.card.items_qty,

				paid_items_checks_amount,
				paid_items_checks_qty,

                paid_items_cash_qty,
                paid_items_cash_amount,

                payment_method_fee,
				sw_fee,

				net_profit,
				
				possible_disputes,
				disputes_collected,

                returned_escrow: statistics.returned_escrow,

				escrow_still_owned,

				uncollected_sw_fee,
				uncollected_escrow,

                sw_fee_items_qty: paid_items_qty,

                paid_escrow: statistics.paid_escrow,

                virtual_transfers_list: statistics.virtual_transfers_list,
                escrow_transfers_list: statistics.escrow_transfers_list
			};
								
			return {
				general,
				event_fees: { per_item_fee }
			};
		}.bind(this))
	}

	__renameStatsFields__ (stats, aliases) {
		let result = {};

		Object.keys(stats).forEach(prop => {
			let resPropName = Boolean(aliases[prop]) ? aliases[prop] : prop;

			result[resPropName] = stats[prop];
		});
		
		return result;
	}

	__getTicketsStatistics__ (eventID, approx, getStats) {
	    return Promise.all([
            this.getTicketsGeneralStats(eventID),
            getStats(this.TRANSFER_TYPES.TICKETS),
        ]).then(data => {
            let [tickets, paymentProviderStatsTickets] = data;

            paymentProviderStatsTickets.credit_net_profit = approx(
                tickets.general.paid_items_cards_amount -
                tickets.general.payment_method_fee -
                tickets.general.sw_fee
            );

            paymentProviderStatsTickets.escrow           = tickets.general.disputes_collected;
            paymentProviderStatsTickets.escrow_to_return = this.__getEscrowToReturn(
                tickets.general.disputes_collected,
                paymentProviderStatsTickets.lost_disputes,
                paymentProviderStatsTickets.lost_ach_payments,
                approx
            )

            tickets.general.lost_disputes   = paymentProviderStatsTickets.lost_disputes;
            tickets.general.lost_ach_payments = paymentProviderStatsTickets.lost_ach_payments;

            tickets.general.escrow_still_owned = this.__getEscrowStillOwned(
                tickets.general.escrow_still_owned,
                paymentProviderStatsTickets.lost_disputes,
                paymentProviderStatsTickets.lost_ach_payments,
                approx
            )

            paymentProviderStatsTickets.available_to_transfer =
                    approx(paymentProviderStatsTickets.available_to_transfer + tickets.general.returned_escrow);

            tickets.merchant = paymentProviderStatsTickets;

            return { tickets };
        })
    }

    __getTeamsStatistics__ (eventID, approx, getStats) {
	    return Promise.all([
            this.getTeamsGeneralStats(eventID),
            getStats(this.TRANSFER_TYPES.TEAMS)
        ]).then(data => {
            let [teams, stripeTeams] = data;

            stripeTeams.credit_net_profit = approx(
                (teams.general.paid_teams_cards_amount + teams.general.paid_teams_ach_amount) -
                teams.general.stripe_fee -
                teams.general.accepted_or_paid_teams_fee
            )

            stripeTeams.escrow           = teams.general.disputes_collected;
            stripeTeams.escrow_to_return = this.__getEscrowToReturn(
                teams.general.disputes_collected, stripeTeams.lost_disputes, stripeTeams.lost_ach_payments, approx
            )

            teams.general = this.__renameStatsFields__(teams.general, TEAMS_STATS_RENAMING_TABLE);

            teams.general.lost_disputes = stripeTeams.lost_disputes;
            teams.general.lost_ach_payments = stripeTeams.lost_ach_payments;

            teams.general.escrow_still_owned = this.__getEscrowStillOwned(
                teams.general.escrow_still_owned, stripeTeams.lost_disputes, stripeTeams.lost_ach_payments, approx
            );

            stripeTeams.available_to_transfer =
                        approx(stripeTeams.available_to_transfer + teams.general.returned_escrow);

            teams.merchant = stripeTeams;

            return { teams };
        })
    }

    __getEscrowToReturn (disputesCollected, lostDisputes, lostACHPayments, approx) {
        return approx(
            disputesCollected - (lostDisputes.penalty + lostDisputes.stripe_fee_sum + lostACHPayments.penalty)
        );
    }

    __getEscrowStillOwned (escrowStillOwned, lostDisputes, lostACHPayments, approx) {
	    return approx(
            escrowStillOwned - (lostDisputes.penalty + lostDisputes.stripe_fee_sum + lostACHPayments.penalty)
        );
    }

    async __getBoothsStatistics__ (eventID, approx, getStats) {
        let booths = {};

        booths = await this.getBoothsGeneralStats(eventID);
        booths.merchant = await getStats(this.TRANSFER_TYPES.BOOTHS);

        booths.merchant.credit_net_profit = approx(
            booths.general.paid_booths_cards_amount -
            booths.general.stripe_fee -
            booths.general.accepted_or_paid_booths_fee
        );

        booths.merchant.escrow = booths.general.disputes_collected;
        booths.merchant.escrow_to_return = this.__getEscrowToReturn(
            booths.general.disputes_collected, booths.merchant.lost_disputes, booths.merchant.lost_ach_payments, approx
        );

        booths.general = this.__renameStatsFields__(booths.general, BOOTHS_STATS_RENAMING_TABLE);

        booths.general.lost_disputes = booths.merchant.lost_disputes;
        booths.general.lost_ach_payments = booths.merchant.lost_ach_payments;

        booths.general.escrow_still_owned = this.__getEscrowStillOwned(
            booths.general.escrow_still_owned, booths.merchant.lost_disputes, booths.merchant.lost_ach_payments, approx
        );

        booths.merchant.available_to_transfer =
            approx(booths.merchant.available_to_transfer + booths.general.returned_escrow);

        return { booths };
    }

    /** !!! */
	async getEventStatistics (eventID, type) {
        const paymentProvider = await EventService.getPaymentProvider(eventID);

        if (!Number.isInteger(eventID) || (eventID <= 0)) {
            return Promise.reject({ validation: 'Invalid Event ID' });
        }

        if(!this.isCorrectTransferType(type)) {
            return Promise.reject({ validation: 'Unknown transfer type' });
        }

		const getStats  = (paymentProvider === PaymentService.__PAYMENT_PROVIDERS__.TILLED && type === this.TRANSFER_TYPES.TICKETS) ?
            this.getTilledStatistics.bind(this, eventID) :
            this.getStripeStatistics.bind(this, eventID);
        const approx    = swUtils.normalizeNumber.bind(swUtils);

		if(type === this.TRANSFER_TYPES.TICKETS) {
		    return this.__getTicketsStatistics__(eventID, approx, getStats);
        } else if(type === this.TRANSFER_TYPES.TEAMS) {
            return this.__getTeamsStatistics__(eventID, approx, getStats);
        } else if(type === this.TRANSFER_TYPES.BOOTHS) {
            return this.__getBoothsStatistics__(eventID, approx, getStats);
        }
	}

	getPaidSWFee (eventID, type, paymentFor = null) {
	    let query = knex('custom_payment AS cp')
            .join('stripe_payment_intent AS spi', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .leftJoin('stripe_dispute AS sd', function (table) {
                table.on('sd.stripe_charge_id', 'spi.stripe_charge_id')
                    .andOn(knex.raw(`cp.status = 'disputed'`))
            })
            .where({
                event_id: eventID,
                payment_for_type: type
            })
            .where('cp.status', '<>', 'canceled')
            .whereRaw(`(sd.status <> 'lost' OR sd.status IS NULL)`)
            .whereRaw(`(cp.status <> 'disputed' OR (cp.status = 'disputed' AND (sd.status <> 'lost' OR sd.status IS NULL)))`)
            .select({
                paid: knex.raw(`COALESCE(SUM(cp.net_profit) FILTER ( WHERE cp.status = 'paid' OR
                                (cp.status = 'disputed' AND (sd.status <> 'lost' OR sd.status IS NULL))),0)::NUMERIC `),
                pending: knex.raw(`COALESCE(SUM(cp.net_profit) FILTER ( WHERE cp.status IN ('requires_action', 'pending') ), 0)::NUMERIC`),
                total: knex.raw(`COALESCE(SUM(cp.net_profit), 0)::NUMERIC`)
            })

        if (paymentFor) {
            query.whereIn('cp.payment_for', [paymentFor]);
        } else {
            query.whereIn('cp.payment_for', ['uncollected_fee', 'lost_dispute_fee_failed_ach_fee']);
        }

        return Db.query(query).then(result => {
            let data = result && result.rows[0] || {};

            return {
                paid: Number(data.paid),
                total: Number(data.total),
                pending: Number(data.pending)
            }
        });
    }
}

module.exports = new FundsTransferService();
