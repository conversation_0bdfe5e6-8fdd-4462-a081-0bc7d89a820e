'use strict';

const IsLocalService = require('./housing/__IsLocalService');
const TeamsExportService = require('./housing/__TeamsExportService');
const HousingHashService = require('./housing/__HousingHashService');
const TeamsService = require('./housing/__TeamsService');

const HOUSING_STATUSES = [
    {
        id: 31,
        name: 'None',
    },
    {
        id: 32,
        name: 'Verified',
    },
    {
        id: 33,
        name: 'Issued',
    },
    {
        id: 34,
        name: 'Below',
    },
    {
        id: 35,
        name: 'Faulty',
    },
];

const HOUSING_STATUS_CODE = {
    NONE        : 31,
    VERIFIED    : 32,
    ISSUED      : 33,
    BELOW       : 34,
    FAULTY      : 35
};

module.exports = {
    HOUSING_STATUSES,

    local: IsLocalService,

    teamsExport: TeamsExportService,

    housingHashService: HousingHashService,

    teams: TeamsService,

    updateEventHousing: function (eventID) {
        return this.__updTeamsHousingStatus__({eventID});
    },

    /**
    * Updates teams' and clubs' housing total values
    */
    update_club_housing: async function ({rosterClubID, recountMaxAcceptedNights = false}, callback) {
        try {
            let {
                club_is_local       : clubIsLocal,
                all_teams_are_local : allTeamsAreLocal
            } = await __findClubData__(rosterClubID);

            await (__calcTeamsTotalValues__(rosterClubID, clubIsLocal, allTeamsAreLocal, recountMaxAcceptedNights));

            if (!clubIsLocal || !allTeamsAreLocal) {
                await (this.__updTeamsHousingStatus__({rosterClubID}));

                await (__updRosterClub__(rosterClubID));
            }

            if (callback) {
                callback();
            }
        } catch (err) {
            if (callback) {
                callback(err);
            } else {
                throw err;
            }
        }

        function __findClubData__ (rosterClubID) {
            return Db.query(
                `SELECT rc.is_local "club_is_local",
                       NOT EXISTS (SELECT rt.is_local
                        FROM roster_team rt
                        WHERE rt.roster_club_id = rc.roster_club_id
                          AND (rt.is_local IS FALSE OR rc.is_local IS FALSE))
                           "all_teams_are_local"
                FROM roster_club rc
                WHERE rc.roster_club_id = $1`,
                [rosterClubID]
            )
            .then(result => result.rows[0] || null)
            .then(club => {
                if (!club) {
                    return Promise.reject({ validation: 'Club Data not found' });
                }

                return club;
            })
        }

        async function __calcTeamsTotalValues__ (rosterClubID, clubIsLocal, allTeamsAreLocal, recountMaxAcceptedNights) {
            let query, params = [rosterClubID];

            if (clubIsLocal && allTeamsAreLocal) {
                /* 32 = Verified reservation, Local or waivered */
                query =
                    `UPDATE "roster_team"
                     SET "status_housing"    = ${HOUSING_STATUS_CODE.VERIFIED}, 
                         "date_housing"      = COALESCE("date_paid", NOW())
                     WHERE "roster_club_id" = $1`;
            } else {
                const teams = await __getLoyaltyTeams(params);

                /* Teams for NON Local clubs should calc total_* values */
                query =
                    `UPDATE roster_team
                    SET total_tentative = COALESCE((
                        SELECT SUM(
                            CASE
                                WHEN ths.ths_hotel_status = 'Tentative'
                                    THEN ths.ths_tentative_nights
                                WHEN ths.ths_hotel_status = 'Accepted'
                                    THEN ths.ths_tentative_nights
                                WHEN ths.ths_hotel_status = 'Confirmed'
                                    THEN ths.ths_confirmed_nights
                                ELSE 0
                            END
                        ) 
                        FROM ths_booking ths
                        WHERE ths.roster_team_id = roster_team.roster_team_id
                            AND ths."ths_loyalty" = (CASE
                                WHEN ths.roster_team_id = ANY ($2::integer[])
                                    THEN 1
                                ELSE 0
                            END)
                    ), 0),
                    total_accepted = COALESCE((
                        SELECT SUM(
                            CASE
                                WHEN ths.ths_hotel_status = 'Accepted'
                                    THEN ths.ths_tentative_nights
                                WHEN ths.ths_hotel_status = 'Confirmed'
                                    THEN ths.ths_confirmed_nights
                                ELSE 0
                            END
                        )
                        FROM ths_booking ths
                        WHERE ths.roster_team_id = roster_team.roster_team_id
                            AND ths."ths_loyalty" = (CASE
                                WHEN ths.roster_team_id = ANY ($2::integer[]) THEN 1
                                ELSE 0
                            END)
                    ), 0),
                    total_confirmed = COALESCE((
                        SELECT SUM(
                            CASE
                                WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
                                ELSE 0
                            END
                        )
                        FROM ths_booking ths
                        WHERE ths.roster_team_id = roster_team.roster_team_id
                            AND ths."ths_loyalty" = (CASE
                                WHEN ths.roster_team_id = ANY ($2::integer[]) THEN 1
                                ELSE 0
                           END)
                    ), 0),
                    ths_loyalty = (
                        CASE
                            WHEN roster_team.roster_team_id = ANY ($2::integer[]) THEN 1
                            ELSE 0
                        END
                    ),
                    ths_total_loyalty_confirmed = COALESCE((
                        SELECT SUM(COALESCE(ths."ths_confirmed_nights", 0))
                        FROM "ths_booking" ths
                        WHERE ths.roster_team_id = roster_team.roster_team_id
                            AND ths."ths_hotel_status" = 'Confirmed'
                            AND ths."ths_loyalty" = 1
                    ), 0),
                    ${recountMaxAcceptedNights 
                        ? `max_total_accepted = COALESCE((SELECT SUM(max_accepted_nights_per_order.max_value)
                            FROM (
                                SELECT MAX(
                                    CASE
                                        WHEN ths_h.ths_hotel_status = 'Accepted'
                                            THEN ths_h.ths_tentative_nights
                                        WHEN ths_h.ths_hotel_status = 'Confirmed'
                                            THEN ths_h.ths_confirmed_nights
                                        ELSE 0
                                    END
                                ) max_value
                                FROM ths_history ths_h
                                WHERE ths_h.roster_team_id = roster_team.roster_team_id
                                    AND ths_h."ths_loyalty" = (CASE
                                        WHEN ths_h.roster_team_id = ANY ($2::integer[])
                                            THEN 1
                                        ELSE 0
                                    END)
                                GROUP BY ths_id
                            ) max_accepted_nights_per_order), 0), `
                        : ''
                    }
                    ths_total_loyalty_accepted  = COALESCE((
                        SELECT SUM(
                            CASE
                                WHEN ths.ths_hotel_status = 'Accepted'
                                    THEN ths.ths_tentative_nights
                                WHEN ths.ths_hotel_status = 'Confirmed'
                                    THEN ths.ths_confirmed_nights
                                ELSE 0
                            END
                        )
                        FROM ths_booking ths
                        WHERE ths.roster_team_id = roster_team.roster_team_id
                            AND ths."ths_loyalty" = 1
                    ), 0)
                    WHERE roster_club_id = $1 
                    ${!clubIsLocal || (clubIsLocal && !allTeamsAreLocal) ? 'AND roster_team.is_local IS NOT TRUE' : ''}`;

                params.push(`{${teams.join(',')}}`);
            }

            return Db.query(query, params);
        }

        function __updRosterClub__ (rosterClubID) {
            let query =
                `UPDATE roster_club 
                   SET 
                       total_tentative = 
                           (SELECT SUM(rt.total_tentative) 
                            FROM roster_team rt 
                            WHERE rt.roster_club_id = roster_club.roster_club_id 
                            AND rt.deleted IS NULL 
                            AND rt.is_local IS NOT TRUE), 
                       total_accepted = 
                           (SELECT SUM(rt.total_accepted) 
                            FROM roster_team rt 
                            WHERE rt.roster_club_id = roster_club.roster_club_id 
                            AND rt.is_local IS NOT TRUE), 
                       total_confirmed = 
                           (SELECT SUM(rt.total_confirmed) 
                            FROM roster_team rt 
                            WHERE rt.roster_club_id = roster_club.roster_club_id 
                            AND rt.is_local IS NOT TRUE), 
                       max_total_accepted = 
                           (SELECT SUM(rt.max_total_accepted) 
                            FROM roster_team rt 
                            WHERE rt.roster_club_id = roster_club.roster_club_id 
                            AND rt.is_local IS NOT TRUE) 
                 WHERE roster_club_id = $1`;

            return Db.query(query, [rosterClubID]);
        }

        async function __getLoyaltyTeams(params) {
            const query =
                `SELECT array_agg(rt.roster_team_id) "teams"
                    FROM roster_team rt
                    WHERE (
                        SELECT SUM(COALESCE(ths."ths_loyalty", 0)) > 0
                        FROM "ths_booking" ths
                        WHERE ths.roster_team_id = rt.roster_team_id
                        AND ths."ths_hotel_status" IN ('Confirmed', 'Accepted', 'Tentative')
                    ) IS TRUE
                    AND rt.roster_club_id = $1`;

            const result = await Db.query(query, params);

            return result?.rows[0]?.teams || [];
        }
    },

    __updTeamsHousingStatus__: async function ({rosterClubID, eventID}) {
        const rosterTeams = await this.__getHousingData({rosterClubID, eventID});

        if (!rosterTeams.length) {
            return 0;
        }

        let tr;
        try {
            tr = await Db.begin();

            await rosterTeams.reduce((prev, rosterTeam) => {
                return prev.then(() => this.__updateHousing(tr, rosterClubID, rosterTeam))
            }, Promise.resolve());

            await tr.commit();

            return rosterTeams.length;
        } catch (err) {

            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    },

    __updateHousing: function (tr, rosterClubID, rosterTeamData) {
        const { roster_team_id, event_id, status_housing, date_housing, max_total_accepted } = rosterTeamData
        const data = {
            status_housing,
            date_housing,
        }

        if(rosterClubID) {
            data.max_total_accepted = max_total_accepted;
        }

        const query = knex('roster_team').update(data)
            .where('roster_team_id', roster_team_id)
            .where('event_id', event_id);

        return tr.query(query);
    },

    __getHousingData({rosterClubID, eventID}) {
        let query = `
            SELECT
                rt.roster_team_id,
                e.event_id, e.housing_nights_required, e.housing_nights_threshold, e.housing_rooming_list_due_date,
                CASE
                    WHEN (rt.is_local IS TRUE OR (rt.is_local IS NULL AND rc.is_local IS TRUE)) THEN ${HOUSING_STATUS_CODE.VERIFIED}
                    WHEN (rt.total_tentative > 0 AND rt.total_accepted = 0) THEN ${HOUSING_STATUS_CODE.ISSUED}
                    WHEN (rt.ths_loyalty = 0 AND
                        CASE
                            WHEN e.housing_rooming_list_due_date IS NOT NULL THEN
                                (CURRENT_DATE <= e.housing_rooming_list_due_date AND
                                    (0 < rt.total_accepted AND rt.total_accepted < e.housing_nights_required))
                                OR
                                (CURRENT_DATE > e.housing_rooming_list_due_date AND
                                    rt.total_confirmed < e.housing_nights_required)
                            ELSE (0 < rt.total_accepted AND rt.total_accepted < e.housing_nights_required) OR
                                rt.total_confirmed < e.housing_nights_required
                        END
                    ) THEN ${HOUSING_STATUS_CODE.BELOW}
                    WHEN (rt.ths_loyalty = 1 AND
                        CASE
                            WHEN e.housing_rooming_list_due_date IS NOT NULL
                                THEN (
                                    CURRENT_DATE > e.housing_rooming_list_due_date AND
                                        (rt.ths_total_loyalty_confirmed < rt.max_total_accepted * 0.9)
                                )
                                ELSE (rt.ths_total_loyalty_confirmed < rt.max_total_accepted * 0.9)
                            END
                    ) THEN ${HOUSING_STATUS_CODE.FAULTY}
                    WHEN (rt.total_accepted >= e.housing_nights_required) THEN ${HOUSING_STATUS_CODE.VERIFIED}
                    ELSE ${HOUSING_STATUS_CODE.NONE}
                END AS status_housing,
                ${rosterClubID 
                    ? `(
                            SELECT GREATEST(rt.total_accepted, rt.max_total_accepted)
                            FROM "roster_team" AS _rt
                            WHERE (_rt.roster_team_id = rt.roster_team_id)
                        ) as max_total_accepted,`
                    : ''
                }
                CASE
                    WHEN (rt.total_accepted < e.housing_nights_required) THEN NULL
                    ELSE (
                        SELECT tb.ths_when_accepted
                        FROM "ths_booking" AS tb
                        WHERE (tb.ths_hotel_status IN ('Confirmed', 'Accepted'))
                            AND (tb.roster_team_id = rt.roster_team_id)
                        ORDER BY tb.ths_when_accepted LIMIT 1
                    )
                END as date_housing
            FROM "event" AS e
            INNER JOIN roster_team AS rt on rt.event_id = e.event_id
            INNER JOIN "roster_club" AS rc ON rc.roster_club_id = rt.roster_club_id
            WHERE e.has_status_housing IS TRUE`;

        if(rosterClubID) {
            query += ` AND rt.roster_club_id = ${rosterClubID}`;
        }

        if(eventID) {
            query += ` AND e.event_id = ${eventID}`;
        }

        return Db.query(query).then(({ rows }) => rows || []);
    },

    update_event_clubs_housing: function (eventID) {
        if (!Number.isInteger(eventID)) {
            return Promise.reject({ validation: 'Invalid Event Identifier' });
        }

        return __findEventClubs__(eventID)
        .then(clubsList => {
            if (clubsList.length === 0) {
                return 0;
            } else {
                return clubsList.reduce((prev, club) =>
                    prev.then(() => this.update_club_housing({rosterClubID: club.id}))
                , Promise.resolve())
                .then(() => clubsList.length)
            }
        })
    },

    changeTeamStatus: async function (event_id, roster_team_id, status_housing) {
        if (!Number.isInteger(event_id)) {
            return Promise.reject({ validation: 'Invalid Event Identifier' });
        }

        if (!Number.isInteger(roster_team_id)) {
            return Promise.reject({ validation: 'Invalid Team Identifier' });
        }

        if (!Number.isInteger(status_housing)) {
            return Promise.reject({ validation: 'Invalid Housing Status Identifier' });
        }

        if(!_.find(this.HOUSING_STATUSES, ({id})=>id === status_housing)) {
            throw { validation: 'Unknown Housing Status Identifier' };
        }

        const rt_fields = [
            'rt.status_housing',
            'rt.team_name',
        ];

        const {rows: [oldRosterTeam]} = await Db.query(
            squel.select()
                .fields(rt_fields)
                .from('roster_team', 'rt')
                .join('roster_club', 'rc', 'rc.roster_club_id = rt.roster_club_id')
                .join('event', 'e', 'e.event_id = rt.event_id')
                .field('rc.is_local', 'is_local_club')
                .field('e.housing_company_id = 1', 'is_ths_event')
                .where('rt.event_id = ?', event_id)
                .where('rt.roster_team_id = ?', roster_team_id)
        );
        if(!oldRosterTeam) {
            throw { validation: "Roster Team not found" };
        }
        if(oldRosterTeam.is_local_club) {
            throw { validation: "Manual Housing Status changes of a local team is forbidden" };
        }

        let updateQuery = squel.update()
            .table('roster_team', 'rt')
            .where('rt.event_id = ?', event_id)
            .where('rt.roster_team_id = ?', roster_team_id)
            .set('status_housing', status_housing)
            .returning(rt_fields.join(', '));

        if (!oldRosterTeam.is_ths_club) {
            updateQuery.set('housing_status_changed_at', 'NOW()');
        }

        const updateResult = await Db.query(updateQuery);

        if(updateResult.rowCount !== 1) {
            throw new Error('Error updating roster_team.status_housing');
        }
        return {
            oldRosterTeam,
            newRosterTeam: updateResult.rows[0],
        };
    },

    getStatusName(id) {
        const status = _.find(this.HOUSING_STATUSES, (status) => status.id === id);
        if(!status) {
            throw { validation: 'Unknown Housing Status Identifier' };
        }
        return status.name;
    },

    /**
     * This method set 'housing_status_changed_at' only for non-THS events.
     *
     * @param {Number} eventID
     * @param {Number|Array} rosterClubID
     * @returns {Promise<void>}
     */
    updateHousingStatusChangedAt: async function({ eventID, rosterClubID }) {
        if (!Number.isInteger(eventID)) {
            throw { validation: 'Invalid Event Identifier'}
        }

        const rosterClubIDValidation = validateRosterClubID(rosterClubID);

        if (!rosterClubIDValidation) {
            throw { validation: 'Invalid Roster Club Identifier'}
        }

        const _rosterClubID = Array.isArray(rosterClubID)
            ? `{${rosterClubID.join(',')}}`
            : `{${rosterClubID}}`;

        const query = squel
            .update()
            .table('roster_team', 'rt')
            .set('housing_status_changed_at', 'NOW()')
            .where('rt.roster_club_id = ANY(?)', _rosterClubID)
            .where('EXISTS(?)',
                squel.select()
                    .from('event', 'e')
                    .field('1')
                    .where('e.event_id = ?', eventID)
                    .where('e.housing_company_id <> 1') // non-THS event
            );

        await Db.query(query);
    },
    getCompanies: async function() {
        const query = knex('housing_company')
            .select({
                id: 'housing_company_id',
                name: 'name',
            })
            .orderBy('housing_company_id');
        const companies = await Db.query(query).then(result => result.rows)
        companies.push({
            id: -1,
            name: 'Other...',
        });

        return companies;
    },
    verifyAuthValidation(authorization, authorizationHashParams) {
        const authorizationHash = this.housingHashService.generateAuthorizationHash(authorizationHashParams);
        const success = authorization === authorizationHash;

        if (!success) {
            throw {
                message: 'ERROR: Authorization failed',
                success: success
            };
        }
    }
};

function __findEventClubs__ (eventID) {
    return Db.query(
        `SELECT rc."roster_club_id" "id"
         FROM "roster_club" rc 
         WHERE rc."event_id" = $1
            AND rc."deleted" IS NULL`,
        [eventID]
    ).then(result => result.rows);
}

function validateRosterClubID(value) {
    const rosterClubIDValidationRules = [
        Array.isArray(value) && value.length,
        Number(value) && Number.isInteger(value),
    ];

    return rosterClubIDValidationRules.some(rule => rule);
}
