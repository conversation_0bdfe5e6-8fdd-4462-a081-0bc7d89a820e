'use strict';

const TeamsPaymentService   = require('./payment/_TeamsService');
const TicketsPaymentService = require('./payment/_TicketsPaymentService');

const { PAYMENT_PROVIDER } = require('../constants/payments');

class PaymentService {
	constructor () {}

	get teams () {
		return TeamsPaymentService;
	}

	get tickets () {
	    return TicketsPaymentService;
    }

    get __PAYMENT_PROVIDERS__() {
        return PAYMENT_PROVIDER;
    }

    /**
     * Returns "purchase" rows, that represent Stripe-Charges created via Stripe Connect 
     * by card/ACH, that have empty "stripe_balance_available" column
     * 
     * "purchase"."stripe_balance_available" - The timestamp when the charge will become 
     * available on the stripe account's balance
     * 
     * @param  {String} minCreationDate - The minimum creation date of a purchase row. YYYY-MM-DD
     * @param  {Number} limit           - Maximum rows number returned from the DB
     * @param  {Number} offset          - Number of rows to skip before beginning to return rows
     */
    findPaymentsToSetAvailability (minCreationDate, limit = 10, offset = 0) {
        return Db.query(
            `SELECT 
                p."stripe_charge_id" "charge_id", 
                p."purchase_id", 
                p."event_id"
             FROM "purchase" p 
             WHERE p.type IN ('card', 'ach')
                AND p.status = 'paid' 
                AND p."created"::DATE >= $1::DATE
                AND p."stripe_balance_available" IS NULL
                AND p."stripe_payment_type" = 'connect'
             ORDER BY p."created" DESC
             LIMIT $2 
             OFFSET $3`,
            [minCreationDate, limit, offset]
        ).then(res => res.rows);
    }

    setPaymentAvailableOnDate (chargeID, availableOn) {
        return Db.query(
            `UPDATE "purchase" p 
             SET "stripe_balance_available" = TO_TIMESTAMP($2)
             WHERE p."stripe_charge_id" = $1
                AND p."stripe_balance_available" IS NULL`,
            [chargeID, availableOn]
        ).then(result => result.rowCount)
    }

    getPaymentDataForQRContent (purchaseID, eventID) {
        return Db.query(
            `SELECT
                e.name "event_short_name", e.long_name "event_name", 
                COALESCE((tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) AS assigned_tickets_mode,
                (p.first || ' ' || p.last) "ticket_holder", e.date_start, e.date_end, p.first, p.last, p.status,
                e.tickets_settings->>'qrcode_version' "qrcode_version",
                p."ticket_barcode", p."event_id", p."purchase_id",
                CASE WHEN p."deactivated_at" IS NULL THEN false ELSE true END AS deactivated,
                (EXTRACT(EPOCH FROM NOW()))::INT "purchase_timestamp", ( 
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
                    FROM ( 
                        SELECT
                            et.event_ticket_id,
                            et.sort_order
                        FROM event_ticket et 
                        WHERE et.event_id = e.event_id 
                        ORDER BY et.event_ticket_id ASC 
                    ) "t" 
                ) "event_tickets", (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
                    FROM (
                        SELECT 
                            pt."quantity",
                            et."sort_order" "order",
                            et.label "ticket_label",
                            et.ticket_type,
                            (
                                SELECT (
                                    ARRAY_AGG(
                                        TO_TIMESTAMP(
                                            vd::TEXT || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS'
                                        )::TIMESTAMP
                                        ORDER BY (TO_TIMESTAMP(vd::TEXT || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')) ASC
                                   )
                               )
                                FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                            ) "valid_dates",
                            et.short_label
                        FROM "purchase_ticket" pt 
                        INNER JOIN "event_ticket" et 
                            ON et."event_ticket_id" = pt."event_ticket_id"
                            AND et."event_id" = p."event_id"
                        WHERE pt."purchase_id" = p."purchase_id"
                    ) "t"
                ) "purchase_tickets"
            FROM "purchase" p 
            INNER JOIN "event" e 
                ON e."event_id" = p."event_id"
            WHERE p."payment_for" = 'tickets' 
                AND p."event_id" = $1
                AND p."purchase_id" = $2
            `,
            [eventID, purchaseID]
        ).then(res => res.rows[0] || null)
    }

	// 🔧TODO: get tickets, get booths
}

module.exports = new PaymentService();
