
class StaffingService {
    constructor() {}

    getArbiterpayExportData(eventID, paymentOption, type, officialAdditionalRoleEnable) {
        return Db.query(`${GET_ARBITERPAY_EXPORT_SQL(eventID, paymentOption, type, officialAdditionalRoleEnable)}`)
            .then(({ rows }) => rows || []);
    }
}

function GET_ARBITERPAY_EXPORT_SQL(eventID, paymentOption, memberType, officialAdditionalRoleEnable) {
    let ratesQuery = OfficialsService.payout.getBaseOfficialPayoutsTotalSql(eventID, officialAdditionalRoleEnable);

    let query = knex('event_official AS eof')
        .select(
            'eof.arbiterpay_username AS username',
            'eof.arbiterpay_account_number AS account_number',
            'e.name AS description',
            {
                total: knex.raw(`
                    ${memberType === OfficialsService.payout.MEMBER_TYPE.OFFICIAL  ? `
                    COALESCE((${ratesQuery
                        .select(knex.raw(`COALESCE(SUM(eor.rate), 0)`))
                        .whereRaw('eo.event_official_id = eof.event_official_id')
                        .groupBy('eo.event_official_id')
                    }), 0)
                    +
                    (${knex('event_official_additional_payment AS ap')
                        .select(knex.raw(`COALESCE(sum(amount), 0)`))
                        .where('event_id', eventID)
                        .whereRaw('ap.event_official_id = eof.event_official_id')
                        .where('ap.member_type', memberType)
                    })` :
                    
                    memberType === OfficialsService.payout.MEMBER_TYPE.STAFF  ? `
                    (${knex('event_official_additional_payment AS ap')
                        .select(knex.raw(`COALESCE(sum(amount), 0)`))
                        .where('event_id', eventID)
                        .whereRaw('ap.event_official_id = eof.event_official_id')
                        .where('ap.member_type', memberType)
                    })` : ''}
                    
                    -
                    (${knex('official_payout AS op')
                        .select(knex.raw(`COALESCE(sum(amount), 0)`))
                        .whereRaw('event_official_id = eof.event_official_id')
                        .where('op.member_type', memberType)
                    })
                `)
            }
        )

        .where('eof.event_id', eventID)

        .innerJoin('event AS e', 'e.event_id', eventID)
        .groupBy(
            'eof.arbiterpay_username',
            'eof.arbiterpay_account_number',
            'e.name',
            'eof.event_official_id'
        )

    if(memberType === OfficialsService.payout.MEMBER_TYPE.OFFICIAL) {
        query.where('eof.payment_option', paymentOption)
            .where('eof.work_status', OfficialsService.payout.WORK_STATUS.APPROVED)
    } else {
        query.where('eof.staff_payment_option', paymentOption)
            .where('eof.staff_work_status', OfficialsService.payout.WORK_STATUS.APPROVED)
    }

    return query;
}

module.exports = new StaffingService();
