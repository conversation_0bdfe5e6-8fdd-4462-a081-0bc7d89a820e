'use strict';

const
    argv = require('optimist').argv,
    co = require('co'),
    assert = require('assert'),
    StripeConnect = require('../lib/StripeConnect'),
    sqlGenerator = require('./teams-payments/_SQLGeneratorService'),
    { PAYMENT_STATUSES } = require('../constants/teams'),
    { FEE_PAYER, PAYMENT_PROVIDER } = require('../constants/payments'),
    moment = require('moment');

const TeamsPaymentNotificationsService = require('../services/payment/teams/notification/_TeamsPaymentNotificationsService');
const BalanceInformationService = require('./teams-payments/BalanceInformationService');
const RefundsService = require('./teams-payments/__RefundService');
const MetadataService = require('./teams-payments/_TeamsStripeMetadataService');

const teamsConstants = require('../constants/teams');

let swUtils = require('../lib/swUtils');
const PaymentHubService = require('./PaymentHubService');

const SETTINGS_KEY = argv.prod ? 'stripe_connect' : 'stripe_connect_dev';
const PLAID_KEY = argv.prod ? 'plaid_prod' : 'plaid_dev';

const FIELDS_TO_PARSE = ['amount', 'total_discount', 'surcharge', 'due', 'teams'];

const PAYMENT_TYPE_REG_EXP = /^check|card|ach$/i;
const STRIPE_TYPE_REG_EXP = /^card|ach$/i;

const MAX_EXTRA_FEE_COEF = 0.8;

class TeamsPaymentService {
    constructor() {
        this._SQLGeneratorService = new sqlGenerator(
            SETTINGS_KEY, PLAID_KEY, (argv.prod === true), teamsConstants.ENTRY_STATUSES.DECLINED);
    }

    get _utils() {
        return swUtils;
    }

    set _utils(value) {
        swUtils = value;
    }

    get CARD_TYPE() {
        return 'card';
    }

    get CHECK_TYPE() {
        return 'check';
    }

    get ACH_TYPE() {
        return 'ach';
    }

    get ACH_MIN_AMOUNT () {
        return 100;
    }

    get refund () {
        return RefundsService;
    }

    get metadata () {
        return MetadataService;
    }

    get PAYMENT_STATUS() {
        return {
            PAID: 22,
            NONE: 21,
            PARTIAL: 23,
            PENDING: 24,
            REFUNDED: 25,
            DISPUTED: 26
        }
    }

    __getTeamDue__(regFee, paid = 0, discount = 0, surcharge = 0) {
        return swUtils.normalizeNumber(regFee + surcharge - paid - discount);
    }

    // covered 😄👍
    recountPrice(settings, receipt, eventSurcharge, paymentType) {
        const teamsList = settings.teams;

        if (!teamsList || teamsList.length === 0) {
            throw new Error('Teams list should not be empty');
        }

        let findTeam = id => {
            for (let team of teamsList) {
                if (team.id === id) {
                    return team;
                }
            }
            return null;
        };

        let total = 0,
            surcharge = 0,
            netProfit = 0,
            swFee = 0,
            purchaseTeams = [];

        for (let receiptItemID of receipt) {
            let team = findTeam(receiptItemID);

            if (!team) {
                throw new Error(`Team #${receiptItemID} is not available for payment.`);
            }

            let teamAmount = this.__getTeamDue__(team.reg_fee, team.paid, team.discount);

            total += teamAmount;

            swFee += team.sw_fee;

            const teamSurcharge = this._getTeamDivisionSurcharge(team, paymentType) || eventSurcharge;

            surcharge += teamSurcharge;

            purchaseTeams.push({
                name: team.name,
                amount: teamAmount,
                division_fee: team.division_fee,
                division_id: team.division_id,
                roster_team_id: receiptItemID,
                surcharge: teamSurcharge,
                discount: team.discount,
                team_sw_fee: team.sw_fee
            });
        }

        surcharge = swUtils.normalizeNumber(surcharge);

        if(settings.teams_sw_fee_payer === FEE_PAYER.BUYER) {
            total = swUtils.normalizeNumber(total + surcharge + swFee);
        } else {
            total = swUtils.normalizeNumber(total + surcharge);
        }

        netProfit = swUtils.normalizeNumber(total - swFee);
        swFee = swUtils.normalizeNumber(swFee);

        return { total, surcharge, netProfit, swFee, purchaseTeams };
    }

    // Not covered
    _getTeamSurcharge(data, type) {
        switch (type) {
            case this.CARD_TYPE:
                return data.card_surcharge;
            case this.ACH_TYPE:
                return data.card_surcharge;
            default:
                return 0;
        }
    }

    _getTeamDivisionSurcharge (data, type) {
        switch (type) {
            case this.CARD_TYPE:
            case this.ACH_TYPE:
                return data.division_surcharge;
            default:
                return 0;
        }
    }

    // Not covered
    _recountPrices(settings, receipt, total, paymentType) {
        let eventSurcharge = this._getTeamSurcharge(settings, paymentType)

        return this.recountPrice(settings, receipt, eventSurcharge, paymentType);
    }

    get POSSIBLE_DISPUTES_ESCROW_RATIO () {
        return 3;
    }

    /**
     * covered 😄👍
     * 
     * NOTE: the column "teams_escrow_collected" is not in use in the project, so this method
     * might be removed in the future
     */
    modifyCollectedEscrowSWFeeAmount (tr, eventID, amount) {
        if (_.isNaN(Number(eventID)) || (Number(eventID) <= 0)) {
            return Promise.reject(new Error('Event ID should be a positive integer'));
        }

        if (!_.isNumber(amount)) {
            return Promise.reject(new Error('Amount should be a number'));
        }

        let dbClient = (tr || Db);

        return dbClient.query(
            `UPDATE "event" e 
             SET "teams_escrow_collected" = (COALESCE(e."teams_escrow_collected", 0) + ($2)::NUMERIC)
             WHERE e."event_id" = $1`,
            [eventID, amount]
        ).then(res => (res.rowCount > 0));
    }

    countPossibleDisputesEscrow (regFee) {
        return this._utils.normalizeNumber(this.POSSIBLE_DISPUTES_ESCROW_RATIO * regFee);
    }

    // covered 😄👍
    getSWFeeEscrowTarget (
        eventID, skipDisputesCounting = false, getSummands = false, currentReceiptSWFee = 0, balanceInformationMode = false, tr, teamsInPayment = []) {

        const db = tr || Db;

        if (!Number.isInteger(eventID)) {
            return Promise.reject(new Error('Event ID should be an integer!'));
        }

        let pendingACH = `(rt."status_paid" = ? AND p.type = 'ach')`;
        let notLostDispute = `(rt.status_paid = ? AND p.dispute_status <> 'lost')`;

        let rosterTeamExpression = squel.expr()
            .and('rt.event_id = e.event_id')
            .and('rt.deleted IS NULL');

        if(teamsInPayment.length) {
            rosterTeamExpression.and(`rt.roster_team_id NOT IN (${teamsInPayment.join(', ')})`);
        }

        let query = squel
            .select()
            .from('event', 'e')
            .field(squel.str(`COALESCE(COUNT(distinct rt."roster_team_id")
                    FILTER ( 
                        WHERE (rt."status_entry" = ? OR (rt."status_paid" = ? OR ${pendingACH} OR ${notLostDispute})
                    )
                    ), 0) + ${teamsInPayment.length}
            `,
                teamsConstants.ENTRY_STATUSES.ACCEPTED,
                teamsConstants.PAYMENT_STATUSES.PAID,
                teamsConstants.PAYMENT_STATUSES.PENDING,
                teamsConstants.PAYMENT_STATUSES.DISPUTED
            ), 'teams_qty')
            .field(`COALESCE(e."teams_entry_sw_fee", 0)`, 'sw_fee')
            .field(`COALESCE(e."reg_fee", 0)`, 'reg_fee')
            .field(`COALESCE(e."teams_sw_target_balance", 0)`, 'additional_balance')
            .left_join('roster_team', 'rt', rosterTeamExpression)
            .left_join(
                'purchase_team',
                'pt',
                `pt.roster_team_id = rt.roster_team_id AND pt.event_id = e.event_id AND pt.canceled IS NULL`
            )
            .left_join('purchase', 'p', `p.purchase_id = pt.purchase_id AND p.payment_for = 'teams'`)
            .where('e.event_id = ?', eventID)
            .group('e.event_id');

        if (balanceInformationMode) {
            query
                .field(
                    squel.str(
                        `COUNT(*) FILTER (
                            WHERE((rt."status_paid" = ? OR ${pendingACH} OR ${notLostDispute}) AND rt.status_entry = ?)
                        )::INTEGER`,
                        teamsConstants.PAYMENT_STATUSES.PAID,
                        teamsConstants.PAYMENT_STATUSES.PENDING,
                        teamsConstants.PAYMENT_STATUSES.DISPUTED,
                        teamsConstants.ENTRY_STATUSES.ACCEPTED
                    ),
                    'count_status_accepted_and_paid')
                .field(
                    squel.str(
                        `COUNT(*) FILTER (
                            WHERE((rt."status_paid" = ? OR ${pendingACH} OR ${notLostDispute}) AND rt.status_entry <> ?)
                        )::INTEGER`,
                        teamsConstants.PAYMENT_STATUSES.PAID,
                        teamsConstants.PAYMENT_STATUSES.PENDING,
                        teamsConstants.PAYMENT_STATUSES.DISPUTED,
                        teamsConstants.ENTRY_STATUSES.ACCEPTED
                    ),
                    'count_status_paid')
                .field(
                    squel.str(
                        'COUNT(*) FILTER (WHERE(rt.status_entry = ? AND rt.status_paid <> ?))::INTEGER'
                        , teamsConstants.ENTRY_STATUSES.ACCEPTED, teamsConstants.PAYMENT_STATUSES.PAID
                    ),
                    'count_status_accepted')
        }

        return db.query(query).then(resp => {
            let data = resp.rows[0] || null;

            if (data === null) {
                return Promise.reject(new Error(`Event "${eventID}" not found`));
            }

            let teamsQty             = parseInt(data.teams_qty, 10),
                swFee                = parseFloat(data.sw_fee),
                regFee               = parseFloat(data.reg_fee),
                additionalBalance    = parseFloat(data.additional_balance),
                disputes             = 0;

            if (!skipDisputesCounting) {
                disputes = this.countPossibleDisputesEscrow(regFee);
            }

            let escrow = 
                    this._utils.normalizeNumber(teamsQty * swFee + disputes + additionalBalance);

            if (escrow === 0 && currentReceiptSWFee > 0) {
                escrow = this._utils.normalizeNumber(escrow + currentReceiptSWFee);
            }

            if (!getSummands && !balanceInformationMode) {
                return escrow;
            }

            let result = { escrow };

            if (getSummands) {
                result.summands = {
                    sw_fee              : swFee,
                    teams_qty           : teamsQty,
                    reg_fee             : regFee,
                    additional_balance  : additionalBalance,
                    disputes
                }
            }

            if (balanceInformationMode) {
                result.statusesInformation = {
                    accepted_or_paid: teamsQty,
                    accepted_and_paid: data.count_status_accepted_and_paid,
                    accepted_only: data.count_status_accepted,
                    paid_only: data.count_status_paid
                }
            }

            return result;
        })
    }

    // covered 😄👍 feeType part is not covered
    getCollectedSWFee (eventID, feeType, dateFrom, dateTo, tr) {
        if (_.isNaN(Number(eventID))) {
            return Promise.reject(new Error('Event ID should be an integer!'));
        }

        const db = tr || Db;

        const COLLECTED = 'COALESCE(p."collected_sw_fee", 0)';
        const ADDITIONAL = 'COALESCE(p."additional_fee_amount", 0)';

        let counterBlock, whereBlock = '', params = [eventID];

        if (feeType === 'collected') {
            counterBlock = COLLECTED;
        } else if (feeType === 'additional') {
            counterBlock = ADDITIONAL;
        } else {
            counterBlock = `${COLLECTED} + ${ADDITIONAL}`;
        }

        if (dateFrom) {
            params.push(dateFrom);
            whereBlock += ` AND p."created" >= ($${params.length})::DATE`;
        }

        if (dateTo) {
            params.push(dateTo);
            whereBlock += ` AND p."created" <= ($${params.length})::DATE`;
        }
        
        /**
        * NOTE: we can collect fee (either SW or SW Extra) only via Stripe Connect's application
        * fee parameter. That is why we check if the payment was created via Connect
        * For NOT connected payments SW fee taxing is performed by leaving some partial amount on
        * the SW Account at the moment of manual transfers. 
        **/
        return db.query(
            `SELECT 
                COALESCE(SUM(${counterBlock}), 0) "sw_fee"
             FROM "purchase" p 
             WHERE p."event_id" = $1 
                AND p."payment_for" = 'teams' 
                AND p."status" <> 'canceled'
                AND p."type" NOT IN('check', 'waitlist')
                AND (p."stripe_payment_type" = 'connect' OR p."payment_provider" = '${PAYMENT_PROVIDER.PAYMENT_HUB}')
                ${whereBlock}`,
            params
        ).then(result => {
            let row = result.rows[0] || { sw_fee: 0 };

            return parseFloat(row.sw_fee, 10);
        })
    }

    /**
     * covered 😄👍
     *
     * @param {number} targetSWFee       - the amount of sw fee to be collected for teams entry
     * @param {number} collectedSWFee    - the amount of sw fee that is already collected
     * @param {number} suggestedSWFee    - sum of sw fee of not canceled teams that are in a receipt
     * @param {number} eoProviderFee       - Stripe Processing Fee amount that EO is taxed.
     *                                       This is not the same amount of the Stripe Processing Fee
     *                                       that the Stripe Account of SW is taxed.
     * @param {number} teamSWFee         - sw fee for each team
     * @param {number} amount            - the amount of purchase
     * @param {Object} settings          - additional event setting
     * @param {boolean} settings.do_not_collect_sw_fee - if true - do not add SW Fee to application fee
     * @param {boolean} settings.collect_extra_fee - if true - fo not add uncollected fee to application fee
     **/

    countSWFeeToTakeInPurchase (
        targetSWFee,
        collectedSWFee,
        suggestedSWFee,
        eoProviderFee,
        teamSWFee,
        amount,
        settings = {}
    ) {
        if (!_.isNumber(targetSWFee)) {
            throw new Error('Expecting target SW fee to be a number');
        }

        if (targetSWFee < 0) {
            throw new Error('Expecting target SW fee to be greater or equal zero');
        }

        if (!_.isNumber(collectedSWFee)) {
            throw new Error('Expecting collected SW fee to be a number');
        }

        if (collectedSWFee < 0) {
            throw new Error('Expecting collected SW fee to be greater or equal zero');
        }

        if (!_.isNumber(suggestedSWFee)) {
            throw new Error('Expecting suggested SW fee to be a number');
        }

        if (suggestedSWFee < 0) {
            throw new Error('Expecting suggested SW fee to be greater than or equal to zero');
        }

        if (!_.isNumber(eoProviderFee)) {
            throw new Error('Expecting EO Stripe fee to be a number');
        }

        if (eoProviderFee <= 0) {
            throw new Error('Expecting EO Stripe fee to be greater than zero');
        }

        if (!_.isNumber(teamSWFee)) {
            throw new Error('Expecting SW Team fee to be a number');
        }

        if (teamSWFee < 0) {
            throw new Error('Expecting SW Team fee to be greater than zero');
        }

        if (!_.isNumber(amount)) {
            throw new Error('Expecting amount to be a number');
        }

        if (amount < 0) {
            throw new Error('Expecting amount to be greater than zero');
        }

        let difference = this._utils.normalizeNumber(targetSWFee - collectedSWFee);

        if (this.__shouldSkipSWFeeCollection(settings)) {
            return this.__calculateFeeWithoutSWFee(eoProviderFee);
        }

        if(settings.collect_extra_fee) {
            return this.__calculateSWFeeInPurchaseWithExtraFee(
                difference, suggestedSWFee, eoProviderFee, teamSWFee, amount
            );
        } else {
            return this.__calculateSWFeeInPurchaseWithoutExtraFee(
                difference, suggestedSWFee, eoProviderFee
            );
        }
    }

    __calculateFeeWithoutSWFee (eoProviderFee) {
        return { application_fee: eoProviderFee, details: { sw_fee: 0, provider_fee: eoProviderFee, extra_fee: 0 } };
    }

    __shouldSkipSWFeeCollection(settings) {
        return settings.do_not_collect_sw_fee;
    }

    __calculateSWFeeInPurchaseWithoutExtraFee (difference, suggestedSWFee, eoProviderFee) {
        let sw_fee,
            provider_fee = eoProviderFee;


        if (difference <= 0) {
            /*
            * The collected amount is greater that the target, so we do not take SW fee
            */
            sw_fee = 0;

        } else if (difference <= suggestedSWFee) {
            sw_fee = difference
        } else {
            sw_fee = suggestedSWFee;
        }

        let application_fee = this._utils.normalizeNumber(provider_fee + sw_fee);

        return { application_fee, details: { sw_fee, provider_fee, extra_fee: 0 } };
    }

    __calculateSWFeeInPurchaseWithExtraFee (difference, suggestedSWFee, eoProviderFee, teamSWFee, amount) {
        let sw_fee,
            extra_fee  = 0,
            provider_fee = eoProviderFee;

        if (difference <= 0) {
            /*
            * The collected amount is greater that the target,
            * so we do not take neither SW fee nor extra SW fee
            */

            sw_fee       = 0;
            extra_fee    = 0;

        } else if (difference <= suggestedSWFee) {
            sw_fee = difference
        } else {
            /*
            * In this case we have difference between target and collected fee amount
            * greater than suggested SW fee, so we can take an extra fee amount.
            * But we have a restriction:
            * extra fee amount should't be greater that the suggested, so overall fee amount
            * can be * MAX_EXTRA_FEE_COEF (0.8)
            */

            sw_fee      = suggestedSWFee;
            extra_fee   = this.calculateExtraFee(amount, teamSWFee);

            if (extra_fee > difference) {
                extra_fee = this._utils.normalizeNumber(difference - sw_fee);
            }

            extra_fee = this.checkAmountWithoutFees(amount, extra_fee, provider_fee, sw_fee, teamSWFee);
        }

        let application_fee = this._utils.normalizeNumber(provider_fee + sw_fee + extra_fee);

        return { application_fee, details: { sw_fee, extra_fee, provider_fee } };
    }

    voidCheckPayment (eventID, purchaseID, clubOwnerID, eventOwnerID, userID, purchaseTeamIDList) {

        let __modifyRosterTeamRows__ = (tr, rosterTeamsIDs, isCheckReceived) => {
            let cancellationStatus = isCheckReceived
                                        ? teamsConstants.PAYMENT_STATUSES.REFUNDED
                                        : teamsConstants.PAYMENT_STATUSES.NOT_PAID;

            return tr.query(
                `UPDATE "roster_team" rt
                    SET
                    "status_paid" = (
                        CASE 
                            WHEN ( 
                                SELECT SUM(pt.amount) "paid" 
                                FROM purchase_team pt 
                                LEFT JOIN purchase p 
                                    ON pt.purchase_id = p.purchase_id
                                WHERE pt.roster_team_id = rt.roster_team_id 
                                    AND pt.canceled IS NULL 
                                    AND p.status = 'paid'
                             ) > 0 
                            THEN ($1)::INTEGER /* paid */ 
                            WHEN ( 
                                SELECT SUM(pt.amount) "pending" 
                                FROM purchase_team pt 
                                LEFT JOIN purchase p 
                                  ON pt.purchase_id = p.purchase_id
                                WHERE pt.roster_team_id = rt.roster_team_id 
                                    AND pt.canceled IS NULL 
                                    AND p.status = 'pending'
                            ) > 0 
                            THEN ($2)::INTEGER /* pending */ 
                            ELSE ($3)::INTEGER /* refunded */ 
                        END
                    ),
                    "date_paid" = (
                        CASE
                           WHEN ( 
                               SELECT SUM(pt.amount) "paid" 
                               FROM purchase_team pt 
                               LEFT JOIN purchase p 
                                   ON pt.purchase_id = p.purchase_id
                               WHERE pt.roster_team_id = rt.roster_team_id 
                                   AND pt.canceled IS NULL 
                                   AND p.status IN('paid', 'pending')
                            ) > 0
                            THEN rt."date_paid"
                            ELSE NULL
                        END
                    )
                 WHERE rt.roster_team_id IN (${rosterTeamsIDs})`, [
                    teamsConstants.PAYMENT_STATUSES.PAID,
                    teamsConstants.PAYMENT_STATUSES.PENDING,
                    cancellationStatus
                 ]
            )
        }

        return co(function* () {
            let tr = yield (Db.begin());

            /* TODO: add validation for full cancellation using purchaseTeamIDList field */

            let isPartialCancellation = __checkPurchaseTeamListExists__(purchaseTeamIDList);

            let purchaseAction;

            if (isPartialCancellation) {
                /* We do not cancel purchase row in case of partial cancellation */
                purchaseAction = __getPurchaseRow__;
            } else {
                purchaseAction = __cancelPurchaseRow__;
            }

            let purchase = yield (purchaseAction(tr, purchaseID, eventID, clubOwnerID, eventOwnerID));

            if (purchase === null) {
                tr.rollback();
                throw {
                    validation: 'Only pending check payments allowed for cancellation'
                }
            }

            let result = yield (__cancelPurchaseTeamRow__(tr, purchaseID, purchaseTeamIDList));

            let rosterTeams = result.rows;

            let rosterTeamsIDs = swUtils.numArrayToString(rosterTeams, 'roster_team_id');

            if (!rosterTeamsIDs) {
                tr.rollback();
                throw {
                    validation: 'No Teams found'
                }
            }

            result = yield (__modifyRosterTeamRows__(tr, rosterTeamsIDs, purchase.is_received));

            if (result.rowCount === 0) {
                tr.rollback();
                throw {
                    validation: 'Event Teams to update not found'
                }
            }

            let { fee: swFeeSum, amount: canceledAmountSum } = rosterTeams.reduce((sumData, team) => {
                sumData.fee     += parseFloat(team.sw_fee, 10);
                sumData.amount  += parseFloat(team.amount, 10);

                return sumData;
            }, { fee: 0, amount: 0 });

            swFeeSum            = swUtils.normalizeNumber(swFeeSum);
            canceledAmountSum   = swUtils.normalizeNumber(canceledAmountSum);

            let dateRefunded;

            if (isPartialCancellation) {
                let canceledQty = yield (__getCanceledPTRowsQty__(tr, purchaseID));

                if (parseInt(canceledQty, 10) === parseInt(purchase.pt_qty, 10)) {
                    loggers.debug_log.verbose('Full cancellation');

                    yield (__cancelPurchaseRow__(tr, purchaseID, eventID, clubOwnerID, eventOwnerID));

                    isPartialCancellation = false;
                } else {
                    let modifiedPurchaseRow = yield (
                        __modifyNetProfitAndAmount__ (tr, purchaseID, swFeeSum, canceledAmountSum)
                    );
                    dateRefunded = Number(modifiedPurchaseRow.date_refunded);
                }
            }

            yield (__insertPurchaseHistoryRow__(
                tr, purchaseID, userID, isPartialCancellation?'payment.partially-canceled':null
            ));

            yield (tr.commit());

            return { 
                teams       : rosterTeams, 
                isFullVoid  : !isPartialCancellation, 
                canceled    : canceledAmountSum, 
                dateRefunded 
            };
        }.bind(this))

        function __checkPurchaseTeamListExists__ (list) {
            return Array.isArray(list) && (list.length > 0);
        }

        function __genWHERESQLBlock__ (purchaseID, eventID, clubOwnerID, eventOwnerID) {
            let query = 
                `WHERE p."purchase_id" = $1
                    AND p."event_id" = $2
                    AND p."type" = 'check'`;

            let params = [purchaseID, eventID]

            if (clubOwnerID) {
                params.push(clubOwnerID);

                query += 
                    `
                    AND p.club_owner_id = $${params.length}`;

            }

            if (eventOwnerID) {
                params.push(eventOwnerID);

                query +=
                    `
                    AND EXISTS (
                        SELECT e."event_id"
                        FROM "event" e 
                        WHERE e."event_id" = p."event_id"
                            AND e."event_owner_id" = $${params.length}
                    )`
            }

            return { query, params };
        }

        function __getPurchaseRow__ (tr, purchaseID, eventID, clubOwnerID, eventOwnerID) {
            let { query: whereBlock, params } = __genWHERESQLBlock__(purchaseID, eventID, clubOwnerID, eventOwnerID);

            let query = 
                `SELECT 
                    p."purchase_id", COUNT(pt.*) "pt_qty",
                    (p."received_date" IS NOT NULL AND p."status" = 'paid') "is_received"
                 FROM "purchase" p 
                 INNER JOIN "purchase_team" pt 
                    ON pt."purchase_id" = p."purchase_id"
                 ${whereBlock}
                 GROUP BY p."purchase_id"`;

            return tr.query(query, params).then(result => result.rows[0] || null);
        }

        function __cancelPurchaseRow__ (tr, purchaseID, eventID, clubOwnerID, eventOwnerID) {
            let { query: whereBlock, params } = __genWHERESQLBlock__(purchaseID, eventID, clubOwnerID, eventOwnerID);

            let query = 
                `UPDATE "purchase" p 
                 SET "status" = 'canceled',
                    "canceled_date" = NOW(),
                    "amount_refunded" = (
                        CASE
                            WHEN (p."received_date" IS NOT NULL)
                                THEN (COALESCE("amount_refunded", 0) + "amount") 
                            ELSE "amount_refunded"
                        END
                    )
                 ${whereBlock}
                 RETURNING p."purchase_id"`;

            return tr.query(query, params).then(result => result.rows[0] || null);
        }

        function __cancelPurchaseTeamRow__ (tr, purchaseID, purchaseTeamIDList) {
            return Promise.resolve().then(() => {
                let params = [purchaseID];

                let __whereBlock__ = 
                    `WHERE pt.purchase_id = $1
                        AND pt."canceled" IS NULL`;

                if (__checkPurchaseTeamListExists__(purchaseTeamIDList)) {
                    __whereBlock__ +=
                        `
                        AND pt."purchase_team_id" IN (${
                            swUtils.numArrayToString(purchaseTeamIDList, null, true)
                        })`
                }

                return tr.query(
                    `UPDATE "purchase_team" pt
                     SET canceled = NOW()
                     ${__whereBlock__}
                     RETURNING 
                        pt.roster_team_id, 
                        COALESCE(pt."sw_fee", 0) "sw_fee", 
                        COALESCE(pt."amount", 0) "amount", (
                        SELECT rt."team_name" 
                        FROM "roster_team" rt 
                        WHERE rt.roster_team_id = pt.roster_team_id
                     )`,
                    params
                )
            })
        }

        function __insertPurchaseHistoryRow__ (tr, purchaseID, userID, action) {
            return tr.query(
                `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id")
                 VALUES ($1, $2, $3, $4)`, [
                    purchaseID,
                    action || 'purchase.canceled',
                    `Check Payment was canceled`,
                    userID
                ]
            ).then(() => {})
        }

        function __getCanceledPTRowsQty__ (tr, purchaseID) {
            return tr.query(
                `SELECT COUNT(pt.*) "qty"
                FROM "purchase_team" pt 
                WHERE pt."purchase_id" = $1 
                    AND pt."canceled" IS NOT NULL`,
                [purchaseID]
            ).then(result => (result.rows[0] && result.rows[0].qty) || 0)
        }

        function __modifyNetProfitAndAmount__ (tr, purchaseID, swFee, canceledAmount) {
            return tr.query(
                `UPDATE "purchase" p 
                 SET "net_profit"   = "amount" - $3::NUMERIC - $2::NUMERIC,
                     "amount"       = "amount" - $3::NUMERIC, 
                     "amount_refunded" = (
                        CASE
                            WHEN (p."received_date" IS NOT NULL)
                                THEN (COALESCE("amount_refunded", 0) + $3::NUMERIC) 
                            ELSE "amount_refunded"
                        END
                    ), 
                    "date_refunded" = (
                        CASE
                            WHEN (p."received_date" IS NOT NULL)
                                THEN NOW()
                            ELSE "date_refunded"
                        END
                    )
                 WHERE p."purchase_id" = $1
                 RETURNING EXTRACT(EPOCH FROM "date_refunded")::BIGINT "date_refunded"`,
                [purchaseID, swFee, canceledAmount]
            ).then(res => res.rows[0] || {});
        }
    }

    receiveCheckPayment (params) {

        let __validateParams__ = (purchaseID, eventID, datePaid, checkNum, receivedAmount) => {
            if (!Number.isInteger(purchaseID) || (purchaseID <= 0)) {
                throw new Error('Invalid purchase identifier');
            }

            if (!Number.isInteger(eventID) || (eventID <= 0)) {
                throw new Error('Invalid event identifier');
            }

            if (!moment(datePaid, 'YYYY-MM-DD').isValid()) {
                throw new Error('Invalid Date')
            }

            if (!_.isString(checkNum) || (checkNum.length === 0)) {
                throw new Error('Invalid Check Number');
            }

            if (!_.isNumber(receivedAmount) || (receivedAmount <= 0)) {
                throw new Error('Invalid Received Amount');
            }
        }

        let __updPurchaseRow__ = (tr, datePaid, checkNum, purchaseID, eventID, receivedAmount) => {
            return tr.query(
                `UPDATE purchase p 
                 SET date_paid      = $1 AT TIME ZONE 'UTC' AT TIME ZONE e.timezone, 
                     received_date  = $1 AT TIME ZONE 'UTC' AT TIME ZONE e.timezone,
                    canceled_date   = null,
                    check_num       = $2,
                    status          = 'paid',
                    received_amount = $5
                 FROM event e
                 WHERE p.purchase_id = $3 
                    AND p."event_id" = $4
                    AND e."event_id" = $4
                 RETURNING e.event_id, roster_club_id, purchase_id, payment_for, received_amount`,
                 [datePaid, checkNum, purchaseID, eventID, receivedAmount]
            ).then(result => result.rows[0] || null)
        }

        let __updRosterTeamRows__ = (tr, purchaseID, datePaid) => {
            return tr.query(
                `UPDATE roster_team rt
                 SET status_paid    = 22,                      
                     date_paid      = $2 AT TIME ZONE 'UTC' AT TIME ZONE e.timezone
                 FROM "event" e
                 WHERE roster_team_id IN (
                    SELECT 
                        "roster_team_id"
                    FROM "purchase_team" 
                    WHERE "purchase_id" = $1
                        AND "canceled" IS NULL
                 ) AND e.event_id = rt.event_id`,
                [purchaseID, datePaid]
            ).then(() => {})
        }

        let __addNotification__ = (eventID, rosterClubID, purchaseID, received_amount) => {
            eventNotifications.add_notification(eventID, {
                roster_club_id  : rosterClubID,
                purchase_id     : purchaseID,
                action          : 'payment.check.paid',
                new_data        : { received_amount }
            })
        }

        const getPurchaseInfo = (purchaseID) => {
            const query = squel.select()
                .from('purchase')
                .field('status')
                .field('type')
                .where('purchase_id = ?', purchaseID);

            return Db.query(query).then(({ rows }) => rows[0]);
        };

        const validatePurchase = ({ status, type }) => {
            if (type !== 'check' || status === 'canceled') {
                throw new Error('Action not allowed')
            }
        };

        return co(function* () {
            let {
                purchaseID,
                eventID,
                datePaid,
                checkNum,
                receivedAmount
            } = params;

            __validateParams__(purchaseID, eventID, datePaid, checkNum, receivedAmount);

            const purchaseInfo = yield getPurchaseInfo(purchaseID);

            validatePurchase(purchaseInfo);

            let tr = yield (Db.begin());

            let purchase = yield (__updPurchaseRow__(tr, datePaid, checkNum, purchaseID, eventID, receivedAmount));

            if (purchase === null) {
                yield (tr.rollback());
                return Promise.reject({ validation: 'Purchase not found!' });
            }

            yield (__updRosterTeamRows__(tr, purchaseID, datePaid));

            yield (tr.commit());
            
            /* 
                Send notification to CD for successful teams payment
                Note: no cc to EO yet
            */
            if(purchase.purchase_id && purchase.payment_for === 'teams') {
                const _g = AEMService.TEAMS_PAYMENTS_GROUP;
                const _t = AEMService.triggers.getPaymentsTmplForPaymentType('teams','any','paid');
                const _p = {
                    purchaseId: purchase.purchase_id
                };
                
                const event_id =  purchase.event_id;

                yield (AEMSenderService.sendTriggerNotification(_g, _t, event_id, _p).catch(() => {}));
            }


            __addNotification__(eventID, purchase.roster_club_id, purchaseID, purchase.received_amount);
        });
    }

    calculateProviderFee(paymentProvider, data) {
        if(!paymentProvider) {
            throw new Error('Payment provider should not be empty')
        }

        if(paymentProvider === PAYMENT_PROVIDER.STRIPE) {
            return this.generateStripeFee(data)
        }

        if(paymentProvider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            return this.calculatePaymentHubFee(data)
        }

        throw new Error(`Unsupported provider ${paymentProvider}`)

    }

    calculatePaymentHubFee(data) {
        const { total, provider_fixed, provider_percentage, fee_payer, type: paymentType } = data;

        if (paymentType === this.CARD_TYPE) {
            const isBuyerPayingFees = fee_payer === FEE_PAYER.BUYER;

            const totalInCents = total * 100;

            const feeInCents = isBuyerPayingFees
                ? PaymentHubService.calculateCustomerFee(
                      totalInCents,
                      provider_percentage,
                      provider_fixed
                  )
                : PaymentHubService.calculateDefaultFee(
                      totalInCents,
                      provider_percentage,
                      provider_fixed
                  );
            return swUtils.normalizeNumber(feeInCents / 100);
        }

        throw new Error(`Unsupported payment type ${paymentType}`)
    }

    // covered 😄👍
    generateStripeFee (data) {
        let paymentType      = data.type,
            total           = data.total,

            stripePercent   = data.stripe_percent,
            stripeFixed     = data.stripe_fixed,

            achPercent      = data.ach_percent,

            stripeFeePayer  = data.fee_payer;

        let stripeFee = 0;

        if (paymentType === this.CARD_TYPE) {
            const method = stripeFeePayer === FEE_PAYER.BUYER
                ? StripeService.customerStripeFee.bind(StripeService)
                : StripeService.defaultStripeFee.bind(StripeService);

            stripeFee = method(total, stripePercent, stripeFixed);
        } else if (paymentType === this.ACH_TYPE) {
            const method = stripeFeePayer === FEE_PAYER.BUYER
                ? StripeService.customerACHStripeFee.bind(StripeService)
                : StripeService.defaultACHStripeFee.bind(StripeService);

            stripeFee = method(total, achPercent);
        } else {
            throw new Error(`Type "${paymentType}" is not supported`);
        }

        return swUtils.normalizeNumber(stripeFee);
    }

    // covered 😄👍
    generatePaymentDescription (user, payment, eventName) {
        return `Event name: ${eventName}; Amount: $${
            payment.amount.toFixed(2)
        }; Name: ${user.first} ${user.last}; Email: ${user.email};${
            user.phone?(' Phone: ' + user.phone + ';'):''
        } Teams: ${payment.namesOfTeams}`
    }

    // covered 😄👍
    _validatePaymentType (paymentType, eventRow) {
        if ([this.CHECK_TYPE, this.CARD_TYPE, this.ACH_TYPE].indexOf(paymentType) === -1) {
            throw new Error(`Type "${paymentType}" is not supported for Teams Payments by SW System`)
        }

        if (
            (paymentType === this.CHECK_TYPE && !eventRow.allow_check_payments) || 
            (paymentType === this.CARD_TYPE  && !eventRow.allow_card_payments) || 
            (paymentType === this.ACH_TYPE   && !eventRow.allow_ach_payments)
        ) {
            throw new Error(`Payment of type "${paymentType}" is not allowed for the Event.`);
        }
    }

    async processDeclinedTeams (tr, purchaseID, declinedTeamsList) {
        
        return {
            canceledQty: await this.cancelTeamPurchase(tr, purchaseID, declinedTeamsList),
            changedPaidStatusQty: await this.updatePaymentStatus(tr, declinedTeamsList)
        }
        
    }
    
    async cancelTeamPurchase (tr, purchaseID, declinedTeamsList) {
        
        const canceledResult = await tr.query(
            `UPDATE "purchase_team" pt
                SET "canceled" = NOW(),
                    "amount" = 0
                WHERE pt."purchase_id" = $1
                AND pt."canceled" IS NULL
                AND pt."roster_team_id" IN (${
                this._utils.numArrayToString(declinedTeamsList, 'roster_team_id')
            })`,
            [purchaseID]
        );
        
        return canceledResult.rowCount;
        
    }
    
    async updatePaymentStatus (tr, declinedTeamsList) {
        
        const changedPaidStatusResult = await tr.query(
            `UPDATE "roster_team" rt
                SET "status_paid" = $1
                WHERE rt."roster_team_id" IN (${
                this._utils.numArrayToString(declinedTeamsList, 'roster_team_id')
            })`,
            [PAYMENT_STATUSES.NOT_PAID]
        );
        
        return changedPaidStatusResult.rowCount;
        
    }

    // covered 😄👍
    getTeamsNamesStr (purchaseTeams) {
        return purchaseTeams.map(team => `"${team.name}"`).join(', ');
    }

    insertPurchaseRow (tr, data) {
        let sql = 
            squel.insert().into('purchase').returning('purchase_id')
                .set('payment_for'      , 'teams')
                .set('event_id'         , data.eventId)
                .set('amount'           , data.amount)
                .set('roster_club_id'   , data.rosterClubId)
                .set('club_owner_id'    , data.clubOwnerId)
                .set('email'            , data.email)
                .set('type'             , data.type)
                .set('user_id'          , data.userId)
                // net_profit = teams_count * per_team_fee + total_surcharge - stripe_fee - sw_fee.
                .set('net_profit'       , data.netProfit)
                .set('status'           , data.paymentStatus)
                .set('source'           , 'site')
                .set('payer_ip'         , data.ip || null)
                // setting initial fees to 0
                .set('stripe_fee'               ,  0)
                .set('collected_sw_fee'         ,  0)
                .set('additional_fee_amount'    ,  0);
                
        if(data.phone) {
            sql.set('phone', data.phone);
        }

        if(data.type === this.CARD_TYPE) {
            let stripePercent = swUtils.normalizeNumber(
                (data.stripe_percent || StripeService.DEFAULT_STRIPE_PERCENT) * 100
            );

            sql.set('stripe_percent', stripePercent);
        }

        if(data.check_num) {
            sql.set('check_num', data.check_num)
        }   

        if(data.received_at) {
            sql.set('received_date', data.received_at);
        }   

        if (data.eventOwnerID) {
            /* This means that the purchase is created by the EO */
            sql.set('event_owner_id', data.eventOwnerID)
        }   

        return tr.query(sql).then(result => {
            let purchase = result.rows[0];  

            if(!purchase) {
                throw new Error('Purchase row is not created.');
            }   

            return purchase.purchase_id;
        });
    }

    saveStripeChargeToDb (tr, purchaseId, charge, useConnect, feeDetails, netProfitDecrease, total) {
        let generateSQLQuery = () => {
            let sql = squel.update().table('purchase')  
                .set('amount'                   , total)
                .set('card_last_4'              , charge.source.last4)
                .set('card_name'                , charge.source.name)
                .set('date_paid'                , 'NOW()')
                .set('received_date'            , 'NOW()')
                .set('stripe_card_id'           , charge.source.id)
                .set('stripe_charge_id'         , charge.id)
                .set('stripe_payment_type'      , (useConnect) ? 'connect' : 'default')
                .set('stripe_card_fingerprint'  , charge.source.fingerprint)
            
            .where('purchase_id = ?', purchaseId)
            .returning('amount, net_profit, stripe_fee, collected_sw_fee')

            if (feeDetails.provider_fee) {
                sql.set('stripe_fee', feeDetails.provider_fee);
            }

            if (feeDetails.extra_fee) {
                sql.set('additional_fee_amount', feeDetails.extra_fee);
            }

            if (feeDetails.sw_fee) {
                sql.set('collected_sw_fee', feeDetails.sw_fee);
            } 

            if (netProfitDecrease > 0) {
                sql.set(`"net_profit" = COALESCE("net_profit", 0) - (${netProfitDecrease})::NUMERIC`)
            }

            return sql;
        }

        let saveToDB = sql => {
            return tr.query(sql)
            .then(result => (result.rows[0] || null))
        }

        let validateDBValues = row => {
            assert(!_.isNull(row), `Purchase #${purchaseId} not found. Stripe charge saving failed.`)
            
            let {amount, net_profit: net, stripe_fee: stripeFee, collected_sw_fee: swFee } = row;

            amount      = Number(amount);
            net         = Number(net);
            stripeFee   = Number(stripeFee);
            swFee       = Number(swFee);

            let recountedNet = swUtils.normalizeNumber(amount - stripeFee - swFee);

            assert(net === recountedNet, 'Invalid Net Profit!');
        }

        return Promise.resolve().then(() => {
            let sql = generateSQLQuery();

            return saveToDB(sql)
        })
        .then(validateDBValues)
    }

    /* This is the duplicate of the next method */
    insertPurchaseHistoryRow (tr, purchaseId, names, userId, amount) {
        return tr.query(
        `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id", "amount")
         VALUES ($1, 'purchase.created', $2, $3, $4)`, [
            purchaseId, 
            `Payment for Teams Entry created: ${names}`, 
            userId, 
            amount
        ]);
    }

    typeChangeHistoryRow (tr, purchaseID, userID, amount, oldType, newType) {
        return tr.query(
            `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id", "amount")
             VALUES ($1, 'purchase.type_changed', $2, $3, $4)`, [
            purchaseID, 
            `Payment type changed from "${oldType}" to "${newType}"`,
            userID,
            amount
        ]);
    }

    getPaymentStatus (type, isReceived) {
        if(type === this.CARD_TYPE || isReceived) {
            return 'paid';
        } else {
            return 'pending';
        }
    }

    // data params: charge_id, amount, fee, stripe_secret
    refundStripeCharge (data, useConnect, skipFeeRefund) {
        if(useConnect) {
            return StripeConnect.refundClientPayment(data, null, skipFeeRefund);
        } else {
            return StripeService.refund(data);
        } 
    }

    // params: type, token, use_connect, total, account_id, description, stripe_statement, receipt_email, stripe_secret
    createStripeCharge (data) {
        return co(function* () {    
            let plaidAccOfEvent = {
                client_id : data.plaid_account_id,
                secret    : data.plaid_secret,
                publicKey : data.plaid_public
            }

            let stripeToken = yield (data.type !== this.ACH_TYPE)
                                        ? Promise.resolve(data.token) 
                                        : PlaidService.exchangePublicToken(
                                            data.token.public_token, data.token.account_id, 
                                            data.use_connect ? null : plaidAccOfEvent);

            if (data.use_connect) {
                return StripeConnect.createClientPayment({
                    token               : stripeToken,
                    amount              : swUtils.normalizeNumber(data.total * 100),
                    stripe_account_id   : data.account_id,
                    application_fee     : swUtils.normalizeNumber(data.application_fee * 100),
                    description         : data.description,
                    metadata            : data.metadata,
                    statement           : data.stripe_statement,
                    receipt_email       : data.receipt_email 
                });
            } else {
                return StripeService.pay({
                    token           : stripeToken,
                    private_key     : data.stripe_secret,
                    amount          : data.total,
                    descr           : data.description,                        
                    statement       : data.stripe_statement,
                    metadata        : data.metadata,
                    receipt_email   : data.receipt_email
                });
            }   
        }.bind(this));
    }

    changePurchaseRowType (tr, eventId, purchaseId, data) {
        return tr.query(
            `UPDATE "purchase" p 
                SET "amount" = $3,
                    "type" = $4,
                    "status" = $5,
                    "net_profit" = $6
             WHERE p.purchase_id = $1
                AND p.event_id = $2`, [
            purchaseId, eventId, data.amount, data.type,
            data.paymentStatus, data.netProfit
        ]);
    }

    /**
     * @param {Object} settings
     * @param {Object} takenFees
     * @param {number} takenFees.sw_fee
     * @param {number} takenFees.extra_fee
     * @param {number} takenFees.provider_fee
     **/
    __getFeesToSubtractFromNetProfit__ (settings, takenFees) {
        let {sw_fee: swFee, provider_fee: stripeFee} = takenFees;

        if(settings.stripe_teams_fee_payer === FEE_PAYER.BUYER) {
            return swUtils.normalizeNumber(swFee);
        } else {
            return swUtils.normalizeNumber(swFee + stripeFee);
        }
    }

    async processStripePayment (tr, eventID, settings, recountedReceipt, user, payment) {
        let { applicationFee, extraFee, swFee, swFeeDetails, providerFee }
            = await this.getApplicationFees(eventID, settings, recountedReceipt, payment, tr);

        const purchaseID          = payment.purchase_id;
        const useConnect          = settings.use_connect;
        const takenFees           = swFeeDetails.details;
        let netProfitSubtract     = this.__getFeesToSubtractFromNetProfit__(settings, takenFees);

        if(settings.stripe_teams_fee_payer === FEE_PAYER.BUYER) {
            recountedReceipt.total += takenFees.provider_fee;
        }

        const metadata = this.metadata.generate({
            user,
            event_name: settings.event_name,
            event_id: eventID,
            purchase_id: purchaseID,
            totals: {
                netProfit: swUtils.normalizeNumber(recountedReceipt.total - netProfitSubtract),
                providerFee,
                extraFee,
                sw_fee: swFee
            }
        });

        const description = this.generatePaymentDescription(user, payment, settings.event_name);

        const charge = await this.createStripeCharge({
            type                : payment.type,
            token               : payment.token,
            use_connect         : settings.use_connect,
            total               : recountedReceipt.total,
            account_id          : settings.account_id,
            description         : description,
            stripe_statement    : settings.stripe_statement,
            receipt_email       : user.email,
            stripe_secret       : settings.secret_key,
            application_fee     : applicationFee,
            plaid_secret        : settings.plaid_secret,
            plaid_public        : settings.plaid_public,
            plaid_account_id    : settings.plaid_account_id,
            metadata
        });

        await (
            this.saveStripeChargeToDb(
                tr, purchaseID, charge, useConnect, takenFees, netProfitSubtract, recountedReceipt.total, settings
            )
                .catch(async err => {
                    await this.refundStripeCharge({
                        charge_id: charge.id,
                        stripe_secret: settings.secret_key,
                    }, settings.use_connect, true);

                    loggers.errors_log.error(err);

                    throw new Error('Internal Error. Please, try again later');
                })
        );

        return { charge_id: charge.id, fee: swFeeDetails };
    }

    async getApplicationFees (eventID, settings, recountedReceipt, payment, tr, teamsInPayment) {
        let doNotCollectedDisputesEscrow = true;
        let getEscrowSummands = false;

        const [
            collected,
            target
        ] = await Promise.all([
            this.getCollectedSWFee(eventID),
            this.getSWFeeEscrowTarget(
                eventID, doNotCollectedDisputesEscrow, getEscrowSummands, recountedReceipt.swFee, false, tr,
                teamsInPayment
            )
        ]);

        assert(target >= recountedReceipt.swFee,
            'Target SW Fee amount is below current purchase SW Fee amount!');

        const providerFee = this.calculateProviderFee(settings.payment_provider, {
            type            : payment.type,
            total           : recountedReceipt.total,
            stripe_percent  : settings.stripe_percent,
            stripe_fixed    : settings.stripe_fixed,
            ach_percent     : settings.ach_percent,
            fee_payer       : this.__getProviderFeePayer(settings),
        });

        const teamSWFee = Number(settings.sw_fee);

        const swFeeDetails = this.countSWFeeToTakeInPurchase(
            target, collected, recountedReceipt.swFee, providerFee, teamSWFee, payment.amount, settings
        );

        const applicationFee  = swFeeDetails.application_fee;
        const extraFee        = swFeeDetails.details.extra_fee || 0;
        const swFee           = swFeeDetails.details.sw_fee;

        // Just to sleep peacefully at night
        assert(
            applicationFee === swUtils.normalizeNumber(providerFee + swFee + extraFee),
            'Invalid Application Fee'
        );

        return { applicationFee, extraFee, swFee, swFeeDetails, providerFee };
    }

    __getProviderFeePayer(settings) {
        if(settings.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            return settings.payment_hub_teams_fee_payer;
        }

        return settings.stripe_teams_fee_payer;
    }

    getSettings (eventId, masterClubId, clubOwnerId, season, purchaseId, isCheckPayment) {
        let sql = this._SQLGeneratorService.getSettingsSQLQuery(Boolean(purchaseId));

        let params = [eventId, masterClubId, clubOwnerId, season, SETTINGS_KEY, teamsConstants.ENTRY_STATUSES.DECLINED];

        if(!!purchaseId) {
            params.push(purchaseId);
        } else {
            params.push(teamsConstants.PAYMENT_STATUSES.PAID, teamsConstants.PAYMENT_STATUSES.PENDING);
        }

        return Db.query(sql, params).then(result => {
            let event =  result.rows[0];    

            if (!event) {
                throw new Error('Event not found');
            }   

            if (event.allow_card_payments && !isCheckPayment) {
                if (event.use_connect && !event.account_id) {
                    throw new Error('Unable to make Stripe Connect payment, because event has not access token.')
                }   

                if (!event.use_connect && !event.secret_key) {
                    throw new Error('Unable to make Stripe payment, because event has not secret key.')
                }
            }   

            if (event.teams.length === 0) {
                throw new Error('No available for payment teams found for the club.')
            }   

            event.stripe_percent    = +event.stripe_percent;
            event.stripe_fixed      = +event.stripe_fixed;
            event.ach_percent       = +event.ach_percent;
            event.balance           = +event.balance;
            event.extra_fee         = +event.extra_fee;
            event.reg_fee           = +event.reg_fee;

            return event;
        });
    }

    getPaymentForTypeChange (eventID, purchaseID, season, masterClubID) {
        let sql = this._SQLGeneratorService.getPaymentForTypeChangeSQLQuery();

        return Db.query(sql,[eventID, purchaseID, season, masterClubID])
        .then(result => result.rows[0] || null);
    }

    getPaymentSettings (eventID, masterClubID, season, purchaseID) {
        let sql = this._SQLGeneratorService.getPaymentDataSQLQuery(!!purchaseID);

        let params = [eventID, masterClubID, season];

        if(!!purchaseID) {
            params.push(purchaseID);
        }

        return Db.query(sql, params)
        .then(result => {
            let data = result.rows[0];

            if(_.isEmpty(data)) {
                return Promise.reject({ 
                    validation: 'No teams available. Make sure you did assign teams to the event' 
                });
            } else {
                // Hard code SW-35, SW-40.
                // Must be deleted
                if (data.event.allow_ach_payments && data.event.plaid_key) {
                    data.event.ach = true;
                }

                if(data.event.allow_card_payments && data.event.stripe_key) {
                    data.event.card = true
                }   

                if(data.payment) {
                    // Do not allow to pay the same method the purchase was paid
                    data.event[data.payment.payment_method] = undefined;
                }   

                data.allow_card_payments = data.allow_ach_payments = undefined; 

                data.master_club = { id : masterClubID };

                return data;
            }
        })
    }

    updateRosterTeamRow (tr, pt, purchaseStatus, eventId, discount) {
        let statusPaid, datePaid;   

        if (purchaseStatus === 'paid') {
            statusPaid  = teamsConstants.PAYMENT_STATUSES.PAID;
            datePaid    = 'NOW()';
        } else if (purchaseStatus === 'pending') {
            statusPaid  = teamsConstants.PAYMENT_STATUSES.PENDING;
            datePaid    = null;
        } else if (purchaseStatus === 'canceled') {
            // TODO
        }   

        let params = [
            pt.roster_team_id, 
            eventId, 
            statusPaid, 
            datePaid,
            (discount >= 0)?discount:null
        ]   

        return tr.query(
            `UPDATE "roster_team"
                SET "status_paid" = $3,
                    "date_paid" = (
                        CASE
                            WHEN "date_paid" IS NOT NULL 
                                THEN "date_paid"
                            ELSE ($4)::TIMESTAMP
                        END
                    ),
                    "discount" = COALESCE($5::NUMERIC, "discount")
             WHERE "roster_team_id" = $1
                AND "event_id" = $2`,
            params
        ).then(result => {
            if(result.rowCount === 0) {
                throw new Error('Team not found');
            }
        });
    }

    async updateTeamRows (tr, purchaseTeamList, status, eventId, purchaseId) {
        const updPurchaseTeamRow = (tr, pt) => {
            return tr.query(
                `UPDATE "purchase_team" pt 
                 SET "amount" = $3,
                    "surcharge" = $4,
                    "sw_fee" = $5
                 WHERE pt.purchase_id = $1
                    AND pt.roster_team_id = $2`, [
                    purchaseId, pt.roster_team_id,
                    pt.amount, pt.surcharge, pt.team_sw_fee
                ]
            ).then(result => {
                if(result.rowCount === 0) {
                    throw new Error(`Purchased Team #${pt.roster_team_id} not found`);
                }
            });
        };  

        for (const purchaseTeam of purchaseTeamList) {
            await updPurchaseTeamRow(tr, purchaseTeam);
            await this.updateRosterTeamRow(tr, purchaseTeam, status, eventId);
        }
    }

    async processReceiptTeams (tr, purchaseId, eventId, eventFee, purchaseTeams, status, discounts = {}) {
        const insertPurchaseTeamRow = (tr, pt) => {
            return tr.query(
                `INSERT INTO "purchase_team" (
                     "purchase_id", "event_id", "roster_team_id", "amount",
                     "division_id", "division_fee", "event_fee", "surcharge",
                     "sw_fee"
                 ) 
                 VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
                [
                    purchaseId, eventId, pt.roster_team_id, pt.amount, 
                    pt.division_id, pt.division_fee, eventFee, pt.surcharge,
                    pt.team_sw_fee
                ]
            ).then(result => {
                if(result.rowCount === 0) {
                    throw new Error('Item insertion failed');
                }
            });
        };  

        for(const purchaseTeam of purchaseTeams) {
            await this.updateRosterTeamRow(tr, purchaseTeam, status, eventId, discounts[purchaseTeam.roster_team_id]);
            await insertPurchaseTeamRow(tr, purchaseTeam);
        }
    }

    getClubPaymentsList (eventID, masterClubID) {
        let sql     = this._SQLGeneratorService.getClubPaymentsListSQLQuery();
        let params  = [eventID, masterClubID];

        return Db.query(sql, params)
        .then(result => {
            let eventData = result.rows[0];

            if (!eventData) {
                return Promise.reject({ validation: 'Event not found' });
            }

            let eventSettings = eventData.event;

            let allow_type_change = (
                   (eventSettings.allow_card_payments && eventSettings.stripe_key) || 
                   (eventSettings.allow_ach_payments && eventSettings.plaid_key)
            )

            let payments = eventData.payments.map(p => {
                FIELDS_TO_PARSE.forEach(field => {
                    p[field] = +p[field];
                });

                return p;
            });

            return { allow_type_change, payments };
        })
    }

    _validatePaymentData (data, mode) {
        return Promise.resolve().then(() => {
            if (data === null) {
                return Promise.reject({ validation: 'No data passed' });
            }

            if (mode === this.CLUB_DIRECTOR_PAYMENT_MODE) {
                if (!Number.isInteger(data.master_club_id)) {
                    return Promise.reject({ validation: 'Invalid master club id' });
                }

                if (!Number.isInteger(data.club_owner_id)) {
                    return Promise.reject({ validation: 'Invalid club owner id' });
                }

                if (!PAYMENT_TYPE_REG_EXP.test(data.type)) {
                    return Promise.reject({ validation: 'Invalid Payment Type' });
                }
            } else if (mode === this.EVENT_OWNER_PAYMENT_MODE) {
                if (!Number.isInteger(data.roster_club_id)) {
                    return Promise.reject({ validation: 'Invalid roster club id' });
                }

                if (!Number.isInteger(data.event_owner_id)) {
                    return Promise.reject({ validation: 'Invalid event owner id' });
                }

                /* Event Owner can create only check payments */
                if (data.type !== this.CHECK_TYPE) {
                    return Promise.reject({ validation: 'Invalid Payment Type' });
                }

                if (toString.call(data.discount) !== '[object Object]') {
                    return Promise.reject({ validation: 'Expecting discount to be an object' });
                }
            } else {
                return Promise.reject({ validation: 'Invalid payment mode' });
            }

            if (!Number.isInteger(data.event_id) || data.event_id <= 0) {
                return Promise.reject({ validation: 'Invalid Event identifier' });
            }

            if (!Array.isArray(data.receipt)) {
                return Promise.reject({ validation: 'Expecting receipt to be an array' });
            }

            if (data.receipt.length === 0) {
                return Promise.reject({ validation: 'Empty Receipt passed' });
            }

            let filteredReceipt = data.receipt.filter(val => (val > 0));

            if (filteredReceipt.length !== data.receipt.length) {
                return Promise.reject({ validation: 'All receipt items must be integers greater than 0' });
            }

            if ((data.type !== this.CHECK_TYPE) && !data.token) {
                return Promise.reject({ validation: `Token required for ${data.type} payments` });
            }

            if (toString.call(data.total) !== '[object Number]') {
                return Promise.reject({ validation: 'Expecting total amount to be a number' });
            }

            if(data.type === this.ACH_TYPE && data.total < this.ACH_MIN_AMOUNT) {
                return Promise.reject({ validation: 'Expecting total amount to be greater ' + this.ACH_MIN_AMOUNT });
            }

            if (data.total <= 0) {
                return Promise.reject({ validation: 'Total amount should be greater than zero' });
            }

            if (toString.call(data.user) !== '[object Object]') {
                return Promise.reject({ validation: 'Expecting user to be an object' });
            }

            assert.deepEqual(Object.keys(data.user), ['user_id', 'email', 'phone', 'first', 'last']);

            if (!data.ip) {
                return Promise.reject({ validation: 'Invalid IP Address' });
            }

            if (!Number.isInteger(data.season)) {
                return Promise.reject({ validation: 'Invalid season' });
            }
        })
    }

    get CLUB_DIRECTOR_PAYMENT_MODE () {
        return 'cd_purchase';
    }

    get EVENT_OWNER_PAYMENT_MODE () {
        return 'eo_purchase';
    }

    getClubData (eventID, rosterClubID) {
        return Db.query(
            `SELECT rc."master_club_id", mc."club_owner_id"
             FROM "roster_club" rc 
             INNER JOIN "master_club" mc 
                ON mc."master_club_id" = rc."master_club_id"
             WHERE rc."roster_club_id" = $1
                AND rc."event_id" = $2`,
            [rosterClubID, eventID]
        ).then(result => result.rows[0] || null);
    }

    mergeTeamsAndDiscount (teams, discount) {
        return teams.map(team => {
            let _discount = parseFloat(discount[team.id], 10);

            if (_discount > 0) {
                team.discount = _discount
            } else {
                team.discount = 0;
            }

            return team;
        });
    }

    /**
     * NOTE: this method might be removed in the future 
     * as we do not use stored escrow value anymore ("event"."teams_escrow_collected")
     */
    _changeEscrow (tr, eventID, feeData, chargeID, sk, useConnect) {
        return Promise.resolve().then(() => {
            let totalSWFee = this._utils.normalizeNumber(feeData.details.extra_fee + feeData.details.sw_fee);

            if (totalSWFee > 0) {
                return this.modifyCollectedEscrowSWFeeAmount(tr, eventID, totalSWFee)
                .then(escrowRes => {
                    assert(escrowRes === true, 'Cannot modify escrow SW fee');
                })
            }
        }).catch(err => {
            this.refundStripeCharge({
                charge_id       : chargeID,
                stripe_secret   : sk,
            }, useConnect, true);

            return Promise.reject(err);
        });
    }

    // covered 😄👍
    makePayment (data = null, mode = this.CLUB_DIRECTOR_PAYMENT_MODE) {
        let tr;

        return co(function* () {
            yield (this._validatePaymentData(data, mode));

            let clubData = null;
            let _balanceInformationService = null;

            if (mode === this.EVENT_OWNER_PAYMENT_MODE) {
                clubData = yield (this.getClubData(data.event_id, data.roster_club_id));

                if (clubData === null) {
                    return Promise.reject({ validation: 'Club not found on the Event' });
                }

                data.master_club_id = clubData.master_club_id;
                data.club_owner_id  = clubData.club_owner_id;
            }

            let isStripePayment = STRIPE_TYPE_REG_EXP.test(data.type);

            let isCheckPayment = (data.type === this.CHECK_TYPE);

            const isBalanceInformationMode = data.type === this.CARD_TYPE && mode === this.CLUB_DIRECTOR_PAYMENT_MODE;

            if (isBalanceInformationMode) {
                _balanceInformationService = new BalanceInformationService();
            }

            let settings =
                yield (this.getSettings(
                        data.event_id, data.master_club_id, data.club_owner_id, data.season, null, isCheckPayment
                        )
                );

            settings.teams = (mode === this.EVENT_OWNER_PAYMENT_MODE)
                                ? this.mergeTeamsAndDiscount(settings.teams, data.discount)
                                : settings.teams;

            this._validatePaymentType(data.type, settings);

            let recountedReceipt = this._recountPrices(settings, data.receipt, data.total, data.type);

            tr = yield (Db.begin());

            /* Only EO can receive checks! */
            let isCheckReceived = (mode === this.EVENT_OWNER_PAYMENT_MODE && (!!data.check_num || !!data.received_at))
            let paymentStatus   = this.getPaymentStatus(data.type, isCheckReceived);

            if (isBalanceInformationMode) {
                yield this.recordBalanceInformation('before', data.event_id, 0, _balanceInformationService);
            }

            /**
            * For check payments "collected_sw_fee" field is always zero
            * But we reduce "net_profit" by sw fee amount
            * So, "net_profit" is the field for statistics calculations, it does not show the real
            * flow of funds
            **/
            let purchaseId = yield (this.insertPurchaseRow(tr, {
                eventId         : data.event_id,
                amount          : recountedReceipt.total,
                rosterClubId    : settings.roster_club_id,
                clubOwnerId     : data.club_owner_id,
                email           : data.user.email,
                type            : data.type,
                userId          : data.user.user_id,
                phone           : data.user.phone,
                /**
                * In case of Stripe Payments we do not subtract sw fee from the net profit
                * during the purchase row insertion: we might not take any sw fee as Stripe's 
                * application fee if we've already collected a target (we take only EO's Stripe 
                * fee in this case), so such early sw fee subtraction may cause invalid net profit
                * amount
                **/
                netProfit       : (isStripePayment) 
                                    ? recountedReceipt.total 
                                    : recountedReceipt.netProfit,
                surcharge       : recountedReceipt.surcharge,
                collectedSwFee  : recountedReceipt.swFee,
                ip              : data.ip,
                eventOwnerID    : data.event_owner_id,
                check_num       : data.check_num,
                received_at     : data.received_at,
                stripe_percent  : settings.stripe_percent,
                paymentStatus
            }));
            /**
             * This is for inserting "purchase_team" rows 
             * and changing statuses of "roster_team" rows
             */
            yield (this.processReceiptTeams(
                tr, purchaseId, data.event_id, 
                settings.reg_fee, recountedReceipt.purchaseTeams, paymentStatus,
                (mode === this.EVENT_OWNER_PAYMENT_MODE)?data.discount:(void 0)
             ));

            let namesOfTeams = this.getTeamsNamesStr(recountedReceipt.purchaseTeams);

            yield (this.insertPurchaseHistoryRow(
                                    tr, purchaseId, namesOfTeams, data.user.user_id, recountedReceipt.total));

            if (isStripePayment) {
                let stripePaymentData = {
                    purchase_id     : purchaseId,
                    type            : data.type,
                    amount          : data.total,
                    namesOfTeams    : namesOfTeams,
                    token           : data.token
                }

                let {charge_id, fee} = yield (this.processStripePayment(
                                                    tr, data.event_id, settings, recountedReceipt,
                                                    data.user, stripePaymentData));

                if (isBalanceInformationMode) {
                    yield this.recordBalanceInformation('after', data.event_id, recountedReceipt.swFee, _balanceInformationService, tr);

                    _balanceInformationService.sw_fee = this._utils.normalizeNumber(fee.details.sw_fee + fee.details.extra_fee);

                    yield _balanceInformationService.updateBalanceInformation(tr, stripePaymentData.purchase_id);
                }

                yield (this._changeEscrow(tr, data.event_id, fee, charge_id, settings.secret_key, settings.use_connect));
            }

            yield (tr.commit());

            if(data.type === this.CARD_TYPE) {
                const _g = AEMService.TEAMS_PAYMENTS_GROUP;
                const _t = AEMService.triggers.getPaymentsTmplForPaymentType('teams','any','paid');
                const _p = {
                    purchaseId: purchaseId
                };

                const event_id  = data.event_id;
                const skipNotifOnError = true;

                yield (AEMSenderService.sendTriggerNotification(_g, _t, event_id, _p, skipNotifOnError)
                    .catch(err => loggers.errors_log.error(err)))
            }

            return purchaseId;
        }.bind(this))
            .catch(err => {
                if(tr && !tr.isCommited) {
                    tr.rollback();
                }
                throw err;
            })
    }

    separateDeclinedTeams (teams) {
        let declinedTeams   = [], 
            properTeams     = [];

        teams.forEach(team => {
            if (team.is_declined) {
                declinedTeams.push(team);
            } else {
                properTeams.push(team);
            }
        });

        return {declinedTeams, properTeams};
    }

    makeReceiptFromTeamsList (teams) {
    
        return _.reduce(teams, (list, team) => {
            if (!team.is_declined) {
                list.push(team.roster_team_id);
            }
            return list;
        }, []);
        
    }

    changePaymentType (data = null) {
        let tr;
        return (async () => {

            if (!Number.isInteger(data.purchase_id) || (data.purchase_id <= 0)) {
                throw { validation: 'Invalid Payment ID' };
            }

            const purchase = await (
                    this.getPaymentForTypeChange(data.event_id, data.purchase_id, data.season, data.master_club_id));

            if (purchase === null) {
                throw { validation: 'Suitable payment not found' };
            }

            if (purchase.type !== this.CHECK_TYPE) {
                throw { validation: 'Only check payments allowed for type change' };
            }

            const receipt = this.makeReceiptFromTeamsList(purchase.teams);

            const _balanceInformationService = new BalanceInformationService();

            /* Just to re-use validator */
            data.receipt = receipt;
            await (this._validatePaymentData(data, this.CLUB_DIRECTOR_PAYMENT_MODE));

            const {declinedTeams, properTeams} = this.separateDeclinedTeams(purchase.teams);
            purchase.teams = properTeams;

            if (purchase.teams.length === 0) {
                throw { validation: 'Purchased Teams not found' };
            }

            const settings = await (
                this.getSettings(data.event_id, data.master_club_id, data.club_owner_id, data.season, data.purchase_id)
            );

            this._validatePaymentType(data.type, settings);

            const recountedReceipt = this._recountPrices(settings, receipt, data.total, data.type);

            tr = await Db.begin();

            const paymentStatus = this.getPaymentStatus(data.type);

            const isStripePayment = STRIPE_TYPE_REG_EXP.test(data.type);

            await this.changePurchaseRowType(tr, data.event_id, data.purchase_id, {
                amount      : recountedReceipt.total,
                type        : data.type,
                netProfit   : (isStripePayment)
                                ? recountedReceipt.total
                                : recountedReceipt.netProfit,
                paymentStatus
            });

            await this.recordBalanceInformation('before', data.event_id, recountedReceipt.swFee, _balanceInformationService, tr);

            await (this.updateTeamRows(
                tr, recountedReceipt.purchaseTeams, paymentStatus, data.event_id, data.purchase_id
            ));

            await (this.typeChangeHistoryRow(
                tr, data.purchase_id, data.user.user_id, recountedReceipt.total, purchase.type, data.type
            ));

            if (isStripePayment) {
                const stripePaymentData = {
                    purchase_id     : data.purchase_id,
                    type            : data.type,
                    amount          : data.total,
                    namesOfTeams    : this.getTeamsNamesStr(recountedReceipt.purchaseTeams),
                    token           : data.token
                }

                const {charge_id, fee} = await (this.processStripePayment(
                                                    tr, data.event_id, settings, recountedReceipt,
                                                    data.user, stripePaymentData));

                await this.recordBalanceInformation('after', data.event_id, recountedReceipt.swFee, _balanceInformationService, tr);

                _balanceInformationService.sw_fee = this._utils.normalizeNumber(fee.details.sw_fee + fee.details.extra_fee);

                await _balanceInformationService.updateBalanceInformation(tr, stripePaymentData.purchase_id);

                await this._changeEscrow(tr, data.event_id, fee, charge_id, settings.secret_key, settings.use_connect);
            }

            if (declinedTeams.length > 0) {
                let {canceledQty, changedPaidStatusQty} = await this.processDeclinedTeams(tr, data.purchase_id, declinedTeams);
                loggers.debug_log.verbose('Canceled', canceledQty, 'declined teams');
                loggers.debug_log.verbose('Changed paid status', changedPaidStatusQty, 'declined teams');
            }

            await tr.commit();
            
            await TeamsPaymentNotificationsService.sendSuccessPaymentNotification(
                { purchase_id: data.purchase_id, event_id: data.event_id }
            );

            eventNotifications.add_notification(data.event_id, {
                action              : 'payment.card.paid',
                purchase_id         : data.purchase_id,
                roster_club_id      : settings.roster_club_id
            });
        })()
            .catch(err => {
                if(tr && !tr.isCommited) {
                    tr.rollback();
                }
                throw err;
            })
    }



    /* === Change Team in existing Payment === */



    __findTeamsToSwap__ (data = {}) {
        return Promise.resolve().then(() => {
            let {
                eventID, purchaseID, purchaseTeamID, oldRosterTeamID, newRosterTeamID
            } = data;

            assert(Number.isInteger(eventID), 'Event ID should be an integer');
            assert(eventID > 0, 'Event ID should be greater than zero');

            assert(Number.isInteger(purchaseID), 'Purchase ID should be an integer');
            assert(purchaseID > 0, 'Purchase ID should be greater than zero');

            assert(Number.isInteger(purchaseTeamID), 'Purchase Item ID should be an integer');
            assert(purchaseTeamID > 0, 'Purchase Item ID should be greater than zero');

            assert(Number.isInteger(oldRosterTeamID), 'Team ID should be an integer');
            assert(oldRosterTeamID > 0, 'Team ID should be greater than zero');

            assert(Number.isInteger(newRosterTeamID), 'New Team ID should be an integer');
            assert(newRosterTeamID > 0, 'New Team ID should be greater than zero');

            return Db.query(
                /* 
                * Note: "paid" amount is the sum of funds in pending 
                * and paid purchases for the specified team
                */
                `SELECT (
                        SELECT ROW_TO_JSON("team")
                        FROM (
                            SELECT 
                                (pt."amount" + pt."surcharge") amount,
                                COALESCE(rt."discount", 0) "discount",
                                rt."team_name", 
                                pt."surcharge"
                        ) "team"
                    ) "old_team", (
                        SELECT ROW_TO_JSON("team")
                        FROM (
                            SELECT 
                                "nrt"."team_name",
                                COALESCE(d."reg_fee", e."reg_fee", 0) "reg_fee",
                                COALESCE("nrt"."discount", 0) "discount",
                                COALESCE(SUM(pt_nrt."amount"), 0) "paid",
                                NULLIF(d."reg_fee", 0) "division_fee"
                            FROM "roster_team" "nrt"
                            INNER JOIN "division" d
                                ON d."division_id" = "nrt"."division_id"
                                AND d."event_id" = "nrt"."event_id"
                            LEFT JOIN "purchase_team" "pt_nrt"
                                ON "pt_nrt"."roster_team_id" = "nrt"."roster_team_id"
                                AND "pt_nrt"."event_id" = p."event_id"
                                AND "pt_nrt"."canceled" IS NULL 
                                AND "pt_nrt"."purchase_team_id" <> $3
                            LEFT JOIN "purchase" "p_nrt"
                                ON p_nrt."purchase_id" = pt_nrt."purchase_id"
                                AND p_nrt."status" <> 'canceled'
                            WHERE "nrt"."roster_team_id" = $5 
                                AND "nrt"."status_paid" NOT IN (22, 24)
                                AND "nrt"."deleted" IS NULL
                                AND "nrt"."roster_club_id" = "rt"."roster_club_id"
                                AND "nrt"."event_id" = "rt"."event_id"
                                AND "nrt"."status_entry" <> 11
                            GROUP BY "nrt"."roster_team_id", d."reg_fee", e."reg_fee"
                        ) "team"
                    ) "new_team"
                FROM "purchase" p 
                INNER JOIN "event" e 
                    ON e."event_id" = p."event_id"
                INNER JOIN "purchase_team" pt 
                    ON pt."purchase_id" = p."purchase_id"
                    AND pt."canceled" IS NULL
                    AND pt."purchase_team_id" = $3
                INNER JOIN "roster_team" rt 
                    ON rt."roster_team_id" = pt."roster_team_id"
                    AND rt."roster_team_id" = $4
                    AND rt."deleted" IS NULL
                    AND rt."event_id" = p."event_id"
                WHERE p."event_id" = $1
                    AND p."status" IN ('pending', 'paid')
                    AND p."payment_for" = 'teams'
                    AND p."purchase_id" = $2`,
                [eventID, purchaseID, purchaseTeamID, oldRosterTeamID, newRosterTeamID]
            )
        }).then(res => {
            let resultRow = res.rows[0] || null;

            if (resultRow === null) {
                return Promise.reject(new Error('Suitable payment not found!'));
            }

            return resultRow;
        })
    }

    __parseNums__ (obj, exclude = []) {
        Object.keys(obj).forEach(key => {
            if (exclude.indexOf(key) === -1) {
                obj[key] = Number(obj[key]);
            }
        })
    }

    __switchRosterTeamID__ (tr, purchaseID, purchaseTeamID, rosterTeamID, divisionFee) {
        return tr.query(
            `UPDATE "purchase_team" 
             SET "roster_team_id" = $3,
                "division_fee" = NULLIF($4::numeric(8,2), 0)
             WHERE "purchase_id" = $1
                AND "purchase_team_id" = $2`,
            [purchaseID, purchaseTeamID, rosterTeamID, divisionFee]
        )
        .then(result => (result.rowCount === 1))
    }

    __resetSwappedTeam__ (tr, rosterTeamID, purchaseTeamID) {
        return tr.query(
            `UPDATE "roster_team" rt 
             SET "status_paid" = (
                    CASE
                        WHEN (
                            SELECT COALESCE(SUM(pt."amount"), 0)
                            FROM "purchase_team" pt 
                            WHERE pt."roster_team_id" = rt."roster_team_id"
                                AND pt."event_id" = rt."event_id"
                                AND pt."canceled" IS NULL 
                                AND pt."purchase_team_id" <> $2
                        ) > 0
                        THEN 24
                        ELSE 21
                    END
                 ),
                 "discount" = 0,
                 "date_paid" = NULL
             WHERE rt."roster_team_id" = $1`,
            [rosterTeamID, purchaseTeamID]
        ).then(res => (res.rowCount === 1))
    }

    __updTeamChangedTo__ (tr, newRosterTeamID, purchaseID, discount = null, oldRosterTeamID) {
        return tr.query(
            `UPDATE "roster_team" rt 
             SET "status_paid" = (
                    SELECT (
                        CASE 
                            WHEN (p."status" = 'paid')
                                THEN 22
                            WHEN (p."status" = 'pending' AND rt."status_paid" = 24)
                                THEN 24
                            ELSE 21
                        END
                     )
                    FROM "purchase" p 
                    WHERE p."purchase_id" = $2
                ),
                "discount" = $3,
                "date_paid" = (SELECT date_paid FROM roster_team WHERE roster_team_id = $4)
             WHERE rt."roster_team_id" = $1`,
            [newRosterTeamID, purchaseID, discount, oldRosterTeamID]
        ).then(res => (res.rowCount === 1))
    }

    __insHistoryRow__ (tr, purchaseID, names, userID, action = 'purchase.team-changed') {

        let descr = `Team "${names.old}" was changed to "${names.another}"`;

        return tr.query(
            `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id")
             VALUES ($1, $2, $3, $4)`, [
                purchaseID,
                action,
                descr,
                userID
            ]
        ).then(() => true)
    }

    /**
    * Changes "roster_team" from one to another in existing payment
    *
    * New team must meet such requirements:
    *   1. Must be a current season team of the same "roster_club" (thus be present at the same event)
    *   2. "status_paid" does not equal 22 (22 means "Paid") and does not equal 24 (24 means "Pending")
    *   3. Not a declined team ("status_entry" does not equal 11)
    *   4. Not a deleted team ("deleted" IS NULL)
    *   5. Team's due amount have to be equal to paid amount of the team to be replaced
    *       Formula: 
    *           R = Registration Fee (division's or event's)
    *           S = surcharge of the old team (may vary depending on payment type)
    *           D = new team's discount
    *           PA = already paid amount
    *      DUE = R + S - D - PA; (see __getTeamDue__() method)
    * 
    * @param {Object} data
    * @param {number} data.eventID - Id of the event the purchase rows belongs belongs to
    * @param {number} data.purchaseID - Id of the purchase row
    * @param {number} data.purchaseTeamID - Id of the item in purchase ("purchase_team" row)
    * @param {number} data.oldRosterTeamID - Id of the team ("roster_team") to be replaced
    * @param {number} data.newRosterTeamID - Id of the new team to switch to ("roster_team")
    * @param {number} data.userID - Id of the user, who performs the operation
    **/
    async changeTeamInPayment (data) {
        if (!_.isObject(data)) {
            throw new Error('Expecting data to be a object');
        }

        if (!Number.isInteger(data.userID)) {
            throw new Error('Expecting user id to be an integer');
        }

        let {old_team: oldTeam, new_team: newTeam} = await (this.__findTeamsToSwap__(data));

        if (_.isEmpty(newTeam)) {
            throw new Error(`Team #${data.newRosterTeamID} can not be used for change`);
        }

        this.__parseNums__(oldTeam, ['team_name']);
        this.__parseNums__(newTeam, ['team_name']);

        /* === Validate Team's Due === */

        /* First, we calculate team's due using the team's data */
        /* Ignore team's discount */
        let discountToSave = 0;
        let teamDue = this.__getTeamDue__(
            newTeam.reg_fee, newTeam.paid, 0, oldTeam.surcharge);

        if (teamDue >= oldTeam.amount) {
            discountToSave = swUtils.normalizeNumber(teamDue - oldTeam.amount);
        } else {
            loggers.errors_log.error('Due', teamDue, '< Amount', oldTeam.amount)
            throw new Error('Total due of the new team differs from the paid amount!');
        }

        let tr;

        try {
            /* === Apply changes to DB Rows === */
            tr = await Db.begin();

            let res = await Promise.all([
                this.__switchRosterTeamID__(
                    tr, data.purchaseID, data.purchaseTeamID,
                    data.newRosterTeamID, newTeam.division_fee),
                this.__updTeamChangedTo__(
                    tr, data.newRosterTeamID, data.purchaseID, discountToSave, data.oldRosterTeamID),
                this.__resetSwappedTeam__(tr, data.oldRosterTeamID, data.purchaseTeamID),
                this.__insHistoryRow__(tr, data.purchaseID, {
                    old         : oldTeam.team_name,
                    another     : newTeam.team_name
                }, data.userID)
            ]);

            /* Validate, if all the records were found in the previous step */
            for (let isUpdated in res) {
                if (!isUpdated) {
                    await (tr.rollback());

                    loggers.errors_log.error('Some of records were not found!')

                    throw new Error('Internal Error');
                }
            }

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    getPaymentState (eventID, paymentID) {
        return Promise.resolve().then(() => {
            if (!Number.isInteger(eventID) || (eventID <= 0)) {
                throw new Error('Invalid Event ID');
            }

            if (!Number.isInteger(paymentID) || (paymentID <= 0)) {

            }

            let sqlQuery = 
                `SELECT 
                    (EXTRACT(EPOCH FROM p."date_refunded" AT TIME ZONE e."timezone") * 1000)::BIGINT "date_refunded",
                    (EXTRACT(EPOCH FROM p."date_paid" AT TIME ZONE e."timezone") * 1000)::BIGINT "date_paid",
                    (EXTRACT(EPOCH FROM p."canceled_date" AT TIME ZONE e."timezone") * 1000)::BIGINT "canceled_date",
                    p."amount_refunded",
                    p."received_amount",
                    p."check_num",
                    p."status",
                    p."amount"
                 FROM "purchase" p 
                 INNER JOIN "event" e 
                    ON e."event_id" = p."event_id"
                 WHERE p."purchase_id" = $1 
                    AND p."event_id" = $2`;

            let castToInt = payment => {
                [
                    'date_refunded', 'date_paid', 'canceled_date', 
                    'amount_refunded', 'received_amount', 'amount'
                ].forEach(field => {
                    payment[field] = Number(payment[field]) || 0;
                })
            }

            return Db.query(sqlQuery, [paymentID, eventID])
            .then(res => {
                let payment = res.rows[0];

                /* jshint eqnull:true */
                if (payment == null) {
                    return Promise.reject(new Error('Payment not found!'))
                }

                castToInt(payment);

                return payment;
            })
        })
    }

    calculateExtraFee(amount, swFeePerTeam) {
        let extraFee = Math.floor(amount * MAX_EXTRA_FEE_COEF);

        if (extraFee % swFeePerTeam !== 0) {
            extraFee = this._utils.normalizeNumber(extraFee - (extraFee % swFeePerTeam));
        }

        return extraFee;
    }

    recountExtraFee(extraFee, swFeePerTeam, amountWithoutFees) {
        if (amountWithoutFees % swFeePerTeam === 0) {
            return this._utils.normalizeNumber(extraFee - amountWithoutFees);
        }

        const remainder = Math.ceil(amountWithoutFees / swFeePerTeam);

        extraFee = this._utils.normalizeNumber(extraFee - remainder * swFeePerTeam);

        return extraFee;
    }

    checkAmountWithoutFees(amount, extraFee, providerFee, swFee, swFeePerTeam) {
        const amountWithoutFees = this._utils.normalizeNumber(amount - extraFee - providerFee - swFee);

        if (amountWithoutFees > 0) {
            return extraFee < 0 ? 0 : extraFee;
        }

        return this.recountExtraFee(extraFee, swFeePerTeam, Math.abs(amountWithoutFees));
    }

    /**
     * This method record information to the BalanceInformationService object.
     *
     * @param {number} eventID
     * @param {number} recountedReceiptSwFee
     * @param {BalanceInformationService} service
     * @param {Object} tr - transaction
     * @param {String} key
     * @returns {Promise<void>}
     */
    async recordBalanceInformation(key, eventID, recountedReceiptSwFee, service, tr = null) {
        const doNotCollectedDisputesEscrow   = true;
        const getEscrowSummands             = false;
        const balanceInformationMode        = true;

        const [
            collected,
            {
                escrow: target,
                statusesInformation
            }
        ] = await Promise.all([
            this.getCollectedSWFee(eventID, null, null, null, tr),
            this.getSWFeeEscrowTarget(eventID, doNotCollectedDisputesEscrow, getEscrowSummands, recountedReceiptSwFee, balanceInformationMode, tr)
        ]);

        service[key] = {
            target,
            collected,
            ...statusesInformation,
        };
    }
}

module.exports = new TeamsPaymentService();
