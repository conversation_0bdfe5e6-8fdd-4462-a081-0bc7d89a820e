
const Validator = require('./ticket-buy-entry-code/TicketBuyEntryCodeValidator');
const SettingsService = require('./ticket-buy-entry-code/SettingsService');
const ListService = require('./ticket-buy-entry-code/TicketBuyEntryCodeList');
const TicketBuyEntryCodeCreator = require('./ticket-buy-entry-code/TicketBuyEntryCodeCreator');

class TicketBuyEntryCodeService {
    constructor () {
        this.settings = SettingsService;
        this.codeCreation = TicketBuyEntryCodeCreator;
    }

    async validateCode (eventID, code) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        if(!code) {
            throw { validation: 'Code required' };
        }

        const settings = await this.settings.getSettings(eventID);

        return new Validator(settings).validate(eventID, code);
    }

    async getCodesList (eventID, filters) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        const settings = await this.settings.getSettings(eventID);

        let codes = await new ListService(settings.activeSources).list(eventID, filters);

        return { codes, settings: settings.rawSettings };
    }

    async getCodeTicketsList (eventID, code) {
        let query = knex('purchase_ticket AS pt')
            .select({
                holder_name: knex.raw(`(p.first || ' ' || p.last)`),
                barcode: 'p.ticket_barcode',
                date_paid:
                    knex.raw(`TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI AM')`),
                status: 'p.status'
            })
            .join('purchase AS p', 'p.purchase_id', 'pt.purchase_id')
            .join('event AS e', 'e.event_id', 'p.event_id')
            .where('p.event_id', eventID)
            .whereRaw(`TRIM(LOWER(pt.ticket_buy_entry_code)) = TRIM(LOWER(?))`, [code]);

        let tickets = await Db.query(query).then(result => result && result.rows);

        if(!tickets || !tickets.length) {
            throw { validation: `Coupon ${code} not found` };
        }

        return tickets;
    }
}

module.exports = new TicketBuyEntryCodeService();
