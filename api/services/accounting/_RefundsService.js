'use strict';

const co = require('co');

class RefundsService {
    constructor () {}

    getRefunds (type, eventID) {
        return co(function* () {
            if(!type) {
                throw new Error('Refunds type required');
            }

            if(!AccountingService.Common.isAllowedPaymentFor(type)) {
                throw new Error('Refunds type incorrect');
            }

            if(!eventID) {
                throw new Error('Event ID required');
            }

            let refundsData = yield this.__getData__(type, eventID);

            let {
                refundSubtotal,
                refundsTotalCost,
                stripeAdjustmentSubtotal,
                refunds,
                collectedSWFeeTotal,
            } = this.__calculateFormulas__(refundsData);

            return {
                totalCost   : refundsTotalCost,
                refunds     : refunds,
                amountTotal : refundSubtotal,
                subTotal    : stripeAdjustmentSubtotal,
                swFeeTotal  : collectedSWFeeTotal
            };

        }.bind(this))
    }

    __getData__ (type, eventID) {
        let query = squel.select().from('purchase', 'p')
            .left_join('event', 'e', 'e.event_id = p.event_id')
            .field('p.amount')
            .field('p.amount_refunded')
            .field(
                squel.case()
                    .when(`p.status = 'canceled'`).then(squel.str('p.collected_sw_fee'))
                    .else(squel.str('0'))
            , 'collected_sw_fee')
            .field(`p.status = 'canceled'`, 'is_purchase_canceled')
            .where('p.payment_for = ?', type)
            .where('p.event_id = ?', eventID)
            .where('p.date_refunded IS NOT NULL')
            .where(`p.type IN ?`, AccountingService.Common.AVAILABLE_FOR_REFUND_PAYMENT_TYPES)
            .group('p.amount, p.amount_refunded, p.status, p.collected_sw_fee');

        if(type === AccountingService.Common.TICKETS_PAYMENT_FOR_TYPE) {
            query.left_join('purchase_ticket', 'p_ticket', 'p_ticket.purchase_id = p.purchase_id')
                .field('e.stripe_tickets_percent', 'percent')
                .field(`COALESCE((INITCAP(p.first) || ' '), '') || COALESCE((INITCAP(p.last)), '')`, 'name')
                .field('COUNT(p_ticket.*)', 'items_count')
                .group('p.user_id, p_ticket.ticket_fee, e.stripe_tickets_percent, p.first, p.last');
        }

        if(type === AccountingService.Common.TEAMS_PAYMENT_FOR_TYPE) {
            query.left_join('purchase_team', 'p_team', 'p_team.purchase_id = p.purchase_id')
                .left_join('roster_club', 'rc', 'rc.roster_club_id = p.roster_club_id')
                .field('p.purchase_id')
                .field('rc.club_name', 'name')
                .field(
                    squel.case()
                        .when('p.stripe_percent IS NULL AND e.stripe_teams_percent IS NULL')
                        .then(squel.str('0::INT'))
                        .else(squel.str('COALESCE(p.stripe_percent, e.stripe_teams_percent) / 100'))
                    , 'percent')
                .field('COUNT(p_team.*)', 'items_count')
                .group('p.roster_club_id, rc.club_name, p_team.sw_fee, e.stripe_teams_percent, p.purchase_id', 'p.stripe_percent');
        }

        if(type === AccountingService.Common.BOOTHS_PAYMENT_FOR_TYPE) {
            query.left_join('purchase_booth', 'p_booth', 'p_booth.purchase_id = p.purchase_id')
                .left_join('sponsor', 'sp', 'sp.sponsor_id = p.sponsor_id')
                .field('e.stripe_teams_percent', 'percent')
                .field('COUNT(p_booth.*)', 'items_count')
                .field('sp.company_name', 'name')
                .group('p.sponsor_id, p_booth.fee, e.stripe_teams_percent, sp.company_name');
        }

        return Db.query(query).then(result => result.rows);
    }

    __getRefundAmt__ (refund) {
        return Number(refund.amount_refunded);
    }

    __calculateStripeAdjustment__ (refund) {
        let stripeFee = StripeService.defaultStripeFee(Number(refund.amount_refunded), refund.percent);

        return stripeFee;
    }

    __calculateRefundsTotalCost__ (refundSubtotal, stripeAdjustmentSubtotal) {
        return refundSubtotal - stripeAdjustmentSubtotal;
    }

    __calculateFormulas__ (refundsData) {
        let refundSubtotal              = 0;
        let stripeAdjustmentSubtotal    = 0;
        let refundsTotalCost            = 0;
        let refunds                     = [];
        let collectedSWFeeTotal         = 0;

        if(refundsData && refundsData.length) {
            refunds = refundsData.map(refund => {
                let amount           = this.__getRefundAmt__(refund);
                let stripeAdjustment = this.__calculateStripeAdjustment__(refund);

                refundSubtotal              += amount;
                stripeAdjustmentSubtotal    += stripeAdjustment;
                collectedSWFeeTotal         += Number(refund.collected_sw_fee) || 0;

                return {
                    amount          : amount,
                    stripeAdjustment: stripeAdjustment,
                    name            : refund.name,
                    items_count     : refund.items_count,
                    purchase_id     : refund.purchase_id,
                    collected_sw_fee: refund.collected_sw_fee
                };
            });
        }

        refundsTotalCost = this.__calculateRefundsTotalCost__(refundSubtotal, stripeAdjustmentSubtotal);

        return {refundSubtotal, refundsTotalCost, stripeAdjustmentSubtotal, refunds, collectedSWFeeTotal};
    }

}

module.exports = new RefundsService();
