const {bansEmail} = require('../../validation-schemas/admin-ban');
const swUtils = require('../../lib/swUtils');

class _AdminBanEmailService {

    get UNIQUE_VIOLATION_ERR_CODE () {
       return '23505';
    }
    get UNIQUE_EMAIL_CONSTRAINT () {
        return 'unique_email';
    }

    getEmailBanInfoBaseQuery (search) {
      const baseQuery = knex
        .from('banned_email AS be')
        .leftJoin('purchase AS p', 'p.purchase_id', 'be.purchase_id')
        .leftJoin('user AS u', 'u.user_id', 'p.user_id')
        .leftJoin('event AS e', 'e.event_id', 'be.event_id');

      if (search) {
          baseQuery.where((builder) => {
            let formattedSearch = `%${search}%`;

            builder
                .where('be.email', 'ILIKE', formattedSearch)
                .orWhere('e.name', 'ILIKE', formattedSearch)
                .orWhere('p.first', 'ILIKE', formattedSearch)
                .orWhere('u.first', 'ILIKE', formattedSearch)
                .orWhere('p.last', 'ILIKE', formattedSearch)
                .orWhere('u.last', 'ILIKE', formattedSearch);
          });
      }

      return baseQuery
    }

    getEmailBanInfo($page, $limit, $search) {
         const limit = $limit || 100,
               page =  $page || 1,
               search = swUtils.escapeStr($search || '');

         const query = this.getEmailBanInfoBaseQuery(search)
                .select('be.email', 'be.banned_email_id', 'be.reason', 'be.history', 'be.created', {name: knex.raw(
                  'CASE WHEN u.user_id IS NOT NULL THEN FORMAT(\'%s %s\', u.first, u.last) '+
                       'ELSE FORMAT(\'%s %s\', p.first, p.last) ' +
                  'END'
                   )}, {event_name: 'e.name'}, {total: knex.raw('count(be.*) OVER()::INT')})
                 .orderBy('be.created')
                 .limit(limit)
                 .offset((page - 1) * limit);

         return Db.query(query)
             .then(result => {
                 return result.rows || [];
             })
     }

    async getEmailBanInfoWithMetadata({ offset = 0, limit = 25, search = '' }) {
        const baseQuery = this.getEmailBanInfoBaseQuery(search);

        const emailsQuery = baseQuery
            .clone()
            .select(
                'be.email',
                'be.banned_email_id',
                'be.reason',
                'be.history',
                'be.created',
                {
                    name: knex.raw(
                        "CASE WHEN u.user_id IS NOT NULL THEN FORMAT('%s %s', u.first, u.last) " +
                        "ELSE FORMAT('%s %s', p.first, p.last) " +
                        'END'
                    ),
                },
                { event_name: 'e.name' }
            )
            .orderBy('be.created', 'desc')
            .limit(limit)
            .offset(offset);

        const totalQuery = baseQuery
          .clone()
          .select(knex.raw('COUNT(*)::int AS "total"'));

        const emails = await Db.query(emailsQuery).then((result) => {
          return result.rows || [];
        });

        const total = await Db.query(totalQuery).then(
          ({ rows }) => rows[0].total
        );

        return {
          emails,
          metadata: {
            total,
            hasMore: offset + limit < total,
            nextOffset: offset + limit,
          },
        };
    }

    insertEmailBan(email, reason) {
        const validation = bansEmail.validate({email, reason});
        const query = 'INSERT INTO banned_email (email, reason) VALUES ($1,$2) RETURNING email, banned_email_id, reason, history, created';
        if (validation.error) {
            throw {validationErrors: validation.error.details};
        }
        return Db.query(query, [email, reason])
            .then(result => {
                return result.rows[0];
            })
            .catch(err => {
                if(err.code === this.UNIQUE_VIOLATION_ERR_CODE && err.constraint === this.UNIQUE_EMAIL_CONSTRAINT) {
                    throw new Error(`Duplicate parameter email : '${email}'`);
                }
                throw err;
            })
    }
    deleteEmailBan(banned_email_id) {
        const query = 'DELETE FROM banned_email WHERE banned_email_id = $1';
        if(isNaN(banned_email_id)){
            throw {validationErrors: `"Banned Email Id" must be a number`};
        }
        return Db.query(query, [banned_email_id] )
            .then(result => {
                if(result.rowCount === 0) {
                    throw new Error(`Email not found`);
                }
            })
    }
}

module.exports = new _AdminBanEmailService();
