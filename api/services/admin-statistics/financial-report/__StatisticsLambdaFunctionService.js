const fetch = require("node-fetch");
const { randomUUID } = require('crypto');
const moment = require('moment');

const LAMBDA_JOB_STATUS = {
    COMPLETED: 'completed',
    FAILED: 'failed',
    STARTED: 'started'
}

class StatisticsLambdaFunctionService {

    get LAMBDA_JOB_STATUS () {
        return LAMBDA_JOB_STATUS;
    }

    async getData (dateStart, dateEnd, paymentFor) {
        const settings = await this.__getLambdaRequestSettings();

        const jobParams = {
            afterDate: dateStart,
            beforeDate: dateEnd,
            paymentFor,
            reportId: this.__getLambdaJobID(),
            expired: this.__getLambdaJobExpirationDate()
        }

        const authHeaders = this.__getHeaders(settings?.credentials);

        await this.__createStatisticsLambdaJob(jobParams, authHeaders, settings.job);

        return jobParams.reportId;
    }

    async __getLambdaRequestSettings () {
        const lambdaSettingsQuery = knex('settings')
            .where({key: 'statistic_lambda_settings'})
            .select({value: 'value'});

        const settings = await Db.query(lambdaSettingsQuery).then(result => result?.rows[0]?.value);

        if(_.isEmpty(settings)) {
            throw new Error('Settings for Statistics Lambda Function not found!');
        }

        return settings;
    }

    __formatLambdaURL (settings) {
        const {
            stage,
            domain,
            method,
            resource,
        } = settings;

        if(!stage) {
            throw new Error('Stage required');
        }

        if(!domain) {
            throw new Error('Domain required');
        }

        if(!method) {
            throw new Error('Request method required');
        }

        if(!resource) {
            throw new Error('Request resource name required');
        }

        return `https://${domain}/${stage}/${resource}`;
    }

    async getJobResult (reportID) {
        const settings = await this.__getLambdaRequestSettings();
        const authHeaders = this.__getHeaders(settings?.credentials);

        let request = this.__formatJobResultRequest(reportID, authHeaders, settings);

        let result = await this.__jobResultRequest(request);

        if(result?.status === this.LAMBDA_JOB_STATUS.FAILED) {
            throw new Error('Statistics request failed');
        }

        if(result?.status === this.LAMBDA_JOB_STATUS.COMPLETED && result?.data) {
            return result.data;
        }

        return [];
    }

    __formatJobResultRequest (reportID, authHeaders, settings) {
        let url = this.__formatLambdaURL(settings.result);
        url += `/${reportID}`;

        return new fetch.Request(url, {
            method: settings.method,
            headers: authHeaders
        })
    }

    async __jobResultRequest (request) {
        const response = await fetch(request);

        const body = await response.json();

        if(!response.ok) {
            loggers.errors_log.error(body);
            throw Error(
                (body.error?.messages || ['Error sending statistics Lambda Job result api request']).join('\n')
            );
        }

        return body;
    }

    async __createStatisticsLambdaJob (jobData, authHeaders, settings) {
        const url = this.__formatLambdaURL(settings);

        let request = new fetch.Request(url, {
            method: settings.method,
            headers: authHeaders,
            body: JSON.stringify(jobData)
        })

        const response = await fetch(request);

        const body = await response.json();

        if(!response.ok) {
            loggers.errors_log.error(body);
            throw Error(
                (body.error?.messages || ['Error sending statistics Lambda Job creation api request']).join('\n')
            );
        }
    }

    __getHeaders (credentials = {}) {
        const { username, password } = credentials;

        if(!username) {
            throw new Error('Credentials username required');
        }

        if(!password) {
            throw new Error('Credentials password required');
        }

        const token = Buffer.from(`${username}:${password}`).toString('base64');

        const headers = new fetch.Headers();
        headers.set('Content-Type', 'application/json');
        headers.set('Authorization', `Basic ${token}`);

        return headers;
    }

    __getLambdaJobID () {
        return randomUUID();
    }

    __getLambdaJobExpirationDate () {
        return String(moment().add(1, 'hour').unix());
    }
}

module.exports = new StatisticsLambdaFunctionService();
