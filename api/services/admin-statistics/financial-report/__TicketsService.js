'use strict';

const swUtils = require('../../../lib/swUtils');
const MinStripePercentService = require('./__MinStripePercentService');
const { FEE_PAYER } = require('../../../constants/payments');

const FIXED = 0.3;

class TicketsService {

    constructor () {
        this.ratioSettings = null;
    }

    get SW_FEE_RATIO_BREAKPOINTS_KEY () {
        return 'sw_fee_ratio';
    }

    async getDeltas (payment) {
        this.STRIPE_PERCENT_MIN = MinStripePercentService.getMinStripePercent(payment);

        let refundDeltas = await this.getRefundDeltas(payment);
        let incomeDeltas = await this.getIncomeDeltas(payment);

        return Object.assign({}, refundDeltas, incomeDeltas);
    }

    async getRefundDeltas (payment) {
        let refundAmount            = Number(payment.refunds_total);
        let refundedAppFeeAmount    = Number(payment.app_fee_refunds_total);
        let initialChargeAmount     = Number(payment.initial_charge_amount);
        let refundCount             = Number(payment && payment.tickets_count.canceled);

        let {
            swFeeDelta: swFeeRefundDelta,
            stripeDelta: stripeRefundDelta,
            swFeeCollected: swFeeRefundCollected
        } = await this.__getDeltas(
            refundAmount,
            refundedAppFeeAmount,
            payment.stripe_percent,
            payment.tickets_sw_fee,
            payment.stripe_tickets_fee_payer,
            initialChargeAmount,
            payment.ratios,
            refundCount
        );

        return { swFeeRefundDelta, stripeRefundDelta, refundCount, swFeeRefundCollected };
    }

    async getIncomeDeltas (payment) {
        let chargeAmount        = Number(payment.charge_amount);
        let appFeeAmount        = Number(payment.app_fee_bt);
        let incomeCount         = Number(payment && payment.tickets_count.paid);

        let { swFeeDelta, stripeDelta, swFeeCollected } = await this.__getDeltas(
            chargeAmount,
            appFeeAmount,
            payment.stripe_percent,
            payment.tickets_sw_fee,
            payment.stripe_tickets_fee_payer,
            null,
            payment.ratios
        );

        return { swFeeDelta, stripeDelta, incomeCount, swFeeCollected };
    }

    async getOtherData (event, after, before) {
        let paidTicketsCounts = await this.__getTicketsCounts(event, after, before);
        let disputesFee = await this.__getDisputeFee(event, after, before);

        if(!_.isEmpty(paidTicketsCounts) && disputesFee > 0) {
            paidTicketsCounts.expected_sw_fee
                = swUtils.normalizeNumber(Number(paidTicketsCounts.expected_sw_fee) + disputesFee);
        }

        return paidTicketsCounts;
    }

    async __getTicketsCounts (event, after, before) {
        let query = `SELECT    COALESCE(COUNT(1) FILTER ( WHERE ticket.type = 'card' ), 0)::INT "card_tickets",
                               COALESCE(COUNT(1) FILTER ( WHERE ticket.type = 'cash' ), 0)::INT "cash_tickets",
                               SUM(COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee, 0))
                                FILTER ( WHERE pt.purchase_id IS NOT NULL )::NUMERIC            "expected_sw_fee",
                               COALESCE(SUM(pt.quantity), 0)::INT                               "total",
                               e.tickets_sw_fee::NUMERIC                                        "sw_fee"
                    FROM event e
                             LEFT JOIN purchase p ON e.event_id = p.event_id 
                                AND p.date_paid BETWEEN :after AND :before
                                AND p.status = 'paid'
                                AND p.payment_for = 'tickets'
                                AND p.is_payment IS TRUE
                             LEFT JOIN purchase ticket ON ticket.linked_purchase_id = p.purchase_id 
                                AND ticket.is_ticket IS TRUE 
                                AND (COALESCE(ticket.canceled_date, p.canceled_date) NOT BETWEEN :after AND :before OR
                                    COALESCE(ticket.canceled_date, p.canceled_date) IS NULL) 
                                AND ticket.type <> 'free'
                             LEFT JOIN purchase_ticket pt ON pt.purchase_id = ticket.purchase_id
                             LEFT JOIN event_ticket et ON et.event_ticket_id = pt.event_ticket_id
                    WHERE e.event_id = :event
                      AND (e."tickets_settings" ->> 'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                    GROUP BY e.tickets_sw_fee`;

        let data = await Db.query(
            knex.raw(query, { event, after, before }).toString()
        ).then(result => result.rows && result.rows[0] || null);

        if(!data) {
            return {};
        }

        return data;
    }

    async __getDisputeFee (event, after, before) {
        let query = `SELECT COALESCE(SUM(pt.quantity), 0) disputes_count, SUM(p.stripe_fee) stripe_fee_sum
                    FROM purchase p
                             JOIN purchase ticket ON ticket.linked_purchase_id = p.purchase_id 
                                AND ticket.is_ticket IS TRUE
                             JOIN purchase_ticket pt ON pt.purchase_id = ticket.purchase_id
                    WHERE p.event_id = :event
                      AND p.dispute_status = 'lost'
                      AND p.dispute_created BETWEEN :after AND :before
                      AND p.payment_for = 'tickets'
                      AND p.type = 'card'`

        let { disputes_count, stripe_fee_sum } = await Db.query(
            knex.raw(query, { event, after, before }).toString()
        ).then(result => result && result.rows[0] || {});

        if(!disputes_count) {
            return 0;
        }

        let disputesPenalty = swUtils.normalizeNumber(disputes_count * StripeService.DISPUTE_FEE);

        return Number(disputesPenalty) + Number(stripe_fee_sum);
    }

    async __getDeltas (amount, appFee, stripePercent, ticketsFee, feePayer, initialChargeAmount, ratio, refundCount) {
        let swFeeDelta = 0;
        let swFeeCollected = 0;

        if(amount === 0 && appFee === 0) {
            return { swFeeDelta, stripeDelta: 0, swFeeCollected };
        }

        let { stripeDelta, stripeFeeSw } = this.__getStripeFeeDelta(
            amount, appFee, stripePercent, feePayer, initialChargeAmount
        );

        if(appFee > 0 && ticketsFee > 0) {
            swFeeDelta = await this.__getDeltaSWFee(ratio, refundCount);
            swFeeCollected = this.__getCollectedSWFee(appFee, ticketsFee, stripeFeeSw);
        }

        return { swFeeDelta, stripeDelta, swFeeCollected };
    }

    __getStripeFeeDelta (amount, appFee, stripePercent, feePayer, initialChargeAmount) {
        let stripePercentMethod;
        let stripeDelta = 0;
        let stripeFeeMin = 0;
        let stripeFeeSw = 0;

        if(feePayer === FEE_PAYER.BUYER) {
            stripePercentMethod = StripeService.customerStripeFee.bind(StripeService);
        } else {
            stripePercentMethod = StripeService.defaultStripeFee.bind(StripeService);
        }

        stripeFeeSw = this.__getStripeFee(amount, stripePercent, initialChargeAmount, stripePercentMethod);

        if(stripePercent > this.STRIPE_PERCENT_MIN) {
            stripeFeeMin = this.__getStripeFee(amount, this.STRIPE_PERCENT_MIN, initialChargeAmount, stripePercentMethod);

            stripeDelta += swUtils.normalizeNumber(stripeFeeSw - stripeFeeMin);
        }

        return { stripeDelta, stripeFeeSw };
    }

    __getCollectedSWFee (collected, eventSWFee, stripeFeeSw) {
        return stripeFeeSw ? swUtils.normalizeNumber(collected - stripeFeeSw) : collected;
    }

    async __getDeltaSWFee (ratio, refundCount) {
        let ratiosValues = Object.assign([], ratio);

        //NOTE: we don't know which one ticket in purchase refunded
        if(refundCount) {
            ratiosValues = _.take(ratiosValues, refundCount);
        }

        let ratioSettings = await this.__getSWFeeRatioSettings();

        return ratiosValues.reduce((sum, r) => {
            let ratioValue = _.findLast(ratioSettings, (setting) => setting.min_value <= r.season_counter);

            sum += r.quantity * (_.isEmpty(ratioValue) ? FIXED : ratioValue.value);
            sum = swUtils.normalizeNumber(sum);

            return sum;
        }, 0);
    }

    __getStripeFee (amount, stripePercent, initialChargeAmount, stripeMethod) {
        if(initialChargeAmount && initialChargeAmount !== amount) {
            return this.__getPartialRefundStripeFee(amount, initialChargeAmount, stripePercent, stripeMethod);
        }

        return stripeMethod(amount, stripePercent, FIXED);
    }

    __getPartialRefundStripeFee (refundAmount, initialChargeAmount, stripePercent, stripeMethod) {
        let stripeFeeInitial = stripeMethod(initialChargeAmount, stripePercent, FIXED);

        if(refundAmount > 0) {
            let newAmount = swUtils.normalizeNumber(initialChargeAmount - refundAmount);
            let stripeFeeNew = stripeMethod(newAmount, stripePercent, FIXED);

            return swUtils.normalizeNumber(stripeFeeInitial - stripeFeeNew);
        }

        return stripeFeeInitial;
    }

    async __getSWFeeRatioSettings () {
        if(!this.ratioSettings) {
            let query = knex('settings')
                .select('value')
                .where('key', this.SW_FEE_RATIO_BREAKPOINTS_KEY);

            let ratioBreakpoints = await Db.query(query).then(result => result && result.rows[0] && result.rows[0].value);

            if(_.isEmpty(ratioBreakpoints)) {
                throw new Error('Ratio settings not found');
            }

            this.ratioSettings = ratioBreakpoints;
        }

        return this.ratioSettings;
    }
}

module.exports = new TicketsService();
