'use strict';

module.exports = {
    /**
     * @param {object} template
     *          {
     *              is_default,
     *              subject,
     *              bee_json,
     *              type,
     *              group,
     *              html,
     *              text
     *          } 
     * @param {number} eventID 
     * @returns {object} 
     *          - template with prop cc, if there is cc for the template
     *          - template as is, if there is no cc for the template
     *          - null, if no template OR template.group OR template.type OR eventID  
     */
    getTemplateCC(template, eventID) {
        if (!template || !template.group || !template.type || !_.isNumber(eventID)) return null;

        const { group, type } = template;

        const sql = getCCSQL(group, type);

        if (!sql) return template;

        return Db.query(sql, [eventID])
            .then(res => {
                if (res.rows[0] && res.rows[0].cc) {
                    template.cc = res.rows[0].cc;
                    return template;
                } else {
                    return template;
                }
            })
            .catch(() => {
                return template;
            });
    }

}

function getCCSQL (group, type) {
    const ccWhen = [
        // OfficialWithdrew  
        {
            group: AEMService.OFFICIAL_GROUP,
            type: AEMService.triggers.getRoleTmplTypeForWorkStatus(
                AEMService.TRIGGER_ROLE_PREFIX.OFFICIAL, 'withdrew'
            ),
            sql: `
                    SELECT array_to_string(array_agg(email),', ') as "cc"
                    FROM ( SELECT email
                        FROM v_head_referee_notification_receiver_data 
                        WHERE event_id = $1) W
                    `
        },
        // ACHPaymentFailed 
        {
            group: AEMService.TEAMS_PAYMENTS_GROUP,
            type: AEMService.triggers.getPaymentsTmplForPaymentType('teams', 'ach', 'canceled'),
            sql: `
                    SELECT DISTINCT eo_email as "cc"
                    FROM v_aem_teams_payments_data
                    WHERE v_aem_teams_payments_data.event_id = $1
                    `
        }
        /*         
        // Successful Payment
        {
            group: AEMService.TEAMS_PAYMENTS_GROUP,
            type: AEMService.triggers.getPaymentsTmplForPaymentType('teams', 'any', 'paid'),
            sql: `
                    SELECT DISTINCT eo_email as "cc"
                    FROM v_aem_teams_payments_data
                    WHERE v_aem_teams_payments_data.event_id = $1
                    `
        } 
        */
    ];

    const cc = ccWhen.find(e => e.group === group && e.type === type);

    return cc ? cc.sql : null;

}
