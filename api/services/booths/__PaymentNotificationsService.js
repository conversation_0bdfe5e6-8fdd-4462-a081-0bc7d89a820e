

class PaymentsNotificationsService {

    constructor (paymentUtils) {
        /**
         * @type {PaymentCommonService}
         */
        this.paymentUtils   = paymentUtils;
    }

    get BOOTHS_PAYMENTS_GROUP_TYPE () {
        return {
            FULL_REFUND      : 'refund.full',
            PAYMENT_ANY_PAID : 'booths.any.paid',
            INVOICE_GENERATED: 'invoice.generated'
        }
    }

    sendPaymentNotification(eventID, purchaseID, type) {
        switch (type) {
            case this.paymentUtils.CARD_PAYMENT_TYPE :
                return this.sendPaymentPaidNotification(eventID, purchaseID);

            case this.paymentUtils.PENDING_PAYMENT_TYPE :
                return this.sendInvoiceGeneratedNotification(eventID, purchaseID);

            case this.paymentUtils.CHECK_PAYMENT_TYPE :
                return this.sendPaymentPaidNotification(eventID, purchaseID);
        }
    }

    sendInvoiceGeneratedNotification(eventID, purchaseID) {
        let templateType = this.BOOTHS_PAYMENTS_GROUP_TYPE.INVOICE_GENERATED;

        return this.__sendNotification(eventID, purchaseID, templateType);
    }

    sendPaymentPaidNotification(eventID, purchaseID) {
        let templateType = this.BOOTHS_PAYMENTS_GROUP_TYPE.PAYMENT_ANY_PAID;

        return this.__sendNotification(eventID, purchaseID, templateType);
    }

    sendFullRefundNotification(eventID, purchaseID) {
        let templateType = this.BOOTHS_PAYMENTS_GROUP_TYPE.FULL_REFUND;

        return this.__sendNotification(eventID, purchaseID, templateType);
    }

    __sendNotification(eventID, purchaseID, templateType) {
        const templateGroup = AEMService.BOOTHS_PAYMENTS_GROUP;
        const filters       = { purchaseID };

        return AEMSenderService.sendTriggerNotification(templateGroup, templateType, eventID, filters).catch(() => {});
    }
}

module.exports = PaymentsNotificationsService;
