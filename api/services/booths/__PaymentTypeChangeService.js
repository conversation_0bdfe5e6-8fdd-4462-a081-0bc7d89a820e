'use strict';

const knex = require('knex')({client: 'pg'});

class PaymentTypeChangeService {
    constructor (paymentUtils, swUtils) {
        this.paymentUtils = paymentUtils;
        this.swUtils = swUtils;
    }

    async change (payment, type, userIdentifiers) {
        loggers.debug_log.verbose(`Start payment creation`);

        let tr              = null;
        let stripeChargeID  = null;

        try {
            tr = await Db.begin({ skipErrAboutCommittedTr: true });

            if(!payment.purchase_id) {
                throw { validation: 'Purchase ID required' };
            }

            let eventSettings = await this.paymentUtils.getEventSettings(payment, type);

            let purchaseID = await this.__changePurchaseType(payment, type, userIdentifiers, eventSettings, tr);

            if(!purchaseID) {
                throw { validation: 'Payment not found' };
            }

            let stripeChargeID = await this.paymentUtils.proceedPayment(type, eventSettings, payment, purchaseID, tr);

            loggers.debug_log.verbose(`Committing rows`);

            return tr.commit();
        } catch (err) {
            if(stripeChargeID) {
                this.paymentUtils.refundClientPayment(stripeChargeID).catch(err => loggers.errors_log.error(err));
            }

            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            throw err;
        }
    }

    async __changePurchaseType (payment, paymentType, userIdentifiers, eventSettings, tr) {
        loggers.debug_log.verbose(`Changing purchase row (${paymentType}) ${payment.token} $${payment.amount}`);

        let purchaseID = await this.__updatePurchaseRow(payment, paymentType, userIdentifiers, eventSettings, tr);

        if(!purchaseID) {
            throw { validation: 'Error on Payment creation' };
        }

        await Promise.all([
            this.__insertPurchaseBooth(purchaseID, payment.booth, tr),
            this.__insertPurchaseHistoryRow(purchaseID, userIdentifiers.user_id, payment.amount, tr)
        ]);

        return purchaseID;
    }

    __insertPurchaseHistoryRow (purchaseID, userID, amount, tr) {
        const query = knex('purchase_history')
            .insert({
                purchase_id : purchaseID,
                action      : this.paymentUtils.PURCHASE_TYPE_CHANGED_ACTION,
                description : 'Booths Payment type changed',
                user_id     : userID,
                amount      : amount
            });

        return tr.query(query);
    }

    __updatePurchaseRow (payment, paymentType, userIdentifiers, eventSettings, tr) {
        let updateObject = {
            amount:             payment.amount,
            type:               paymentType,
            notes:              payment.notes,
            booth_label:        payment.booth_label,
            event_owner_id:     userIdentifiers.event_owner_id || null,
            sales_manager_id:   userIdentifiers.sales_manager_id || null,
            user_id:            userIdentifiers.user_id || null,
            // By default, we set net_profit = amount, but net_profit value can be changed eventually, if
            // the payment type is card
            net_profit:         payment.amount
        };

        if(paymentType === this.paymentUtils.CARD_PAYMENT_TYPE) {
            updateObject.stripe_percent = this.swUtils.normalizeNumber(
                (eventSettings.stripe_percent || StripeService.DEFAULT_STRIPE_PERCENT) * 100
            );
        }

        let query = knex('purchase')
            .update(updateObject)
            .where('purchase_id', payment.purchase_id)
            .returning('purchase_id');

        return tr.query(query).then(result => result.rows[0] && result.rows[0].purchase_id);
    }

    async __insertPurchaseBooth (purchaseID, booth, tr) {
        await this.__deletePreviousBooths(purchaseID, tr);

        loggers.debug_log.verbose(`Inserting purchase_booth rows # ${purchaseID}`);

        const query = knex('purchase_booth');

        if (Array.isArray(booth)) {
            booth.forEach(b => {
                b.purchase_id = purchaseID;
            });
        } else {
            booth.purchase_id = purchaseID;
        }

        query.insert(booth);

        return tr.query(query);
    }

    __deletePreviousBooths (purchaseID, tr) {
        const query = knex('purchase_booth').where('purchase_id', purchaseID).del();

        return tr.query(query);
    }

}

module.exports = PaymentTypeChangeService;
