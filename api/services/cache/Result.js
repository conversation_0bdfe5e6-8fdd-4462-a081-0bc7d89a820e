class Result {
    constructor(fromCache) {
        const {
            result,
            created_at,
            ttl,
            tagVersions = {},
        } = Serializer.deserialize(fromCache);
        this._result = result;
        this._created_at = created_at;
        this._ttl = ttl;
        this._tagVersions = tagVersions;
    }

    get ttl() {
        return _.isNumber(this._ttl)
            ? this._ttl
            : Cache.getTtl(this._ttl);
    }

    get result() {
        return this._result;
    }

    get created_at() {
        return this._created_at;
    }

    get expiresAt() {
        return this.created_at + this.ttl * 1000;
    }

    isValid({tagVersions}) {
        return this.expiresAt > Date.now() && _.isEqual(this._tagVersions, tagVersions);
    }

    static formatForCaching({result, ttl, tagVersions}) {
        return Serializer.serialize({
            result,
            created_at: Date.now(),
            ttl,
            tagVersions,
        });
    }
}

module.exports = Result;
