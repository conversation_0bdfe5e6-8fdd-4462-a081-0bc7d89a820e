'use strict';

const 
	utils = require('../lib/swUtils');

const 
	DEFAULT_STRIPE_PERCENT 	= 0.029,
	DEFAULT_STRIPE_FIXED 	= 0.3;

module.exports = {
	getStatistics: function (eventId) {
		return Db.query(
            `SELECT ec.name "camp_name", ec.event_camp_id, (
                 SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("camp_types"))), '[]'::JSON)
                 FROM (
                     SELECT 
                         et.event_ticket_id, et.label, COALESCE(et.current_price, et.initial_price, 0) "price"
                     FROM "event_ticket" et
                     WHERE et.event_id = e.event_id 
                         AND et.event_camp_id = ec.event_camp_id
                 ) "camp_types"
             ) "types"
             FROM "event" e
             INNER JOIN "event_camp" ec 
                 ON ec.event_id = e.event_id
             WHERE e.event_id = $1
                 AND e.ticket_camps_registration IS TRUE
             ORDER BY ec.date_start `,
            [eventId]
        ).then(function (result) {
            return result.rows;
        }).then(function (camps) {
        	return Promise.all([
        		__calcCampsStats(eventId, camps),
        		__findStripeFeeAmount(eventId),
        		__findSWFeeAmount(eventId),
                __paymentTypesToShow(eventId),
                __countPenalty(eventId)
        	]).then(function (results) {
        		let stats 					= results[0];
        		
        		stats.totals.stripe_fee 	= results[1];
        		stats.totals.sw_fee 		= results[2];
                stats.av_opts               = results[3];
                stats.penalty               = results[4];

        		return stats;
        	})
        })
	}
}

function __countPenalty (eventId) {
    let achPenaltySQL = 

        `SELECT COALESCE(COUNT(p.*), 0) "qty"
         FROM "purchase" p 
         WHERE p.event_id = $1
            AND p.payment_for = 'tickets'
            AND p.type = 'ach'
            AND p.status = 'canceled'
            AND p.canceled_date IS NOT NULL 
            AND p.date_refunded IS NULL 
            AND (p.amount_refunded = 0 OR p.amount_refunded IS NULL)
            AND p.dispute_created IS NULL`;

    return Promise.all([
        Db.query(achPenaltySQL, [eventId]),
        FundsTransferService._countLostDisputesPenalty(eventId, 'tickets')
    ]).then(result => {
        let achRes      = result[0].rows[0],
            disputeRes  = result[1];

        let ach     = { 
            qty         : +achRes.qty, 
            tax         : StripeService.FAILED_ACH_PAYMENT_FEE, 
            amount      : utils.normalizeNumber(achRes.qty * StripeService.FAILED_ACH_PAYMENT_FEE)
        };

        let dispute = disputeRes;

        return { ach, dispute };
    });
}

function __paymentTypesToShow (eventId) {
    return Db.query(
        `SELECT (
                SELECT ROW_TO_JSON("opts")
                FROM (
                    SELECT 
                        e.tickets_purchase_by_card "card",
                        e.tickets_purchase_by_check "check",
                        e.tickets_purchase_by_ach "ach", (
                            SELECT BOOL_OR(et.waitlisted)
                        ) "waitlist"
                ) "opts"
            ) "options", (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(DISTINCT "p"."type")), '[]'::JSON)
                FROM "purchase" p 
                WHERE p.event_id = e.event_id
                    AND p.payment_for = 'tickets'
                    AND (p.status <> 'canceled' OR p.status IS NULL)
                    AND p.canceled_date IS NULL
            ) "existing"
        FROM "event" e
        LEFT JOIN "event_ticket" et 
            ON et.event_id = e.event_id
        WHERE e.event_id = $1
        GROUP BY e.event_id`,
        [eventId]
    ).then(result => {
        let data = result.rows[0];

        if(!data) {
            throw new Error('Event Data not found');
        }

        let existing    = data.existing,
            options     = data.options;

        return Object.keys(options).reduce((output, opt) => {
            output[opt] = (existing.indexOf(opt) >= 0) || options[opt];
            return output;
        }, {})
    });
}

function __calcCampsStats (eventId, camps) {
	let eventTotals = {
    	total_amount 			: 0,
    	total_qty 	 			: 0,
    	card_amount  			: 0,
    	check_pending_amount 	: 0,
    	check_received_amount 	: 0,
    	total_check 			: 0,
        ach_pending_amount      : 0,
        ach_received_amount     : 0,
        total_ach               : 0
    };
	return Promise.all(
        camps.map(function (camp) {
            return __getCampStatistics(eventId, camp.types)
            .then(function (campTypesStatistics) {
                let totalPurchases = 0, totalMoney = 0;

                camp.card           = { qty     : 0, amount     : 0 };
                camp.check_received = { qty     : 0, amount     : 0 };
                camp.check_pending  = { qty     : 0, amount     : 0 };
                camp.check_total    = { qty     : 0, amount     : 0 };
                camp.ach_received   = { qty     : 0, amount     : 0 };
                camp.ach_pending    = { qty     : 0, amount     : 0 };
                camp.ach_total      = { qty     : 0, amount     : 0 };
                camp.waitlisted     = { qty     : 0, amount     : 0 }; 
                camp.amount         = { total   : 0, pending    : 0 };

                camp.types.forEach(function (cType, index) {
                    cType.stats         = campTypesStatistics[index];

                    cType.stats.amount = {
                        total   : utils.normalizeNumber(
                                		utils.safeParse(cType.stats.card.amount) + 
                                		utils.safeParse(cType.stats.check_total.amount)),
                        pending : utils.normalizeNumber(utils.safeParse(cType.stats.check_pending.amount))
                    }

                    cType.total_qty     = utils.normalizeNumber(
                                                utils.safeParse(cType.stats.card.qty) + 
                                                utils.safeParse(cType.stats.check_received.qty) + 
                                                utils.safeParse(cType.stats.check_pending.qty)
                                            );

                    cType.total_amount   = utils.normalizeNumber(
                                                utils.safeParse(cType.stats.card.amount) +
                                                utils.safeParse(cType.stats.check_received.amount) +
                                                utils.safeParse(cType.stats.check_pending.amount)
                                            );


                    totalPurchases += utils.safeParse(cType.stats.card.qty);
                    totalPurchases += utils.safeParse(cType.stats.check_received.qty);
                    totalPurchases += utils.safeParse(cType.stats.check_pending.qty);

                    totalMoney += utils.safeParse(cType.stats.card.amount);
                    totalMoney += utils.safeParse(cType.stats.check_received.amount);
                    totalMoney += utils.safeParse(cType.stats.check_pending.amount);

                    camp.card.qty               += utils.safeParse(cType.stats.card.qty);
                    camp.card.amount            += utils.safeParse(cType.stats.card.amount);

                    camp.check_received.qty     += utils.safeParse(cType.stats.check_received.qty);
                    camp.check_received.amount  += utils.safeParse(cType.stats.check_received.amount);

                    camp.check_pending.qty      += utils.safeParse(cType.stats.check_pending.qty);
                    camp.check_pending.amount   += utils.safeParse(cType.stats.check_pending.amount);

                    camp.check_total.qty        += utils.safeParse(cType.stats.check_total.qty);
                    camp.check_total.amount     += utils.safeParse(cType.stats.check_total.amount);

                    camp.ach_received.qty           += utils.safeParse(cType.stats.ach_received.qty);
                    camp.ach_received.amount        += utils.safeParse(cType.stats.ach_received.amount);

                    camp.ach_pending.qty            += utils.safeParse(cType.stats.ach_pending.qty);
                    camp.ach_pending.amount         += utils.safeParse(cType.stats.ach_pending.amount);

                    camp.ach_total.qty              += utils.safeParse(cType.stats.ach_total.qty);
                    camp.ach_total.amount           += utils.safeParse(cType.stats.ach_total.amount);

                    camp.waitlisted.qty         += utils.safeParse(cType.stats.waitlisted.qty);
                    camp.waitlisted.amount      += utils.safeParse(cType.stats.waitlisted.amount);

                    camp.amount.total   += cType.stats.amount.total;
                    camp.amount.pending += cType.stats.amount.pending;
                });

                camp.total_qty      = utils.normalizeNumber(totalPurchases);
                camp.total_amount   = utils.normalizeNumber(totalMoney);

                camp.card.qty               = utils.normalizeNumber(camp.card.qty);
                camp.check_received.qty     = utils.normalizeNumber(camp.check_received.qty);
                camp.check_pending.qty      = utils.normalizeNumber(camp.check_pending.qty);
                camp.check_total.qty        = utils.normalizeNumber(camp.check_total.qty);
                camp.waitlisted.qty         = utils.normalizeNumber(camp.waitlisted.qty);
                camp.ach_received.qty       = utils.normalizeNumber(camp.ach_received.qty);
                camp.ach_pending.qty        = utils.normalizeNumber(camp.ach_pending.qty);
                camp.ach_total.qty          = utils.normalizeNumber(camp.ach_total.qty);
                camp.amount.total           = utils.normalizeNumber(camp.amount.total);

                camp.card.amount               = utils.normalizeNumber(camp.card.amount);
                camp.check_received.amount     = utils.normalizeNumber(camp.check_received.amount);
                camp.check_pending.amount      = utils.normalizeNumber(camp.check_pending.amount);
                camp.check_total.amount        = utils.normalizeNumber(camp.check_total.amount);
                camp.waitlisted.amount         = utils.normalizeNumber(camp.waitlisted.amount); 
                camp.ach_received.amount       = utils.normalizeNumber(camp.ach_received.amount);
                camp.ach_pending.amount        = utils.normalizeNumber(camp.ach_pending.amount);
                camp.ach_total.amount          = utils.normalizeNumber(camp.ach_total.amount);
                camp.amount.pending            = utils.normalizeNumber(camp.amount.pending);

                eventTotals.total_amount 			+= camp.total_amount;
                eventTotals.total_qty 	 			+= camp.total_amount;
                eventTotals.card_amount 			+= camp.card.amount;
                eventTotals.check_pending_amount 	+= camp.check_pending.amount;
                eventTotals.check_received_amount 	+= camp.check_received.amount;
                eventTotals.total_check 			+= camp.check_total.amount;
                eventTotals.ach_pending_amount      += camp.ach_pending.amount;
                eventTotals.ach_received_amount     += camp.ach_received.amount;
                eventTotals.total_ach               += camp.ach_total.amount;

                return camp;
            });
        })
    ).then(function (camps) {
    	let normalize 	= utils.normalizeNumber;

        Object.keys(eventTotals).forEach(prop => {
            eventTotals[prop] = normalize(eventTotals[prop]);
        });
    		
        return {
        	camps 	: camps,
        	totals 	: eventTotals
        }
    })
}

function __findStripeFeeAmount (eventId) {
	return Db.query(
		`SELECT
		    (
		    	d."card_sum" * COALESCE(d."stripe_percent", $2, 0) + COALESCE(d."stripe_fixed", $3, 0) ) "stripe_fee",
		    d.*
		 FROM (
		     SELECT (
		         SELECT      
		             COALESCE(SUM(pt.ticket_price * pt.quantity), 0)
		         FROM "purchase" p 
		         INNER JOIN "purchase_ticket" pt 
		             ON pt.purchase_id = p.purchase_id
		             AND pt.amount > 0
		             AND pt.quantity > 0
		         WHERE p.event_id = e.event_id
		             AND p.status <> 'canceled'
		             AND p.payment_for = 'tickets'
		             AND p.amount > 0
		             AND p.type = 'card'
		             AND (
		                 p.amount_refunded IS NULL 
		                 OR p.amount_refunded < p.amount
		             )
		      ) "card_sum", (
		         CASE
		             WHEN (e.stripe_tickets_percent > 0)
		                 THEN (e.stripe_tickets_percent / 100)
		             ELSE 0
		         END
		      ) "stripe_percent",
		      COALESCE(e.stripe_tickets_fixed, 0) "stripe_fixed"
		     FROM "event" e 
		     WHERE e.event_id = $1
		) "d"`,
		[eventId, DEFAULT_STRIPE_PERCENT, DEFAULT_STRIPE_FIXED]
	).then(result => {
		return utils.safeParse(result.rows[0].stripe_fee)
	});
}

function __findSWFeeAmount (eventId) {
    /**
     * NOTE: we do not collect SW fee for checks paymehts
     */
	return Db.query(
		`SELECT (
		      SELECT      
		          COALESCE(SUM(COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee)), 0)
		      FROM "purchase" p 
		      INNER JOIN "purchase_ticket" pt 
		          ON pt.purchase_id = p.purchase_id
		          AND pt.amount > 0
		          AND pt.quantity > 0
		      INNER JOIN "event_ticket" et 
		          ON et.event_ticket_id = pt.event_ticket_id
		      WHERE p.event_id = e.event_id
		          AND p.status <> 'canceled'
		          AND p.payment_for = 'tickets'
		          AND p.amount > 0
                  AND p.type = 'card'
		          AND (
		              p.amount_refunded IS NULL 
		              OR p.amount_refunded < p.amount
		          )
		   ) "sw_fee"
		  FROM "event" e 
		  WHERE e.event_id = $1`,
		[eventId]
	).then(result => {
		return utils.safeParse(result.rows[0].sw_fee)
	})
}

function __getCampStatistics (eventId, campTypes) {
    return Promise.all(
        campTypes.map(function (campType) {
            return Promise.all([
                __typeStat(eventId, campType.event_ticket_id, 'card'),
                __typeStat(eventId, campType.event_ticket_id, 'check_received'),
                __typeStat(eventId, campType.event_ticket_id, 'check_pending'),
                __typeStat(eventId, campType.event_ticket_id, 'waitlist'),
                __typeStat(eventId, campType.event_ticket_id, 'ach_received'),
                __typeStat(eventId, campType.event_ticket_id, 'ach_pending')
            ]).then(function (result) {
                return {
                    card            : result[0],
                    check_received  : result[1],
                    check_pending   : result[2],
                    waitlisted      : result[3],
                    ach_received    : result[4],
                    ach_pending     : result[5],
                    check_total     : {
                        qty     : utils.normalizeNumber(
                        			utils.safeParse(result[1].qty) + 
                        			utils.safeParse(result[2].qty)),
                        amount  : utils.normalizeNumber(
                        			utils.safeParse(result[1].amount) + utils.safeParse(result[2].amount))
                    },
                    ach_total       : {
                        qty     : utils.normalizeNumber(
                                    utils.safeParse(result[4].qty) + 
                                    utils.safeParse(result[5].qty)
                                  ),
                        amount  : utils.normalizeNumber(
                                    utils.safeParse(result[4].amount) + 
                                    utils.safeParse(result[5].amount)
                                  )
                    }
                }
            })
        })
    )
}

function __typeStat (eventId, eventTicketId, statType) {
    let whereBlock;
    switch (statType) {
        case 'card':
            whereBlock = ` AND p.type = 'card'`
            break;
        case 'check_received':
            whereBlock = ` AND p.type = 'check' AND p.received_date IS NOT NULL`
            break;
        case 'check_pending':
            whereBlock = ` AND p.type = 'check' AND p.received_date IS NULL`
            break;
        case 'waitlist':
            whereBlock = ` AND p.type = 'waitlist'`
            break;
        case 'ach_received':
            whereBlock = ` AND p.type = 'ach' AND p.status = 'paid'`;
            break;
        case 'ach_pending':
            whereBlock = ` AND p.type = 'ach' AND p.status = 'pending'`;
            break;
    }

    return Db.query(
        `SELECT COUNT(pt.*) "qty", COALESCE(SUM(pt.amount), 0) "amount"
         FROM "purchase" p 
         INNER JOIN "purchase_ticket" pt 
             ON pt.purchase_id = p.purchase_id
             AND pt.event_ticket_id = $2
             AND pt.amount > 0
             AND pt.quantity > 0
         WHERE p.event_id = $1
             AND (p.status <> 'canceled' OR p.status IS NULL)
             AND p.payment_for = 'tickets'
             AND p.amount > 0
            ${whereBlock}`,
        [eventId, eventTicketId]     
    ).then(function (result) {
        return _.first(result.rows) || {}
    })
}
