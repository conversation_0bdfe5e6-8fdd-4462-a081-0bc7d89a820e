
const InvoiceCreatedNotificationService = require('./notification/_InvoiceCreatedNotificationService');
const SuccessPaymentNotificationService = require('./notification/_SuccessPaymentNotificationService');
const FailedPaymentNotificationService = require('./notification/_FailedPaymentNotificationService');
const RefundedNotificationService = require('./notification/_RefundedNotificationService');
const {DEFAULT_SENDER} = require('../../constants/emails')

class ClubInvoiceNotificationService {

    constructor () {
        this.invoiceCreated = new InvoiceCreatedNotificationService(this);
        this.successPayment = new SuccessPaymentNotificationService(this);
        this.failedPayment = new FailedPaymentNotificationService(this);
        this.refunded = new RefundedNotificationService(this);
    }

    async send (email, subject, data, template) {
        if(!email) {
            throw new Error('Receiver email required');
        }

        if(!subject) {
            throw new Error('Subject required');
        }

        if(_.isEmpty(data)) {
            throw new Error('Template data required');
        }

        if(!template) {
            throw new Error('Template required');
        }

        return EmailService.renderAndSend({
            template,
            data,
            subject,
            from: DEFAULT_SENDER,
            to: email,
        });
    }
}

module.exports = new ClubInvoiceNotificationService();
