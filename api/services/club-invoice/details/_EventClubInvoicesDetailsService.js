'use strict';

const {PAYMENT_FOR} = require("../../../constants/payments");
const {PG_CURRENCY_FORMAT} = require("../../../constants/db");

class EventsClubInvoicesListService {

    async get (eventId, purchaseId) {
        if(!eventId) {
            throw { validation: 'Event Id required' };
        }

        if(!purchaseId) {
            throw { validation: 'Purchase Id required' };
        }

        const query = knex('purchase AS p')
            .select({
                purchase_id: 'p.purchase_id',
                created: knex.raw(`TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI, AM')`),
                paid: knex.raw(`TO_CHAR((p.date_paid::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI, AM')`),
                amount: knex.raw(`TO_CHAR(p.amount::NUMERIC, '${PG_CURRENCY_FORMAT}')`),
                amount_refunded: knex.raw(`TO_CHAR(p.amount_refunded::NUMERIC, '${PG_CURRENCY_FORMAT}')`),
                payer_name: knex.raw(`FORMAT('%s %s', INITCAP(mc.director_first), INITCAP(mc.director_last))`),
                email: 'mc.director_email',
                stripe_charge_id: 'p.stripe_charge_id',
                card_last_4: 'p.card_last_4',
                description: 'p.notes',
                type: 'p.type',
                status: 'p.status',
            })
            .join('roster_club AS rc', (join) => {
                join.on('rc.roster_club_id', 'p.roster_club_id')
                    .andOnNull('rc.deleted')
                    .andOn(knex.raw('rc.is_virtual IS NOT TRUE'));
            })
            .join('master_club as mc', 'mc.master_club_id', 'rc.master_club_id')
            .join('event AS e', 'e.event_id', 'p.event_id')
            .where('p.event_id', eventId)
            .where('p.purchase_id', purchaseId)
            .where('p.payment_for', PAYMENT_FOR.CLUB_INVOICE);

        const { rows: [clubInvoice] = [] } = await Db.query(query);

        return clubInvoice;
    }
}

module.exports = new EventsClubInvoicesListService();
