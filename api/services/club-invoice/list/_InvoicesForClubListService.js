'use strict';

const {PAYMENT_FOR, PAYMENT_STATUS, PAYMENT_TYPE} = require("../../../constants/payments");
const {PG_CURRENCY_FORMAT} = require("../../../constants/db");
const {CLUB_INVOICE_LIST_FILTER} = require("../../../constants/club-invoice");

class EventsClubInvoicesListService {
    get LIMIT () {
        return 20;
    }

    async get (masterClubId, filters = {}) {
        if(!masterClubId) {
            throw { validation: 'Master Club ID required' };
        }

        const query = knex('purchase AS p')
            .select({
                total_rows: knex.raw(`COUNT(*) OVER () :: INT`),
                purchase_id: 'p.purchase_id',
                created: knex.raw(`TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI, AM')`),
                paid: knex.raw(`TO_CHAR((p.date_paid::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI, AM')`),
                refunded: knex.raw(`TO_CHAR((p.date_refunded::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI, AM')`),
                amount: knex.raw(`TO_CHAR(p.amount::NUMERIC, '${PG_CURRENCY_FORMAT}')`),
                type: 'p.type',
                status: knex.raw(`(
		            CASE
		                WHEN (p.status = '${PAYMENT_STATUS.CANCELED}' and p.date_refunded IS NOT NULL)
		                    THEN '${CLUB_INVOICE_LIST_FILTER.PAYMENT_STATUS.REFUNDED}'
		                ELSE p.status
		            END
                )`),
                event_name: 'e.name'
            })
            .join('roster_club AS rc', (join) => {
                join.on('rc.roster_club_id', 'p.roster_club_id')
                    .andOnNull('rc.deleted')
                    .andOn(knex.raw('rc.is_virtual IS NOT TRUE'));
            })
            .join('master_club as mc', 'mc.master_club_id', 'rc.master_club_id')
            .join('event as e', 'e.event_id', 'p.event_id')
            .where('mc.master_club_id', masterClubId)
            .where('p.payment_for', PAYMENT_FOR.CLUB_INVOICE)
            .whereRaw(`(p.type <> '${PAYMENT_TYPE.PENDING_PAYMENT}' OR p.status <> '${PAYMENT_STATUS.CANCELED}')`)
            .limit(this.LIMIT);

        this.addSearch(query, filters);

        this.addFilters(query, filters);

        this.addOrdering(query, filters);

        const { rows: clubInvoices } = await Db.query(query);

        return clubInvoices;
    }

    addSearch (query, filters) {
        const {search} = filters;

        if(search) {
            query.where('e.name', 'ILIKE', `%${search}%`);
        }
    }

    addOrdering (query, filters) {
        if(!filters.order) {
            query.orderBy([{column: 'created'}]);
        } else {
            let orderFieldName;
            let orderDirection;

            if(filters.order === 'created') {
                orderFieldName = 'p.created';
            }

            if(filters.order === 'paid') {
                orderFieldName = 'p.date_paid';
            }

            if(filters.order === 'amount') {
                orderFieldName = 'p.amount';
            }

            if(filters.order === 'type') {
                orderFieldName = 'p.type';
            }

            if(filters.order === 'status') {
                orderFieldName = 'p.status';
            }

            if(filters.order === 'event_name') {
                orderFieldName = 'e.name';
            }

            orderDirection = filters.revert === 'true' ? 'desc' : 'asc';

            query.orderBy(orderFieldName, orderDirection);
        }
    }

    addFilters (query, filters) {
        if(_.isEmpty(filters)) {
            return;
        }

        if(filters.page) {
            query.offset((filters.page - 1) * this.LIMIT);
        }

        if(!_.isEmpty(filters.statuses)) {
            const statuses = _.isArray(filters.statuses) ? filters.statuses : [filters.statuses];

            query.where(builder => {
                for(const status of statuses) {
                    if(status === CLUB_INVOICE_LIST_FILTER.PAYMENT_STATUS.NOT_PAID) {
                        builder.orWhereRaw(`(p.type = '${PAYMENT_TYPE.PENDING_PAYMENT}' AND p.status = '${PAYMENT_STATUS.PENDING}')`);
                    } else if(status === CLUB_INVOICE_LIST_FILTER.PAYMENT_STATUS.PENDING) {
                        builder.orWhereRaw(`(p.type != '${PAYMENT_TYPE.PENDING_PAYMENT}' AND p.status = '${PAYMENT_STATUS.PENDING}')`);
                    } else if(status === CLUB_INVOICE_LIST_FILTER.PAYMENT_STATUS.REFUNDED) {
                        builder.orWhereRaw(`(p.status = '${PAYMENT_STATUS.CANCELED}' and p.date_refunded is not null)`);
                    } else {
                        builder.orWhere("p.status", status);
                    }
                }
            })
        }
    }
}

module.exports = new EventsClubInvoicesListService();
