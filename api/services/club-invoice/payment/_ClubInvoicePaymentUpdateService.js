const swUtils = require('../../../lib/swUtils');

const {
    PAYMENT_FOR,
    PAYMENT_TYPE,
    PAYMENT_STATUS,
    STRIPE_PAYMENT_TYPE
} = require('../../../constants/payments');

const {
    clubInvoicePurchaseUpdate: purchaseUpdateSchema
} = require('../../../validation-schemas/club-invoice');

class ClubInvoicePaymentUpdateService {
    constructor(swUtils) {
        this.swUtils = swUtils;
    }

    async updatePaymentIntent(paymentData) {
        this.#validatePaymentData(paymentData);

        const recountedTotals = await this.#getRecountedTotals(paymentData);

        await this.#updatePaymentIntent(paymentData.payment_intent_id, recountedTotals);
    }

    async updatePayment(paymentData) {
        this.#validatePaymentData(paymentData);

        const recountedTotals = await this.#getRecountedTotals(paymentData);

        await this.#updatePurchase(paymentData, recountedTotals);
    }

    #validatePaymentData(paymentData) {
        const { error } = purchaseUpdateSchema.validate(paymentData);

        if (error) {
            throw { validation: error.details[0].message };
        }
    }

    async #getRecountedTotals(paymentData) {
        const {
            purchase_id,
            master_club_id,
            club_owner_id,
            payment_type,
            total_amount,
        } = paymentData;

        const invoiceData = await this.#getInvoiceData(purchase_id, master_club_id, club_owner_id);

        const recountedTotals = this.#recountAmount(invoiceData, payment_type);

        if(total_amount !== recountedTotals.total) {
            throw { validation: `Price validation failed. Got amount from client: ${
                    total_amount}; recounted amount: ${recountedTotals.total}` };
        }

        return recountedTotals;
    }

    async #getInvoiceData(purchaseId, masterClubId, clubOwnerId) {
        const query = knex('purchase as p')
            .select({
                amount: knex.raw(`p.amount::NUMERIC`),
                stripeFixed: 'e.stripe_teams_fixed',
                cardPercent: knex.raw(`ROUND((
                    CASE
                        WHEN (e.stripe_teams_percent > 0)
                            THEN (e.stripe_teams_percent / 100)
                        ELSE 0
                    END
                ), 3)::FLOAT`),
                achPercent: knex.raw(`ROUND((
                    CASE 
                        WHEN (e.ach_teams_percent > 0)
                            THEN (e.ach_teams_percent / 100)
                        ELSE 0
                    END
                ), 3)::FLOAT`),
                allowCardPayments: 'e.allow_card_payments',
                allowACHPayments: 'e.allow_ach_payments',
            })
            .join('event as e', 'e.event_id', 'p.event_id')
            .join('roster_club as rc', 'rc.roster_club_id', 'p.roster_club_id')
            .join('master_club as mc', 'mc.master_club_id', 'rc.master_club_id')
            .where('p.purchase_id', purchaseId)
            .where('rc.master_club_id', masterClubId)
            .where('mc.club_owner_id', clubOwnerId)
            .where('p.status', PAYMENT_STATUS.PENDING)
            .where('p.type', PAYMENT_TYPE.PENDING_PAYMENT)
            .where('p.payment_for', PAYMENT_FOR.CLUB_INVOICE);

        const invoice = await Db.query(query).then(({ rows }) => rows[0] || {});

        if(_.isEmpty(invoice)) {
            throw { validation: 'No such invoice' };
        }

        return invoice;
    }

    #recountAmount(invoiceData, paymentType) {
        const {amount, cardPercent, stripeFixed, achPercent} = invoiceData;
        const subtotal = this.swUtils.normalizeNumber(amount);

        let stripeFee = 0;
        if(paymentType === PAYMENT_TYPE.CARD) {
            stripeFee = StripeService.customerStripeFee(subtotal, cardPercent, stripeFixed);
        } else if(paymentType === PAYMENT_TYPE.ACH) {
            stripeFee = StripeService.customerACHStripeFee(subtotal, achPercent);
        }

        return  {
            subtotal,
            merchantFee: stripeFee,
            total: this.swUtils.normalizeNumber(subtotal + stripeFee),
            cardPercent,
            achPercent,
        };
    }

    #updatePaymentIntent(paymentIntentId, paymentIntentData) {
        const paymentIntent = {
            amount: this.swUtils.normalizeNumber(paymentIntentData.total * 100),
            application_fee_amount: this.swUtils.normalizeNumber(paymentIntentData.merchantFee * 100),
        };

        return StripeService.paymentCard.stripeService.updatePaymentIntent(paymentIntentId, paymentIntent);
    }

    async #updatePurchase(purchaseData,  recountedTotals) {
        const {
            purchase_id,
            payment_intent_id,
            payment_type,
            user
        } = purchaseData;

        let purchase = {
            payment_intent_id,
            stripe_payment_type: STRIPE_PAYMENT_TYPE.CONNECT,
            type: payment_type,
            stripe_fee: recountedTotals.merchantFee,
            net_profit: recountedTotals.subtotal,
            amount: recountedTotals.total,
            date_paid: knex.fn.now(),
            ...user
        }

        if(purchaseData.payment_type === PAYMENT_TYPE.ACH) {
            purchase.stripe_percent = this.swUtils.normalizeNumber(recountedTotals.achPercent * 100);
        }

        if(purchaseData.payment_type === PAYMENT_TYPE.CARD) {
            purchase.stripe_percent = this.swUtils.normalizeNumber(recountedTotals.cardPercent * 100);
        }

        const query = knex('purchase').update(purchase)
            .where('purchase_id', purchase_id)
            .where('payment_for', PAYMENT_FOR.CLUB_INVOICE)
            .where('type', PAYMENT_TYPE.PENDING_PAYMENT)
            .where('status', PAYMENT_STATUS.PENDING);

        const {rowCount} = await Db.query(query);

        if (!rowCount) {
            throw new Error('Purchase not updated');
        }
    }
}

module.exports = new ClubInvoicePaymentUpdateService(swUtils);
