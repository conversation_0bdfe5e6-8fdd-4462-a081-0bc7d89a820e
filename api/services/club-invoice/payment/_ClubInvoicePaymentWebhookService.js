'use strict';

const {
    PAYMENT_STATUS,
    PAYMENT_TYPE,
    PAYMENT_FOR,
    ACH_PAYMENT_SUB_TYPES
} = require('../../../constants/payments');
const swUtils = require('../../../lib/swUtils');

const StripeConnect = require('../../../lib/StripeConnect');

class ClubInvoicePaymentWebhookService {

    async process(webhookData) {
        const stripePaymentIntent = this.#getPaymentIntent(webhookData);

        const clubInvoice = await this.#getClubInvoiceByPaymentIntentId(stripePaymentIntent.id);
        const newClubInvoiceStatus = this.#mapStripeWebhookTypeToClubInvoiceStatus(webhookData);

        if (!clubInvoice) {
            if(newClubInvoiceStatus === PAYMENT_STATUS.CANCELED) {
                return;
            }

            const paymentIntent = this.#getPaymentIntent(webhookData);

            throw Error('Club invoice not found for stripe payment intent: ' + paymentIntent?.id);
        }

        await this.#validatePayment(stripePaymentIntent, clubInvoice);

        let tr;

        try {
            tr = await Db.begin();

            await this.#savePaymentIntentData(tr, webhookData, clubInvoice);
            await this.#updatePayment(tr, newClubInvoiceStatus, webhookData, clubInvoice);
            await this.#sendNotification(newClubInvoiceStatus, clubInvoice);

            await tr.commit();
        } catch(err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async #getClubInvoiceByPaymentIntentId(stripePaymentIntentID) {
        const query = knex('purchase as p')
            .select({
                type: 'p.type',
                amount: 'p.amount',
                status: 'p.status',
                purchaseId: 'p.purchase_id',
                stripe_percent: 'p.stripe_percent',
                net_profit: 'p.net_profit',
            })
            .where('p.payment_intent_id', stripePaymentIntentID)
            .where('p.status', PAYMENT_STATUS.PENDING)
            .where('p.payment_for', PAYMENT_FOR.CLUB_INVOICE);

        const { rows: [clubInvoice] } = await Db.query(query);

        return clubInvoice;
    }

    #mapStripeWebhookTypeToClubInvoiceStatus(webhookData) {
        switch (webhookData.type) {
            case 'payment_intent.requires_action':
            case 'payment_intent.processing':
                return PAYMENT_STATUS.PENDING;
            case 'payment_intent.succeeded':
                return PAYMENT_STATUS.PAID;
            case 'payment_intent.payment_failed':
                return PAYMENT_STATUS.CANCELED;
            default:
                throw new Error(`Not supported webhook type ${webhookData.type}`);
        }
    }

    #matchPaymentTypes(paymentType) {
        if(ACH_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = PAYMENT_TYPE.ACH;
        }

        return paymentType;
    }

    #prepareFailedPaymentData(purchase) {
        return {
            payment_intent_id: null,
            type: PAYMENT_TYPE.PENDING_PAYMENT,
            first: null,
            last: null,
            user_id: null,
            payer_ip: null,
            email: null,
            phone: null,
            date_paid: null,
            stripe_percent: null,
            stripe_fee: null,
            stripe_payment_type: null,
            net_profit: null,
            status: PAYMENT_STATUS.PENDING,
            amount: purchase.net_profit,
            stripe_card_id: null,
            received_date: null,
            stripe_card_fingerprint: null,
            card_last_4: null,
            stripe_charge_id: null,
            card_name: null,
        }
    }

    #prepareSuccessPaymentData(webhookData) {
        const stripePaymentIntent = this.#getPaymentIntent(webhookData);
        const charge = this.#getCharge(stripePaymentIntent) || {};
        const paymentStatus = this.#mapStripeWebhookTypeToClubInvoiceStatus(webhookData);
        const paymentType = this.#getPaymentIntentPaymentMethodType(stripePaymentIntent);

        const paymentMethod = this.#getPaymentMethod(charge);

        const paymentDataForUpdate = {
            stripe_card_id: charge?.source?.id,
            card_name: charge?.source?.name,
            stripe_charge_id: charge.id,
            card_last_4: paymentMethod?.last4,
            stripe_card_fingerprint: paymentMethod?.fingerprint,
            status: paymentStatus,
            is_payment: true,
            is_ticket: true
        }

        const billingEmail = charge?.billing_details?.email;

        if(paymentType === PAYMENT_TYPE.ACH && billingEmail) {
            paymentDataForUpdate.email = billingEmail;
        }

        if(paymentStatus === PAYMENT_STATUS.PAID) {
            paymentDataForUpdate.received_date = knex.fn.now();
        }

        return paymentDataForUpdate;
    }

    async #updatePayment(tr, newClubInvoiceStatus, webhookData, clubInvoice) {
        const isProcessedPayment = this.#isProcessedPayment(newClubInvoiceStatus, clubInvoice.type);
        const isFailedPayment = this.#isFailedPayment(newClubInvoiceStatus);

        if(isFailedPayment) {
            await this.#setPaymentFailed(tr, clubInvoice.purchaseId, clubInvoice);
        } else if(isProcessedPayment) {
            await this.#updateProcessedPayment(tr, clubInvoice.purchaseId, webhookData);
        }
    }

    #setPaymentFailed(tr, purchaseID, clubInvoice) {
        const updateData = this.#prepareFailedPaymentData(clubInvoice);

        return this.#updatePaymentRow(tr, purchaseID, updateData);
    }

    #updateProcessedPayment(tr, purchaseID, webhookData) {
        const updateData = this.#prepareSuccessPaymentData(webhookData);

        return this.#updatePaymentRow(tr, purchaseID, updateData);
    }

    async #updatePaymentRow(tr, purchaseId, data) {
        const query = knex('purchase')
            .update(data)
            .where({
                purchase_id: purchaseId,
                payment_for: PAYMENT_FOR.CLUB_INVOICE,
                status: PAYMENT_STATUS.PENDING
            })

        const { rowCount } = await tr.query(query);

        if (!rowCount) {
            throw new Error('Club invoice not updated');
        }
    }

    async #sendNotification(newClubInvoiceStatus, clubInvoice) {
        const isSuccessPayment = this.#isSuccessPayment(newClubInvoiceStatus);
        const isFailedPayment = this.#isFailedPayment(newClubInvoiceStatus);

        if(isFailedPayment) {
            await ClubInvoiceService.notification.failedPayment.send(clubInvoice.purchaseId, clubInvoice.type, clubInvoice.amount);
        } else if(isSuccessPayment) {
            await ClubInvoiceService.notification.successPayment.send(clubInvoice.purchaseId);
        }
    }

    #getPaymentIntent(webhookData) {
        return webhookData?.data?.object;
    }

    #getPaymentIntentPaymentMethodType (stripePaymentIntent = {}) {
        const charge = this.#getCharge(stripePaymentIntent);

        return this.#matchPaymentTypes(charge?.payment_method_details?.type);
    }

    #getCharge(stripePaymentIntent) {
        const latestChargeID = stripePaymentIntent?.latest_charge;
        const paymentIntentCharges = stripePaymentIntent?.charges?.data;

        return paymentIntentCharges.find((charge) => charge?.id === latestChargeID);
    }

    #savePaymentIntentData(tr, webhookData, settings) {
        const stripePaymentIntent = this.#getPaymentIntent(webhookData);

        return this.#createPaymentIntentRow(tr, stripePaymentIntent, settings.stripe_percent);
    }

    #createPaymentIntentRow(tr, stripePaymentIntent, stripePercent) {
        const dataForInsert = {
            payment_intent_id: stripePaymentIntent.id,
            payment_intent_status: stripePaymentIntent.status,
            amount: swUtils.normalizeNumber(stripePaymentIntent.amount / 100),
            stripe_percent: stripePercent,
        };

        const charge = this.#getCharge(stripePaymentIntent);

        if(charge) {
            const paymentMethod = this.#getPaymentMethod(charge);

            dataForInsert.stripe_charge_id = charge.id;
            dataForInsert.stripe_fee = swUtils.normalizeNumber(charge?.application_fee_amount / 100);
            dataForInsert.stripe_card_fingerprint = paymentMethod?.fingerprint;
        }

        const query = knex('stripe_payment_intent AS spi').insert(dataForInsert);

        return tr.query(query);
    }

    #isSuccessPayment(newClubInvoiceStatus) {
        return newClubInvoiceStatus === PAYMENT_STATUS.PAID;
    }

    #isProcessedPayment(newClubInvoiceStatus, paymentType) {
        return newClubInvoiceStatus === PAYMENT_STATUS.PAID
            || (newClubInvoiceStatus === PAYMENT_STATUS.PENDING && paymentType === PAYMENT_TYPE.ACH)
    }

    #isFailedPayment(newClubInvoiceStatus) {
        return newClubInvoiceStatus === PAYMENT_STATUS.CANCELED;
    }

    #getPaymentMethod(charge) {
        const paymentMethod = charge?.payment_method_details;
        const paymentMethodType = paymentMethod?.type;

        return paymentMethod[paymentMethodType];
    }

    async #validatePayment(stripePaymentIntent, clubInvoice) {
        const paymentTypeFromWebhook = this.#getPaymentIntentPaymentMethodType(stripePaymentIntent);

        try {
            if(clubInvoice.type !== paymentTypeFromWebhook) {
                throw new Error(
                    `Invalid payment type. Initial is ${clubInvoice.type}, type from webhook - ${paymentTypeFromWebhook}`
                );
            }
        } catch (err) {
            // If payment type created in stripe and saved to DB are not equal
            // make an auto refund and set payment failed
            await Promise.all([
                this.#tryAutoRefund(stripePaymentIntent),
                this.#tryCancel(clubInvoice),
            ]).catch(err => loggers.errors_log.error(err));
        }
    }

    async #tryAutoRefund(stripePaymentIntent) {
        const charge = this.#getCharge(stripePaymentIntent);

        try {
            await StripeConnect.refundClientPayment({ charge_id: charge.id });

            loggers.errors_log.error(`Charge with ID: ${charge.id} auto refunded.`);
        } catch (err) {
            loggers.errors_log.error(`Charge with ID: ${charge.id} not auto refunded.`);
            loggers.errors_log.error(err);
        }
    }

    async #tryCancel(clubInvoice) {
        try {
            await this.#setPaymentFailed(Db, clubInvoice.purchaseId, clubInvoice);

            loggers.errors_log.error(`Payment cancelled.`);
        } catch (err) {
            loggers.errors_log.error(`Try cancel failed.`);
            loggers.errors_log.error(err);
        }
    }
}

module.exports = new ClubInvoicePaymentWebhookService();
