'use strict';

const swUtils = require('../../../../lib/swUtils');

const {PAYMENT_FOR, PAYMENT_STATUS, PAYMENT_TYPE} = require("../../../../constants/payments");
const {CLUB_INVOICE_REFUND_FULL} = require("../../../../constants/notification-actions");
const StripeConnect = require("../../../../lib/StripeConnect");

class ClubInvoiceFullRefundService {
    constructor(swUtils) {
        this.swUtils = swUtils;
    }

    async proceed (eventId, invoiceId, userId) {
        const invoiceData = await this.#getInvoiceData(eventId, invoiceId);

        await this.#doRefund(invoiceId, invoiceData, userId);

        await ClubInvoiceService.notification.refunded.send(invoiceId);
    }

    async #getInvoiceData (eventId, invoiceId) {
        const query =
            `SELECT p.stripe_charge_id,
                p.amount,
                p.type,
                p.event_id,
                e.stripe_teams_fixed as stripeFixed,
                ROUND((
                    CASE
                        WHEN (e.stripe_teams_percent > 0)
                            THEN (e.stripe_teams_percent / 100)
                        ELSE 0
                    END
                ), 3)::FLOAT as cardPercent,
                ROUND((
                    CASE
                        WHEN (e.ach_teams_percent > 0)
                            THEN (e.ach_teams_percent / 100)
                        ELSE 0
                    END
                ), 3)::FLOAT as achPercent
            FROM purchase p
            INNER JOIN event e ON e.event_id = p.event_id
            WHERE p.event_id = $1
                AND p.purchase_id = $2
                AND p.type IN ('${PAYMENT_TYPE.CARD}', '${PAYMENT_TYPE.ACH}')
                AND p."status" = '${PAYMENT_STATUS.PAID}'
                AND p."payment_for" = '${PAYMENT_FOR.CLUB_INVOICE}'`;

        const {rows: [clubInvoice] = []} = await Db.query(query, [eventId, invoiceId]);

        if (_.isEmpty(clubInvoice)) {
            throw ({validation: 'No invoice found'});
        }

        return clubInvoice;
    }

    async #doRefund (invoiceId, invoiceData, userId) {
        await StripeConnect.refundClientPayment({charge_id: invoiceData.stripe_charge_id});

        let tr;
        try {
            tr = await Db.begin();

            await this.#updatePurchaseRow(tr, invoiceId, invoiceData);

            await this.#saveRefundHistory(tr, invoiceId, userId, invoiceData.amount);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }
            throw err;
        }
    }

    async #updatePurchaseRow (tr, purchaseId, purchaseData) {
        const query = `
            UPDATE "purchase"
            SET date_refunded   = NOW(),
                amount_refunded = COALESCE("amount_refunded", 0) + "amount",
                status          = '${PAYMENT_STATUS.CANCELED}',
                canceled_date   = NOW()
            WHERE purchase_id = $1
                AND event_id = $2;`;

        const {rowCount} = await tr.query(query, [purchaseId, purchaseData.event_id]);

        if(!rowCount) {
            throw { validation: 'Purchase not found' };
        }
    }

    #saveRefundHistory (tr, purchaseId, userId, amount = 0) {
        const query = knex('purchase_history')
            .insert({
                purchase_id: purchaseId,
                user_id: userId,
                amount: amount,
                action: CLUB_INVOICE_REFUND_FULL
            });

        return tr.query(query);
    }
}

module.exports = new ClubInvoiceFullRefundService(swUtils);
