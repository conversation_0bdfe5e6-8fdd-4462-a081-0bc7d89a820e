
const COUPON_HISTORY_ACTIONS = {
    ACTIVATE: 'activate',
    DEACTIVATE: 'deactivate',
};

class CouponHistoryService {
    get ACTIONS() {
        return COUPON_HISTORY_ACTIONS;
    }

    async createEvent(couponID, action, data) {
        if(!Object.values(this.ACTIONS).includes(action)) {
            throw { validation: `Unknown action '${action}'` }
        }
        const query = knex('ticket_coupon_history')
            .insert({
                ticket_coupon_id: couponID,
                action,
                data,
            });
        await Db.query(query);
    }

    getLastDeactivationReasonQuery(ticketCouponId) {
        return knex('ticket_coupon_history as tch')
            .select(knex.raw(`"tch"."data"->>'reason'`))
            .where('tch.ticket_coupon_id', ticketCouponId)
            .where('tch.action', this.ACTIONS.DEACTIVATE)
            .orderBy('tch.ticket_coupon_history_id', 'desc')
            .limit(1)
    }
}

module.exports = new CouponHistoryService();
