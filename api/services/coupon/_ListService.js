
class ListService {
    constructor() {}

    get LIMIT () {
        return 100;
    }

    get ACTIVATION_STATUS () {
        return {
            ACTIVATED: 'activated',
            DEACTIVATED: 'deactivated'
        }
    }

    get EMAIL_SENDING_STATUS () {
        return {
            SENT: 'sent',
            NOT_SENT: 'not_sent'
        }
    }

    getList (eventID, filters) {
        let innerQuery = knex('event_ticket AS et')
            .select({
                total_rows: knex.raw('COUNT(tc.*) OVER()::INT'),
                team_name: knex.raw(`COALESCE(rt.team_name, '-')`),
                coupon_code: knex.raw('UPPER(tc.code)'),
                valid_dates: knex.raw(`(
                    SELECT ARRAY_AGG(TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'YYYY Dy, Mon DD')
                        ORDER BY (TO_TIMESTAMP(vd, 'YYYY-MM-DD')) ASC)
                    FROM JSONB_OBJECT_KEYS(et.valid_dates) vd
                )`),
                quantity: 'tc.quantity',
                bought_qty: knex.raw('COUNT(pt.*)'),
                id: 'tc.ticket_coupon_id',
                emails: knex.raw(`
                    (SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(data)))
                    FROM (
                         ${this.__emailReceiversListSubQuery()}
                     ) data )
                `),
                active: 'tc.active',
            })
            .join('ticket_coupon AS tc', 'et.event_ticket_id', 'tc.event_ticket_id')
            .leftJoin('ticket_coupon_receiver AS tcr', function () {
                this.on('tcr.ticket_coupon_id', 'tc.ticket_coupon_id')
                    .andOnNotNull('tcr.roster_team_id')
            })
            .leftJoin('roster_team AS rt', function () {
                this.on('rt.roster_team_id', 'tcr.roster_team_id')
                    .andOn('rt.event_id', 'et.event_id')
            })
            .leftJoin('purchase_ticket AS pt', function () {
                this.on('et.event_ticket_id', 'pt.event_ticket_id')
                    .andOn('tc.ticket_coupon_id', 'pt.ticket_coupon_id')
                    .andOnNull('pt.canceled')
            })
            .where('et.event_id', eventID)
            .groupBy(
                'rt.roster_team_id', 'tc.ticket_coupon_id', 'et.event_ticket_id', 'et.valid_dates', 'et.event_id'
            )

        this.addOrdering(innerQuery, filters);
        this.addFilters(innerQuery, filters);

        let query = knex.queryBuilder().select().from(innerQuery.as('data'))
            .limit(this.LIMIT);

        this.filterSearch(query, filters);

        return Db.query(query).then(({rows}) => rows);
    }

    addOrdering (query, filters) {
        if(!filters.order) {
            query.orderBy([{column: 'rt.team_name'},
                {
                    column: knex.raw(`(
                        SELECT MIN(TO_TIMESTAMP(vd.*, 'YYYY-MM-DD'))
                        FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                    )`),
                }]);
        } else {
            let orderFieldName;
            let orderDirection;

            if(filters.order === 'quantity') {
                orderFieldName = 'tc.quantity';
            }

            if(filters.order === 'bought_qty') {
                orderFieldName = 'bought_qty';
            }

            if(filters.order === 'team_name') {
                orderFieldName = 'rt.team_name';
            }

            if(filters.order === 'valid_dates') {
                orderFieldName = knex.raw(
                    `(
                        SELECT MIN(TO_TIMESTAMP(vd.*, 'YYYY-MM-DD'))
                        FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                    )`
                );
            }

            orderDirection = filters.revert === 'true' ? 'desc' : 'asc';

            query.orderBy(orderFieldName, orderDirection);
        }
    }

    filterSearch (query, filters) {
        if(filters.search) {
            query.where(builder => {
                let search = `%${filters.search}%`;

                builder.where('data.coupon_code', 'ILIKE', search)
                    .orWhereRaw(
                        `(
                            SELECT TRUE FROM json_to_recordset(data.emails) AS data(email text) 
                            WHERE email ILIKE ? LIMIT 1
                        )`
                    , search)
                    .orWhere('data.team_name','ILIKE', search);
            })
        }
    }

    addFilters (query, filters) {
        if(_.isEmpty(filters)) {
            return;
        }

        if(filters.page) {
            query.offset((filters.page - 1) * this.LIMIT);
        }

        if(!_.isEmpty(filters.valid_dates)) {
            query.whereExists(
                knex.raw(
                    `SELECT 1 FROM jsonb_object_keys(et.valid_dates) AS d WHERE d.* = ANY (?)`,
                    [filters.valid_dates]
                )
            )
        }

        if(!_.isEmpty(filters.ticket_names)) {
            query.whereIn('et.event_ticket_id', filters.ticket_names);
        }

        if(!_.isEmpty(filters.email_statuses)) {
            let emailInHistorySubQuery = `
                SELECT 1 FROM event_email ee 
                WHERE ee.event_id = et.event_id 
                    AND ee.ticket_coupons @> to_jsonb(tc.ticket_coupon_id)`;

            let emailSent = filters.email_statuses.includes(this.EMAIL_SENDING_STATUS.SENT);
            let emailNotSent = filters.email_statuses.includes(this.EMAIL_SENDING_STATUS.NOT_SENT);

            if(!(emailSent && emailNotSent)) {
                if(emailSent) {
                    query.whereExists(knex.raw(`(${emailInHistorySubQuery})`));
                }

                if(emailNotSent) {
                    query.whereNotExists(knex.raw(`(${emailInHistorySubQuery})`));
                }
            }
        }

        if(!_.isEmpty(filters.activation_statuses)) {
            let isActive = filters.activation_statuses.includes(this.ACTIVATION_STATUS.ACTIVATED);
            let isDeactivated = filters.activation_statuses.includes(this.ACTIVATION_STATUS.DEACTIVATED);

            if(!(isActive && isDeactivated)) {
                if(isActive) {
                    query.whereRaw('tc.active IS TRUE');
                }

                if(isDeactivated) {
                    query.whereRaw('tc.active IS NOT TRUE');
                }
            }
        }

        if(Array.isArray(filters.coupons) && filters.coupons.length) {
            query.whereIn('tc.ticket_coupon_id', filters.coupons);
        }
    }

    __emailReceiversListSubQuery () {
        return `SELECT ee.email_to "email",
                (
                    CASE
                        WHEN ee.master_staff_id IS NOT NULL AND recipient_type = 'club'
                            THEN (ms.first || ' ' || ms.last)
                        WHEN ee.recipient_type = 'club' 
                            THEN (mc.director_first || ' ' || mc.director_last)
                        ELSE (tcr_email.first || ' ' || tcr_email.last)    
                        END
                    )       "name",
                ms.master_staff_id IS NOT NULL "is_staff",
                rt.roster_team_id IS NOT NULL "is_club"
             FROM event_email ee
                  LEFT JOIN ticket_coupon_receiver tcr_email 
                    ON tcr_email.ticket_coupon_id = tc.ticket_coupon_id 
                    AND tcr_email.email = ee.email_to
                  LEFT JOIN master_staff ms ON ms.email = ee.email_to 
                    AND ms.master_staff_id = ee.master_staff_id
                  LEFT JOIN roster_club rc ON rc.roster_club_id = ee.roster_club_id  
                  LEFT JOIN master_club mc ON 
                        mc.master_club_id = rc.master_club_id AND 
                        mc.director_email = ee.email_to AND 
                        ee.master_staff_id IS NULL
             WHERE ee.event_id = et.event_id 
                AND ee.ticket_coupons @> to_jsonb(tc.ticket_coupon_id)
             GROUP BY ee.email_to, ee.master_staff_id, ee.recipient_type, ms.first, ms.last, mc.director_first,
                          mc.director_last, tcr_email.first, tcr_email.last, ms.master_staff_id   
        `
    }
}

module.exports = new ListService()
