

class StatisticsCustomPaymentsService {
    get LIMIT () {
        return 20;
    }

    get PAYMENT_FOR_TYPE () {
        return {
            TICKETS: 'tickets',
            TEAMS: 'teams',
            BOOTHS: 'booths'
        }
    }

    get ALLOWED_PAYMENT_FOR_TYPE () {
        return Object.values(this.PAYMENT_FOR_TYPE);
    }

    getPaymentsList (eventID, paymentForType, filters) {
        if(!eventID) {
            return Promise.reject({ validation: 'Event ID required' });
        }

        if(!paymentForType) {
            return Promise.reject({ validation: 'Payment For Type required' });
        }

        if(!this.ALLOWED_PAYMENT_FOR_TYPE.includes(paymentForType)) {
            return Promise.reject({ validation: 'Payment For Type passed, not allowed' });
        }

        return this.__getPayments(eventID, paymentForType, filters);
    }

    __getPayments (eventID, paymentForType, filters) {
        let query = knex('custom_payment AS cp')
            .select({
                date_paid_long: knex.raw(`TO_CHAR(cp.created::TIMESTAMP, 'Mon DD, YYYY, HH12:MI AM')`),
                date_paid: knex.raw(`TO_CHAR(cp.created::TIMESTAMP, 'DD/MM')`),
                total_amount: knex.raw('cp.amount::NUMERIC'),
                net_profit: knex.raw('cp.net_profit::NUMERIC'),
                merchant_fee: 'cp.merchant_fee',
                uncollected_sw_fee: 'cpufbi.current_balance_sum',
                new_uncollected_sw_fee: 'cpufbi.new_balance_sum',
                balance_details: 'cpufbi.balance_details',
                total_rows: knex.raw('count(cp.*) OVER()::INT'),
                sw_fee: 'cpufbi.sw_fee',
                status: knex.raw(`
                    CASE
                        WHEN (cp.status = 'disputed' AND sd.status <> 'lost')
                            OR cp.status = 'paid' THEN 'paid'
                        WHEN (cp.status = 'disputed' AND sd.status = 'lost') THEN 'disputed' 
                        ELSE cp.status
                    END        
                `)
            })
            .leftJoin(
                'custom_payment_uncollected_fee_balance_info AS cpufbi',
                'cpufbi.custom_payment_id',
                'cp.custom_payment_id'
            )
            .join('stripe_payment_intent AS spi', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .leftJoin('stripe_dispute AS sd', (table) => {
                table.on('sd.stripe_charge_id', 'spi.stripe_charge_id')
                    .andOn(knex.raw(`cp.status = 'disputed'`))
            })
            .where('cp.event_id', eventID)
            .where('cp.payment_for_type', paymentForType);

        let limit = this.LIMIT;
        let orderFieldName = 'cp.created';
        let orderDirection = 'desc';

        if(!_.isEmpty(filters)) {
            if(filters.limit) {
                limit = filters.limit;
            }

            if(filters.page) {
                query.offset((filters.page - 1) * limit);
            }

            if(filters.order) {
                if(filters.order === 'date_paid') {
                    orderFieldName = 'cp.created';
                }

                if(filters.order === 'total_amount') {
                    orderFieldName = 'cp.amount';
                }

                if(filters.order === 'net_profit') {
                    orderFieldName = 'cp.net_profit';
                }

                if(filters.order === 'merchant_fee') {
                    orderFieldName = 'cp.merchant_fee';
                }

                orderDirection = filters.revert === 'true' ? 'desc' : 'asc';
            }
        }

        query.orderBy(orderFieldName, orderDirection);
        query.limit(limit);

        return Db.query(query).then(result => result && result.rows);
    }

}

module.exports = new StatisticsCustomPaymentsService()
