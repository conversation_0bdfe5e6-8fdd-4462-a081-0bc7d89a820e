
const swUtils = require('../../../../lib/swUtils');

const TicketsService = require('./failed-ach-fee-balance-info/tickets/_BalanceInfoTicketsService');
const TeamsService = require('./failed-ach-fee-balance-info/teams/_BalanceInfoTeamsService');

const {PAYMENT_FOR} = require('../../../../constants/payments');

class FailedACHFeeBalanceService {
    constructor(swUtils) {
        this.swUtils = swUtils;

        this.teams = new TeamsService(swUtils);
        this.tickets = new TicketsService(swUtils);
    }

    async getBalance(eventID, paymentForType) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        if(!paymentForType || ![PAYMENT_FOR.TEAMS, PAYMENT_FOR.TICKETS].includes(paymentForType)) {
            throw new Error('Payment For Type invalid: ' + paymentForType);
        }

        return this[paymentForType].getBalance(eventID);
    }

    async getCustomPaymentBalance(customPaymentID, paymentForType) {
        if(!customPaymentID) {
            throw { validation: 'Custom Payment ID required' };
        }

        if(!paymentForType || ![PAYMENT_FOR.TEAMS, PAYMENT_FOR.TICKETS].includes(paymentForType)) {
            throw new Error('Payment For Type invalid: ' + paymentForType);
        }

        return this[paymentForType].getCustomPaymentBalance(customPaymentID);
    }
}

module.exports = new FailedACHFeeBalanceService(swUtils);
