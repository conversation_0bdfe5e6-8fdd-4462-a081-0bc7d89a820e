
const swUtils = require('../../../../lib/swUtils');

const TicketsService = require('./lost-disputes-balance-info/tickets/_BalanceInfoTicketsService');
const TeamsService = require('./lost-disputes-balance-info/teams/_BalanceInfoTeamsService');

const { PAYMENT_FOR } = require('../../../../constants/payments');

class LostDisputesFeeBalanceService {
    constructor(swUtils) {
        this.swUtils = swUtils;

        this[PAYMENT_FOR.TEAMS] = new TeamsService(swUtils);
        this[PAYMENT_FOR.TICKETS] = new TicketsService(swUtils);
    }

    async getBalance(eventID, paymentForType) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        if(!paymentForType || ![PAYMENT_FOR.TEAMS, PAYMENT_FOR.TICKETS].includes(paymentForType)) {
            throw new Error('Payment For Type invalid: ' + paymentForType);
        }

        return this[paymentForType].getBalance(eventID);
    }

    async getCustomPaymentBalance(customPaymentID, paymentForType) {
        if(!customPaymentID) {
            throw { validation: 'Custom Payment ID required' };
        }

        if(!paymentForType || ![PAYMENT_FOR.TEAMS, PAYMENT_FOR.TICKETS].includes(paymentForType)) {
            throw new Error('Payment For Type invalid: ' + paymentForType);
        }

        return this[paymentForType].getCustomPaymentBalance(customPaymentID);
    }
}

module.exports = new LostDisputesFeeBalanceService(swUtils);
