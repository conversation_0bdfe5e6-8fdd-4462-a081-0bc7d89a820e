
const moment = require('moment');

const {
    CUSTOM_PAYMENT,
    DISPUTE_FEE,
    DISPUTE_PENALTY_CHANGE_2024_09_01,

} = require('../../../../../constants/payments');

class BalanceInfoService {
    constructor(swUtils) {
        this.approx = swUtils.normalizeNumber.bind(swUtils);
    }

    PAYMENT_FOR = null;

    async getBalance(eventID) {
        const disputesToPay = await this.#getDisputesToPay(eventID);

        return {
            totals: this.#countTotals(disputesToPay),
            chargeIDs: this.#extractChargeIDs(disputesToPay),
        }
    }

    async getCustomPaymentBalance(customPaymentID) {
        const query = knex('custom_payment as cp')
            .select({
                stripeChargeID: 'p.stripe_charge_id',
                stripeFee: knex.raw('p.stripe_fee::FLOAT'),
                disputeCreationDate: 'p.dispute_created',
                disputedAmount: knex.raw(`COALESCE((d.amount / 100), 0)`),
            })
            .join(
                'custom_payment_dispute_fee_failed_ach_fee as paid_dp',
                'paid_dp.custom_payment_id',
                'cp.custom_payment_id'
            )
            .join('purchase as p', 'p.stripe_charge_id', 'paid_dp.stripe_charge_id')
            .leftJoin('stripe.dispute as d', 'd.charge_id', 'paid_dp.stripe_charge_id')
            .where('paid_dp.type', 'dispute_fee')
            .where('cp.custom_payment_id', customPaymentID);

        const { rows: paymentsData } = await Db.query(query);

        if(!_.isEmpty(paymentsData)) {
            return {
                totals: this.#countTotals(paymentsData),
                chargeIDs: this.#extractChargeIDs(paymentsData),
            }
        }
    }

    #extractChargeIDs(paymentsData) {
        return paymentsData.map(disputeData => disputeData.stripeChargeID);
    }

    async #getDisputesToPay(eventID) {
        const [
            allDisputesToPay,
            alreadyPaidDisputes
        ] = await Promise.all([
            this.#getAllDisputesToPay(eventID),
            this.#getPaidDisputes(eventID)
        ]);

        return allDisputesToPay.filter(
            (dispute) => !alreadyPaidDisputes.includes(dispute.stripeChargeID)
        );
    }

    async #getAllDisputesToPay(eventID) {
        const query = knex('purchase AS p')
            .select({
                stripeChargeID: 'p.stripe_charge_id',
                stripeFee: knex.raw('p.stripe_fee::FLOAT'),
                disputeCreationDate: 'p.dispute_created',
                disputedAmount: knex.raw(`COALESCE((d.amount / 100), 0)`)
            })
            .leftJoin('stripe.dispute as d', 'd.charge_id', 'p.stripe_charge_id')
            .where('p.event_id', eventID)
            .whereNotNull('p.dispute_created')
            .whereRaw('p.dispute_created > ?', [CUSTOM_PAYMENT.UNCOLLECTED_FEE_AND_ADDITIONAL_FEES_SEPARATION_DATE])
            .where('p.payment_for', this.PAYMENT_FOR)
            .where('p.dispute_status', StripeService.DISPUTE_STATUS.LOST);

        const { rows: disputedPayments } = await Db.query(query);

        return disputedPayments;
    }

    async #getPaidDisputes(eventID) {
        const query = knex('custom_payment as cp')
            .select({
                paidChargeIDs: knex.raw(`COALESCE(JSONB_AGG(DISTINCT paid_dp.stripe_charge_id), '[]'::JSONB)`)
            })
            .join('custom_payment_dispute_fee_failed_ach_fee AS paid_dp', (join) => {
                join.on('paid_dp.custom_payment_id', 'cp.custom_payment_id')
                    .andOn(knex.raw(`paid_dp.type = ?`, 'dispute_fee'))
            })
            .where('cp.event_id', eventID)
            .whereIn('cp.status', ['pending', 'paid', 'requires_action'])
            .where('cp.payment_for', CUSTOM_PAYMENT.PAYMENT_FOR.LOST_DISPUTE_FEE_FAILED_ACH_FEE)
            .where('cp.payment_for_type', this.PAYMENT_FOR);

        const { rows: [result] } = await Db.query(query);

        return result?.paidChargeIDs;
    }

    #countTotals(disputedChargesList) {
        let totalPenalties = 0;
        let totalStripeFee = 0;
        let totalDisputedAmount = 0;

        for (const disputedCharge of disputedChargesList) {
            const {
                stripeChargeID,
                stripeFee,
                disputeCreationDate,
                disputedAmount,
            } = disputedCharge;

            const disputePenalty = this.#getDisputePenaltyAmount(stripeChargeID, disputeCreationDate);

            totalPenalties += Number(disputePenalty);
            totalStripeFee += Number(stripeFee);
            totalDisputedAmount += Number(disputedAmount);
        }

        return {
            totalPenalties: this.approx(totalPenalties),
            totalStripeFee: this.approx(totalStripeFee),
            totalDisputedAmount: this.approx(totalDisputedAmount),
            totalAmount: this.approx(totalPenalties + totalStripeFee),
            count: disputedChargesList.length,
        }
    }

    #getDisputePenaltyAmount(stripeChargeID, disputeCreationDate) {
        let dateDisputeFeeChanged = moment(
            DISPUTE_PENALTY_CHANGE_2024_09_01.DATE, moment.ISO_8601
        );

        if (!disputeCreationDate || !moment(disputeCreationDate, 'YYYY-MM-DD', true).isValid()) {
            console.error(
                `Dispute creation date not found or is not a valid date: ${disputeCreationDate}. 
                Payment: ${stripeChargeID}`
            );

            return DISPUTE_FEE;
        }

        if (moment().isAfter(dateDisputeFeeChanged)) {
            return DISPUTE_PENALTY_CHANGE_2024_09_01.FEE;
        } else {
            return DISPUTE_FEE;
        }
    }
}

module.exports = BalanceInfoService;
