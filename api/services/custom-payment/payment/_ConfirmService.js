
const argv = require('optimist').argv;
const stripeSettingsRow = argv.prod?'stripe_connect':'stripe_connect_dev';

const PaymentNotificationService = require('./_NotificationService');
const BalanceInfoService = require('./_BalanceInfoService');

const { CUSTOM_PAYMENT } = require('../../../constants/payments');

class ConfirmCustomPaymentService {
    constructor (NotificationService) {
        this.notifications = NotificationService;
        this.balanceInfo = BalanceInfoService;
    }

    getPendingPaymentsList (eventIDs) {
        if(!Array.isArray(eventIDs) || !eventIDs.length) {
            return Promise.reject(new Error('Event IDs required'));
        }

        let query = knex('custom_payment AS cp')
            .select({
                amount: 'cp.amount',
                card_last_4: 'spm.card_last_4',
                created: knex.raw(`TO_CHAR(cp.created, 'Mon DD, YYYY, HH12:MI AM')`),
                event_name: 'e.name',
                custom_payment_id: 'cp.custom_payment_id',
                description: 'cp.description'
            })
            .join('stripe_payment_intent AS spi', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .join('stripe_payment_method AS spm', 'spm.stripe_payment_method_id', 'spi.last_error_payment_method_id')
            .join('event AS e', 'e.event_id', 'cp.event_id')
            .where('cp.status', CUSTOM_PAYMENT.PAYMENT_STATUS.REQUIRES_ACTION)
            .where(
                'spi.payment_intent_status',
                StripeService.paymentCard.stripeService.PAYMENT_INTENT_STATUS.REQUIRES_PAYMENT_METHOD
            )
            .whereIn('cp.event_id', eventIDs);

        return Db.query(query).then(result => result && result.rows);
    }

    getConfirmationData (customPaymentID) {
        if(!customPaymentID) {
            return Promise.reject(new Error('Custom Payment ID required'));
        }

        let query = knex('custom_payment AS cp')
            .select({
                last_error_payment_method_id: 'spi.last_error_payment_method_id',
                last_error_client_secret: 'spi.last_error_client_secret',
                stripe_key: knex
                    .select({ public_key: knex.raw(`"value"->>'public_key'`) })
                    .from('settings')
                    .where('key', stripeSettingsRow)
            })
            .join('stripe_payment_intent AS spi', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .where('cp.custom_payment_id', customPaymentID);

        return Db.query(query).then(result => result && result.rows[0]);
    }

    async confirm (customPaymentID) {
        if(!customPaymentID) {
            throw new Error('Custom Payment ID required');
        }

        let paymentData = await this.__getPaymentData(customPaymentID);

        if(_.isEmpty(paymentData)) {
            throw new Error('Payment not found');
        }

        let lastStripeChargeID = await this.__getLastStripeChargeID(paymentData.payment_intent_id);

        let tr;

        try {
            tr = await Db.begin();

            await this.__updateStripeCustomPayment(
                tr,
                StripeService.paymentCard.stripeService.PAYMENT_INTENT_STATUS.SUCCEEDED,
                paymentData,
                lastStripeChargeID
            );

            await this.__updateCustomPaymentStatus(tr, customPaymentID, CUSTOM_PAYMENT.PAYMENT_STATUS.PAID);

            await tr.commit();

            const balanceInfo = await this.__getBalanceData(paymentData);

            await this.__sendSuccessNotification(paymentData, balanceInfo);
        } catch (err) {
            //Log origin error for case when in catch fires another error
            loggers.errors_log.error(err);

            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            /*
            * Cancel payment intent and refund charge if payment confirmation update failed
            * */
            if(lastStripeChargeID) {
                await this.__proceedFailedPayment(customPaymentID, lastStripeChargeID, paymentData);
            }

            throw err;
        }
    }

    async __getBalanceData ({ payment_for, custom_payment_id, payment_for_type }) {
        const balanceType = this.balanceInfo.getBalanceTypeForCustomPaymentType(payment_for);

        return this.balanceInfo[balanceType].getCustomPaymentBalance(custom_payment_id, payment_for_type);
    }

    async __sendSuccessNotification (paymentData, balanceInfo) {
        return this.notifications.success.send({
            event_id: paymentData.event_id,
            custom_payment_id: paymentData.custom_payment_id,
            event_name: paymentData.event_name,
            event_short_name: paymentData.event_short_name,
            totals: _.pick(paymentData, ['amount', 'total', 'merchant_fee']),
            email: paymentData.notification_email,
            balance: balanceInfo,
            payment_for_type: paymentData.payment_for_type,
            payment_method_type: paymentData.payment_method_type,
            card_last_4: paymentData.card_last_4,
        }, paymentData.payment_for).catch(err => loggers.errors_log.error(err));
    }

    async __getLastStripeChargeID (paymentIntentID) {
        let paymentIntent = await StripeService.paymentCard.stripeService.getPaymentIntent(paymentIntentID)

        if(_.isEmpty(paymentIntent.charges)) {
            throw { validation: 'No Payment Intent Charges Not Found' };
        }

        let charge = paymentIntent.charges.data.filter(charge =>
            charge.status === StripeService.paymentCard.stripeService.STRIPE_CHARGE_STATUS.SUCCEEDED
        )[0];

        return charge.id;
    }

    __getPaymentData (customPaymentID) {
        let query = knex('custom_payment AS cp')
            .select(
                'spi.payment_intent_id', 'spm.fingerprint', 'e.long_name AS event_name', 'e.name AS event_short_name',
                'cp.net_profit AS amount', 'e.event_id', 'cp.payment_for_type',
                'spi.stripe_payment_intent_id', 'cp.net_profit AS amount', 'cp.amount AS total', 'cp.merchant_fee',
                'spm.type as payment_method_type', 'cp.custom_payment_id', 'cp.payment_for',
                knex.raw(`(
                    CASE
                       WHEN cp.payment_for = '${CUSTOM_PAYMENT.PAYMENT_FOR.UNCOLLECTED_FEE}'
                           THEN COALESCE(NULLIF(e.team_fees_notification_email, ''), u.email)
                       ELSE u.email
                    END
                ) as "notification_email"`),
                knex.raw(`(
		            CASE
		                WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD}')
		                    THEN spm.card_last_4
                        WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH}')
		                    THEN spm.bank_account_last_4
		                ELSE ''
		            END
		         ) AS "card_last_4"`)
            )
            .join('stripe_payment_intent AS spi', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .join('stripe_payment_method AS spm', 'spm.stripe_payment_method_id', 'spi.last_error_payment_method_id')
            .join('user_stripe_customer AS usc', 'usc.stripe_customer_id', 'spm.stripe_customer_id')
            .join('event AS e', 'e.event_id', 'cp.event_id')
            .join('user AS u', 'u.user_id', 'usc.user_id')
            .where('cp.status', CUSTOM_PAYMENT.PAYMENT_STATUS.REQUIRES_ACTION)
            .where(
                'spi.payment_intent_status',
                StripeService.paymentCard.stripeService.PAYMENT_INTENT_STATUS.REQUIRES_PAYMENT_METHOD
            )
            .where('cp.custom_payment_id', customPaymentID);

        return Db.query(query).then(result => result && result.rows[0]);
    }

    __updateStripeCustomPayment (tr, status, paymentData, stripeChargeID) {
        let query = knex('stripe_payment_intent AS spi')
            .update({
                payment_intent_status: status,
                last_error_payment_method_id: null,
                last_error_client_secret: null,
                stripe_card_fingerprint: paymentData.fingerprint,
                stripe_charge_id: stripeChargeID
            })
            .where('spi.stripe_payment_intent_id', paymentData.stripe_payment_intent_id);

        return tr.query(query);
    }

    __updateCustomPaymentStatus (tr, customPaymentID, status) {
        let query = knex('custom_payment AS cp')
            .update('status', status)
            .where('cp.custom_payment_id', customPaymentID);

        return tr.query(query);
    }

    async __proceedFailedPayment (customPaymentID, lastStripeChargeID, paymentData) {
        await StripeService.paymentCard.stripeService.refundDirectPlatformPayment(lastStripeChargeID);

        let tr;

        try {
            tr = await Db.begin();

            await this.__updateStripeCustomPayment(
                tr,
                StripeService.paymentCard.stripeService.PAYMENT_INTENT_STATUS.CANCELED,
                paymentData,
                lastStripeChargeID
            );

            await this.__updateCustomPaymentStatus(
                tr,
                customPaymentID,
                CUSTOM_PAYMENT.PAYMENT_STATUS.CANCELED
            );

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            ErrorSender.uncollectedFeePaymentFailedSavingAfterConfirm(err);

            throw err;
        }
    }
}

module.exports = new ConfirmCustomPaymentService(PaymentNotificationService);
