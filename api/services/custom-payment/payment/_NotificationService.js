
const SuccessNotificationService = require('./notifications/_SuccessPaymentNotificationService');
const FailedNotificationService = require('./notifications/_FailedPaymentNotificationService');
const RequiresActionNotificationService = require('./notifications/_RequiresActionNotificationService');
const PendingPaymentNotificationService = require('./notifications/_PendingPaymentNotificationService');

const DEFAULT_SENDER = '"SportWrench" <<EMAIL>>';

class PaymentNotificationService {

    constructor () {
        this.success = new SuccessNotificationService(this);
        this.error = new FailedNotificationService(this);
        this.authentication = new RequiresActionNotificationService(this);
        this.pending = new PendingPaymentNotificationService(this);
    }

    get DEFAULT_SENDER () {
        return DEFAULT_SENDER;
    }

    get EMAIL_TEMPLATE_GROUPS () {
        return {
            [FundsTransferService.TRANSFER_TYPES.TICKETS]: AEMService.TICKETS_UNCOLLECTED_FEE_PAYMENTS_GROUP,
            [FundsTransferService.TRANSFER_TYPES.TEAMS]: AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP,
            [FundsTransferService.TRANSFER_TYPES.BOOTHS]: AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP,
        }
    }

    async sendByEmailModule (type, eventID, data) {
        if(!type) {
            throw new Error('Type required');
        }

        if(!eventID) {
            throw new Error('Event Id required');
        }

        if(_.isEmpty(data)) {
            throw new Error('Template data required');
        }

        const { payment_for_type } = data;
        const group = this.EMAIL_TEMPLATE_GROUPS[payment_for_type];

        return AEMSenderService.sendTriggerNotification(group, type, eventID, data);
    }

    async send (email, subject, data, template) {
        if(!email) {
            throw new Error('Receiver email required');
        }

        if(!subject) {
            throw new Error('Subject required');
        }

        if(_.isEmpty(data)) {
            throw new Error('Template data required');
        }

        if(!template) {
            throw new Error('Template required');
        }

        return EmailService.renderAndSend({
            template,
            data,
            subject,
            from: this.DEFAULT_SENDER,
            to: email,
        });
    }
}

module.exports = new PaymentNotificationService();
