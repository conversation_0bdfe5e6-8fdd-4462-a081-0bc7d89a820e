class PendingPaymentNotificationService {
    constructor(NotificationsService) {
        this.NotificationsService = NotificationsService;
    }

    get TYPES () {
        return {
            [FundsTransferService.TRANSFER_TYPES.TICKETS]: AEMService.TICKETS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE.PENDING,
            [FundsTransferService.TRANSFER_TYPES.TEAMS]: AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE.PENDING,
            [FundsTransferService.TRANSFER_TYPES.BOOTHS]: AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE.PENDING,
        }
    }

    send(notificationData) {
        const { event_id, payment_for_type } = notificationData;
        const type = this.TYPES[payment_for_type];

        return this.NotificationsService.sendByEmailModule(
            type,
            event_id,
            notificationData
        );
    }
}

module.exports = PendingPaymentNotificationService;
