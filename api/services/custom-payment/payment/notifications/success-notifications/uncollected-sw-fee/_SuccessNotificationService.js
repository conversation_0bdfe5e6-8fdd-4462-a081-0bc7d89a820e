

class SuccessPaymentNotificationService {
    constructor (NotificationsService) {
        this.NotificationsService = NotificationsService;
    }

    get TEMPLATES () {
        return {
            [FundsTransferService.TRANSFER_TYPES.TICKETS]: 'custom-offline-payment/ticket-fee-receipt',
            [FundsTransferService.TRANSFER_TYPES.TEAMS]: 'custom-offline-payment/team-fee-receipt',
            [FundsTransferService.TRANSFER_TYPES.BOOTHS]: 'custom-offline-payment/booth-fee-receipt',
        }
    }

    get SUBJECTS () {
        return {
            [FundsTransferService.TRANSFER_TYPES.TICKETS]: 'Receipt for SW Ticket Fees',
            [FundsTransferService.TRANSFER_TYPES.TEAMS]: 'Receipt for SW Team Fees',
            [FundsTransferService.TRANSFER_TYPES.BOOTHS]: 'Receipt for SW Booths Fees',
        }
    }

    get PAYMENT_METHOD_NAME () {
        return {
            CARD: 'credit card',
            ACH: 'bank account'
        }
    }

    get TYPES () {
        return {
            [FundsTransferService.TRANSFER_TYPES.TICKETS]: AEMService.TICKETS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE.SUCCESS,
            [FundsTransferService.TRANSFER_TYPES.TEAMS]: AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE.SUCCESS,
            [FundsTransferService.TRANSFER_TYPES.BOOTHS]: AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE.SUCCESS,
        }
    }

    send (params) {
        const { payment_for_type, custom_payment_id, event_id } = params;

        if(payment_for_type === FundsTransferService.TRANSFER_TYPES.BOOTHS) {
            return this.__notifyEO(params);
        } else {
            const type = this.TYPES[payment_for_type];
            const notificationData = {
                custom_payment_id,
                payment_for_type
            };

            return this.NotificationsService.sendByEmailModule(type, event_id, notificationData);
        }
    }

    __getNotificationData(params) {
        const { payment_for_type, event_name, payment_method_type } = params;

        const template = this.TEMPLATES[payment_for_type];
        const subject = `${this.SUBJECTS[payment_for_type]}: ${event_name}`;
        const data = {
            ...params,
            current_year: new Date().getFullYear(),
            payment_method_type: payment_method_type ? this.PAYMENT_METHOD_NAME[payment_method_type.toUpperCase()] : null,
        }

        return {
            template,
            subject,
            data,
        }
    }

    __notifyEO (params) {
        const { template, subject, data } = this.__getNotificationData(params);

        return this.NotificationsService.send(params.email, subject, data, template);
    }
}

module.exports = SuccessPaymentNotificationService;
