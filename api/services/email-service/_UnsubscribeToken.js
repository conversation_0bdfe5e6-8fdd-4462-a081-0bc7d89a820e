'use strict';

const TOKEN_NOT_FOUND_ERROR = Symbol('TOKEN_NOT_FOUND_ERROR');
const UNSUBSCRIBE_LEVEL = {
    EVENT: 'event',
    ALL: 'all'
}
const ALL_EVENTS_UNSUBSCRIBE_EVENT_ID =  0;

class UnsubscribeToken {
    get TOKEN_NOT_FOUND_ERROR() {
        return TOKEN_NOT_FOUND_ERROR;
    }

    get UNSUBSCRIBE_LEVEL() {
        return UNSUBSCRIBE_LEVEL;
    }

    get ALL_EVENTS_UNSUBSCRIBE_EVENT_ID () {
        return ALL_EVENTS_UNSUBSCRIBE_EVENT_ID;
    }
    async batchCreate(emails, metadata) {
        const tokensData = emails.map(
            email => ({
                email,
                metadata,
            })
        )

        const insertedTokens = await Db.query(
            knex('unsubscribe_token')
                .insert(tokensData)
                .returning(['token'])
        );

        return insertedTokens.rows.map(row => row.token);
    }

    async unsubscribeByToken(token, unsubscribeLevel = this.UNSUBSCRIBE_LEVEL.ALL) {
        const data = await this.find(token);
        if(!data) {
            throw TOKEN_NOT_FOUND_ERROR;
        }
        const emails = this._parseEmails(data.email);
        const query = knex('email_unsubscribe_list')
            .insert(
                emails.map((email) => ({
                    email,
                    unsubscribe_token_id: data.unsubscribe_token_id,
                    event_id: unsubscribeLevel === this.UNSUBSCRIBE_LEVEL.EVENT
                        ? data.metadata?.event_id
                        : this.ALL_EVENTS_UNSUBSCRIBE_EVENT_ID,
                }))
            );
        await Db.query(
            query.toString() + '\nON CONFLICT DO NOTHING'
        );
    }

    async find(token) {
        try {
            const result = await Db.query(
                knex('unsubscribe_token as ut')
                    .select('ut.*', 'e.long_name AS event_name')
                    .where('token', token)
                    .leftJoin('event AS e', knex.raw(`e.event_id = (ut.metadata ->> 'event_id')::NUMERIC`))
                    .limit(1)
            );

            return result.rows?.[0];
        }
        catch(err) {
            if(err?.toString?.()?.includes?.('invalid input syntax for type uuid')) {
                return undefined;
            }
            throw err;
        }
    }

    _parseEmails(recipientsString) {
        return recipientsString.split(',')
            .map((recipient) => {
                const match = /(.*<(?<email>.*?)>|(?<email2>.*))/.exec(recipient.trim());
                return match.groups.email || match.groups.email2;
            })
            .filter(email => email?.length > 0)
    }
}

module.exports = new UnsubscribeToken();
