const {REENTRY_ACTION_TYPE, SCAN_ACTION_TYPE} = require("../../constants/online-checkin");

class OfficialCheckinService {
    eventsList () {
        let query = knex('event AS e')
            .select({
                event_id: 'e.event_id',
                name: 'e.long_name',
                short_name: 'e.name',
                date_start: knex.raw(`TO_CHAR(e.date_start, 'Mon DD, YYYY')`),
                date_end: knex.raw(`TO_CHAR(e.date_end, 'Mon DD, YYYY')`)
            })
            .whereNull('e.deleted')
            .where(knex.raw(`(e.date_start - INTERVAL '1 week')::DATE <= (NOW() AT TIME ZONE e.timezone)::DATE`))
            .where(knex.raw(`(e.date_end + INTERVAL '2 week') > (NOW() AT TIME ZONE e.timezone)`))
            .where(knex.raw('e.has_officials IS TRUE'));

        return Db.query(query).then(result => result?.rows || []);
    }

    async scan (eventID, barcode) {
        if(!eventID) {
            throw { validation: 'Event ID Required' };
        }

        if(!barcode) {
            throw { validation: 'Barcode Required' };
        }

        let query = knex('event_official AS eo')
            .select({
                name: knex.raw(`concat_ws(' ', u.first, u.last)`),
                head_official: knex.raw(`COALESCE(eo.head_official, FALSE)`)
            })
            .join('official AS o', 'o.official_id', 'eo.official_id')
            .join('user AS u', 'u.user_id', 'o.user_id')
            .where(knex.raw('eo.is_official IS TRUE'))
            .where(knex.raw(`eo.work_status = ?`, ['approved']))
            .whereNull('eo.deleted')
            .where('eo.event_id', eventID)
            .where('o.checkin_barcode', barcode);

        const official = await Db.query(query).then(result => result?.rows?.[0]);

        if(_.isEmpty(official)) {
            throw { validation: 'Official Not Found' };
        }

        return official;
    }

    async addScanHistory(eventID, barcode) {
        const scanHistoryData = await this.prepareScanHistoryData(eventID, barcode);

        return Db.query(knex('official_scan_history').insert(scanHistoryData));
    }

    async prepareScanHistoryData (eventId, barcode) {
        const isScanned = await this.isBarcodeScanned(eventId, barcode);
        const actionType = isScanned ? REENTRY_ACTION_TYPE : SCAN_ACTION_TYPE;

        return {
            barcode,
            event_id: eventId,
            action_type: actionType,
        }
    }

    isBarcodeScanned (eventId, barcode) {
        const query = knex('official_scan_history as osh')
            .select('osh.official_scan_history_id')
            .where('osh.barcode', barcode)
            .where('osh.event_id', eventId)
            .where('osh.action_type', SCAN_ACTION_TYPE)

        return Db.query(query).then(({ rowCount }) => rowCount > 0);
    }
}

module.exports = new OfficialCheckinService();
