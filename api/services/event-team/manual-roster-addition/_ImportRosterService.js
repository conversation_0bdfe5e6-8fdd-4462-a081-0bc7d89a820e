const { spawn } = require('child_process');
const path = require('path');

class ImportRosterService {

    get importFile() {
        if(!this._importFile) {
            this._importFile = require('../../../lib/FileStreamUploadService');
        }
        return this._importFile;
    }

    get IMPORTER_PATH() {
        return path.resolve(__dirname, '..', '..', '..', '..', 'sw-utils');
    }

    get IMPORT_FILENAME() {
        return 'manual-members-import.js';
    }

    get S3_FOLDER() {
        return 'manualMembersImport';
    }

    async importTeams(eventID, file, getFileName) {
        let importFile;
        try {
            importFile = await this.importFile.uploadFile(file.stream, getFileName, this.S3_FOLDER);

            let importResult = await this.__runImportProcess(importFile.serverFilePath, eventID);

            return importResult;
        } finally {
            if(importFile) {
                importFile.removeServerFile().catch(err => loggers.errors_log.error(err));
            }
        }
    }

    __runImportProcess(filePath, eventID) {
        return new Promise((resolve, reject) => {
            let result = [];
            let error = [];

            const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

            let procParams = [
                this.IMPORT_FILENAME,
                `--event=${eventID}`,
                `--path=${filePath}`,
                `--conn=${Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64')}`,
            ];

            let proc = spawn('node', procParams, {
                cwd     : this.IMPORTER_PATH,
                stdio   : 'pipe'
            });

            const onProcessEnd = (code) => {
                if(code > 0) {
                    const errorString = Buffer.concat(error).toString();
                    try {
                        reject(JSON.parse(errorString));
                    }
                    catch(err){
                        reject({error: errorString});
                    }
                } else {
                    try {
                        const scriptOutput = Buffer.concat(result).toString();
                        resolve(JSON.parse(scriptOutput));
                    } catch (err) {
                        reject(err);
                    }
                }
            };

            proc.on('error', (err) => reject(err));

            proc.on('close', onProcessEnd);

            proc.stdout.on('data', (data) => result.push(data));
            proc.stderr.on('data', (err) => error.push(err));
        })
    }
}

module.exports = new ImportRosterService();
