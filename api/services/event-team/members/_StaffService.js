const { staffer: stafferSchema } = require('../../../validation-schemas/event-schemas');
let swUtils = require('../../../lib/swUtils');

class StaffService {
    constructor() {
        this.ACTIONS = {
            ACTIVATE: ACTIONS.ACTIVATE,
            DEACTIVATE: ACTIONS.DEACTIVATE,
        }
    }

    /**
     * @param {Object} param
     * @param {number} param.eventID
     * @param {number} param.stafferID
     *
     * @returns {Promise}
     */
    deactivate({ eventID, stafferID, tr = null }) {
        let DbClient = tr || Db;

        const query = knex('event_team_checkin')
            .update({
                deactivated_at: knex.raw(`NOW()`),
            })
            .where({
                'event_id': eventID,
                'master_staff_id': stafferID
            });

        return DbClient.query(query);
    }

     /**
     * @param {Object} param
     * @param {number} param.eventID
     * @param {number} param.stafferID
     *
     * @returns {Promise}
     */
    activate({ eventID, stafferID, tr = null }) {
        let DbClient = tr || Db;

        const query = knex('event_team_checkin')
            .update({
                deactivated_at: null,
            })
            .where({
                'event_id': eventID,
                'master_staff_id': stafferID
            });

        return DbClient.query(query);
    }

    /**
     * @param {Object} param
     *
     * @param {number} param.eventID,
     * @param {number} param.stafferID,
     * @param {number} param.userID,
     * @param {string|null} param.reason,
     * @param {string} param.action,
     *
     * @returns {Promise}
     */
    createHistoryRow({ eventID, stafferID, userID, reason, action, tr = null }) {
        let DbClient = tr || Db;

        const historyAction = getHistoryAction(action);

        const query = knex('event_change')
            .insert({
                event_id: eventID,
                action: historyAction,
                comments: reason,
                user_id: userID,
                master_staff_id: stafferID,
            });

        return DbClient.query(query);
    }

    validateDeactivationStatus({ eventID, stafferID, action }) {
        const query = knex('event_team_checkin')
            .select('deactivated_at')
            .where({
                event_id: eventID,
                master_staff_id: stafferID,
            })
            .limit(1);

        return Db.query(query).then(({ rows: [row] }) => {
            if (!row) {
                throw { validation: 'Staffer Not Found' };
            }

            if (action === ACTIONS.ACTIVATE && !row.deactivated_at) {
                throw { validation: `Barcode is already activated` };
            }

            if (action === ACTIONS.DEACTIVATE && row.deactivated_at) {
                throw { validation: 'Barcode is already deactivated' };
            }
        })
    }

    async removeFromRoster (memberID, rosterTeamID, eventID, userID) {
        let tr;

        try {
            tr = await Db.begin({skipErrAboutCommittedTr: true});

            let staff = await this.__removeFromRoster({memberID, rosterTeamID, eventID, tr});

            if(staff.primary) {
                let needDeactivation = true;
                try {
                    await this.validateDeactivationStatus({eventID, stafferID: memberID, action: this.ACTIONS.DEACTIVATE});
                } catch (err) {
                    needDeactivation = false;
                }

                if(needDeactivation) {
                    await this.deactivate({eventID, stafferID: memberID, tr});

                    const reason = `Deactivate reason: removed from the roster.`;

                    await this.createHistoryRow({
                        eventID, stafferID: memberID, userID, action: this.ACTIONS.DEACTIVATE, reason, tr
                    });
                }
            }

            await tr.commit();

            return staff;
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async update (eventID, rosterTeamID, masterStaffID, eventOwnerID, season, staffer, userId) {
        const validationResult = stafferSchema.validate(staffer);

        if(validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }

        let tr;

        try {
            tr = await Db.begin();

            const oldData = await __getOldStaffData(masterStaffID, rosterTeamID);
            const newData = {
                email: staffer.email,
                phone: staffer.phonem,
                phoneh: staffer.phoneh,
                phoneo: staffer.phoneo,
                phonew: staffer.phonew,
                cert: staffer.cert,
                isImpact: staffer.is_impact,
                safesportStatus: parseInt(staffer.safesport_statusid, 10) === 2 ? 'OK' : 'NO',
                name: oldData.name,
                role: await __getRoleNameById(staffer.role_id),
                primary: staffer.primary ? 'enable' : 'disable'
            }
            // old OK
            // new NO
            const modifiedStaffer = await __updateMasterStaffRow(
                eventID,
                masterStaffID,
                rosterTeamID,
                season,
                staffer,
                tr
            );

            const rosterStaffRoleID = await __updateRosterStaffRoleRow(
                eventID,
                masterStaffID,
                rosterTeamID,
                staffer,
                tr
            );

            let stafferCompletedCheckinProcess = false;
            let checkinNotificationSent = false;

            if(_.isBoolean(staffer.primary)) {
                await __removePrimaryRoleFromTeams(eventID, masterStaffID, rosterStaffRoleID, staffer, tr);


                if(staffer.primary) {
                    stafferCompletedCheckinProcess = await __processStaffTeamCheckin(
                        eventID,
                        masterStaffID,
                        rosterTeamID,
                        modifiedStaffer,
                        season,
                        tr
                    );
                }
            }
            
            if(!_.isEqual(oldData, newData)) {
                await __addChangeHistoryRow(eventID, rosterTeamID, eventOwnerID, userId, oldData, newData, tr);
            }

            await tr.commit();

            const wristbandsCount = await __getStaffWristbandsCount(rosterTeamID);

            if(stafferCompletedCheckinProcess) {
                try {
                    await OnlineCheckinService.team.notifyStaffers({
                        eventId: Number(eventID),
                        stafferIds: [masterStaffID],
                        checkinMode: OnlineCheckinService.team.CHECKIN_MODE.PRIMARY_STAFF_BARCODES
                    });

                    checkinNotificationSent = true;
                } catch (err) {
                    loggers.errors_log.error(err);
                }
            }

            return {
                wristbands: wristbandsCount,
                checked_in_online: stafferCompletedCheckinProcess,
                checkin_email_notification_sent: checkinNotificationSent
            };
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async __removeFromRoster ({memberID, rosterTeamID, eventID, tr}) {
        let DbClient = tr || Db;

        let query =
            `WITH "team" AS ( 
             SELECT rt.roster_team_id "id",  
                    rt.team_name,  
                    rt.organization_code "team_code" 
             FROM "roster_team" rt 
             WHERE rt.roster_team_id = $2 
                 AND rt.event_id = $3 
         ), "staff" AS ( 
             SELECT ms.first, ms.last, ms.organization_code "usav_code" 
             FROM "master_staff" ms  
             WHERE ms.master_staff_id = $1 
         ) 
         UPDATE "roster_staff_role" rsr  
             SET "deleted_by_user" = NOW() 
         WHERE rsr.master_staff_id = $1 
             AND EXISTS ( 
                 SELECT "team"."id" FROM "team" 
                 WHERE "team"."id" = rsr.roster_team_id 
             ) 
             AND rsr.deleted IS NULL 
             AND rsr.deleted_by_user IS NULL 
         RETURNING   (SELECT "usav_code" FROM "staff"), 
                     (SELECT "first" FROM "staff"), 
                     (SELECT "last" FROM "staff"), 
                     (SELECT "primary" FROM "staff"),
                     (SELECT "team_name" FROM "team"), 
                     (SELECT "team_code" FROM "team")`;

        let member = await DbClient.query(query, [memberID, rosterTeamID, eventID]).then(result => result?.rows?.[0]);

        if (_.isEmpty(member)) {
            throw { validation: 'Member not found' };
        }

        return member;
    }
}

const ACTIONS = {
    ACTIVATE: 'activate',
    DEACTIVATE: 'deactivate',
};

/**
 *
 * @param {string} action
 *
 * @returns {string} - formatted string for history
 */
function getHistoryAction(action) {
    const actions = {
        [ACTIONS.ACTIVATE]: 'team.roster.staff.checkin.activated',
        [ACTIONS.DEACTIVATE]: 'team.roster.staff.checkin.deactivated',
    };

    return actions[action];
}

function __getStaffWristbandsCount(teamID) {
    return Db.query(
        `SELECT rt.wristbands_count_staff "count" FROM roster_team rt
                    WHERE rt.roster_team_id = $1`, [teamID]
    ).then(result => result.rows[0] && result.rows[0].count);
}

async function __updateMasterStaffRow (eventID, stafferID, rosterTeamID, season, staffer, tr) {
    const query = squel.update().table('master_staff', 'ms')
        .setFields(_.omit(staffer, 'primary', 'role_id', 'phonem', 'name'))
        .set('phone', staffer.phonem)
        .where('ms.master_staff_id = ?', stafferID)
        .where('ms.season = ?', season)
        .where(
            `EXISTS (
                SELECT master_staff_id
                FROM "roster_staff_role" rsr
                INNER JOIN "roster_team" rt 
                    ON rt.roster_team_id = rsr.roster_team_id
                WHERE rsr.master_staff_id = ms.master_staff_id
                    AND rsr.roster_team_id = ?
                    AND rt.event_id = ?
                    AND rsr.deleted IS NULL
                    AND rsr.deleted_by_user IS NULL
            )`, rosterTeamID, eventID
        )
        .returning('master_staff_id AS staff_id, *');

    const modifiedStaffer = await tr.query(query).then(result => result?.rows?.[0]);

    if(_.isEmpty(modifiedStaffer)) {
        throw { validation: 'Staffer not found' };
    }

    return modifiedStaffer;
}

async function __updateRosterStaffRoleRow (eventID, stafferID, rosterTeamID, staffer, tr) {
    const query = squel.update().table('roster_staff_role')
        .set('role_id', staffer.role_id)
        .where('master_staff_id = ?', stafferID)
        .where('roster_team_id = ?', rosterTeamID)
        .where(
            `EXISTS (
                SELECT roster_team_id from "roster_team"
                WHERE roster_team_id = ? 
                    AND event_id = ?
             )`, rosterTeamID, eventID
        ).returning('roster_staff_role_id');

    if(_.isBoolean(staffer.primary)) {
        query.set('"primary"', staffer.primary);
    }

    const updatedStafferRole = await tr.query(query).then(result => result?.rows?.[0]);

    if(_.isEmpty(updatedStafferRole)) {
        throw { validation: `Staffer is not present on the Event` };
    }

    return updatedStafferRole.roster_staff_role_id;
}

function __removePrimaryRoleFromTeams (eventID, stafferID, rosterStaffRoleID, staffer, tr) {
    const query = squel.update().table('roster_staff_role')
            .where('master_staff_id = ?', stafferID)
            .where('roster_staff_role_id <> ?', rosterStaffRoleID)
            .where(
                `roster_staff_role_id IN (
                    SELECT roster_staff_role_id 
                    FROM roster_staff_role rsr
                    INNER JOIN "roster_team" rt 
                        ON rt.roster_team_id = rsr.roster_team_id
                    WHERE rsr.master_staff_id = ?
                        AND rt.event_id = ?
                        AND rsr.roster_staff_role_id <> ?
                )`, stafferID, eventID, rosterStaffRoleID
            );

    if(staffer.primary) {
        query.set('"primary"', false);
    } else {
        query.set('"primary" = COALESCE("primary", FALSE)');
    }

    return tr.query(query);
}

function __addChangeHistoryRow (eventID, rosterTeamID, eventOwnerID, userId, oldData, newData, tr) {
    const query= knex('event_change').insert({
        event_id: eventID,
        roster_team_id: rosterTeamID,
        event_owner_id: eventOwnerID,
        user_id: userId,
        action: 'team.roster.staff.changed.eo',
        old_data: JSON.stringify(oldData),
        new_data: JSON.stringify(newData)
    })
    return  tr.query(query);
}

async function __processStaffTeamCheckin (eventID, staffID, rosterTeamID, staffData, season, tr) {
    const staffRequiresCheckin = await __isStaffRequiresOnlineCheckin(eventID, staffID, rosterTeamID);

    if(!staffRequiresCheckin) {
        return false;
    }

    await OnlineCheckinService.team.modifyStafferInfo({
        tr,
        staffData,
        masterClubId: staffData.master_club_id,
        eventId: eventID,
        season
    });

    await __removeStaffersTeamCheckinRows(eventID, staffID, tr);
    await __addTeamOnlineCheckinRow(eventID, staffID, rosterTeamID, tr);

    return true;
}

function __removeStaffersTeamCheckinRows (eventID, staffID, tr) {
    const query = knex('event_team_checkin').delete()
        .where({
            event_id: eventID,
            master_staff_id: staffID
        });

    return tr.query(query);
}

async function __isStaffRequiresOnlineCheckin (eventID, staffID, rosterTeamID) {
    const query = `
        select COUNT(etc.*) FILTER ( WHERE "etc"."master_staff_id" = $1 ) > 0  as "staffCheckedIn",
               COUNT(etc.*) FILTER ( WHERE "etc"."master_staff_id" <> $1 ) > 0 as "teamCheckedIn"
        from "event" as "e"
                 left join "event_team_checkin" as "etc"
                           on "e"."event_id" = "etc"."event_id" and
                              "etc"."roster_team_id" = $2
        where "e"."event_id" = $3
          and "e"."online_team_checkin_mode" = 'primary_staff_barcodes';
    `;

    const result = await Db.query(query, [staffID, rosterTeamID, eventID])
        .then(result => result?.rows?.[0]);

    if(!result.teamCheckedIn) {
        return false;
    }

    return !result.staffCheckedIn;
}

async function __addTeamOnlineCheckinRow (eventID, staffID, rosterTeamID, tr) {
    const staffType = 1; //Person Completed Online Team Check In

    const query = knex('event_team_checkin')
        .insert({
            event_id: eventID,
            master_staff_id: staffID,
            roster_team_id: rosterTeamID,
            staff_type: staffType
        });

    const checkinRowCreated = await tr.query(query).then(result => result?.rowCount > 0);

    if(!checkinRowCreated) {
        throw new Error(`Can't create online checkin row for staffer.`);
    }
}

async function __getOldStaffData (masterStaffID, rosterTeamID) {
    const query = `
        SELECT FORMAT('%s %s', ms.first, ms.last) "name", ms.email, ms.phone, ms.phoneh, ms.phoneo, ms.phonew,
        ms.cert, ms.is_impact, COALESCE(rsr.primary, msr.primary) "primary", r.short_name role_name,
        (CASE WHEN ms.safesport_statusid = '2' THEN 'OK' ELSE 'NO' END) safesport_status
        FROM master_staff ms
        LEFT JOIN roster_staff_role rsr ON rsr.master_staff_id = ms.master_staff_id
            AND rsr.roster_team_id = $2
            AND rsr.deleted IS NULL
            AND rsr.deleted_by_user IS NULL
        LEFT JOIN master_staff_role msr ON msr.master_staff_id = ms.master_staff_id
        AND msr.master_team_id = rsr.master_team_id
        LEFT JOIN "role" r ON r.role_id = COALESCE(NULLIF(rsr.role_id, 0), msr.role_id)
        WHERE ms.master_staff_id = $1
            AND ms.deleted IS NULL;
        `;
    const result = await Db.query(query, [masterStaffID, rosterTeamID])
        .then(result => result.rows[0]);
    
    return {
        email: result.email,
        phone: result.phone,
        phoneh: result.phoneh,
        phoneo: result.phoneo,
        phonew: result.phonew,
        cert: result.cert,
        isImpact: result.is_impact,
        safesportStatus: result.safesport_status,
        name: result.name,
        role: result.role_name,
        primary: result.primary ? 'enable' : 'disable'
    }
}

async function __getRoleNameById (roleID) {
    const query = `SELECT short_name "name" FROM "role" WHERE role_id = $1;`;
    const result = await Db.query(query, [roleID])
        .then(result => result.rows && result.rows[0]);
    
    return result?.name;
}

module.exports = new StaffService();
