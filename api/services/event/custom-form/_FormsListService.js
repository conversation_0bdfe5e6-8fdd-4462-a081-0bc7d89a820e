

class FormsListService {

    get LIMIT () {
        return 100;
    }

    async get (eventID, filters = {}) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        const query = knex('custom_form_event AS cfe')
            .select({
                name: 'cfe.name',
                total_rows: knex.raw(`COUNT(*) OVER () :: INT`),
                published: knex.raw(`cfe.published IS NOT NULL`),
                created: knex.raw(`to_char(cfe.created, 'Mon DD, YYYY, HH12:MI AM')`),
                type: 'cfe.type',
                custom_form_event_id: 'cfe.custom_form_event_id',
                editable: knex.raw(`(
                    SELECT COUNT(*) = 0 FROM custom_form_submitted_field_value cfsfv
                    WHERE cfe.custom_form_event_id = cfsfv.custom_form_event_id
                )`)
            })
            .where('cfe.event_id', eventID)
            .limit(this.LIMIT);

        this.addOrdering(query, filters);
        this.filterSearch(query, filters);

        const { rows: forms } = await Db.query(query);

        return forms;
    }

    filterSearch (query, filters) {
        if(filters.search) {
            let search = `%${filters.search}%`;
            query.where('cfe.name', 'ILIKE', search);
        }
    }

    addOrdering (query, filters) {
        if(!filters.order) {
            query.orderBy([{column: 'created'}]);
        } else {
            let orderFieldName;
            let orderDirection;

            if(filters.order === 'name') {
                orderFieldName = 'cfe.name';
            }

            if(filters.order === 'published') {
                orderFieldName = 'cfe.published';
            }

            if(filters.order === 'created') {
                orderFieldName = 'cfe.created';
            }

            if(filters.order === 'type') {
                orderFieldName = 'cfe.type';
            }

            orderDirection = filters.revert === 'true' ? 'desc' : 'asc';

            query.orderBy(orderFieldName, orderDirection);
        }
    }

    addFilters (query, filters) {
        if (_.isEmpty(filters)) {
            return;
        }

        if (filters.page) {
            query.offset((filters.page - 1) * this.LIMIT);
        }
    }
}

module.exports = new FormsListService();
