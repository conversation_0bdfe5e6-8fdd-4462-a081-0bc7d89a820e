'use strict';

const { PAYMENT_SESSION_STATUS } = require('../constants/payments');
const teamsConstants = require('../constants/teams');

/*
Actions to notify:
5. Team entered // notification
*/

module.exports = {
    manage2: async function (data, cb) {
        try {
            if(_.isEmpty(data)) {
                throw { validation: 'Empty data passed', type: 'Argument' };
            }

            const $event_id       = data.event_id,
                $master_team_id = data.master_team_id,
                $roster_team_id = data.roster_team_id,
                $division_id    = data.division_id,
                $club_owner_id  = data.club_owner_id,
                $roster_club_id = data.roster_club_id,
                $master_club_id = data.master_club_id,
                $currentSeason  = parseInt(sails.config.sw_season.current, 10) || null,
                $reg_method     = data.reg_method;

            if(!$event_id) {
                throw { validation: 'No event identifier passed', type: 'Argument' };
            }
            if(!$roster_team_id && !$master_team_id) {
                throw { validation: 'No team identifier passed', type: 'Argument' };
            }
            if(!$roster_club_id && !$master_club_id) {
                throw { validation: 'No club identifier passed', type: 'Argument' };
            }
            if(!$division_id) {
                throw { validation: 'No division identifier passed', type: 'Argument' };
            }

            let [rosterTeam, division, rosterClub] = await Promise.all([
                _getRosterTeam($event_id, $roster_team_id, $master_team_id),
                _getDivision($event_id, $division_id),
                _getRosterClub($event_id, $roster_club_id, $master_club_id),
            ]);
            /* if roster team id exists do teamModif() else teamCreation() */
            if(rosterTeam.roster_team_id) {
                /* Removes or updates roster team's division then updates or removes roster club*/
                await _modifyRosterTeam($division_id, division, rosterTeam, $event_id, rosterClub, $currentSeason);
            } else {
                /* Creates roster club if it not exists then creates roster team */
                if(division.closed) {
                    throw {
                        validation: 'Division is closed',
                        type: 'Argument'
                    };
                }
                if(!$club_owner_id) {
                    throw {
                        validation: 'Club owner identifier required to assing club to event',
                        type: 'Argument'
                    };
                }

                rosterClub = await _upsertRosterClub(rosterClub, $event_id, $club_owner_id, $master_club_id);

                rosterTeam = await _createRosterTeam(
                    division, $club_owner_id, rosterClub, $event_id, $master_club_id, $division_id, $master_team_id,
                    $reg_method, $currentSeason
                );
            }

            if(cb) {
                return cb(null, rosterTeam, division, rosterClub);
            }
            else {
                return { rosterTeam, division, rosterClub };
            }
        }
        catch(err) {
            loggers.errors_log.error(err);
            if(cb) {
                return cb(err);
            }
            else {
                throw err;
            }
        }
    }
};

async function _upsertRosterClub(rosterClub, eventID, clubOwnerID, masterClubID) {
    // (count(rt.*) + 1) -> we need to add +1 because the query executes before team is created,
    // so at the time of club creation counter will return teams count minus one team
    // that is why we need to add +1 beforehand
    const query =
        `with update_club AS ( 
        update roster_club rc  
        set deleted = (  
            select case when ((count(rt.*) + 1) = 0) then now() else null end  
            from roster_team rt  
            where rt.roster_club_id = rc.roster_club_id  
            and rt.deleted is null  
        )  
        where roster_club_id = $1 and event_id = $2  
        returning  
          'update'::text "action", club_name, code, is_local, distance_to_event,   
          roster_club_id, to_char(deleted, 'Mon DD, YYYY, HH12:MI AM') deleted 
        ), 
        insert_club AS ( 
            insert into roster_club(  
                                club_name, code, address, city, state, zip,   
                                country, region, master_club_id, event_id )  
        select  mc.club_name, mc.code, mc.address, mc.city, mc.state, mc.zip,  
                mc.country, mc.region, mc.master_club_id, $2 
        from master_club mc 
        where NOT EXISTS (SELECT * FROM update_club) 
            AND mc.club_owner_id = $3  
            and mc.master_club_id = $4  
            and mc.club_name is not null  
            and mc.code is not null  
            and mc.address is not null  
            and mc.city is not null  
            and mc.zip is not null  
            and mc.country is not null  
            and mc.region is not null      
        returning  
          'insert'::text "action", club_name, code, is_local, distance_to_event,  
          roster_club_id, to_char(deleted, 'Mon DD, YYYY, HH12:MI AM') deleted 
        ) 
        SELECT * FROM update_club UNION ALL SELECT * FROM insert_club`;
    const upsertResult = await Db.query(query, [rosterClub.roster_club_id || -1, eventID, clubOwnerID, masterClubID]);
    let upsertedClub = _.first(upsertResult.rows);

    if(_.isEmpty(upsertedClub)) {
        throw {
            validation: 'Club not found or has invalid data entered. ' +
                'Please check the club data and try again',
            type: 'Entrance'
        };
    }
    loggers.debug_log.debug('Club after upsert:', upsertedClub);

    _sendClubNotification(eventID, rosterClub, upsertedClub);

    return Object.assign(rosterClub, upsertedClub);
}

async function _getRosterTeam(eventID, rosterTeamID, masterTeamID) {
    const params = [eventID];
    let query =
        `SELECT  
            rt.deleted is_team_deleted, 
            rt.roster_club_id, 
            rt.division_id, 
            rt.roster_team_id, 
            rt.status_paid, 
            rt.status_entry, 
            rt.team_name, 
            rt.master_team_id,
            EXISTS (
                SELECT 1 
                FROM payment_session ps
                INNER JOIN payment_session_team pst ON ps.payment_session_id = pst.payment_session_id
                WHERE pst.roster_team_id = rt.roster_team_id 
                  AND ps.status = '${PAYMENT_SESSION_STATUS.PENDING}'
            ) AS "has_pending_payment_session"
        FROM roster_team rt 
        WHERE rt.event_id = $1`;
    if (rosterTeamID) {
        query += ' AND rt.roster_team_id = $2';
        params.push(rosterTeamID);
    } else if (masterTeamID) {
        query += ' AND rt.master_team_id = $2';
        params.push(masterTeamID);
    }
    const result = await Db.query(query, params);
    if(result.rowCount > 1) {
        throw {
            validation  : 'More than one row returned for roster team',
            type        : 'Integrity'
        };
    }
    const row = _.first(result.rows);
    if(row) {
        if(row.status_paid ===  teamsConstants.PAYMENT_STATUSES.PAID) {
            throw {
                validation: `Can't change division when payment is accepted`,
                type: 'Argument',
            };
        }
        if(row.status_entry ===  teamsConstants.PAYMENT_STATUSES.ACCEPTED) {
            throw {
                validation: `Can't change division when team entry status is accepted`,
                type: 'Argument',
            };
        }
        if(row.has_pending_payment_session) {
            throw {
                validation: `Can't change division when payment is pending`,
                type: 'Argument',
            };
        }
        
        return row;
    }
    return {};
}

async function _getDivision(eventID, divisionID) {
    if(divisionID < 0) {
        return {};
    }
    const result = await Db.query(
        `SELECT d.name, d.division_id, d.closed 
        FROM division d 
        WHERE d.division_id = $1 
            AND d.event_id = $2`,
        [divisionID, eventID]
    );
    let division = _.first(result.rows);
    if(_.isEmpty(division)) {
        throw {
            validation: 'Division not found',
            type: 'Entrance',
        };
    }
    return division;
}

async function _getRosterClub(eventID, rosterClubID, masterClubID) {
    const params = [eventID];
    let query =
        `select 
            rc.roster_club_id, 
            rc.deleted, 
            rc.is_local, 
            rc.master_club_id, 
            rc.distance_to_event 
        from roster_club rc 
        where rc.event_id = $1`;

    if(rosterClubID) {
        query += ' and rc.roster_club_id = $2';
        params.push(rosterClubID);
    } else if(masterClubID) {
        query += ' and rc.master_club_id = $2';
        params.push(masterClubID);
    }
    const result = await Db.query(query, params);

    return _.first(result.rows) || {};
}

async function _modifyRosterTeam(divisionID, division, rosterTeam, eventID, rosterClub, currentSeason) {
    let query = 'UPDATE roster_team SET {0} WHERE roster_team_id = $1 RETURNING modified';
    if(divisionID < 0) {// removing team
        query = query.format('deleted = NOW()');
    } else if(division.closed) {
        throw {
            validation: `Division "${division.name}" is closed`,
            type: 'Argument'
        };
    } else {
        query = query.format(`division_id = ${divisionID}, deleted = null`);
    }
    const result = await Db.query(query, [rosterTeam.roster_team_id]);
    if(result.rowCount < 1) {
        throw {
            validation: '{0} failed. Please, try again later'
                .format(
                    ( divisionID > 0 ) ? 'Division change' : 'Removing'),
            type: 'Entrance'
        };
    }
    // send notifications
    if(divisionID > 0) {
        if(rosterTeam.is_team_deleted) {
            ClubMessageSender.teamEntered(eventID, {
                roster_club_id: rosterClub.roster_club_id,
                roster_team_id: rosterTeam.roster_team_id,
                division_id: divisionID
            });
        } else {
            ClubMessageSender.divisionChanged(eventID, {
                roster_team_id: rosterTeam.roster_team_id,
                roster_club_id: rosterClub.roster_club_id,
                division_id: division.division_id
            });
        }
    } else {
        ClubMessageSender.teamRemoved(eventID, {
            roster_team_id: rosterTeam.roster_team_id,
            roster_club_id: rosterClub.roster_club_id,
            division_id: division.division_id
        });
    }

    const club = await _updateRosterClub(rosterClub, eventID);
    _.first(result.rows);
    if(_.isEmpty(club)) {
        throw {
            validation: 'Club not found',
            type: 'Entrance'
        };
    }

    _sendClubNotification(eventID, rosterClub, club);
    rosterClub.deleted = club && club.deleted;

    // remove roster members (set deleted = now())
    if(divisionID < 0) {
        try {
            const [aCount, sCount] = await RosterSnapshotService.removeRoster(rosterTeam.roster_team_id);
            loggers.debug_log.debug('Removed roster members. Athletes', aCount, 'Staff', sCount)
        }
        catch(err) {
            loggers.errors_log.error('Remove roster members error', err);
        }
    } else if(rosterTeam.is_team_deleted) {
        try {
            await RosterSnapshotService.makeRosterSnapshot(
                rosterTeam.master_team_id, rosterTeam.roster_team_id,
                eventID, currentSeason
            );
        }
        catch(err) {
            loggers.errors_log.error('Create Roster Members Error:', err);
        }
    }
}

async function _updateRosterClub(rosterClub, eventID) {
    const result = await Db.query(
        `update roster_club rc 
        set deleted = ( 
            select case when (count(rt.*) = 0) then now() else null end 
            from roster_team rt 
            where rt.roster_club_id = rc.roster_club_id 
            and rt.deleted is null 
        ) 
        where roster_club_id = $1 
            and event_id = $2 
        returning roster_club_id, to_char(deleted, 'Mon DD, YYYY, HH12:MI AM') deleted`,
        [rosterClub.roster_club_id, eventID]
    );
    return _.first(result.rows);
}

async function _createRosterTeam(division, clubOwnerID, rosterClub, eventID, masterClubID, divisionID, masterTeamID, regMethod, currentSeason) {
    // createTeam
    const result = await Db.query(
        `insert into roster_team ( 
                team_name, organization_code, gender, age, rank, sport_id,  
                master_team_id, roster_club_id, division_id, club_owner_id,  
                date_entered, status_paid, status_entry, status_housing, event_id, 
                reg_method, seasonality) 
        select  mt.team_name, mt.organization_code, mt.gender, mt.age, mt.rank, 
                                                                        mt.sport_id, 
                mt.master_team_id, $1, $2, $3,  
                now(), 21, 13, $4, $5, $7 :: team_reg_method, mt.seasonality  
        from master_team mt 
        where mt.master_team_id = $6 
        and mt.club_owner_id = $3 
        returning team_name, roster_team_id, roster_club_id, division_id, 
        status_paid, status_entry`,
        [rosterClub.roster_club_id, divisionID, clubOwnerID,
            rosterClub.is_local ? 32 : 31, eventID, masterTeamID, regMethod]
    );
    const team = _.first(result.rows);

    if(_.isEmpty(team)) {
        throw {
            validation: 'Team not found or team data is invalid. ' +
                'Please, check team data and try again.',
            type: 'Entrance'
        };
    }
    ClubMessageSender.teamEntered(eventID, {
        roster_club_id: rosterClub.roster_club_id,
        roster_team_id: team.roster_team_id,
        division_id: team.division_id
    });
    try {
        await RosterSnapshotService.makeRosterSnapshot(
            masterTeamID, team.roster_team_id,
            eventID, currentSeason
        );
    }
    catch(err) {
        loggers.errors_log.error('Create Roster Members Error:', err);
    }

    return team;
}

function _sendClubNotification(eventID, oldRosterClub, newRosterClub) {
    if(oldRosterClub.deleted !== newRosterClub.deleted) {
        if(newRosterClub.deleted) {
            ClubMessageSender.clubRemoved(eventID, {
                roster_club_id: newRosterClub.roster_club_id
            });
        } else {
            ClubMessageSender.clubEntered(eventID, {
                roster_club_id: newRosterClub.roster_club_id
            });
        }
    }
}
