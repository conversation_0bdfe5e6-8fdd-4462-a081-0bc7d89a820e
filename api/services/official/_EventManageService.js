'use strict';

class EventManageService {
	constructor() {}

	// covered 😄👍
	_toggleHeadOfficialRole (eventID, officialID, isHeadOfficial) {
		if (!Number.isInteger(eventID)) {
			return Promise.reject({ validation: 'Event Identifier should be an integer' })
		}

		if (!Number.isInteger(officialID)) {
			return Promise.reject({ validation: 'Official Identifier should be an integer' })
		}

		if (toString.call(isHeadOfficial) !== '[object Boolean]') {
			return Promise.reject({ validation: 'Head Official Role flag should be a boolean' })
		}

		let query = squel.update().table('event_official')
                    .set('head_official', isHeadOfficial)
                    .where('official_id = ?', officialID)
					.where('event_id = ?', eventID)
					.returning('*');
					
		if (!isHeadOfficial) {
			query.set('is_email_notifications_receiver', false);
			query.set('is_email_contact_provider', false);
		}

		if (isHeadOfficial) {
			query.where('work_status = ?', 'approved');

			// set is_email_notifications_receiver to TRUE by default
			query.set('is_email_notifications_receiver', true);

			// set is_email_contact_provider to TRUE if this is first HR, FALSE overwise
			query.set('is_email_contact_provider', 
							squel.select().from('event_official')
							.field('CASE WHEN COUNT(*) > 0 THEN FALSE ELSE TRUE END', 'first_hr')
							.where('head_official = ?', true)
							.where('event_id = ?', eventID)
						);
			
		}

        return Db.query(query)
        .then(result => {
        	if (result.rowCount === 0) {
        		return Promise.reject({ validation: 'Official not found for the event' });
        	} else {
				return result.rows[0];
			}
        })
	}

	// covered 😄👍
	makeHead (eventID, officialID) {
		return this._toggleHeadOfficialRole(eventID, officialID, true);
	}
	// covered 😄👍
	removeHead (eventID, officialID) {
		return this._toggleHeadOfficialRole(eventID, officialID, false);
	}
	setOfficialIsClinic (eventID, officialID, useClinic) {
		const query = knex("event_official AS eo")
            .update({
                use_clinic: useClinic,
            })
            .where("eo.official_id", officialID)
            .whereExists(
                knex("event AS e")
                    .select(1)
                    .where("e.event_id", eventID)
                    .where("e.use_clinic", true)
            );

		return Db.query(query).then(result => {
			if (result.rowCount === 0) {
				throw {
                    validation:
                        "Official not found for the event or Event does not use clinic",
                };
			}
		});
	}
	setEmailNotificationsReceiver(eventID, officialID, isEmailNotificationsReceiver) {
		let query = squel.update().table('event_official')
			.set('is_email_notifications_receiver', isEmailNotificationsReceiver)
			.where('official_id = ?', officialID)
			.where('event_id = ?', eventID);

		return Db.query(query)
			.then(result => {
				if (result.rowCount === 0) {
					return Promise.reject({ validation: 'Official not found for the event' });
				}
			});
	}

	setEmailContactProvider(eventID, officialID, isEmailContactProvider) {
		let query;
		// if isEmailContactProvider == true, 
		// first, we need to set HR with is_email_contact_provider = TRUE to FALSE
		if (isEmailContactProvider) {
			query = `
					WITH w AS 
					(
							SELECT event_official_id , FALSE as new_value
							FROM event_official
							WHERE event_id = $1 AND is_email_contact_provider = TRUE
							UNION ALL
							SELECT event_official_id , TRUE as new_value
							FROM event_official
							WHERE event_id = $1 AND official_id = $2
					)
					UPDATE event_official eof
					SET is_email_contact_provider = w.new_value
					FROM w 
					WHERE eof.event_official_id = w.event_official_id
					`;
		} else {
			query = 
					`
					UPDATE event_official eof
					SET is_email_contact_provider = FALSE
					WHERE event_id = $1 AND official_id = $2
					`;	
		}
		
		let params = [eventID, officialID];

		return Db.query(query, params)
			.then(result => {
				if (result.rowCount === 0) {
					return Promise.reject({ validation: 'Official not found for the event' });
				}
			});
	}

	async confirmOfficialParticipation({ eventID, officialID }) {
		await isOfficiaParticipationAlreadyConfirmed({ eventID, officialID });

		const query = knex('event_official')
			.update({
				is_official_participation_confirmed: knex.fn.now(),
			})
			.where({
				event_id: eventID,
				official_id: officialID
			});

		return Db.query(query);
	}
}

async function isOfficiaParticipationAlreadyConfirmed({ eventID, officialID }) {
	const query = knex('event_official')
		.select('is_official_participation_confirmed')
		.where({
			event_id: eventID,
			official_id: officialID
		});

	return Db.query(query).then(({ rows: [row] }) => {
		if (!row) {
			throw { validation: 'Official Not Found' };
		};

		if (row.is_official_participation_confirmed) {
			throw { validation: 'Official Participation Already Confirmed' };
		}
	});
}

module.exports = new EventManageService();