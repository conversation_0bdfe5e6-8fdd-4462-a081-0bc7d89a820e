const { PAYMENT_PROVIDER } = require('../../../constants/payments');
const { STATUS: PAYMENT_INTENT_STATUS } = require('../../../constants/stripe/payment-intent');
const TeamsPaymentSessionService = require('./_TeamsPaymentSessionService');


class RemovePaymentService {

    async removeNotFinishedPayment(paymentIntentID, userID, paymentProvider) {
        const paymentIntentIsCancellable = await this.#isPaymentIntentCancelable(paymentIntentID);

        if(paymentIntentIsCancellable) {
            await Promise.all([
                this.#cancelPaymentProviderPaymentIntent(paymentIntentID, paymentProvider),
                this.#removeStripePaymentSession(paymentIntentID, userID)
            ]);
        }
    }

    async #cancelPaymentProviderPaymentIntent(paymentIntentID, paymentProvider) {
        if(paymentProvider === PAYMENT_PROVIDER.STRIPE) {
            return await this.#cancelStripePaymentIntent(paymentIntentID);
        } else {
            loggers.errors_log.error('Only Stripe Payment Provider Payment Intent can be cancelled for now.');
        }
    }

    async #cancelStripePaymentIntent(paymentIntentID) {
        try {
            await StripeService.paymentCard.stripeService.cancelPaymentIntent(paymentIntentID);
        } catch (err) {
            loggers.errors_log.error(
                `Stripe Payment Intent not canceled: ${paymentIntentID}. Error: ${JSON.stringify(err, null, 2)}`
            );
        }
    }

    async #isPaymentIntentCancelable(paymentIntentID) {
        try {
            const paymentIntent = await StripeService.paymentCard.stripeService.getPaymentIntent(paymentIntentID);

            if (_.isEmpty(paymentIntent)) {
                return false;
            }

            const notCancelableStatuses = [PAYMENT_INTENT_STATUS.PROCESSING, PAYMENT_INTENT_STATUS.SUCCEEDED];

            return !notCancelableStatuses.includes(paymentIntent.status);
        } catch (error) {
            return false;
        }
    }

    async #removeStripePaymentSession(paymentIntentID, userID) {
        try {
            await TeamsPaymentSessionService.stripe.removePaymentSession({
                provider_payment_intent_id: paymentIntentID,
                user_id: userID,
            })
        } catch (error) {
            loggers.errors_log.error(`Payment Session not removed from storage. Payment Intent ID ${paymentIntentID}`);
        }
    }
}

module.exports = new RemovePaymentService();
