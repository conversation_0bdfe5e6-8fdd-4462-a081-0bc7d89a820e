const { PAYMENT_PROVIDER } = require("../../../../constants/payments");
const _AbstractTeamsPaymentService = require("./_AbstractTeamsPaymentService");

class PaymentHubTeamsPaymentDataService extends _AbstractTeamsPaymentService {
    async savePaymentData(tr, { webhookData }) {
        return this.__createPaymentHubPayment(tr, webhookData)
    }

    async updatePaymentMetadata(webhookData, metadata) {
        return Promise.resolve()
    }

    __mapProviderFields({fees}) {
        return {
            provider_fee: fees.providerFee,
            payment_provider: PAYMENT_PROVIDER.PAYMENT_HUB
        }
    }

    __getProviderFeePayer(settings) {
        return settings.payment_hub_teams_fee_payer;
    }

    __mapWebhookDataToPaymentRow(webhookData) {
        return {
            payment_hub_payment_intent_id: this.__getPaymentHubPaymentIntentId(webhookData),
        }
    }

    __mapWebhookDataToPurchaseHistory(webhookData) {
        return {
            payment_hub_payment_intent_id: this.__getPaymentHubPaymentIntentId(webhookData)
        }
    }

    __getPaymentStatus(webhookData) {
        switch (webhookData.data.status) {
            case 'settled':
                return 'paid';
            default:
                throw new Error('Payment status not success!');
        }
    }

    async __createPaymentHubPayment(tr, webhookData) {
        const dataForInsert = {
            payment_id: webhookData.data.paymentId,
            payment_intent_id: webhookData.data.paymentIntentId,
            status: webhookData.data.status,
            amount: webhookData.data.amount,
            currency: webhookData.data.currency,
        };

        const query = knex('payment_hub.payment').insert(dataForInsert);

        return tr.query(query);
    }

    async updatePaymentData(tr, paymentHubPaymentId, dataForUpdate) {
        let query = knex('payment_hub.payment AS p')
            .update(dataForUpdate)
            .where('p.payment_id', paymentHubPaymentId);

        let updated = await tr.query(query).then(result => result?.rowCount === 1);

        if (!updated) {
            throw new Error('Payment Hub payment row does not updated: ' + paymentHubPaymentId);
        }
    }

    async cancelPendingPurchase(tr, webhookData) {
        const data = {
            status: 'canceled',
            canceled_date: knex.fn.now(),
        };

        let query = knex('purchase')
            .update(data)
            .where('payment_hub_payment_intent_id', this.__getPaymentHubPaymentIntentId(webhookData))
            .where('status', 'pending')
            .returning(['event_id', 'purchase_id']);

        let purchase = await tr.query(query).then(result => result?.rows?.[0]);

        if (_.isEmpty(purchase)) {
            throw new Error('Failed purchase not updated');
        }

        return purchase;
    }

    async getPendingPurchaseRow(paymentHubPaymentIntentId) {
        let query = knex('purchase AS p')
            .select('p.purchase_id', 'p.event_id', 'spi.payment_intent_status', 'e.long_name AS event_name')
            .leftJoin('event AS e', 'e.event_id', 'p.event_id')
            .leftJoin('stripe_payment_intent AS spi', 'spi.payment_intent_id', 'p.payment_intent_id')
            .where('payment_hub_payment_intent_id', paymentHubPaymentIntentId)
            .where('p.status', 'pending');

        return Db.query(query).then(result => result?.rows?.[0]);
    }

    __getPaymentHubPaymentIntentId(webhookData) {
        return webhookData.data.paymentIntentId;
    }
}

module.exports = new PaymentHubTeamsPaymentDataService();
