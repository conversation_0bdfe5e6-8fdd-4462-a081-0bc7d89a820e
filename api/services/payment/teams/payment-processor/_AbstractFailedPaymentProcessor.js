const TeamsPaymentNotificationsService = require("../notification/_TeamsPaymentNotificationsService");
const BasePaymentProcessor = require("./_BasePaymentProcessor");

class AbstractFailedPaymentProcessor extends BasePaymentProcessor {
    async __updatePaymentData(tr, webhookData) {
        throw new Error('__updatePaymentData is not implemented')
    }

    async __sendNotifications(purchase, webhookData) {
        throw new Error('__sendNotifications not implemented')
    }

    async processPayment(webhookData) {
        let tr;

        try {
            tr = await Db.begin();

            const purchase = await this.__updatePayment(tr, webhookData);

            let { purchase_id: purchaseID } = purchase;

            await Promise.all([
                this.teamsPaymentDataService.setRosterTeamPaymentStatusCancelled(tr, purchaseID),
                this.__insertHistoryRow(tr, purchaseID, webhookData),
                this.__updatePaymentData(tr, webhookData),
            ]);

            await tr.commit();

            await this.__sendNotifications(purchase, webhookData)
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async __updatePayment(tr, webhookData) {
        let purchase = await this.teamsPaymentDataService.cancelPendingPurchase(tr, webhookData);

        await this.__updatePurchaseTeam(tr, purchase.purchase_id);

        return purchase;
    }

    async __updatePurchaseTeam(tr, purchaseID) {
        return this.teamsPaymentDataService.updatePurchaseTeamRow(tr, purchaseID, { canceled: knex.fn.now() });
    }

    async __insertHistoryRow(tr, purchaseId, webhookData) {
        let action = 'purchase.failed';

        return this.teamsPaymentDataService.insertHistoryRow(tr, { purchaseId, webhookData, action });
    }
}

module.exports = AbstractFailedPaymentProcessor
