const TeamsPaymentSessionService = require("../../_TeamsPaymentSessionService");
const PaymentHubTeamsPaymentDataService = require("../../data/_PaymentHubTeamsPaymentDataService");
const AbstractSuccessPaymentProcessor = require("../_AbstractSuccessPaymentProcessor");

class PaymentHubSuccessPaymentProcessor extends AbstractSuccessPaymentProcessor {
    async __savePaymentCard() {
        return null;
    }

    async __removePaymentSession({ tr, provider_payment_intent_id, user_id }) {
        await TeamsPaymentSessionService.paymentHub.removePaymentSession({
            tr,
            provider_payment_intent_id,
            user_id,
        });
    }
}

module.exports = new PaymentHubSuccessPaymentProcessor(
    PaymentHubTeamsPaymentDataService
);
