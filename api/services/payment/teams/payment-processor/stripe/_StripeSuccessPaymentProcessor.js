const StripeTeamsPaymentDataService = require("../../data/_StripeTeamsPaymentDataService");
const AbstractSuccessPaymentProcessor = require("../_AbstractSuccessPaymentProcessor");
const checkRules = require("../../rules/_CheckRules");
const PaymentIntentHasPendingStatusRule = require("../../rules/stripe/_PaymentIntentHasProcessingStatusRule");
const PaymentIntentAndPaymentTypesAreEqualRule = require("../../rules/stripe/_PaymentIntentAndPaymentTypesAreEqualRule");
const StripeConnect = require('../../../../../lib/StripeConnect');
const TeamsPaymentSessionService = require("../../_TeamsPaymentSessionService");

class StripeSuccessPaymentProcessor extends AbstractSuccessPaymentProcessor {
    async __savePaymentCard(tr, webhookData) {
        const paymentIntent = webhookData.data.object;

        return StripeService.paymentCard
            .saveUserPaymentMethodIgnoreDuplicate(
                tr,
                paymentIntent.customer,
                paymentIntent.payment_method
            )
            .catch((err) => loggers.errors_log.error(err));
    }

    async __removePaymentSession({ tr, provider_payment_intent_id, user_id }) {
        await TeamsPaymentSessionService.stripe.removePaymentSession({
            tr,
            provider_payment_intent_id,
            user_id,
        });
    }

    async validatePayment(webhookData, payment) {
        const paymentIntentPaymentType = StripeTeamsPaymentDataService.getPaymentIntentPaymentMethodType(webhookData);
        const paymentIntent = StripeTeamsPaymentDataService.__getPaymentIntent(webhookData);

        try {
            checkRules(
                new PaymentIntentHasPendingStatusRule(paymentIntent.status),
                new PaymentIntentAndPaymentTypesAreEqualRule(
                    paymentIntentPaymentType,
                    payment?.type
                )
            );
        } catch (err) {
            await this.__tryRefund(webhookData);
            await this.__tryCancel(webhookData);

            loggers.errors_log.error('Failed payment validation');
            loggers.errors_log.error(err);
            loggers.errors_log.error('Payment Intent: ' + paymentIntent.id);
            throw err;
        }

    }

    async __tryRefund(webhookData) {
        const charge = StripeTeamsPaymentDataService.getCharge(webhookData);

        try {
            await StripeConnect.refundClientPayment({ charge_id: charge.id });

            loggers.errors_log.error(`Charge with ID: ${charge.id} auto refunded.`);
        } catch (err) {
            loggers.errors_log.error(`Charge with ID: ${charge.id} not auto refunded.`);
            loggers.errors_log.error(err);
        }
    }

    async __tryCancel(webhookData) {
        let tr;
        try {

            tr = await Db.begin();

            let purchase = await StripeTeamsPaymentDataService.cancelPendingPurchase(tr, webhookData);
            loggers.errors_log.error(`Payment cancelled.`);

            await this.teamsPaymentDataService.updatePurchaseTeamRow(
                tr,
                purchase.purchase_id,
                {
                    canceled: knex.fn.now()
                }
            );
            loggers.errors_log.error(`Purchase team rows cancelled.`);

            await StripeTeamsPaymentDataService.setRosterTeamPaymentStatusCancelled(tr, purchase.purchase_id);
            loggers.errors_log.error(`Roster team statuses updated.`);

            await tr.commit();

        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback().catch(loggers.errors_log.error.bind(loggers.errors_log));
            }
            loggers.errors_log.error(`Try cancel failed.`);
            loggers.errors_log.error(err);
        }
    }
}

module.exports = new StripeSuccessPaymentProcessor(StripeTeamsPaymentDataService);
