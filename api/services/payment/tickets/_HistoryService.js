const moment = require('moment-timezone');
class HistoryService {
    constructor() {}

    async getHistory({ eventID, ticketBarcode }) {
        const [paymentHistory, emailSendHistory]  = await Promise.all([
            getPaymnetHistory({ eventID, ticketBarcode }),
            getEmailSendHistory({ eventID, ticketBarcode }),
        ]);

        return formatHistoryRows([
            ...paymentHistory,
            ...emailSendHistory
        ]);
    }

    formatTicketChangeHistoryRow(target, from, to, date, timezone, user, source) {
        if(from === to) {
            return null;
        }
        const dateOfChange = formatDateForTicketHistory(date, timezone);
        const fullName = `${user.first} ${user.last}`;
        return `${target} Change by “${fullName}” from “${from}” to “${to}” on “${dateOfChange}” (${source})`;
    }
}

function formatHistoryRows(rows) {
    return rows.sort((a,b) => {
        return new Date(b.created) - new Date(a.created);
    }).map(row => {
        row.created = moment(row.created).format('LLL');

        return row;
    })
}

function addPurchaseClasure(query, { eventID, ticketBarcode }) {
    return query
        .where('p.event_id = ?', eventID)
        .where(`p.payment_for = 'tickets'`)
        .where('p.ticket_barcode = ?', ticketBarcode);
}

function getPaymnetHistory({ eventID, ticketBarcode }) {
    let query = squel.select()
        .field(`ph.created::TIMESTAMPTZ AT TIME ZONE e.timezone`, 'created')
        .field(`
        (
            (
               CASE
                   WHEN ph.purchase_ticket_id is not null AND ph.quantity > 0
                       THEN 'Refunded ' || ph.quantity || ' ' || et.label
                   WHEN ph.purchase_ticket_id is not null AND ph.quantity = 0
                       THEN 'Refunded ' || et.label
                   ELSE ph.description
               END
            )
        )
        `, 'description')
        .field('ph.notes', 'title')
        .field('ph.amount')
        .field('ph.action')
        .field('ph.check_num')
        .from('purchase', 'p')
        .join('purchase_history', 'ph', 'ph.purchase_id = p.purchase_id')
        .join('event', 'e', 'e.event_id = p.event_id')
        .left_join('purchase_ticket', 'pt', 'pt.purchase_ticket_id = ph.purchase_ticket_id')
        .left_join('event_ticket', 'et', 'pt.event_ticket_id = et.event_ticket_id')

    query = addPurchaseClasure(query, { eventID, ticketBarcode });

    return Db.query(query).then(({ rows }) => rows || []);
}

function getEmailSendHistory({ eventID, ticketBarcode }) {
    let query = squel.select()
        .field(`'Email Sent'`, 'title')
        .field('ee.created::TIMESTAMPTZ AT TIME ZONE e.timezone', 'created')
        .field('ee.email_subject')
        .field(`CASE 
            WHEN ee.email_to LIKE '%<%>%' 
            THEN SPLIT_PART(REGEXP_REPLACE(ee.email_to,'[<>]', '', 'g'), ' ', '3') 
            ELSE ee.email_to 
          END`, 'email_to')
        .field('ee.event_email_id')
        .field('ech.event_change_id')
        .from('purchase', 'p')
        .join('event', 'e', 'e.event_id = p.event_id')
        .join('event_email', 'ee', 'ee.purchase_id = p.purchase_id')
        .join('event_change', 'ech', 'ech.event_email_id = ee.event_email_id')
        
    query = addPurchaseClasure(query, { eventID, ticketBarcode });

    return Db.query(query).then(({ rows }) => rows || []);
}

function formatDateForTicketHistory(datetime, timezone) {
    return moment(datetime).utc().tz(timezone).format('YYYY/MM/DD h:mm:ss a');
}

module.exports = new HistoryService();
