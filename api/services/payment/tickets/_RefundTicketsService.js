'use strict';

const argv 			    = require('optimist').argv;
const StripeConnect     = require('../../../lib/StripeConnect');
const swUtils 		    = require('../../../lib/swUtils');
const QRTicketsUtils    = require('../../../lib/QRTicketsGenerator');
const moment            = require("moment-timezone");
const { FEE_PAYER } = require('../../../constants/payments');
const TilledService = require('../../TilledService');
const SalesHubRefundAPIService = require('../../sales-hub/SalesHubAPIService').refund;
const SalesHubRefundStorageService = require('../../sales-hub/SalesHubStorageService').refund;

class RefundTicketsService {
    constructor (StripeConnect) {
        this.StripeConnect = StripeConnect;
    }

    get PROVIDER_FEE_FIELDS () {
        return {
            [PaymentService.__PAYMENT_PROVIDERS__.STRIPE]: 'stripe_fee',
            [PaymentService.__PAYMENT_PROVIDERS__.PAYMENT_HUB]: 'stripe_fee',
            [PaymentService.__PAYMENT_PROVIDERS__.TILLED]: 'tilled_fee',
        }
    }
    get FEE_PAYER () {
        return FEE_PAYER;
    }

    get SALES_TYPE () {
        return {
            CAMPS: 'camps'
        }
    }

    get SETTINGS_KEY () {
        return argv.prod
            ?'stripe_connect'
            :'stripe_connect_dev'
    }

    async partialRefund (eventID, code, total, receipt, notes, providerFee, swFee, userName) {
        if(!code) {
            throw { validation: 'No ticket barcode provided' };
        }

        if(!total) {
            throw { validation: `Refund amount can't be 0` };
        }

        if(_.isEmpty(receipt)) {
            throw { validation: 'No tickets passed' };
        }

        let tr = null;

        try {
            tr = await (Db.begin());

            let ptIDList = this.__formatPurchaseTickets__(receipt);

            let productData = await (this.__getProduct__(tr, eventID, code, ptIDList));

            if(!productData) {
                throw { validation: 'Product not found' };
            }

            // if productData.payment_id is NOT NULL - "ticket with names" mode enabled
            let paymentID = (productData.payment_id || productData.product.purchase_id);
            let productID = productData.product.purchase_id;

            let paymentData = await (this.__getPaymentData__(tr, eventID, paymentID));

            if(!paymentData) {
                throw { validation: 'Payment not found' };
            }

            let allFeesPaysSeller = this.__sellerPaysAllFees__(paymentData);

            let isNamedTicketsMode  = paymentData.is_named_tickets;

            if(allFeesPaysSeller && paymentData.is_named_tickets) {
                if(productData.product.amount < total) {
                    throw { validation: `Too big Total Amount ${total} > ${productData.product.amount}` };
                }
            } else {
                if(paymentData.payment.amount <= total) {
                    throw { validation: `Too big Total Amount ${total} >= ${productData.product.amount}` };
                }
            }

            if(isNamedTicketsMode) {
                //receipt for assigned tickets should contain all tickets in payment includes refunding ticket
                let receiptData = await (this.__getReceiptData__(eventID, code, true));
                receipt         = receipt.concat(this.__formatReceiptData__(receiptData, productID));


                if(allFeesPaysSeller) {
                    // Total for assigned tickets partial refund: <all payment amount> - <amount refunded>
                    total           = paymentData.payment.amount - (productData.product.amount - total);
                }

                //ticket types with correct bought tickets data for assigned tickets getting from __getReceiptData__
                productData.ticket_types = await (this.__getReceiptData__(eventID, code));
            }

            let { recounted, refundAmount, refundFeeAmount } =
                this.__countTicketsFees__(productData, paymentData, total, receipt, providerFee, swFee, {
                    isNamedTicketsPartialRefund: true
                });

            let refundedTicket = null;

            if (isNamedTicketsMode) {
                refundedTicket = this.__formatRefundedTicket__(productData.ticket_types, productID, { refundAmount, refundFeeAmount, ticketsAfterRefund: receipt });
            }

            let actions = [];

            let purchaseRefundAmount = this.__getCorrectPartialRefundAmount(
                refundAmount, allFeesPaysSeller, paymentData, recounted
            );
    
            const historyLine = this.__generateHistoryLine__({
                isPartial: true,
                userName,
                timezone: productData.timezone,
                amount: refundAmount
            });
            
            actions.push(this.__productPartialRefundUpdate__(tr, productID, purchaseRefundAmount, historyLine));
            actions.push(
                this.__paymentPartialRefundUpdate__(tr, paymentID, paymentData.payment_provider, refundAmount, recounted, isNamedTicketsMode)
            );

            let sqlResult = await (Promise.all(actions));

            if(sqlResult[0] === 0 || sqlResult[1] === 0) {
                throw { validation: 'Payment not found' };
            }

            let isCamp = paymentData.sales_type === this.SALES_TYPE.CAMPS;

            await (Promise.all(
                receipt.map(item => this.__reducePurchaseTicketCount__(tr, item, productID, isCamp))
            ));

            let product = isNamedTicketsMode ? productID : null;

            receipt.refundedTickets = this.getRefundedTickets(productData.ticket_types, receipt);

            await (this.__updateHistoryFields__(tr, paymentData, notes, product, refundAmount, receipt.refundedTickets));

            await (this.__makeRefund__(tr, paymentData, null, refundAmount, refundFeeAmount, refundedTicket));

            await this.__updateMetadata__(paymentData, {
                netProfit: recounted.taxedTotals.netProfit,
                providerFee: recounted.taxedTotals.providerFee,
                swFee: recounted.recountedPrices.applicationFee,
                extraFee: 0
            });

            await (tr.commit());
        } catch (err) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }
            throw err;
        }
    }

    async refund (eventID, code, notes, userName) {
        if(!code) {
            throw { validation: 'No ticket barcode provided' };
        }

        let tr = null;

        try {
            tr = await Db.begin();

            let recounted       = null,
                refundAmount    = null,
                refundFeeAmount = null,
                refundedTicket  = null;

            let productData = await (this.__getProduct__(tr, eventID, code));
            
            if(!productData) {
                throw { validation: 'Product not found' };
            }

            let paymentID   = (productData.payment_id || productData.product.purchase_id);
            let productID   = productData.product.purchase_id;

            let paymentData = await (this.__getPaymentData__(tr, eventID, paymentID));

            if(!paymentData) {
                throw { validation: 'Payment not found' };
            }

            let allFeesPaysSeller = this.__sellerPaysAllFees__(paymentData);

            // Assigned Ticket mode
            let isNamedTicketsMode  = paymentData.is_named_tickets;
            
            let isLastTicketRefund  = this.__isLastTicketRefund__(
                isNamedTicketsMode, allFeesPaysSeller ,productData, paymentData
            );
            let additionalFeeAmount = Number(paymentData.payment.additional_fee_amount);

            let balanceQuery = (additionalFeeAmount > 0)
                ? this.__updateEventBalance__(tr, eventID, additionalFeeAmount)
                : null;

            let actions = [balanceQuery];

            if(isNamedTicketsMode) {
                //receipt for assigned tickets should contain all tickets in payment exclude refunding ticket
                let ticketTypes = await (this.__getReceiptData__(eventID, code, true));
                let receipt     = this.__formatReceiptData__(ticketTypes, productID);

                refundedTicket = this.__formatRefundedTicket__(ticketTypes, productID);

                // NOTE: for Assigned Tickets - if EO refunds not last ticket, its need to calculate ticket's
                // fees and make "partial" refund with fees and refund amount. But if ticket is last, it
                // will be full stripe charge refund.
                if(!isLastTicketRefund) {
                    let paymentAmount = paymentData.payment.amount;

                    //use payment net_profit as paymentAmount if stripe_fee_payer OR sw_fee_payer is 'buyer'
                    if(!allFeesPaysSeller && !this.__isPaymentHubProvider__(paymentData)) {
                        paymentAmount = paymentData.payment.net_profit;
                    }

                    // Total for assigned tickets partial refund: <all payment amount> - <product amount>
                    let total = swUtils.normalizeNumber(
                            paymentAmount - productData.product.amount
                        );


                    //ticket types with correct bought tickets data for assigned tickets getting from __getReceiptData__
                    productData.ticket_types = await (this.__getReceiptData__(eventID, code));

                    let fees = this.__countTicketsFees__(
                        productData, paymentData, total, receipt, null, null,  { isNamedTicketsFullRefund: true }
                    );

                    recounted       = fees.recounted;
                    refundAmount    = fees.refundAmount;
                    refundFeeAmount = fees.refundFeeAmount;

                    actions.push(
                        this.__paymentPartialRefundUpdate__(tr, paymentID, paymentData.payment_provider, refundAmount, recounted, isNamedTicketsMode)
                    )


                    await (Promise.all(
                        receipt.map(item => this.__reducePurchaseTicketCount__(tr, item, productID))
                    ));

                } else {
                    refundAmount = productData.product.amount;
                    refundFeeAmount = productData.product.sw_fee;
                    actions.push(
                        this.__paymentFullRefundUpdate__(tr, paymentID, productData.product.amount, isLastTicketRefund)
                    );
                }
                
                actions.push(this.__updateHistoryFields__(tr, paymentData, notes, productID));
            } else {
                // regular tickets mode refund
                actions.push(this.__updateHistoryFields__(tr, paymentData, notes, refundAmount));
            }
    
            const historyLine = this.__generateHistoryLine__({
                isPartial: false,
                userName,
                timezone: productData.timezone
            });
            
            actions.push(this.__productFullRefundUpdate__(tr, productID, historyLine));
            actions.push(
                QRTicketsUtils.removeTicketImage(
                    _.extend({ event_id: eventID, ticket_barcode: code }, paymentData.payment)
                )
            );

            await (Promise.all(actions));

            await (this.__makeRefund__(tr, paymentData, isLastTicketRefund, refundAmount, refundFeeAmount, refundedTicket));

            await this.__updateMetadata__(paymentData, {
                netProfit: 0,
                extraFee: 0,
                swFee: 0,
                providerFee: 0
            })
            
            await (tr.commit());

            return productID
        } catch (err) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }
            throw err;
        }
    }

    async webhookFullRefundAssignedTickets(purchase) {
        if(!purchase || !_.isObject(purchase) || !Number.isInteger(purchase.purchase_id)) {
            throw new Error('purchase parameter is invalid');
        }

        const [purchases] = await Promise.all([
            Db.query(
            squel.update().table('purchase', 'p')
                .set('amount', 0)
                .set('amount_refunded', squel.str('COALESCE(amount_refunded, 0) + amount'))
                .set('date_refunded = NOW()')
                .set('status', 'canceled')
                .where('p.purchase_id = ? OR p.linked_purchase_id = ?', purchase.purchase_id, purchase.purchase_id)
                .returning('p.ticket_barcode, p.purchase_id, amount_refunded')
            ).then(result => result.rows),
            Db.query(
                squel.update().table('purchase_ticket', 'pt')
                    .from('purchase', 'p')
                    .set('canceled = NOW()')
                    .where('p.purchase_id = pt.purchase_id AND p.linked_purchase_id = ?', purchase.purchase_id)
            ),
        ]);

        if(_.isEmpty(purchases)) {
            throw new Error('purchase not found');
        }

        let purchaseObject  = purchases.filter(p => p.purchase_id !== purchase.purchase_id)[0];
        let ticketBarcode   = purchaseObject && purchaseObject.ticket_barcode; //purchase
        let paymentID       = purchase.purchase_id;    //payment

        const refundedTickets = await PaymentService.tickets.getFullRefundedTickets({ paymentID });

        let notificationsData = {
            ticketCode: ticketBarcode,
            refundedTickets,
        };

        return Promise.all([
            this.sendFullRefundNotification(purchase.event_id, notificationsData),
            this.sendStripeDashboardRefundNotification(purchase.event_id, notificationsData),
            this.sendStripeDashboardRefundAdminNotification(
                ticketBarcode, purchaseObject.refunded_amount, purchase.event_id
            )
        ])
    }

    __isLastTicketRefund__ (isNamedTicketsMode, allFeesPaysSeller, productData, paymentData) {
        let paymentAmount = allFeesPaysSeller ? paymentData.payment.amount : paymentData.payment.net_profit;

        return isNamedTicketsMode && (productData.product.amount === paymentAmount);
    }

    __formatPurchaseTickets__ (receipt) {
        return receipt.map(item => {
            let ptID = +item.purchase_ticket_id;

            if(!ptID) {
                throw new Error('Invalid Tickets Object');
            }

            return ptID;
        });
    }

    __getPaymentData__ (tr, eventID, paymentID) {
        let query = squel.select().from('event', 'e')
            .field(`("tickets_settings"->>'require_recipient_name_for_each_ticket') :: BOOLEAN`, 'is_named_tickets')
            .field('e.event_id')
            .field('e.tickets_use_connect', 'use_connect')
            .field(
                `(
                    CASE
                    WHEN e.tickets_use_connect IS TRUE
                      THEN sa.secret_key
                    ELSE COALESCE(
                        sa.secret_key, (
                          SELECT "value" ->> 'secret_key'
                          FROM "settings"
                          WHERE "key" = 'stripe_connect_dev'
                        )
                    )
                    END
                  )`
            , 'stripe_secret')
            .field(
                `(
                    CASE
                    WHEN e.ticket_camps_registration IS TRUE
                      THEN 'camps'
                    ELSE 'tickets'
                    END
                  ) `
            , 'sales_type')
            .field(`COALESCE(NULLIF(e.tickets_stripe_statement, ''), e.stripe_statement)`, 'stripe_statement')
            .field('e.tickets_sw_fee', 'app_fee')
            .field(`sa.stripe_connect -> 'access_token'`, 'access_token')
            .field('sa.stripe_account_id')
            .field('e.stripe_tickets_fee_payer', 'stripe_fee_payer')
            .field('e.tilled_tickets_fee_payer', 'tilled_fee_payer')
            .field('e.tickets_sw_fee_payer', 'sw_fee_payer')
            .field(`COALESCE((p.payment_provider), '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}')`, 'payment_provider')
            .field(
                `(
                    CASE
                    WHEN (p.stripe_percent > 0)
                      THEN ROUND(p.stripe_percent / 100, 4)
                    ELSE 0
                    END
                  )`
            , 'stripe_percent')
            .field(
                `(
                    CASE
                    WHEN (p.tilled_percentage > 0)
                      THEN ROUND(p.tilled_percentage / 100, 4)
                    ELSE 0
                    END
                  )`
            , 'tilled_percentage')
            .field(`COALESCE(e.stripe_tickets_fixed, 0)`, 'stripe_fixed')
            .field(`COALESCE(e.tilled_tickets_fixed, 0)`, 'tilled_fixed')
            .field(
                `(
                    SELECT ROW_TO_JSON("payment")
                    FROM (
                           SELECT
                             p.purchase_id,
                             (p.stripe_payment_type = 'connect') "use_connect",
                             p.stripe_charge_id                  "charge_id",
                             ch.stripe_payment_id,
                             p.amount,
                             p.type,
                             p.stripe_fee,
                             p.tilled_fee,
                             p.tilled_payment_intent_id,
                             p.sales_hub_payment_id,
                             COALESCE((p.payment_provider), '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}'),
                             p.net_profit,
                             p.collected_sw_fee,
                             COALESCE(p.amount_refunded, 0)      "amount_refunded",
                             p.user_id,
                             p.additional_fee_amount
                         ) "payment"
                  )`
            , 'payment')
            .join(
                'purchase',
                'p',
                `p.event_id = e.event_id 
                AND p.purchase_id = ${paymentID}
                AND p.payment_for = 'tickets' 
                AND p.is_payment IS TRUE
                AND p.type IN ('card', 'ach')
                AND (p.status <> 'canceled' OR p.status IS NULL)
                AND p.canceled_date IS NULL 
                AND p.amount > 0
                AND (
                    (COALESCE((p.payment_provider), '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') = '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}'
                        AND p.stripe_charge_id IS NOT NULL) 
                    OR (p.payment_provider = '${PaymentService.__PAYMENT_PROVIDERS__.TILLED}' AND p.tilled_payment_intent_id IS NOT NULL)
                    OR  (p.payment_provider = '${PaymentService.__PAYMENT_PROVIDERS__.PAYMENT_HUB}' AND p.sales_hub_payment_id IS NOT NULL)
                )`
            )
            .left_join('stripe_account', 'sa', 'sa.secret_key = e.stripe_tickets_private_key')
            .left_join('stripe_charge', 'ch', 'ch.stripe_charge_id = p.stripe_charge_id')
            .where('e.event_id = ?', eventID);

        return tr.query(query).then(({rows: [result]}) => {
            return result ? this.__overridePaymentHubFeePayer(result) : null
        });
    }

    __overridePaymentHubFeePayer(paymentData) {
        if(this.__isPaymentHubProvider__(paymentData)) {
            paymentData.stripe_fee_payer = this.FEE_PAYER.SELLER
            paymentData.sw_fee_payer = this.FEE_PAYER.SELLER
        }

        return paymentData;
    }

    __getProduct__ (tr, eventID, code, paymentItems) {
        let query = squel.select().from('event', 'e')
            .field('e.event_id')
            .field('p.linked_purchase_id', 'payment_id')
            .field(
                `(
                    SELECT ROW_TO_JSON("product")
                    FROM (
                       SELECT
                         p.purchase_id,
                         p.amount,
                         p.type,
                         COALESCE(p.amount_refunded, 0) "amount_refunded"
                    ) "product"
                )`
            , 'product')
            .field(
                `(
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]' :: JSON)
                    FROM (
                           SELECT
                             et.event_ticket_id,
                             et.current_price          "price",
                             et.application_fee,
                             et.label,
                             et.sort_order,
                             et.event_camp_id,
                             pt.quantity               "bought_qty",
                             pt.ticket_price           "bought_ticket_price",
                             pt.purchase_ticket_id,
                             COALESCE(
                                 NULLIF(pt.ticket_fee, 0),
                                 NULLIF(et.application_fee, 0),
                                 e.tickets_sw_fee,
                                 0
                             )                         "bought_app_fee",
                             (pt.canceled IS NOT NULL) "is_canceled",
                             pt."discounted_quantity",
                             td.discount               "discount_amount",
                             pt.discount,
                             (
                                CASE 
                                    WHEN (pt.quantity = 0) AND (pt.amount > 0)
                                        THEN pt.amount
                                    ELSE 0
                                END
                            ) "cancellation"
                           FROM "event_ticket" et
                             LEFT JOIN "purchase_ticket" pt
                               ON pt.purchase_id = p.purchase_id
                                  AND pt.event_ticket_id = et.event_ticket_id
                                  ${
                                    (paymentItems && paymentItems.length)
                                    ? `AND pt.purchase_ticket_id IN (${paymentItems.join(', ')})`
                                    :''
                                  }
                                 LEFT JOIN "ticket_discount" td
                                   ON td.ticket_discount_id = pt.ticket_discount_id
                               WHERE et.event_id = e.event_id
                               ORDER BY et.event_ticket_id ASC
                             ) t
                      )`
            , 'ticket_types')
            .field('e.timezone')
            .join(
                'purchase',
                'p',
                `p.event_id = e.event_id 
                AND p.ticket_barcode = ${code}
                AND p.payment_for = 'tickets'
                AND p.is_ticket IS TRUE
                AND p.type IN ('card', 'ach')
                AND (p.status <> 'canceled' OR p.status IS NULL)
                AND p.canceled_date IS NULL
                AND p.amount > 0`
            )
            .where('e.event_id = ?', eventID)
            .toString();

        // Manually append the 'FOR UPDATE' clause
        query += " FOR UPDATE";

        return tr.query(query).then(result => result.rows[0] || null)
            .then(data => {

                if(!data) {
                    throw { validation: 'Suitable payment not found' };
                }

                data.product.sw_fee = this.__countSWFee__(data.ticket_types);

                this.__parseNumbers__(data.ticket_types);

                return data;
            })
    }

    __countSWFee__ (purchaseItems) {
        return purchaseItems.reduce((feeSum, item) => {
            if(item.purchase_ticket_id && !item.is_canceled) {
                feeSum += item.bought_qty * item.bought_app_fee;
            }
            return feeSum;
        }, 0);
    }

    __parseNumbers__ (ticketTypes) {
        ticketTypes.forEach(ticket => {
            ticket.price 				= +ticket.price;
            ticket.application_fee 		= +ticket.application_fee;
            ticket.sort_order 			= +ticket.sort_order;
            ticket.bought_qty 			= +ticket.bought_qty;
            ticket.bought_ticket_price 	= +ticket.bought_ticket_price;
            ticket.bought_app_fee 		= +ticket.bought_app_fee;
            ticket.discounted_quantity 	= +ticket.discounted_quantity;
            ticket.discount_amount 		= +ticket.discount_amount;
        });
    }

    __recountRefundPrices__ (total, receipt, itemsType, method, productData, paymentData, isNamedTicketsPartialRefund) {
        let recountedPrices =
            SWTPaymentsService.recountTotalPrice(
                productData.ticket_types, receipt, paymentData.app_fee, itemsType, method, null/* TODO: add purchase_discount here */, true,
                SWTPaymentsService.REFUND_COUNTER_MODE, paymentData.payment_provider
            );
        let taxedTotals;

        if(method === SWTPaymentsService.CARD_METHOD && this.__isPaymentHubProvider__(paymentData)){
            let recountedTotal = recountedPrices.total;

            if(!this.__sellerPaysProviderFee__(paymentData)) {
                /**
                 * When we buy with sales hub using buyer/buyer mode, we divide fees equally and add it to amount.
                 * Therefore, it is now same as seller/seller mode when refunding.
                 * Use seller/seller mode when refunding
                 */
                throw new Error('Buyer mode is not supported for recounting payment hub refunding fees');
            }

            taxedTotals = SWTPaymentsService.getTaxedTotals(
                recountedTotal, recountedPrices.applicationFee, method, paymentData);
        } else if(method === SWTPaymentsService.CARD_METHOD || method === SWTPaymentsService.ACH_METHOD) {
            taxedTotals = SWTPaymentsService.getTaxedTotals(
                recountedPrices.total, recountedPrices.applicationFee, method, paymentData);
        } else {
            taxedTotals = recountedPrices;
        }

        if(!isNamedTicketsPartialRefund && !this.__isPaymentHubProvider__(paymentData)) {
            if(!this.__sellerPaysProviderFee__(paymentData)) {
                total += taxedTotals.providerFee;
            }

            if(paymentData.sw_fee_payer === this.FEE_PAYER.BUYER) {
                total += recountedPrices.applicationFee;
            }
        }

        total = swUtils.normalizeNumber(total);

        if(total !== taxedTotals.total) {
            throw {
                validation  	: 'Invalid Total Amount',
                description 	: `passed: $${total} <> recounted: $${taxedTotals.total}`
            }
        }

        return { recountedPrices, taxedTotals };
    }

    __reducePurchaseTicketCount__ (tr, ticket, purchaseID, isCamp) {

        //purchase_id for assigned tickets is in ticket object
        purchaseID = ticket.purchase_id || purchaseID;

        return tr.query(
            `UPDATE "purchase_ticket" pt
         SET
             "available"    = ticket.available,
             "discount"     = ticket.discount,
             "quantity"     = ticket.quantity,
             "amount"       = (
                CASE 
                    WHEN (ticket.cancellation > 0)
                        THEN ticket.cancellation
                    WHEN (ticket.quantity > 0)
                        THEN (pt.ticket_price * ticket.quantity - ticket.discount)
                    ELSE (pt.ticket_price * ticket.quantity)
                END
             ),
             "registration_status" = (
                CASE
                    WHEN (ticket.quantity = 0 AND ticket.is_camp IS TRUE)
                        THEN 'canceled'
                    ELSE 'active'
                END        
             )::ticket_registration_status,
             "canceled" = (
                CASE
                    WHEN (ticket.quantity = 0 AND ticket.is_camp IS NOT TRUE)
                        THEN NOW()
                    ELSE NULL
                END        
             )
         FROM (
             SELECT (
                 CASE  
                     WHEN (pt.available - (pt.quantity - $3)) >= 0 
                         THEN (pt.available - (pt.quantity - $3)) 
                     ELSE 0 
                 END
             ) "available", COALESCE(($4)::NUMERIC, 0) "discount", $3 "quantity", $6::BOOLEAN "is_camp",
             COALESCE(($5)::NUMERIC, 0) "cancellation",
             pt.purchase_ticket_id, pt.purchase_id
             FROM "purchase_ticket" pt
             WHERE pt.purchase_ticket_id = $1
                 AND pt.purchase_id = $2
         ) "ticket"
         WHERE ticket.purchase_ticket_id = pt.purchase_ticket_id
             AND ticket.purchase_id = pt.purchase_id`,
            [ticket.purchase_ticket_id, purchaseID, ticket.quantity, ticket.discount || null, ticket.cancellation || null, isCamp]
        ).then(function (result) {
            if(result.rowCount === 0) {
                tr.rollback();
                throw {
                    validation: 'Invalid Ticket Type Passed (not matching the selected Purchase)'
                }
            }
        })
    }


    __addHistoryRow__ (tr, {
        purchaseId,
        refundAmount,
        notes,
        userId,
        purchase_ticket_id,
        quantity
    }) {
        let dataToInsert = {
            purchase_id: purchaseId,
            action: 'refund.created',
            description: (refundAmount)?`Refund of ${refundAmount} created`:`Full refund created`,
            notes,
            user_id: userId,
            amount: refundAmount,
        }

        if(purchase_ticket_id) {
            dataToInsert.purchase_ticket_id = purchase_ticket_id;
        }

        if(quantity > 0) {
            dataToInsert.quantity = quantity;
        }

        let query = knex('purchase_history').insert(dataToInsert);

        return (tr || Db).query(query);
    }

    __productPartialRefundUpdate__ (tr, productID, refundAmount, historyLine) {
        let query =
            `UPDATE "purchase"
              SET "amount_refunded" = COALESCE(amount_refunded, 0) + $2,
                  "amount"          = "amount" - $2,
                  "tickets_scan"    = CONCAT_WS(CHR(10), tickets_scan, ($3)::TEXT)
              WHERE "purchase_id" = $1
                 AND "payment_for" = 'tickets'`;

        return tr.query(query, [productID, refundAmount, historyLine]).then(result => result.rowCount);
    }

    __paymentPartialRefundUpdate__ (tr, paymentID, paymentProvider, refundAmount, recounted, isTicketsWithNames = null) {
        let params  = [
            paymentID,
            recounted.taxedTotals.providerFee,
            recounted.taxedTotals.netProfit,
            recounted.recountedPrices.applicationFee
        ];

        if(isTicketsWithNames) {
            params.push(refundAmount);
        }

        const providerFeeField = this.PROVIDER_FEE_FIELDS[paymentProvider];

        if(!providerFeeField) {
            throw new Error('Provider fee field invalid')
        }

        let query =
            `UPDATE "purchase"
              SET "${providerFeeField}" = $2,
                  "net_profit" = $3,
                  "collected_sw_fee" = $4
                  ${isTicketsWithNames 
                    ? `,"amount_refunded" = COALESCE(amount_refunded, 0) + $5, 
                        "amount" = "amount" - $5, "date_refunded" = NOW()`
                    : ''
                  }
              WHERE "purchase_id" = $1
                 AND "payment_for" = 'tickets'`;

        return tr.query(query, params).then(result => result.rowCount);
    }

    __productFullRefundUpdate__ (tr, productID, historyLine) {
        const query = squel.update()
            .table('purchase')
            .with(
                'purchase_ticket_update',
                this.__setPurchaseTicketCanceledQuery(productID)
            )
            .set('date_refunded = NOW()')
            .set('amount_refunded = COALESCE(amount_refunded, 0) + amount')
            .set('amount = 0')
            .set(`status = 'canceled'`)
            .set('canceled_date = NOW()')
            .set(`tickets_scan = CONCAT_WS(CHR(10), tickets_scan, ?::TEXT)`, historyLine)
            .where('purchase_id = ?', productID);

        return tr.query(query).then(result => result.rowCount);
    }

    __paymentFullRefundUpdate__ (tr, paymentID, refundAmount, isPaymentRefund) {
        let query =
            `UPDATE "purchase"
             SET "date_refunded" = NOW(),
                 "amount_refunded" = COALESCE("amount_refunded", 0) + $2
                 ${isPaymentRefund 
                    ? `, "status" = 'canceled', "canceled_date" = NOW()`
                    : ''
                }
             WHERE "purchase_id" = $1`;

        return tr.query(query, [paymentID, refundAmount]).then(result => result.rowCount);
    }

    __updateEventBalance__ (tr, eventID, additionalFeeAmount) {
        let query =
            `UPDATE "event"
                 SET "tickets_sw_balance" = COALESCE("tickets_sw_balance", 0) - $2
                 WHERE "event_id" = $1`;

        return tr.query(query, [eventID, additionalFeeAmount]).then(result => result.rowCount);
    }

    async __updateHistoryFields__ (tr, paymentData, notes, productID = null, refundAmount = null, refundedTickets) {
        let userID = paymentData.payment.user_id;
        let paymentID = paymentData.payment.purchase_id;

        await this.__addHistoryRow__(tr, {
            purchaseId: paymentID,
            refundAmount,
            notes,
            userId: userID
        });

        if(!_.isEmpty(refundedTickets)) {
            for(let ticket of refundedTickets) {
                await this.__addHistoryRow__(tr, {
                    purchaseId: productID || paymentID,
                    refundAmount: ticket.amountRefunded,
                    notes,
                    userId: userID,
                    quantity: ticket.quantity,
                    purchase_ticket_id: ticket.purchase_ticket_id,
                });
            }
        }
    }

    // data params: charge_id, amount, fee, stripe_secret
    __makeStripeRefund__ (paymentData, skipFeeRefund, refundAmount, refundFeeAmount) {
        let data = {
            charge_id       : paymentData.payment.charge_id,
            stripe_secret   : paymentData.private_key
        };

        if(!skipFeeRefund && (refundAmount !== null && refundFeeAmount !== null)) {
            data.amount = refundAmount;
            data.fee    = refundFeeAmount;
        }

        if(paymentData.payment.use_connect) {
            return this.StripeConnect.refundClientPayment(data, null, skipFeeRefund);
        } else {
            return StripeService.refund(data);
        }
    }

    async __makeTilledRefund__ (tr, paymentData, skipFeeRefund, refundAmount, refundFeeAmount) {
        let refundTotal = refundAmount;

        if(!skipFeeRefund && (refundAmount !== null && refundFeeAmount !== null)) {
            refundTotal + refundFeeAmount
        }

        const amountCents = refundAmount ? swUtils.normalizeNumber(refundAmount * 100) : null;

        const tilledRefund = await TilledService.createRefund({ tilledPaymentIntentId: paymentData.payment.tilled_payment_intent_id, amount: amountCents })

        await this.__saveTiledRefund___(tr, tilledRefund)

    }

    async __makePaymentHubRefund__ (tr, paymentData, isLastTicketFullRefund, refundAmount, refundFeeAmount, refundedTicket) {
        const amountCents = refundAmount ? swUtils.normalizeNumber(refundAmount * 100) : null;
        const refundFeeCents = swUtils.normalizeNumber(refundFeeAmount * 100);

        if(!refundedTicket) {
            throw new Error('Ticket must be provided for refund');
        }

        if (refundedTicket.amountAfterRefund > 0) {
            const minimumRequiredRemainingBalance = this.__calculateSalesHubMinimumRequiredRemainingBalance(refundedTicket.applicationFeeAfterRefund);

            if(minimumRequiredRemainingBalance > refundedTicket.amountAfterRefund) {
               throw { validation: `Remaining amount cannot be below $${minimumRequiredRemainingBalance}` };
            }
        }

         const salesHubRefund = isLastTicketFullRefund
            ? await SalesHubRefundAPIService.createFullRefund({
                  paymentId: paymentData.payment.sales_hub_payment_id,
              })
            : await SalesHubRefundAPIService.create({
                  paymentId: paymentData.payment.sales_hub_payment_id,
                  amount: amountCents,
                  marketplaceFee: refundFeeCents,
                  items: [
                      {
                          orderItemId: refundedTicket.salesHubOrderItemId,
                          amount: amountCents,
                          marketplaceFee: refundFeeCents,
                          quantity: refundedTicket.quantity,
                      },
                  ],
              });

        await new SalesHubRefundStorageService(tr).create(salesHubRefund);
    }

    __calculateSalesHubMinimumRequiredRemainingBalance(applicationFee) {
        return swUtils.normalizeNumber((applicationFee + StripeService.DEFAULT_STRIPE_FIXED) / (1 - StripeService.DEFAULT_STRIPE_PERCENT));
    }

    async __saveTiledRefund___ (tr, tilledRefund) {
        const data = {
            tilled_refund_id: tilledRefund.id,
            status: tilledRefund.status,
            amount: tilledRefund.amount,
            tilled_payment_intent_id: tilledRefund.payment_intent_id,
            metadata: tilledRefund.metadata,
            tilled_balance_transaction_id: tilledRefund.balance_transaction,
            reason: tilledRefund.reason,
            failure_code: tilledRefund.failure_code,
            failure_message: tilledRefund.failure_message
        }

        await tr.query(knex('tilled.refund').insert(data))
    }

    __makeRefund__(tr, paymentData, isLastTicketFullRefund, refundAmount, refundFeeAmount, refundedTicket) {
        const payment_provider = paymentData.payment_provider

        if(this.__isStripeProvider__(paymentData)) {
            return this.__makeStripeRefund__(
                paymentData,
                isLastTicketFullRefund,
                isLastTicketFullRefund ? null : refundAmount,
                isLastTicketFullRefund ? null : refundFeeAmount
            );
        }
        
        if(this.__isTilledProvider__(paymentData)) {
            return this.__makeTilledRefund__(
                tr,
                paymentData,
                isLastTicketFullRefund,
                isLastTicketFullRefund ? null : refundAmount,
                isLastTicketFullRefund ? null : refundFeeAmount
            );
        }

        if(this.__isPaymentHubProvider__(paymentData)) {
            return this.__makePaymentHubRefund__(tr, paymentData, isLastTicketFullRefund, refundAmount, refundFeeAmount, refundedTicket)
        }

        throw new Error(`Invalid payment provider ${payment_provider}`)
    }

    __updateMetadata__ (paymentData, totals) {
        const payment_provider = paymentData.payment_provider

        if(this.__isStripeProvider__(paymentData)) {
            return this.__updateStripeMetadata__(paymentData, totals)
        }
        
        if(this.__isTilledProvider__(paymentData)) {
            return this.__updateTilledMetadata__(paymentData, totals)
        }

        if(this.__isPaymentHubProvider__(paymentData)) {
            return; // TODO: add metadata update on sales hub
        }

        throw new Error(`Invalid payment provider ${payment_provider}`)
    }

    __isStripeProvider__(paymentData) {
        const payment_provider = paymentData.payment_provider
        return payment_provider === PaymentService.__PAYMENT_PROVIDERS__.STRIPE
    }

    __isPaymentHubProvider__(paymentData) {
        const payment_provider = paymentData.payment_provider
        return payment_provider === PaymentService.__PAYMENT_PROVIDERS__.PAYMENT_HUB
    }

    __isTilledProvider__(paymentData) {
        const payment_provider = paymentData.payment_provider
        return payment_provider === PaymentService.__PAYMENT_PROVIDERS__.TILLED
    }

    __isPaymentHubProvider__(paymentData) {
        const payment_provider = paymentData.payment_provider
        return payment_provider === PaymentService.__PAYMENT_PROVIDERS__.PAYMENT_HUB
    }


    __updateStripeMetadata__(paymentData, { providerFee, ...totals }) {
        return StripeService.updateChargeMetadataAfterRefund({
            stripe_account_id: paymentData.stripe_account_id,
            charge_id: paymentData.payment.charge_id,
            stripe_payment_id: paymentData.payment.stripe_payment_id
        }, {
            ...totals,
            stripeFee: providerFee
        });
    }

    async __updateTilledMetadata__(paymentData, { netProfit, providerFee, extraFee, swFee }) {
        const tilledPaymentIntentId = paymentData.payment.tilled_payment_intent_id;

        const tilledPaymentIntent = await this.__getTilledPaymentIntent(tilledPaymentIntentId);

        const paymentCommonMetadataFields = TilledService.paymentCommonMetadataFields({
            netProfit,
            providerFee,
            extraFee,
            swFee,
        })

        const newMetadata = {
            ...tilledPaymentIntent.metadata,
            ...paymentCommonMetadataFields,
        }

        await TilledService.updatePaymentIntentMetadata(tilledPaymentIntentId, newMetadata)

        await this.__updateTilledPaymentIntentRow(tilledPaymentIntentId, {
            metadata: newMetadata
        })
    }

    async __updateTilledPaymentIntentRow(tilledPaymentIntentId, data) {
        const query = knex('tilled.payment_intent')
            .update(data)
            .where({ tilled_payment_intent_id: tilledPaymentIntentId })
            .returning('*');

        const updatedCount = await Db.query(query).then(({ rowCount }) => rowCount);

        if(updatedCount === 0) {
            throw new Error('Payment intent not found');
        }
    }

    __getTilledPaymentIntent(tilledPaymentIntentId) {
        const query = knex('tilled.payment_intent')
            .select(['metadata', 'tilled_payment_intent_id'])
            .where({tilled_payment_intent_id: tilledPaymentIntentId});

        return Db.query(query).then(({ rows })=>rows[0]);
    }

    __countTicketsFees__ (productData, paymentData, total, receipt, providerFee, swFee,
                          {isNamedTicketsPartialRefund, isNamedTicketsFullRefund}) {
        let recounted = this.__recountRefundPrices__(
            total, receipt, paymentData.sales_type, productData.product.type, productData, paymentData, isNamedTicketsPartialRefund
        );

        if(providerFee && (providerFee !== recounted.taxedTotals.providerFee)) {
            throw {
                validation 		: 'Invalid Provider Fee Amount',
                description 	: `passed: $${providerFee} <> recounted: $${recounted.taxedTotals.providerFee}`
            };
        }

        if(swFee && (swFee !== recounted.taxedTotals.swFee)) {
            throw {
                validation 		: 'Invalid SW Fee Amount',
                description 	: `passed: $${swFee} <> recounted: $${recountedSWFee}`
            };
        }

        // Note: for assigned tickets partial refund and for assigned tickets full refund (not last ticket in payment)
        // we use payment amount instead product
        let amount = isNamedTicketsPartialRefund || isNamedTicketsFullRefund
            ? paymentData.payment.amount
            : productData.product.amount;

        // Note: for assigned tickets full refund and for assigned tickets partial refund (not last ticket in payment)
        // we use payment collected sw fee not product
        let swFeeAmount = isNamedTicketsFullRefund || isNamedTicketsPartialRefund
            ? paymentData.payment.collected_sw_fee
            : productData.product.sw_fee;


        let refundAmount    = swUtils.normalizeNumber(amount - recounted.taxedTotals.total);

        const refundFeeAmount = this.__calculateRefundFeeAmount(paymentData, recounted.taxedTotals, swFeeAmount);

        return { recounted, refundAmount, refundFeeAmount };
    }

    __calculateRefundFeeAmount(paymentData, recountedTaxedTotals, swFeeAmount) {
        if(this.__isStripeProvider__(paymentData)) {
            return swUtils.normalizeNumber(this.__getPaymentProviderFee__(paymentData) + swFeeAmount - recountedTaxedTotals.applicationFee);
        }

        if(this.__isPaymentHubProvider__(paymentData)){
            return swUtils.normalizeNumber(swFeeAmount - recountedTaxedTotals.applicationFee);
        }

        if(this.__isTilledProvider__(paymentData)) {
            return swUtils.normalizeNumber(swFeeAmount - recountedTaxedTotals.applicationFee);
        }
    }

    __sellerPaysAllFees__(paymentData) {
        const sellerPaysSWFee = paymentData.sw_fee_payer !== this.FEE_PAYER.BUYER;

        return this.__sellerPaysProviderFee__(paymentData) && sellerPaysSWFee
    }

    __sellerPaysProviderFee__(paymentData) {
        const { payment, stripe_fee_payer, tilled_fee_payer } = paymentData;
        const { payment_provider } = payment

        if(this.__isPaymentHubProvider__(paymentData)) {
            return this.FEE_PAYER.SELLER
        }

        if(this.__isStripeProvider__(paymentData)) {
            return stripe_fee_payer !== this.FEE_PAYER.BUYER
        }

        if(this.__isTilledProvider__(paymentData)) {
            return tilled_fee_payer !== this.FEE_PAYER.BUYER
        }

        throw new Error(`Invalid payment provider passed ${payment_provider}`)
    }

    __getPaymentProviderFee__(paymentData) {
        const { payment_provider, stripe_fee, tilled_fee } = paymentData.payment

        if(this.__isStripeProvider__(paymentData) || this.__isPaymentHubProvider__(paymentData)) {
            return stripe_fee
        }

        if(this.__isTilledProvider__(paymentData)) {
            return tilled_fee
        }

        throw new Error(`Invalid payment provider passed ${payment_provider}`)
    }

    __formatReceiptData__ (tickets, id) {
        let result = [];

        tickets.forEach(ticket => {
            if(ticket.purchase_id && ticket.purchase_id !== Number(id)) {
                result.push({
                    id                  : ticket.event_ticket_id,
                    camp_id             : ticket.event_camp_id,
                    purchase_ticket_id  : ticket.purchase_ticket_id,
                    quantity            : ticket.bought_qty,
                    discount            : ticket.discount,
                    cancellation        : ticket.cancellation,
                    purchase_id         : ticket.purchase_id
                })
            }
        });

        return result;
    }

    __formatRefundedTicket__ (tickets, id, { refundAmount = null, refundFeeAmount = null, ticketsAfterRefund = [] } = {}) {
        const [ ticket ] =  tickets.filter(ticket => ticket.purchase_id && ticket.purchase_id === Number(id));

        if(!ticket) {
            throw new Error('Refunding ticket cannot be found')
        }

        const ticketAfterRefund = ticketsAfterRefund.find(({ purchase_ticket_id }) => {
            return purchase_ticket_id === ticket.purchase_ticket_id;
        });

        const currentApplicationFee = ticket.bought_app_fee;
        const quantityAfterRefund = ticketAfterRefund?.quantity || 0;

        return {
            purchase_id: ticket.purchase_id,
            event_ticket_id: ticket.event_ticket_id,
            quantity: ticket.bought_qty - quantityAfterRefund,
            application_fee: currentApplicationFee,
            amountAfterRefund: refundAmount !== null ? swUtils.normalizeNumber(ticket.current_total_amount - refundAmount) : 0,
            amountBeforeRefund: ticket.current_total_amount,
            applicationFeeAfterRefund: refundFeeAmount !== null ? swUtils.normalizeNumber(currentApplicationFee - refundFeeAmount) : 0,
            salesHubOrderItemId: ticket.sales_hub_order_item_id,
        }
    }

    __getCorrectPartialRefundAmount (refundAmount, allFeesPaysSeller, paymentData, recounted) {
        let purchaseRefundAmount = 0;

        if(!allFeesPaysSeller && !this.__isPaymentHubProvider__(paymentData)) {
            if(paymentData.is_named_tickets) {
                purchaseRefundAmount = swUtils.normalizeNumber(
                    paymentData.payment.net_profit - recounted.recountedPrices.total
                );
            } else {
                purchaseRefundAmount = swUtils.normalizeNumber(
                    paymentData.payment.amount -
                    (
                        recounted.taxedTotals.providerFee +
                        recounted.taxedTotals.netProfit +
                        recounted.recountedPrices.applicationFee
                    )
                );
            }
        } else {
            purchaseRefundAmount = refundAmount;
        }

        return purchaseRefundAmount;
    }

    __getReceiptData__ (eventID, code, isReceiptData) {
        let excludeCanceledPurchases = isReceiptData ?
            `AND p2.payment_for = 'tickets'
            AND p2.is_ticket IS TRUE
            AND p2.type IN ('card', 'ach')
            AND (p2.status <> 'canceled' OR p2.status IS NULL)
            AND p2.canceled_date IS NULL
            AND p2.amount > 0` : '';

        return Db.query(
            `SELECT (
                     SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]' :: JSON)
                     FROM (
                            SELECT
                              et.event_ticket_id,
                              et.current_price          "price",
                              et.application_fee,
                              pt.purchase_id,
                              et.label,
                              pt.kiosk_surcharge,
                              et.sort_order,
                              et.event_camp_id,
                              pt.quantity               "bought_qty",
                              pt.ticket_price           "bought_ticket_price",
                              pt.purchase_ticket_id,
                              pt.amount "current_total_amount",
                              pt.sales_hub_order_item_id,
                              (ticket_purchase.amount + COALESCE(ticket_purchase.amount_refunded, 0)) "initial_total_amount",
                              COALESCE(
                                  NULLIF(pt.ticket_fee, 0),
                                  NULLIF(et.application_fee, 0),
                                  e.tickets_sw_fee,
                                  0
                              )                         "bought_app_fee",
                              (pt.canceled IS NOT NULL) "is_canceled",
                              pt."discounted_quantity",
                              td.discount               "discount_amount",
                              pt.discount,
                              (
                                CASE
                                WHEN (pt.quantity = 0) AND (pt.amount > 0)
                                  THEN pt.amount
                                ELSE 0
                                END
                              )                         "cancellation"
                            FROM "purchase_ticket" pt
                              LEFT JOIN "event_ticket" et
                                ON pt.event_ticket_id = et.event_ticket_id
                              INNER JOIN purchase as ticket_purchase
                                ON ticket_purchase.purchase_id = pt.purchase_id
                              LEFT JOIN "ticket_discount" td
                                ON td.ticket_discount_id = pt.ticket_discount_id
                            WHERE et.event_id = e.event_id 
                            AND pt.purchase_id IN (SELECT purchase_id
                                  FROM purchase p2
                                  WHERE p2.linked_purchase_id = p.linked_purchase_id ${excludeCanceledPurchases})
                            ORDER BY et.event_ticket_id ASC
                          ) t
                   ) AS ticket_types
            FROM "event" AS e
              INNER JOIN "purchase" AS p
                ON p.event_id = e.event_id
                   AND p.ticket_barcode = $2
            WHERE e.event_id = $1`, [eventID, code]
        ).then(result => result.rows[0] && result.rows[0].ticket_types);
    }

    getRefundedTickets(ticketsBeforeRefund, ticketsAfterRefund) {
        const tickets = [];

        for (let i = 0; i < ticketsAfterRefund.length; i++) {
            const ticketAfterRefund = ticketsAfterRefund[i];
            const ticketBeforeRefund = ticketsBeforeRefund.find(({ purchase_ticket_id }) => {
                return purchase_ticket_id === ticketAfterRefund.purchase_ticket_id;
            });

            const quantity = ticketAfterRefund.quantity === 0 ? 0 : ticketBeforeRefund.bought_qty - ticketAfterRefund.quantity;

            const amountBeforeRefund = ticketBeforeRefund.bought_qty === 0 && ticketBeforeRefund.cancellation
                ? ticketBeforeRefund.cancellation
                : (ticketBeforeRefund.price * ticketBeforeRefund.bought_qty) - ticketBeforeRefund.discount;

            const amountAfterRefund = ticketAfterRefund.quantity === 0 && ticketAfterRefund.cancellation
                ? ticketAfterRefund.cancellation
                : (ticketAfterRefund.price * ticketAfterRefund.quantity) - ticketAfterRefund.discount;

            const amountRefunded = amountBeforeRefund - amountAfterRefund;

            if(amountRefunded > 0) {
                tickets.push({
                    purchase_ticket_id: ticketAfterRefund.purchase_ticket_id,
                    label: ticketAfterRefund.label,
                    quantity: quantity,
                    amountRefunded: amountRefunded,
                });
            }
        }

        return tickets;
    }

    __setPurchaseTicketCanceledQuery (productID) {
        return squel.update()
            .table('purchase_ticket')
            .set('canceled = NOW()')
            .where('purchase_id = ?', productID);
    }

    async webhookFullRefundBasicTickets (amount, chargeID, stripeEventID) {
        let refundedPurchase = await this.__proceedStripeWebhookBasicTicketsRefund(amount, chargeID);

        if(!refundedPurchase.length) {
            throw new Error('No purchase found');
        }

        const purchase = _.first(refundedPurchase);

        if (!(purchase && purchase.event_id)) {
            throw new Error('Purchase without event_id');
        }

        await Db.query(this.__setPurchaseTicketCanceledQuery(purchase.purchase_id));

        let notificationData = {ticketCode: purchase.ticket_barcode};

        await Db.query(
            squel.insert().into('purchase_history')
                .set('purchase_id', purchase.purchase_id)
                .set('action', 'refund.created')
                .set('amount', amount)
                .set('stripe_event_id', stripeEventID)
                .set('description', `Refund Created ($${amount}). Purchase ${purchase.purchase_id}, charge ${chargeID}`)
        );

        return Promise.all([
            this.sendFullRefundNotification(purchase.event_id, notificationData),
            this.sendStripeDashboardRefundNotification(purchase.event_id, notificationData),
            this.sendStripeDashboardRefundAdminNotification(
                purchase.ticket_barcode, purchase.refunded_amount, purchase.event_id
            )
        ]);
    }

    __proceedStripeWebhookBasicTicketsRefund (amount, chargeID) {
        let query = `
            UPDATE "purchase" p
             SET amount_refunded = ($1)::NUMERIC,  
                 amount = (amount + COALESCE(amount_refunded, 0) - ($1)::NUMERIC), 
                 date_refunded = NOW(),
                 status = 'canceled'
            WHERE stripe_charge_id = ($2)::TEXT
                AND p.is_payment IS TRUE AND p.is_ticket IS TRUE
                AND (
                    "amount_refunded" IS NULL OR "amount_refunded" <> ($1)::NUMERIC
                ) AND NOT EXISTS (
                    SELECT ph.purchase_history_id
                    FROM "purchase_history" ph 
                    WHERE ph.purchase_id    = p.purchase_id
                        AND ph."action"     = 'debt.refunded'
                        AND ph.amount       = ($1)::NUMERIC
                )
            RETURNING event_id, payment_for, ticket_barcode, 
                amount, amount_refunded, purchase_id, user_id, 
                stripe_charge_id, p.additional_fee_amount`;

        return Db.query(query, [amount, chargeID]).then(result => result.rows);
    }



    sendFullRefundNotification (eventID, data, addEOToBcc) {
        return this.sendRefundNotification(
            AEMService.TICKETS_REFUNDS_GROUP_TYPE.FULL_REFUND, eventID, data, addEOToBcc
        );
    }

    sendPartialRefundNotification (eventID, data, addEOToBcc) {
        return this.sendRefundNotification(
            AEMService.TICKETS_REFUNDS_GROUP_TYPE.PARTIAL_REFUND, eventID, data, addEOToBcc
        );
    }

    sendStripeDashboardRefundNotification (eventID, data, addEOToBcc) {
        return this.sendRefundNotification(
            AEMService.TICKETS_REFUNDS_GROUP_TYPE.STRIPE_DASHBOARD_REFUND, eventID, data, addEOToBcc
        );
    }

    sendStripeDashboardRefundAdminNotification (barcode, refundedAmount, eventID) {

        let refundNotificationData = {
            ticketCode      : barcode,
            refundedAmount  : refundedAmount
        };

        return this.sendRefundNotification(
            AEMService.TICKETS_REFUNDS_GROUP_TYPE.STRIPE_DASHBOARD_REFUND_ADMIN, eventID, refundNotificationData
        );
    }

    sendRefundNotification (type, eventID, data, addEOToBcc) {
        return this.__sendNotification({
            group       : AEMService.TICKETS_REFUNDS_GROUP,
            type        : type,
            eventID     : Number(eventID),
            data,
            addEOToBcc  : false
        })
    }

    __sendNotification({ group, type, eventID, data, addEOToBcc }) {
        return AEMSenderService.sendTriggerNotification(group, type, eventID, data, false, addEOToBcc);
    }
    
    __generateHistoryLine__({ isPartial, userName, timezone, amount = 0 }) {
        const datetime = new Date();
    
        const format = 'YYYY/MM/DD h:mm:ss a';
        const formattedDate = moment(datetime).utc().tz(timezone).format(format)
        
        let refundTxt = `Refunded by "${userName}" on "${formattedDate}"`;
        
        if(isPartial) {
            refundTxt = `Partially Refunded by "${userName}" Amount of Refund $${amount} on "${formattedDate}"`;
        }
        
        return refundTxt;
    }
}

module.exports = new RefundTicketsService(StripeConnect);
