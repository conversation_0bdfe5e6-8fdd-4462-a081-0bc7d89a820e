const { PAYMENT_INTENT } = require('../../constants/stripe/webhook-events');
const { PAYMENT_FOR, CUSTOM_PAYMENT } = require("../../constants/payments");

class TeamsUncollectedFeePaymentsBuilder {
    constructor() {}

    notificationReceiversList (eventID, receiversData) {
        const {
            custom_payment_id: customPaymentID,
            payment_for_type
        } = (receiversData || {});

        if(!eventID) {
            throw new Error('Event ID required');
        }

        if(!customPaymentID) {
            throw new Error('Custom Payment Id required');
        }

        const query = knex('custom_payment AS cp')
            .select(
                'e.long_name AS event_name',
                'e.name AS event_short_name',
                knex.raw(`(date_part('year'::text, now())) AS "current_year"`),
                knex.raw(`COALESCE(e.teams_entry_sw_fee, 0) AS sw_fee`),
                knex.raw(`(
                   CASE
                       WHEN cp.payment_for = '${CUSTOM_PAYMENT.PAYMENT_FOR.UNCOLLECTED_FEE}'
                           THEN COALESCE(NULLIF(e.team_fees_notification_email, ''), u.email)
                       ELSE u.email
                   END
                ) as "email"`),
                knex.raw(`(
		            CASE
                        WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD}')
		                    THEN 'credit card'
                        WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH}')
		                    THEN 'bank account'
		                ELSE ''
		            END
                ) AS "payment_method"`),
                knex.raw(`(
		            CASE
                        WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD}')
		                    THEN spm.card_last_4
                        WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH}')
		                    THEN spm.bank_account_last_4
		                ELSE ''
		            END
                ) AS "card_last_4"`),
                knex.raw(`COALESCE("cpufbi"."balance_details"::JSON->>'accepted_or_paid', 'Unknown') AS teams_accepted_or_paid`),
                knex.raw(`COALESCE("cpufbi"."balance_details"::JSON->>'accepted_and_paid', 'Unknown') AS teams_paid_and_accepted`),
                knex.raw(`COALESCE("cpufbi"."balance_details"::JSON->>'paid_only', 'Unknown') AS teams_paid_only`),
                knex.raw(`COALESCE("cpufbi"."balance_details"::JSON->>'accepted_only', 'Unknown') AS teams_accepted_only`),
                knex.raw(`COALESCE("se"."data"::JSON->'data'->'object'->'last_payment_error'->>'message', '') AS stripe_error_message`),
                'cp.amount AS payment_total_amount',
                'cp.merchant_fee AS payment_merchant_fee',
                'cp.net_profit AS payment_net_amount'
            )
            .join('event AS e', 'e.event_id', 'cp.event_id')
            .leftJoin('stripe_payment_intent AS spi', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .join('event_payment_method AS epm', 'epm.event_id', 'e.event_id')
            .join('stripe_payment_method AS spm', 'spm.stripe_payment_method_id', 'epm.stripe_payment_method_id')
            .join('user_stripe_customer AS usc', 'usc.stripe_customer_id', 'spm.stripe_customer_id')
            .join('user AS u', 'u.user_id', 'usc.user_id')
            .leftJoin('stripe_event AS se', (table) => {
                table.on(knex.raw(`se.type = ?`, PAYMENT_INTENT.PAYMENT_FAILED))
                    .andOn('se.stripe_id', 'spi.payment_intent_id')
            })
            .leftJoin(
                'custom_payment_uncollected_fee_balance_info AS cpufbi',
                'cpufbi.custom_payment_id',
                'cp.custom_payment_id'
            )
            .where('cp.event_id', eventID)
            .where('cp.custom_payment_id', customPaymentID);

        if(payment_for_type === PAYMENT_FOR.TEAMS) {
            query.whereRaw('e.allow_teams_registration IS TRUE');
        } else if(payment_for_type === PAYMENT_FOR.BOOTHS) {
            query.whereRaw('e.has_exhibitors IS TRUE');
        }

        return query;
    }
}

module.exports = new TeamsUncollectedFeePaymentsBuilder();
