
const SENDER = {
    CLUB: 'club',
    EVENT: 'event'
}

const TEAM_AGREEMENT_VALUE = {
    ACCEPTED: 'accepted',
    DECLINED: 'declined'
}

const NOTIFICATION_ACTION = {
    EVENT: {
        BID_ACCEPTED: 'team.bid.agreement.accepted.eo',
        BID_DECLINED: 'team.bid.agreement.declined.eo'
    },
    CLUB: {
        BID_ACCEPTED: 'team.bid.agreement.accepted.club',
        BID_DECLINED: 'team.bid.agreement.declined.club'
    }
}

class PrevQualificationService {
    constructor () {}

    get SENDER () {
        return SENDER;
    }

    get TEAM_AGREEMENT_VALUE () {
        return TEAM_AGREEMENT_VALUE;
    }

    async setBidAcceptedForTeams (eventID, teams, sender) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        if(_.isEmpty(teams)) {
            throw { validation: 'Teams object required' };
        }

        if(!sender || !Object.values(this.SENDER).includes(sender)) {
            throw { validation: 'Sender invalid' };
        }

        await this.__validateEventType({eventID});

        let tr;

        try {
            tr = await Db.begin();

            await Promise.all([
                this.__saveAcceptedBidAgreementForTeams(tr, eventID, sender, teams[this.TEAM_AGREEMENT_VALUE.ACCEPTED]),
                this.__saveDeclinedBidAgreementForTeams(tr, eventID, sender, teams[this.TEAM_AGREEMENT_VALUE.DECLINED]),
            ])

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }
        }
    }

    async updateTeamQualification (rosterTeamID, data)  {
        await this.__validateEventType({rosterTeamID});

        const extra = {
            show_accepted_bid           : data.show_accepted_bid    || null,
            prev_qual                   : data.prev_qual            || null,
            prev_qual_age               : data.prev_qual_age        || null,
            prev_qual_division          : data.prev_qual_division   || null,
            earned_at                   : data.earned_at            || null,
            show_previously_accepted_bid: this.__formatPrevQualTitle(data)
        };

        return this.__updateTeamQualification(rosterTeamID, extra);
    }

    __saveAcceptedBidAgreementForTeams (tr, eventID, sender, teams = []) {
        const isBidAccepted = true;

        return this.__saveBidAgreementForTeams(eventID, teams, sender, isBidAccepted, tr);
    }

    __saveDeclinedBidAgreementForTeams (tr, eventID, sender, teams = []) {
        const isBidAccepted = false;

        return this.__saveBidAgreementForTeams(eventID, teams, sender, isBidAccepted, tr);
    }

    async __saveBidAgreementForTeams (eventID, teams, sender, isBidAccepted, tr) {
        if(!teams.length) {
            return;
        }

        let query = knex('roster_team AS rt')
            .update(
                'extra',
                knex.raw(`jsonb_set(COALESCE(rt.extra, '{}'::JSONB),'{ bid_agreement_accepted }', '${isBidAccepted}')`)
            )
            .where('rt.event_id', eventID)
            .whereIn('rt.roster_team_id', teams);

        let allTeamsUpdated = await tr.query(query).then(result => result?.rowCount === teams.length);

        if(!allTeamsUpdated) {
            loggers.errors_log.error(
                `Not all teams agreements saved for teams: ${teams.join(', ')} on event: ${eventID}.`
            );
        } else {
            await Promise.all(
                teams.map(teamID => this.__saveBidAgreementChangeToHistory(eventID, teamID, isBidAccepted, sender, tr)
                    .catch(err => loggers.errors_log.error(err))
                )
            );
        }
    }

    async __validateEventType (params) {
        const eventType = await RosterTeamService.getEventType(params);

        if (eventType !== 'qualifier') {
            throw { validation: 'Invalid event type' }
        }
    }

    __updateTeamQualification (rosterTeamID, data) {
        const query = knex('roster_team')
            .update('extra', knex.raw(`COALESCE(extra, '{}'::JSONB) || ?::JSONB`, JSON.stringify(data)))
            .where('roster_team_id', rosterTeamID);

        return Db.query(query);
    }

    __formatPrevQualTitle({ prev_qual, prev_qual_division, prev_qual_age }) {
        if (!prev_qual) {
            return null;
        }

        return `${prev_qual_age}${prev_qual_division[0].toUpperCase()}`;
    }

    __saveBidAgreementChangeToHistory (eventID, rosterTeamID, isBidAccepted, sender, tr) {
        let action;

        if(sender === SENDER.CLUB) {
            action = isBidAccepted ? NOTIFICATION_ACTION.CLUB.BID_ACCEPTED : NOTIFICATION_ACTION.CLUB.BID_DECLINED;
        } else if(sender === SENDER.EVENT) {
            action = isBidAccepted ? NOTIFICATION_ACTION.EVENT.BID_ACCEPTED : NOTIFICATION_ACTION.EVENT.BID_DECLINED;
        } else {
            throw { validation: 'Sender invalid' };
        }

        return RosterTeamService.history.addHistoryRow(action, { rosterTeamID, eventID }, tr);
    }
}

module.exports = new PrevQualificationService();
