class UnresolvedMembershipsService {

    getBaseQuery () {
        return knex('unresolved_memberships AS um')
            .leftJoin('master_club AS mc', (join) => {
                join.on('mc.region', 'um.region')
                    .andOn('mc.code', 'um.club_code')
            })
    }

    async getAll({offset = 0, limit = 25}) {
        const baseQuery = this.getBaseQuery();


        const query = baseQuery
            .clone()
            .select(
                'mc.club_name',
                'mc.director_email as club_director_email',
                'um.organization_code',
                'um.membership_definition_id',
                'um.membership_name',
                'um.membership_status',
                {
                    membership_end_date: knex.raw(`to_char(um.membership_end_date, 'MM/DD/YYYY HH24:MI:SS')`),
                },
                'um.age_group',
                'um.error_message',
            )
            .orderBy('um.created', 'desc')
            .limit(limit)
            .offset(offset);

        const totalQuery = query
            .clone()
            .select(knex.raw('COUNT(um.*) OVER()::INT AS "total"'));

        const unresolvedMemberships = await Db.query(query)
            .then(({rows}) => rows || [])

        const total = await Db.query(totalQuery).then(
            ({ rows: [{total}] }) => total
        );

        return {
            unresolvedMemberships,
            metadata: {
                total,
                hasMore: offset + limit < total,
                nextOffset: offset + limit,
            },
        };
     }
}

module.exports = new UnresolvedMembershipsService();
