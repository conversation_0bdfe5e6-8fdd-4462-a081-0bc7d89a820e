const squel = require("../../squel");

class UnresolvedMembershipService {
    constructor(SportEngineUtils) {
        /**
         * @type {SportEngineUtilsService}
         */
        this.Utils = SportEngineUtils;
    }

    async createUnresolvedMembership (member) {
        const membership = this._prepareMembership(member);
        return this._insertMembership(membership);
    }

    _prepareMembership(member) {
        return {
            membership_definition_id: member[this.Utils.SE_FIELDS.MEMBERSHIP_DEFINITION_ID],
            club_code: member[this.Utils.SE_FIELDS.CLUB_CODE],
            region: member[this.Utils.SE_FIELDS.USAV_REGION],
            organization_code: member[this.Utils.SE_FIELDS.ORGANIZATION_CODE],
            first_name: member[this.Utils.SE_FIELDS.FIRST],
            last_name: member[this.Utils.SE_FIELDS.LAST],
            birthdate: member[this.Utils.SE_FIELDS.BIRTHDATE] || null,
            membership_name: member[this.Utils.SE_FIELDS.MEMBERSHIP_NAME],
            membership_status: member[this.Utils.SE_FIELDS.MEMBERSHIP_STATUS],
            membership_start_date:
                member[this.Utils.SE_FIELDS.MEMBERSHIP_START_DATE],
            membership_end_date:
                member[this.Utils.SE_FIELDS.MEMBERSHIP_END_DATE],
            age_group: member[this.Utils.UM_FIELDS.MEMBER_TYPE],
            error_message: member[this.Utils.UM_FIELDS.MESSAGE],
            membership_data: JSON.stringify(member)
        };
    }

    _insertMembership (membership) {
        const unresolved_membership =
            Object.assign({}, membership);
        const query = squel.insert().into('unresolved_memberships')
            .setFields(unresolved_membership)
            .returning(
                '*, TO_CHAR(membership_end_date, \'MM/DD/YYYY HH24:MI:SS\') "membership_end_date"',
            )
            .onConflict(['region', 'club_code', 'organization_code'], {});

        return Db.query(query).then(({rows}) => rows[0] || {});
    }
}

module.exports = UnresolvedMembershipService;
