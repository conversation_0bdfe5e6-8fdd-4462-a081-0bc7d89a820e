

class ChargeUpdateService {
    constructor(connect) {
        this.connect = connect;
    }

    updateStripeCharge (chargeData, chargeID) {
        if(_.isEmpty(chargeData)) {
            throw new Error('Data for update required');
        }

        if(!chargeID) {
            throw new Error('Charge ID required');
        }

        return this.updateStripeChargeMetadata(chargeData, chargeID);
    }

    updateConnectedAccountStripeCharge (paymentData, paymentID, accountID) {
        if(_.isEmpty(paymentData)) {
            throw new Error('Data for update required');
        }

        if(!paymentID) {
            throw new Error('Charge ID required');
        }

        if(!accountID) {
            throw new Error('Stripe Account ID required');
        }

        return this.updateStripeChargeMetadata(paymentData, paymentID, accountID);
    }

    async updateStripeChargeMetadata (chargeData, chargeID, accountID) {
        let stripe = await this.connect.getInstance();

        if(accountID) {
            return stripe.charges.update(chargeID, chargeData, { stripeAccount: accountID});
        } else {
            return stripe.charges.update(chargeID, chargeData);
        }
    }
}

module.exports = ChargeUpdateService;
