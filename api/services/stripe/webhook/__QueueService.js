const Queue = require('bull');
const { STRIPE_WEBHOOK_QUEUE } = require('./../../../constants/workers-queue');

class QueueService {
    constructor() {
        this.queue = new Queue(STRIPE_WEBHOOK_QUEUE, sails.config.redis_queue.workers_queue);
    }

    async addJob(webhookData, options = {}) {
        await this.queue.add(webhookData, {
            ...options,
            backoff: { type: 'webhookBackoff' },
        });
    }

    async moveToDeadLetterQueue(queueName, payload) {
        throw new Error('Not implemented');
    }
}

module.exports = QueueService;
