const swUtils = require('../../../../lib/swUtils');
const approx = swUtils.normalizeNumber.bind(swUtils);

class ChargeRowService {
    constructor (parent) {
        this._ParentService = parent;
    }

    async modifyRow ({chargeID, stripeAccountID = null}, method, tr) {
        if (!chargeID) {
            throw new Error('No charge ID passed!');
        }

        loggers.debug_log.verbose('Expanding charge object ...')

        let charge = await this._ParentService.expandStripeCharge(chargeID);
        let stripePaymentID = charge.transfer && charge.transfer.destination_payment || null;

        loggers.debug_log.verbose('Preparing charge object ...')

        let prepared = this.__generateStripeChargeRowData__(charge, stripePaymentID, stripeAccountID);
        if(_.isString(stripeAccountID) && ( stripeAccountID.length > 0 )) {
            prepared.stripe_account_id = stripeAccountID;
        }

        if (method === 'update') {
            loggers.debug_log.verbose('Updating "stripe_charge" row ...')
        } else if (method === 'insert') {
            loggers.debug_log.verbose('Creating "stripe_charge" row ...')
        } else {
            throw new Error(`Method '${method} was not recognized!'`);
        }
        const isSaved = await this.__saveStripeChargeDBRow__(prepared, method, tr);

        if (!isSaved) {
            throw new Error('Stripe Charge row was not saved!');
        }
    }

    __generateStripeChargeRowData__ (charge, stripePaymentID, stripeAccountID) {
        let amount 				= charge.amount,
            refundedAmount 		= charge.amount_refunded || 0;

        amount = approx((amount - refundedAmount) / 100);

        let stripeProcessingFee = 0;
        let swCollectedFee = 0;

        if(!_.isEmpty(charge.balance_transaction)) {
            stripeProcessingFee = charge.balance_transaction.fee;

            let stripeProcessingFeeRefunded = this.__countRefundedFee__(charge.refunds.data);

            stripeProcessingFee = approx((stripeProcessingFee + stripeProcessingFeeRefunded) / 100);

            if (charge.application_fee) {
                let swCollectedFeeRefunded = charge.application_fee.amount_refunded || 0;

                swCollectedFee = charge.application_fee.amount || 0;
                swCollectedFee = approx((swCollectedFee - swCollectedFeeRefunded) / 100);
            }
        }

        return {
            stripe_charge_id 		: charge.id,
            fee 					: stripeProcessingFee,
            balance_transaction 	: charge.balance_transaction || {},
            collected_fee 			: swCollectedFee,
            type                    : charge.metadata.purchase_type === 'custom' ? 'default' : 'connect',
            amount,
            stripe_payment_id: stripePaymentID,
            stripe_account_id: stripeAccountID,
        }
    }

    __countRefundedFee__ (refundsList) {
        if (!Array.isArray(refundsList)) {
            throw new Error('Expecting refunds list to be an array')
        }

        return refundsList.reduce((sum, refund) => {
            if (refund.status === 'succeeded') {
                /* we have negative fee for refunds */
                sum += refund.balance_transaction.fee || 0
            }

            return approx(sum);
        }, 0);
    }

    // covered 😄👍
    __saveStripeChargeDBRow__(charge, method, tr = Db) {
        const query = knex('stripe_charge');
        if(method === 'update') {
            query.update(charge)
                .where('stripe_charge_id', charge.stripe_charge_id)
        }
        else {
            query.insert(charge);
        }

        return tr.query(query)
            .then(res => ( res.rowCount > 0 ));
    }
}

module.exports = ChargeRowService;
