'use strict';

const INVOICE       = 'invoice';
const TYPE_CHANGE   = 'type_change';
const CLIENT_SIDE   = 'client';
const SERVER_SIDE   = 'server';

function __getSQL__ (purpose, side = SERVER_SIDE) {
    let isServerSide = (side === SERVER_SIDE);
    const isTypeChange = purpose === TYPE_CHANGE;

    let query = 
        `SELECT ${
                isServerSide ? 'p.purchase_id, p.type' : 'u.country'
            },
            ${ isTypeChange ? 'p.net_profit AS amount' : 'p.amount' },
             
             (CASE WHEN p.first IS NOT NULL THEN p.first ELSE u.first END) "first", 
             (CASE WHEN p.last IS NOT NULL THEN p.last ELSE u.last END) "last",
             p.email, p.phone, COALESCE(p.tickets_additional, '{}'::JSON) "additional",
             p.ticket_barcode "barcode", (
                 SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("pt"))), '[]'::JSON)
                 FROM (
                     SELECT
                         ec.name "camp_name", 
                         et.label, 
                         pt.quantity, 
                         pt.amount ${ isServerSide ? '"price"' : '' }, 
                         pt.ticket_price, 
                         pt.ticket_fee "fee" ${
                            isServerSide
                                ? 
                                    `, et.sort_order,
                                     et.event_ticket_id "id", 
                                     ec.event_camp_id "camp_id"`
                                : ''
                         }                         
                     FROM "purchase_ticket" pt 
                     INNER JOIN "event_ticket" et 
                         ON et.event_ticket_id = pt.event_ticket_id 
                     LEFT JOIN "event_camp" ec 
                         ON ec.event_camp_id = et.event_camp_id
                     WHERE pt.purchase_id = p.purchase_id 
                         AND pt.canceled IS NULL
                         AND pt.quantity > 0
                 ) "pt"
             ) "items" 
             ${
                isServerSide 
                    ? `, ( 
                        CASE 
                            WHEN e.ticket_camps_registration IS TRUE 
                                THEN 'camps'
                            ELSE 'tickets'
                        END
                     ) "sales_type",
                     e.event_tickets_code "event_code"`
                    : ''
             }
         FROM "purchase" p
         INNER JOIN "user" u 
             ON u.user_id = p.user_id
         INNER JOIN "event" e 
            ON e.event_id = p.event_id
         WHERE p.ticket_barcode = $2
              AND e.event_tickets_code = $1`;

    if (purpose === INVOICE) {
        query +=
            `
              AND p.type = 'waitlist'
              AND p.status IN ('pending', 'pending_card', 'pending_check', 'pending_ach')`
    } else if (purpose === TYPE_CHANGE) {
        query += 
            `
            AND e.ticket_camps_registration IS TRUE
            AND e."tickets_purchase_by_card" IS TRUE
            AND (e."date_end" > (NOW() AT TIME ZONE e."timezone"))
            AND p."type" = 'check'
            AND p."status" = 'pending'`;
    }

    return query;
}

function getPurchase (eventId, invoiceId, query) {
    return Db.query(query, [eventId, invoiceId])
    .then(res => res.rows[0] || null);
}

// ---

function getWaitlistedPurchase (eventId, invoiceId) {
    let query = __getSQL__(INVOICE);

    return getPurchase(eventId, invoiceId, query);
}

function getCheckPurchase (eventId, invoiceId) {
    let query = __getSQL__(TYPE_CHANGE);

    return getPurchase(eventId, invoiceId, query);
}

// ---

function getInvoiceData (eventId, invoiceId) {
    let query = __getSQL__(INVOICE, CLIENT_SIDE);

    return getPurchase(eventId, invoiceId, query);
}

function getPurchaseForTypeChange (eventId, invoiceId) {
    let query = __getSQL__(TYPE_CHANGE, CLIENT_SIDE);

    return getPurchase(eventId, invoiceId, query);
}

function getPaidPendingPayment (eventID, purchaseID) {
    return Db.query(
        `WITH "tickets" AS (
            SELECT 
                ticket."amount",
                ticket."purchase_id",
                ticket."ticket_barcode" "barcode",
                ticket."first",
                ticket."last",
                ticket."user_id",
                ticket."status",
                EXTRACT(EPOCH FROM ticket.created)::INT "created",
                ticket."event_id",
                ticket."is_ticket",
                pt."quantity",
                pt."available",
                pt."purchase_ticket_id",
                pt."event_ticket_id",
                (pt."available" = 0) "is_scanned",
                et."label",
                et."sort_order" "order",
                ticket.zip AS user_zip
            FROM "purchase" ticket 
            INNER JOIN "purchase_ticket" pt 
                ON pt."purchase_id" = ticket."purchase_id"
            INNER JOIN "event_ticket" et 
                ON et."event_ticket_id" = pt."event_ticket_id"
            WHERE (
                ticket."purchase_id" = $1 OR 
                ticket."linked_purchase_id" = $1
             )
                AND ticket."event_id" = $2
                AND ticket."is_ticket" IS TRUE
        )
        SELECT 
            payment."purchase_id" "id",
            payment."amount", 
            payment."first",
            payment."last",
            payment."user_id",
            payment."email",
            payment."phone",
            payment."type" "method", (
                SELECT 
                    COALESCE(JSON_AGG("t"), '[]'::JSON)
                FROM (
                    SELECT * FROM "tickets"
                    WHERE "status" = 'paid'
                ) "t"
            ) "tickets", (
                SELECT 
                    COALESCE(JSON_AGG("t"), '[]'::JSON)
                FROM (
                    SELECT * FROM "tickets"
                    WHERE "status" = 'canceled'
                ) "t"
            ) "canceled_tickets",
            payment.zip AS user_zip,
            TO_CHAR(payment.date_paid::timestamptz AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') purchased_at
         FROM "purchase" payment
         INNER JOIN "purchase_history" ph 
            ON ph."purchase_id" = payment."purchase_id"
            AND ph."action" = 'pending-payment.paid'
        INNER JOIN "event" e
            ON e.event_id = $2
         WHERE payment."purchase_id" = $1 
            AND payment."payment_for" = 'tickets' 
            AND payment."event_id" = $2
            AND payment."is_payment" IS TRUE
            AND payment."status" = 'paid'`,
        [purchaseID, eventID]
    ).then(res => res.rows[0])
        .then(data => {
            let dataForReceiptSending = _.omit(data, ['canceled_tickets']);
            let payment               = _.omit(data, ['user_id', 'first', 'last', 'email', 'phone']);
            payment.tickets           = payment.tickets.map(item =>
                                            _.omit(item, ['user_id', 'order', 'created', 'event_id', 'is_ticket'])
                                        );

            return { dataForReceiptSending, payment };
        })
}

module.exports = {
    getWaitlistedPurchase, 
    getCheckPurchase,

    getInvoiceData,
    getPurchaseForTypeChange,
    getPaidPendingPayment
};
