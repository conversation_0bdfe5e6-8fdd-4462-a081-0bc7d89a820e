class BalanceInformationService {
    constructor() {
        this._before = {};
        this._after = {};
        this._sw_fee = null;
    }

    set before(value) {
        this._before = value;
    }

    set after(value) {
        this._after = value;
    }

    set sw_fee(value) {
        this._sw_fee = value;
    }

    async updateBalanceInformation(tr, purchaseID) {
        const query = squel
            .update()
            .table('purchase')
            .set('teams_balance_info', JSON.stringify({
                before: this._before,
                after: this._after,
                sw_fee: this._sw_fee,
            }))
            .where('purchase_id = ?', purchaseID);

        return tr.query(query);
    }
}

module.exports = BalanceInformationService;
