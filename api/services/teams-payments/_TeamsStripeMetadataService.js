

class TeamsStripeMetadataService {
    generate (data) {
        const paymentCommonMetadataFields = StripeService.paymentCommonMetadataFields({
            netProfit: data.totals.netProfit,
            stripeFee: data.totals.stripeFee,
            extraFee: data.totals.extraFee,
            swFee: data.totals.sw_fee
        })

        return {
            purchase_id     : data.purchase_id,
            project         : 'sw',
            email           : data.user.email,
            phone           : data.user.phone,
            event_name      : data.event_name,
            event_id        : data.event_id,
            link_to_purchase: this.getPurchasePage(data.event_id, data.purchase_id),
            ...paymentCommonMetadataFields
        }
    }

    getPurchasePage (eventID, purchaseID) {
        if(eventID && purchaseID) {
            return `${sails.config.urls.main_app.baseUrl}/#/event/${eventID}/payments?purchase_id=${purchaseID}`;
        }

        return undefined;
    }
}

module.exports = new TeamsStripeMetadataService();
