'use strict';

const PartialRefundService          = require('./refunds/__PartialRefundService');
const FullRefundService             = require('./refunds/__FullRefundService');
const StripeDashboardRefundService  = require('./refunds/__StripeDashboardRefundService');

const RefundNotificationsService = require('./refunds/__RefundNotificationsService');

const argv          = require('optimist').argv;
const SETTINGS_KEY  = argv.prod?'stripe_connect':'stripe_connect_dev';

const StripeConnect = require('../../lib/StripeConnect');

const swUtils = require('../../lib/swUtils');

class RefundService {
    constructor () {
        this.utils = swUtils;

        this.STRIPE_SETTINGS_KEY = SETTINGS_KEY;

        this.partial = new PartialRefundService(this);
        this.full = new FullRefundService(this);
        this.stripeDashboard = new StripeDashboardRefundService(RefundNotificationsService);
    }

    makeStripeRefund (data, useConnect, skipFeeRefund) {
        if(useConnect) {
            return StripeConnect.refundClientPayment(data, null, skipFeeRefund);
        } else {
            return StripeService.refund(data);
        }
    }
    
    async saveRefundHistory ({ tr, purchaseID, userID, isPartial, amount = 0 }) {
        let action = 'purchase.team.refund.full';
        
        if(isPartial) {
            action = 'purchase.team.refund.partial';
        }
        
        const query = knex('purchase_history')
            .insert({
                purchase_id : purchaseID,
                user_id     : userID,
                amount      : amount,
                action      : action
            });
        
        await tr.query(query);
    }
    
}

module.exports = new RefundService();
