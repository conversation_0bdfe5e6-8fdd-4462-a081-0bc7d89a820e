const TeamsSource = require('./sources/TeamCode');
const CustomSource = require('./sources/CustomCode');

class TicketBuyEntryCodeList {
    constructor (activeSources) {
        this.activeSources = activeSources;

        this.sources = [];
    }

    get LIMIT () {
        return 100;
    }

    get ALL_SOURCES () {
        return {
            teams: TeamsSource,
            custom: CustomSource,
        }
    }

    collectSources () {
        let activeSourcesNames = this.activeSources.map(item => item.name);

        this.sources = Object.keys(this.ALL_SOURCES).reduce((all, sourceName) => {
            if(activeSourcesNames.includes(sourceName)) {
                all[sourceName] = this.ALL_SOURCES[sourceName];
            }

            return all;
        }, {});
    }

    async validate (eventID, code) {
        if(!code) {
            throw { validation: 'Code not found' };
        }

        if(!eventID) {
            throw { validation: 'Event ID not found' };
        }

        let success = false;

        this.collectSources();

        if(!_.isEmpty(this.sources)) {
            for(let sourceName of Object.keys(this.sources)) {
                let source = this.sources[sourceName];

                let [{settings: activeSourceSettings} = {}]
                    = this.activeSources.filter(item => item.name === sourceName);

                if(!_.isFunction(source.search)) {
                    loggers.errors_log.error(`Search function for ${source.NAME} not found`);
                } else {
                    let exists = await source.search(eventID, code, activeSourceSettings);

                    if(exists) {
                        success = true;
                    }
                }
            }
        }

        return { success };
    }

    async list (eventID, filters) {
        if(!eventID) {
            throw { validation: 'Event ID not found' };
        }

        //union group by problem - https://github.com/knex/knex/issues/913
        let query = knex.queryBuilder().select({
            ticket_buy_entry_code: knex.raw('NULL'),
            additional_fields: knex.raw(`'{}'::JSONB`),
        }).where(false);

        let unions = []
        let result = [];

        this.collectSources();

        if(!_.isEmpty(this.sources)) {
            for(let sourceName of Object.keys(this.sources)) {
                let source = this.sources[sourceName];

                let [{settings: activeSourceSettings} = {}]
                    = this.activeSources.filter(item => item.name === sourceName);

                if(!_.isFunction(source.list)) {
                    loggers.errors_log.error(`List function for ${source.NAME} not found`);
                } else {
                    let sourceQuery = source.list(eventID, filters, activeSourceSettings);

                    unions.push(sourceQuery);
                }
            }
        }

        if(query) {
            if(unions.length) {
                query = knex.unionAll([query, ...unions], true /* wrap - the queries will be individually wrapped in parentheses */);
            }

            let baseQuery = knex.queryBuilder()
                .select(
                    knex.raw('COUNT(*) OVER()::INT AS total_rows'),
                    'data.*',
                    knex.raw('COUNT(pt.*) FILTER (WHERE p.purchase_id IS NOT NULL) AS bought_qty')
                )
                .from(query.as('data'))
                .leftJoin(
                    'purchase_ticket AS pt',
                    knex.raw('LOWER(pt.ticket_buy_entry_code) = LOWER(data.ticket_buy_entry_code)')
                )
                .leftJoin('purchase AS p',
                    (table) => {
                        table
                            .on(`p.event_id`, eventID)
                            .on(knex.raw(`p.purchase_id = pt.purchase_id`))
                })
                .groupBy('data.ticket_buy_entry_code', 'data.additional_fields')
                .limit(this.LIMIT);

            if(filters.page) {
                baseQuery.offset((filters.page - 1) * this.LIMIT);
            }

            this.__addOrdering(baseQuery, filters);

            result = await Db.query(baseQuery).then(({rows}) => rows);
        }

        return result;
    }

    __addOrdering (query, filters) {
        if(!filters.order) {
            query.orderBy('data.ticket_buy_entry_code');
        } else {
            let orderFieldName;
            let orderDirection;

            if(filters.order === 'team_name') {
                orderFieldName = knex.raw(
                    `(SELECT fields -> 'team_name' FROM JSONB_ARRAY_ELEMENTS(data.additional_fields) fields)`
                )
            }

            if(filters.order === 'ticket_buy_entry_code') {
                orderFieldName = 'data.ticket_buy_entry_code';
            }

            if(filters.order === 'bought_qty') {
                orderFieldName = 'bought_qty';
            }

            orderDirection = filters.revert === 'true' ? 'desc' : 'asc';

            query.orderBy(orderFieldName, orderDirection);
        }
    }
}

module.exports = TicketBuyEntryCodeList;
