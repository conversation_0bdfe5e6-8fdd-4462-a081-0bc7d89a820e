const { FEE_PAYER } = require('../../../constants/payments');
const swUtils = require('../../../lib/swUtils');

class AbstractSWTFeeStrategy {
    constructor(settings, paymentMethod) {
        this.settings = settings;
        this.paymentMethod = paymentMethod;
    }

    calculateTotals(amount, applicationFee) {
        const subtotal = this.__calculateSubtotal(amount, applicationFee);

        const providerFee = this.__getProviderFee(subtotal);

        const providerApplicationFee = this.__calculateProviderApplicationFee(
            applicationFee,
            providerFee
        );

        const total = this.__calculateTotal(subtotal, providerFee);
        const netProfit = total - applicationFee - providerFee;

        return {
            applicationFee: swUtils.normalizeNumber(providerApplicationFee),
            total: swUtils.normalizeNumber(total),
            netProfit: swUtils.normalizeNumber(netProfit),
            providerFee,
            swFee: applicationFee,
        };
    }

    __getProviderFee(subtotal) {
        const { defaultFee, customerFee } =
            this.calculatePaymentProviderFee(subtotal);

        if (this.__isBuyerPayingProviderFee()) {
            return customerFee;
        }

        return defaultFee;
    }

    /**
     * Some providers take money from platform, some from merchant account.
     * If provider takes money from platform, we add providerFee to applicationFee to compensate
     * Otherwise, just return applicationFee
     * @param {Number} applicationFee
     * @param {Number} providerFee
     */
    __calculateProviderApplicationFee(applicationFee, providerFee) {
        if (this.__doesPlatformCoverProviderFee()) {
            return applicationFee + providerFee;
        }

        return applicationFee;
    }

    __calculateTotal(subtotal, providerFee) {
        if (this.__isBuyerPayingProviderFee()) {
            return subtotal + providerFee;
        }

        return subtotal;
    }

    __calculateSubtotal(amount, applicationFee) {
        if (this.__isBuyerPayingApplicationFee()) {
            return amount + applicationFee;
        }

        return amount;
    }

    calculatePaymentProviderFee(amount) {
        switch (this.paymentMethod) {
            case SWTPaymentsService.CARD_METHOD:
                return this.__calculateCardFee(amount);
            case SWTPaymentsService.ACH_METHOD:
                return this.__calculateACHFee(amount);
            case SWTPaymentsService.CASH_METHOD:
                return this.__calculateCashFee(amount);
            case SWTPaymentsService.CHECK_METHOD:
                return this.__calculateCheckFee(amount);
            default:
                throw new Error(`Invalid payment method ${this.paymentMethod}`);
        }
    }

    __doesPlatformCoverProviderFee() {
        throw new Error('__doesPlatformCoverProviderFee not implemented');
    }

    __isBuyerPayingProviderFee() {
        throw new Error('__isBuyerPayingProviderFee not implemented');
    }

    __isBuyerPayingApplicationFee() {
        return this.settings.sw_fee_payer === FEE_PAYER.BUYER;
    }

    __calculateCardFee() {
        throw new Error('__calculateCardFee not implemented');
    }

    __calculateACHFee() {
        throw new Error('__calculateACHFee not implemented');
    }

    __calculateCashFee() {
        return {
            defaultFee: 0,
            customerFee: 0,
        };
    }

    __calculateCheckFee() {
        return {
            defaultFee: 0,
            customerFee: 0,
        };
    }
}

module.exports = AbstractSWTFeeStrategy;
