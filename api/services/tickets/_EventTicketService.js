const validationSchemas = require('../../validation-schemas/tickets');
const { FEE_PAYER } = require("../../constants/payments");
const { MINIMUM_TICKET_MARGIN } = require("../../constants/tickets");

class EventTicketService {
    constructor () {}

    async getEventTicketTypes ({ ticketBarcode, eventID }) {
        const query = `
            SELECT et.event_ticket_id,
                   et.short_label,
                   et.label,
                   (SELECT
                        COALESCE(
                            JSONB_OBJECT_AGG(
                                TO_CHAR(TO_TIMESTAMP(vd::TEXT, 'YYYY-MM-DD'), 'Dy, Mon DD'),
                                TRUE
                            ),
                            '{}'::JSONB
                        )
                    FROM JSONB_OBJECT_KEYS(et.valid_dates) vd) AS valid_dates
            FROM event e
                     JOIN purchase p ON p.ticket_barcode = $1 AND p.event_id = e.event_id
                     JOIN purchase_ticket pt ON pt.purchase_id = p.purchase_id
                     JOIN event_ticket et_current ON e.event_id = et_current.event_id 
                        AND et_current.ticket_type = $2 
                        AND pt.event_ticket_id = et_current.event_ticket_id
                     JOIN event_ticket et ON e.event_id = et.event_id 
                        AND et.ticket_type = $2
                        AND pt.event_ticket_id <> et.event_ticket_id
                        AND 
                            COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee, 0) = 
                            COALESCE(NULLIF(et_current.application_fee, 0), e.tickets_sw_fee, 0)
                        AND et.current_price = p.amount + COALESCE(p.amount_refunded, 0)
            WHERE (NOW() AT TIME ZONE e.timezone) <= e.date_end
              AND (e.tickets_settings ->> 'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
              ${eventID ? `AND e.event_id = $3` : ''}`;

        const params = [ticketBarcode, TicketsService.EVENT_TICKET_TYPE.DAILY];

        if(eventID) {
            params.push(eventID);
        }

        const {
            rows: eventTicketTypes = []
        } = await Db.query(query, params);

        return eventTicketTypes;
    }

    async changeEventTicketType ({ eventID, ticketBarcode, eventTicketID, user }) {
        const newEventTicketTypeData = await this.__getNewEventTicketData ({ eventTicketID, eventID, ticketBarcode });

        let tr;

        try {
            tr = await Db.begin();

            const oldEventTicketData = await this.__updatePurchaseTicketRow(tr, { eventID, ticketBarcode, eventTicketID });
            await this.__saveEventTicketChangeToHistory(tr, user, newEventTicketTypeData, oldEventTicketData);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async createTicketType (eventID, ticketType) {
        let validationResult = validationSchemas.createTicketType.validate(ticketType, {
            abortEarly: false
        });

        if (validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }

        const { sw_fee } = await this._prepareAndValidatePrice(eventID, ticketType);

        try {
            ticketType.sort_order = await this.__getMaxSortOrder(eventID);

            const { application_fee, event_ticket_id } = await this.__insertEventTicket(eventID, ticketType);
            const swFee = application_fee || sw_fee || 0;

            const {
                current_price,
                is_free,
            } = await this.__setCurrentPrice(eventID, event_ticket_id);

            return {
                event_ticket_id,
                sw_fee: swFee,
                current_price,
                is_free
            }
        } catch (err) {
            throw err;
        }
    }

    async updateTicketType (eventID, eventTicketID, ticketTypeUpdateData) {
        let validationResult = validationSchemas.updateTicketType.validate(ticketTypeUpdateData, {
            abortEarly: false
        });

        if (validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }

        await this._prepareAndValidatePrice(eventID, ticketTypeUpdateData);

        try {
            ticketTypeUpdateData.prices = JSON.stringify(ticketTypeUpdateData.prices);
        } catch (e) {
            loggers.errors_log.error(e.message);

            throw { validation: 'Invalid price changes values' };
        }

        try {
            ticketTypeUpdateData.valid_dates = JSON.stringify(ticketTypeUpdateData.valid_dates);
        } catch (e) {
            loggers.errors_log.error(e.message);

            throw { validation: 'Invalid valid dates values' };
        }

        try {
            await this.__updateEventTicketRow(eventID, eventTicketID, ticketTypeUpdateData);

            const {
                current_price,
                is_free,
            } = await this.__setCurrentPrice(eventID, eventTicketID);

            return {
                current_price,
                is_free
            }
        } catch (err) {
            throw err;
        }
    }

    async __getNewEventTicketData ({ eventTicketID, eventID, ticketBarcode }) {
        const ticketTypes = await this.getEventTicketTypes({ eventID, ticketBarcode });
        const [newTicketTypeData] = ticketTypes.filter(tt => tt.event_ticket_id === eventTicketID);

        if(_.isEmpty(newTicketTypeData)) {
            throw { validation: 'Event ticket type change not allowed' };
        }

        return newTicketTypeData;
    }

    async __updatePurchaseTicketRow (tr, { eventID, ticketBarcode, eventTicketID }) {
        const query = `
            WITH current_data AS (
                SELECT 
                    et.label, 
                    pt.purchase_ticket_id, 
                    p.purchase_id
                FROM event_ticket et
                JOIN purchase_ticket pt ON pt.event_ticket_id = et.event_ticket_id
                JOIN purchase p ON pt.purchase_id = p.purchase_id
                WHERE p.ticket_barcode = $2
                    AND p.deactivated_at IS NULL
                    AND p.scanned_at IS NULL
                    AND p.canceled_date IS NULL
                    AND et.ticket_type = $3
                    ${eventID ? `AND p.event_id = $4`: ''}
            )
            UPDATE purchase_ticket pt
            SET event_ticket_id = $1
            WHERE pt.purchase_ticket_id = (SELECT current_data.purchase_ticket_id FROM current_data)
            RETURNING (SELECT row_to_json(d) FROM (SELECT * FROM current_data) AS d) old_data`;

        const params = [eventTicketID, ticketBarcode, TicketsService.EVENT_TICKET_TYPE.DAILY]

        if(eventID) {
            params.push(eventID)
        }

        const { rowCount, rows } = await tr.query(query, params);

        if(!rowCount) {
            throw { validation: 'Ticket not found' };
        }

        return rows[0].old_data;
    }

    async __saveEventTicketChangeToHistory (tr, user, newEventTicketTypeData, oldEventTicketTypeData) {

        const description = `Event Ticket Type for ticket changed from "${
            oldEventTicketTypeData.label}" to "${newEventTicketTypeData.label}"`;

        const query = knex('purchase_history as ph')
            .insert({
                purchase_id: oldEventTicketTypeData.purchase_id,
                purchase_ticket_id: oldEventTicketTypeData.purchase_ticket_id,
                user_id: user.user_id,
                action: 'ticket.event_ticket.changed',
                description,
                notes: knex.raw(`FORMAT('Changed by %s %s', INITCAP(?), INITCAP(?))`, [user.first, user.last])
            });

        const { rowCount } = await tr.query(query);

        if(!rowCount) {
            throw { validation: 'Can\'t save history row' };
        }
    }

    _validateEventID(eventID) {
        if (!eventID) {
            throw { validation: 'Event id required' };
        }
    }

    _validateChangeData({ data, schemaType, errorMessage }) {
        const schema = validationSchemas[schemaType];
        const validationResult = schema.validate(data);

        if (validationResult.error) {
            throw { validation: errorMessage };
        }
    }

    _handlePriceChangeError(err) {
        loggers.errors_log.error('Error updating price change:', err);
        throw err;
    }

    async _processPriceChange(eventID, updatedOptions) {
        await this._updateTicketOptions(eventID, updatedOptions);
        await TicketsCron.setCurrentPrice(eventID);
    }

    async createPriceChange(eventID, changeData) {
        this._validateEventID(eventID);
        this._validateChangeData({
            data: changeData,
            schemaType: 'addPrice',
            errorMessage: 'Invalid date. Please, check input values and try again'
        });

        try {
            const { change } = changeData;
            const options = await this._getEventTicketOptions(eventID);
            const data = _.isEmpty(options) ? change : _.extend(options, change);
            const updatedOptions = JSON.stringify(data);
            await this._processPriceChange(eventID, updatedOptions);
        } catch (err) {
            this._handlePriceChangeError(err);
        }
    }

    async updatePriceChange(eventID, changeData) {
        this._validateEventID(eventID);
        this._validateChangeData({
            data: changeData,
            schemaType: 'updatePrice',
            errorMessage: 'Invalid data provided. Please, check entered values and try again'
        });

        try {
            const { key, value } = changeData;
            const options = await this._getEventTicketOptions(eventID);
            options[key] = value;
            const updatedOptions = JSON.stringify(options);

            await this._processPriceChange(eventID, updatedOptions);
        } catch (err) {
            this._handlePriceChangeError(err);
        }
    }

    async _getEventTicketOptions(eventId) {
        const query = knex("event")
                .select(knex.raw("COALESCE(tickets_options, '{}'::json) AS tickets_options"))
                .where("event_id", eventId)
                .first();

        const { rows: [event] } = await Db.query(query);

        if (_.isEmpty(event)) {
            throw { validation: 'Event not found' };
        }

        return event.tickets_options;
    }

    async _updateTicketOptions(eventId, options) {
        await Db.query(
            knex('event')
                .update({ tickets_options: options })
                .where('event_id', eventId)
        );
    }

    async removePriceChange(eventId, changeId) {
        this._validateEventID(eventId);
        if (!changeId) {
            throw { validation: '"change" required' };
        }

        try {
            const [changes, tickets] = await Promise.all([
                this._removeChangeFromOptions(eventId, changeId),
                this._updateTicketPrices(eventId, changeId)
            ]);

            const updatedCurrentPrices = await TicketsCron.setCurrentPrice(eventId);

            const updatedTickets = this.__updateTicketsWithCurrentPrices({ eventId, tickets, currentPrices: updatedCurrentPrices });

            return { changes, tickets: updatedTickets };
        } catch (err) {
            loggers.errors_log.error('Error removing price change:', err);
            throw err;
        }
    }

    async _getTicketsOptions(eventId) {
        const query = knex("event")
            .select(knex.raw('tickets_options::json as tickets_options'))
            .where("event_id", eventId)
            .first();

        const { rows: [event] } = await Db.query(query);

        if (_.isEmpty(event.tickets_options)) {
            throw { validation: 'No changes in passed event' };
        }

        return event.tickets_options;
    }

    async _removeChangeFromOptions(eventId, changeId) {
        try {
            const options = await this._getTicketsOptions(eventId);
            const updatedChanges = this._shiftTicketsPrices(changeId, options);

            if (!updatedChanges) {
                return {};
            }

            const updatedChangesStr = JSON.stringify(updatedChanges);

            await this._updateTicketOptions(eventId, updatedChangesStr);

            return updatedChanges;
        } catch (err) {
            loggers.errors_log.error('Error removing price change:', err);
            throw err;
        }
    }

    async _updateTicketPrices(eventId, changeId) {
        try {
            const tickets = await this._getEventTickets(eventId);

            const { updatedTickets, allTickets } = this._adjustTicketPriceIndices(tickets, changeId);

            const updateQueries = updatedTickets
                .map(ticket => this._updateTicketPriceInDb({ ticket, eventId }));

            await Promise.all(updateQueries);

            return allTickets;
        } catch (err) {
            loggers.errors_log.error('Error updating ticket prices:', err);
            throw err;
        }
    }

    _shiftTicketsPrices(removalIndex, pricesObj) {
        const keys = Object.keys(pricesObj);
        const resultObj = {};

        if (+keys[keys.length - 1] < removalIndex) return { ...pricesObj };

        for (const key of keys) {
            const currentKey = +key;

            if (currentKey > removalIndex) {
                resultObj[currentKey - 1] = _.clone(pricesObj[currentKey]);
            } else if (currentKey < removalIndex) {
                resultObj[currentKey] = _.clone(pricesObj[currentKey]);
            }
        }
        return resultObj;
    }

    async __getMaxSortOrder (eventID) {
        let maxOrder = 0;

        const query = `SELECT  
                                   COALESCE(MAX(et.sort_order), 0)::INT max_order 
                               FROM event_ticket et 
                               WHERE et.event_id = $1`

        const { rows: [eventTicket] } = await Db.query(query, [eventID]);

        if(!_.isEmpty(eventTicket)) {
            maxOrder = eventTicket?.max_order;
        }

        return ++maxOrder;
    }

    async __insertEventTicket (eventID, ticketTypeData) {
        let data = _.omit(ticketTypeData, 'event_ticket_id');
        data.event_id = eventID;

        const query = knex('event_ticket as et')
            .insert(data)
            .returning([
                'et.event_ticket_id',
                    knex.raw('NULLIF(et.application_fee, 0) as application_fee')
                ]);

        const { rows: [eventTicket] } = await Db.query(query);

        if(_.isEmpty(eventTicket)) {
            throw { validation: 'Event Ticket bot created' };
        }

        return eventTicket;
    }

    async __setCurrentPrice (eventID, eventTicketID) {
        const updatedCurrentPrice = await TicketsCron.setCurrentPrice(eventID, eventTicketID);

        if(_.isEmpty(updatedCurrentPrice)) {
            throw { validation: 'Current Price not updated' };
        }

        let { price: current_price, is_free } = updatedCurrentPrice[eventID][eventTicketID] || {};

        return {
            current_price,
            is_free
        }
    }

    async __updateEventTicketRow (eventID, eventTicketID, updateData) {
        const query = knex('event_ticket as et')
            .update(updateData)
            .where({
                event_ticket_id: eventTicketID,
                event_id: eventID
            });

        const { rowCount } = await Db.query(query);

        if(!rowCount) {
            throw { validation: 'Event Ticket not updated' };
        }
    }

    async _validateMinimumPrice(eventFeeData, price) {
        const minPrice = this._calculateMinimumPrice(price, eventFeeData);

        if (price < minPrice) {
            throw {
                validation: `Price must be greater than Service Fee. Minimum price: $${minPrice.toFixed(2)}`
            };
        }
    }

    async _getEventFeeData(eventID) {
        const { rows } = await Db.query(`
            SELECT COALESCE(e.tickets_sw_fee, 0) as "sw_fee", stripe_tickets_percent,
                stripe_tickets_fixed, stripe_tickets_fee_payer,
                COALESCE((e.tickets_payment_provider), '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "payment_provider"
            FROM "event" e
            WHERE e.event_id = $1
        `, [eventID]);

        return rows.length ? rows[0] : null;
    }

    _calculateMinimumPrice(price, event) {
        const { sw_fee, stripe_tickets_fee_payer } = event;
        const parsedSwFee = parseFloat(sw_fee) || 0;

        const { defaultFee, customerFee } = SWTPaymentsService.calculatePaymentProviderFee(price, SWTPaymentsService.CARD_METHOD, event);

        const stripeFee = stripe_tickets_fee_payer === FEE_PAYER.BUYER ? customerFee : defaultFee;
        return parsedSwFee + stripeFee + MINIMUM_TICKET_MARGIN;
    }

    _getCurrentPrice(ticketData) {
        const { prices, initial_price } = ticketData;
        const priceValues = Object.values(prices);

        return priceValues.length > 0 ? priceValues[priceValues.length - 1].value : initial_price;
    }

    async _prepareAndValidatePrice(eventID, ticketType) {
        const priceToValidate = this._getCurrentPrice(ticketType);
        const eventFeeData = await this._getEventFeeData(eventID);

        if (!eventFeeData) {
            throw { validation: 'Event not found' };
        }

        if (priceToValidate > 0) {
            await this._validateMinimumPrice(eventFeeData, priceToValidate);
        }

        return {
            ...eventFeeData,
            price: priceToValidate
        };
    }

    async _getEventTickets(eventId) {
        const query = knex('event_ticket')
            .select(
                'published',
                knex.raw('prices::json as prices'),
                'event_ticket_id',
                'label',
                'short_label',
                knex.raw('initial_price::NUMERIC as initial_price')
            )
            .where('event_id', eventId)
            .orderByRaw('sort_order, event_ticket_id');

        const result = await Db.query(query);
        return result.rows;
    }

    _updateTicketPriceInDb({ ticket, eventId }) {
        const { prices, event_ticket_id } = ticket;
        const query = knex('event_ticket')
            .update({ prices: JSON.stringify(prices) })
            .where({ event_id: eventId, event_ticket_id: event_ticket_id });

        return Db.query(query);
    }

    _adjustTicketPriceIndices(tickets, changeId) {
        const filteredTickets = tickets.filter(ticket => !_.isEmpty(ticket.prices));
        return {
            updatedTickets: filteredTickets.map(ticket => ({
                ...ticket,
                prices: this._shiftTicketsPrices(changeId, ticket.prices)
            })),
            allTickets: tickets.map(ticket => ({
                ...ticket,
                prices: this._shiftTicketsPrices(changeId, ticket.prices)
            }))
        };
    }

    __updateTicketsWithCurrentPrices ({ eventId, tickets, currentPrices }) {
        if(_.isEmpty(currentPrices) || !currentPrices[eventId]) {
            return tickets;
        }

        return tickets.map(ticket => {
            const eventTicketId = ticket.event_ticket_id;
            const { price: current_price, is_free } = currentPrices[eventId][eventTicketId] || {};

            return {
                ...ticket,
                ...(current_price ? { current_price } : {}),
                ...(is_free ? { is_free } : {}),
            };
        });
    }
}

module.exports = new EventTicketService();
