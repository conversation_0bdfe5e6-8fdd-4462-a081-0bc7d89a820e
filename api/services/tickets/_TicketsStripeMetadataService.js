const PaymentService = require("../PaymentService");
const TilledService = require("../TilledService");

class TicketsMetadataService {
    generate (purchase, taxedTotals, tickets, assignedTicketMode, params) {
        const createPurchases = this._prepareData(tickets);
        const ticket_holders = this._generateTicketHolders(createPurchases, assignedTicketMode);

        const isTilledProvider = purchase.payment_provider === PaymentService.__PAYMENT_PROVIDERS__.TILLED;

        const commonMetadataFields = {
            ...taxedTotals,
            swFee: params.sw_fee
        };

        const paymentCommonMetadataFields = isTilledProvider
            ? this._generateTilledPaymentCommonMetadata(commonMetadataFields)
            : this._generateStripePaymentCommonMetadata(commonMetadataFields);

        return {
            purchase_id     : purchase.purchase_id,
            barcode         : this._getBarcode(createPurchases, assignedTicketMode),
            email           : purchase.email || (void 0),
            phone           : purchase.phone,
            event_name      : params.event_name,
            tickets         : purchase.description,
            cardholder      : purchase.cardholder,
            coupon          : params.coupon,
            receipt_url     : params.receipt_url || (void 0),
            ticket_holders  : ticket_holders,
            link_to_purchase: this.getPurchasePage(
                purchase.event_id, !_.isEmpty(createPurchases) && createPurchases[0].barcode
            ),
            ...paymentCommonMetadataFields
        }
    }

    _generateTilledPaymentCommonMetadata({ netProfit, extraFee, providerFee, swFee }) {
        return TilledService.paymentCommonMetadataFields({
            netProfit, extraFee, providerFee, swFee
        })
    }

    _generateStripePaymentCommonMetadata({ netProfit, extraFee, providerFee, swFee }) {
        return StripeService.paymentCommonMetadataFields({
            netProfit,
            stripeFee: providerFee,
            extraFee,
            swFee,
        })
    }

    getPurchasePage (eventID, firstBarcodeInPurchase) {
        if(eventID && firstBarcodeInPurchase) {
            return `${sails.config.urls.main_app.baseUrl}/#/event/${
                eventID
            }/tickets-payments?barcode=${firstBarcodeInPurchase}`;
        }

        return undefined;
    }

    _prepareData(tickets) {
        return tickets.reduce((all, ticket) => {
            if(ticket.is_ticket) {
                const _ticket = _.omit(ticket, 'barcode');

                all.push({
                    items: [this._renameKey(_ticket, 'event_ticket_id', 'id')],
                    is_ticket: true,
                    barcode: ticket.barcode,
                });
            }

            return all;
        }, []);
    }

    _renameKey(obj, key, newKey) {
        if(_.has(obj, key)) {
            obj[newKey] = _.clone(obj[key], true);

            delete obj[key];
        }

        return obj;
    }

    _generateTicketHolders (purchases, assignedTicketMode) {
        let ticketsInfo     = null;
        const _purchases    = assignedTicketMode ? purchases : purchases[0].items;

        ticketsInfo = assignedTicketMode
            ? this._generateAssignedTickets(_purchases)
            : this._generateDefaultTickets(_purchases);

        return JSON.stringify(ticketsInfo);
    }


    _generateAssignedTickets (purchases) {
        const result = [];

        for (let i = 0; i < purchases.length; i++) {
            const purchase  = purchases[i];
            const item      = purchase.items[0];

            if (!purchase.is_ticket) {
                continue;
            }

            result.push({
                barcode         : purchase.barcode,
                price           : item.price,
                quantity        : item.quantity,
                event_ticket_id : item.id,
            })
        }

        return result;
    }

    _generateDefaultTickets (purchases) {
        return purchases.map(purchase => (
            {
                event_ticket_id : purchase.id,
                price           : purchase.price,
                quantity        : purchase.quantity
            }
        ));
    }

    _getBarcode(purchases, assignedTicketsMode) {
        if (!assignedTicketsMode) {
            return purchases[0].barcode;
        }

        const barcodes = [];

        for (let i = 0; i < purchases.length; i++) {
            const purchase = purchases[i];

            if (!purchase.is_ticket) {
                continue;
            }

            barcodes.push(purchase.barcode);
        }

        return barcodes.join(', ');
    }
}

module.exports = new TicketsMetadataService()
