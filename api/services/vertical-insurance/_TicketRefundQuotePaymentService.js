const { QUOTE } = require('../../constants/vertical-insurance');


class TicketRefundQuotePaymentService {
    constructor(ApiService, UtilsService) {
        this.ApiService = ApiService;
        this.UtilsService = UtilsService;
    }

    async payQuote(quoteData) {
        const {
            quote_id: quoteID,
            payment_method_token: paymentMethodToken,
        } = quoteData || {};

        console.log(quoteData, quoteID, paymentMethodToken)
        if(!quoteID) {
            throw new Error('Quote ID required');
        }

        if(!paymentMethodToken) {
            throw new Error('Payment Method Token required');
        }
        
        await this.ApiService.payQuoteWithPaymentMethodToken(
            quoteID,
            paymentMethodToken,
            QUOTE.PRODUCT_TYPE.TICKET_REFUND
        );
    }
}

module.exports = TicketRefundQuotePaymentService;
