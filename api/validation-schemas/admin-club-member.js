const Joi = require("joi");

const copyRole = Joi.object().keys({
    membership_number: Joi.number()
        .positive()
        .integer()
        .required()
        .label("Membership Number"),
    birthdate: Joi.string()
        .pattern(
            new RegExp(/^\d{4}\-(0[1-9]|1[012])\-(0[1-9]|[12][0-9]|3[01])$/)
        )
        .required()
        .messages({
            "string.pattern.base": "Birthdate format must be in format 'YYYY-MM-DD'",
        })
        .label("Birthdate"),
    from: Joi.string().valid("athlete", "staff").required().label("From"),
    to: Joi.string().valid("athlete", "staff").required().label("To"),
});

module.exports.copyRole = copyRole;
