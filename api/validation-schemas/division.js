'use strict';

let Jo<PERSON> = require('joi');
const { TEAM_USAV_SEASONALITY } = require('../lib/joi-constants');

let divisionSchema = Joi.object().keys({
    name                : Joi.string().min(1).max(120).required().label('Division Name'),
    short_name          : Joi.string().min(1).max(15).required().label('Division Short Name'),
    max_teams           : Joi.number().required().default(40).label('Max Teams'),
    late_reg_penalty    : Joi.number().optional().allow(null).default(null).label('Late Registration Penalty'),
    locked              : Joi.boolean().default(false).optional(),
    published           : Joi.boolean().default(true).optional(),
    closed              : Joi.boolean().default(false).allow(null),
    roster_deadline     : Joi.string().allow(null).optional().label('Division Roster Deadline'),
    date_reg_close      : Joi.string().allow(null).optional().label('Division Registration Close Date'),
    athletes_maxcount   : Joi.number().allow(null).optional().label('Division Athletes Max Count For Acceptance'),
    athletes_maxcount_checkin      : Joi.number().allow(null).optional().label('Division Athletes Max Count For Check In'),
    sort_order          : Joi.number().integer().positive().allow(null).optional().label('Division Sort Order'),
    has_flow_chart      : Joi.boolean().optional().label('Division Has Flow Chart'),
    level               : Joi.string().min(0).max(120).allow(null).optional().label('Division Level'),
    gender              : Joi.string().required().valid('male', 'female', 'coed').label('Division Gender'),
    max_age             : Joi.number().required().label('Max Age'),
    level_sort_order    : Joi.number().integer().positive().allow(null).optional().label('Division Level Sort Order'),
    max_auto_enter      : Joi.number().allow(null).optional().default(null).label('Max Teams Auto Enter'),
    max_waiting         : Joi.number().allow(null).optional().default(null).label('Max Teams Waiting'),
    reg_fee             : Joi.number().allow(null).optional().default(null).label('Registration Fee'),
    credit_surcharge    : Joi.number().allow(null).optional().default(null).label('Credit Surcharge'),
    is_qualifying       : Joi.boolean().allow(null).optional().default(false).label('Is a Qualifying Division'),
    seasonality         : Joi.alternatives().conditional('has_usav_sanctioning', {
                                is: true,
                                then: Joi.string()
                                    .valid(TEAM_USAV_SEASONALITY.FULL, TEAM_USAV_SEASONALITY.LOCAL)
                                    .required(),
                                otherwise: Joi.string().allow(null).optional()
                            }).label('USAV Seasonality'),
    has_usav_sanctioning: Joi.boolean().strip()
});

module.exports = { divisionSchema };
