const Joi = require('joi');

const amountRule = Joi.number().required().label('Amount');
const identifierRule = Joi.number().min(1).required();

const setDefaultSchema = Joi.object().keys({
    categories: Joi.array().items(
        Joi.object().keys({
            official_additional_payment_category_id: identifierRule.label('Official Additional Payment Category Identifier'),
            amount: amountRule
        })
    ).required().label('Categories')
})

const updateSchema = Joi.object().keys({
    event_official_id: identifierRule.label('Event Official Identifier'),
    official_additional_payment_category_id: identifierRule.label('Official Additional Payment Category Identifier'),
    amount: amountRule
})

module.exports = {
    setDefaultSchema,
    updateSchema,
}
