'use strict';

const Joi = require('joi');
const { EMAIL_REGEX } = require("../lib/joi-constants");

let sponsor = Joi.object().keys({
    company_name        : Joi.string().required().max(200).label('Company name'),
    first               : Joi.string().required().max(200).label('First name'),
    last                : Joi.string().required().max(200).label('Last name'),
    email               : Joi.string().required().pattern(EMAIL_REGEX).max(200).label('Email'),
    mobile_phone        : Joi.string().required().max(20).label('Mobile Phone'),
    office_phone        : Joi.string().allow(null, '').max(20).label('Office Phone'),
    street              : Joi.string().allow(null, '').max(200).label('Street'),
    city                : Joi.string().allow(null, '').max(200).label('City'),
    state               : Joi.string().allow(null, '').max(2).label('State'),
    zip                 : Joi.string().allow(null, '').max(10).label('Zip'),
    company_description : Joi.string().allow(null, '').label('Company description'),
    badge_names         : Joi.string().allow(null, '').label('Badge names'),
    is_exhibitor        : Joi.boolean().default(false),
    is_sponsor          : Joi.boolean().default(false),
    is_non_profit       : Joi.boolean().default(false),
    is_other            : Joi.boolean().default(false),
    samples_food        : Joi.boolean().allow(null).default(false),
    samples_beverages   : Joi.boolean().allow(null).default(false),
    sponsor_title       : Joi.string().optional().allow(null, '').label('Sponsor title'),
    website_url         : Joi.string().optional().allow(null, '').max(500).label('Website url'),
    company_samples     : Joi.boolean().default(false)
});

let invoice = Joi.object().keys({
    purchase_id : Joi.number().required().label('Invoice Identifier'),
    sponsor_id  : Joi.number().required().label('Sponsor Identifier'),
    event_id    : Joi.alternatives().try(Joi.string(), Joi.number()).required().label('Tournament Identifier'),
    total       : Joi.number().required().min(0).label('Total Amount'),
    amount      : Joi.number().required().min(0).label('Payment Amount'),
    booth       : Joi.array().items(Joi.object().keys({
        fee             : Joi.number().required().min(0).label('Booth Fee'),
        quantity        : Joi.number().required().min(1).label('Booths Quantity'),
        event_booth_id  : Joi.number().required().allow(null).label('Event Booth Identifier'),
        title           : Joi.string().required().label('Booth Title'),
    })).label('Booths'),
    type        : Joi.string().valid('card', 'check').required().label('Payment Method'),
    token 	    : Joi.alternatives().conditional(
        Joi.ref('type'), {
            switch: [
                {
                    is: 'card',
                    then: Joi.string().required(),
                },
                {
                    is: 'check',
                    then: Joi.any().forbidden(),
                    otherwise: Joi.any().allow(null),
                },
            ],
        }
    )
        .label('Card'),
});

let paymentSchema = Joi.object().keys({
    payment: Joi.object().keys({
        sponsor_id  : Joi.number().required().label('Sponsor Identifier'),
        event_id    : Joi.alternatives().try(Joi.string(), Joi.number()).required().label('Tournament Identifier'),
        total       : Joi.number().required().min(0).label('Total Amount'),
        amount      : Joi.number().required().min(0).label('Payment Amount'),
        pk          : Joi.string().required().label('Stripe Key'),
        token 	    : Joi.alternatives().conditional(
            Joi.ref('type'), {
                switch: [
                    {
                        is: 'card',
                        then: Joi.string().required(),
                    },
                    {
                        is: 'check',
                        then: Joi.any().forbidden(),
                        otherwise: Joi.any().allow(null),
                    },
                ],
            }
        )
        .label('Card'),
        booth       : Joi.array().items(Joi.object().keys({
            quantity        : Joi.number().required().min(1).label('Booths Quantity'),
            fee             : Joi.number().required().positive().label('Booth Fee'),
            event_booth_id  : Joi.number().required().allow(null).label('Event Booth Identifier'),
            amount          : Joi.number().required().positive().label('Amount'),
            title           : Joi.string().required().label('Booth Title'),
            description     : Joi.string().required().allow(null).label('Booth Description'),
            booth_label     : Joi.string().max(10).required().allow('').label('Booth Label'),
            notes           : Joi.string().required().allow('').label('Notes'),
        })).label('Booths'),
    }).required().label('Payment Body')
})

const updateInvoiceSchema = Joi.object().keys({
    sponsor_id: Joi.number().required().label('Sponsor Identifier'),
    amount: Joi.number().required().positive().label('Payment Amount'),
    booths: Joi.array().min(1).required().items(Joi.object().keys({
        quantity        : Joi.number().required().min(1).label('Booths Quantity'),
        fee             : Joi.number().required().positive().label('Booth Fee'),
        event_booth_id  : Joi.number().required().allow(null).label('Event Booth Identifier'),
        amount          : Joi.number().required().positive().label('Amount'),
        title           : Joi.string().required().label('Booth Title'),
        description     : Joi.string().required().allow(null).label('Booth Description'),
        booth_label     : Joi.string().max(10).required().allow('').label('Booth Label'),
        notes           : Joi.string().required().allow('').label('Notes'),
    })).label('Booths'),
})

module.exports = {
    invoice         : invoice,
    sponsor         : sponsor,
    sales_sponsor   : sponsor.keys({
        is_exhibitor        : Joi.boolean().required(),
        is_sponsor          : Joi.boolean().required(),
        is_non_profit       : Joi.boolean().required(),
        is_other            : Joi.boolean().required(),
        gender              : Joi.string().required().label('Gender'),
        sponsor_title       : Joi.string().optional().allow(null, '').label('Sponsor title'),
        website_url         : Joi.string().optional().allow(null, '').max(500).label('Website url'),
    }),
    paymentSchema,
    updateInvoiceSchema,
};
