const Joi = require('joi');

module.exports = {
    update_staffers_info: Joi.object().keys({
        data: Joi.object().keys({
            staff_work_status: Joi.string().required().valid('pending', 'approved', 'declined', 'waitlisted').label('Staff Work Status'),
        }).required(),
        staffers: Joi.array().items(Joi.number().integer()).unique().min(1).label('Staffers Identifiers')
    })
};


