
exports.up = function (knex) {
    return knex.schema.raw(`
        DROP VIEW v_aem_admin_templates;
        DROP VIEW v_aem_trigger_templates;
        DROP VIEW v_aem_manual_mailing_templates;
        DROP VIEW v_aem_basic_layouts;
        DROP VIEW v_aem_teams_payments_data;
        DROP VIEW v_officials_notification_contact_provider;
        DROP VIEW v_head_referee_notification_receiver_data;
        DROP VIEW v_swt_participant_age;
        DROP VIEW v_officials_payout_history;
        DROP VIEW v_officials_payout_creation_info;
        ALTER TABLE "public"."event_note" ALTER COLUMN "note" TYPE TEXT;
        ALTER TABLE "public"."event_ticket" ALTER COLUMN "description" TYPE TEXT;
        ALTER TABLE "public"."purchase_booth" ALTER COLUMN "notes" TYPE TEXT;
        ALTER TABLE "public"."purchase_booth" ALTER COLUMN "description" TYPE TEXT;
        ALTER TABLE "public"."event_booth" ALTER COLUMN "description" TYPE TEXT;    
        ALTER TABLE "public"."sponsor" ALTER COLUMN "company_description" TYPE TEXT;
        ALTER TABLE "public"."sponsor" ALTER COLUMN "badge_names" TYPE TEXT;
        ALTER TABLE "public"."sponsor" ALTER COLUMN "sponsor_title" TYPE TEXT;
        ALTER TABLE "public"."balance_transaction_account" ALTER COLUMN "automatic_transfer_id" TYPE TEXT;
        ALTER TABLE "public"."balance_transaction_account" ALTER COLUMN "id" TYPE TEXT;
        ALTER TABLE "public"."balance_transaction_account" ALTER COLUMN "source_id" TYPE TEXT;
        ALTER TABLE "public"."bracketteams" ALTER COLUMN "3rd" TYPE TEXT;
        ALTER TABLE "public"."bracketteams" ALTER COLUMN "win" TYPE TEXT;
        ALTER TABLE "public"."email_template" ALTER COLUMN "title" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "address" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "custom_housing_company" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "email" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "host" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "hosting_org_address" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "hosting_org_city" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "hosting_org_name" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "location" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "long_name" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "payment_address" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "payment_city" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "payment_name" TYPE TEXT;
        ALTER TABLE "public"."event" ALTER COLUMN "admin_security_pin" TYPE TEXT; 
        ALTER TABLE "public"."event" ALTER COLUMN "stripe_teams_private_key" TYPE TEXT; 
        ALTER TABLE "public"."event" ALTER COLUMN "stripe_tickets_private_key" TYPE TEXT;
        ALTER TABLE "public"."event_chat" ALTER COLUMN "user_name" TYPE TEXT;
        ALTER TABLE "public"."event_official_schedule" ALTER COLUMN "match_name" TYPE TEXT;
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "account_email" TYPE TEXT;
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "secret_key" TYPE TEXT;
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "public_key" TYPE TEXT;
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "account_statement" TYPE TEXT;
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "account_name" TYPE TEXT;
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "stripe_account_id" TYPE TEXT; 
        ALTER TABLE "public"."password_recovery" ALTER COLUMN "user_agent" TYPE TEXT; 
        ALTER TABLE "public"."roster_team" ALTER COLUMN "team_alert_note" TYPE TEXT; 
        ALTER TABLE "public"."division" ALTER COLUMN "color" TYPE TEXT; 
        ALTER TABLE "public"."master_staff" ALTER COLUMN "phone" TYPE TEXT;
        CREATE VIEW v_aem_trigger_templates AS 
             SELECT ett.type AS type_id,
                ett.title AS type_title,
                et.title,
                et.email_template_id AS id,
                et.is_valid,
                et.email_template_id = ett.default_email_template_id AS is_default,
                et.published,
                et.event_id IS NULL AND et.event_owner_id IS NOT NULL AS all_events,
                COALESCE(usg.qty, 0::bigint) > 0 AS is_in_use,
                COALESCE(usg.qty, 0::bigint) AS usage_qty,
                ett.is_trigger,
                et.img_name,
                et.event_id,
                et.event_owner_id,
                et.email_template_group AS "group",
                etg.title AS group_title    
             FROM email_template_type ett
             JOIN email_template et ON et.email_template_type = ett.type AND et.email_template_group = ett.email_template_group AND et.deleted IS NULL
             LEFT JOIN email_template_group etg ON ett.email_template_group = etg."group"
             LEFT JOIN v_aem_template_usage usg ON usg.email_template_id = et.email_template_id AND usg.email_template_type = et.email_template_type AND usg.email_template_group = et.email_template_group
             WHERE ett.is_trigger IS TRUE AND ett.type <> 'content'::text;
        CREATE VIEW v_aem_manual_mailing_templates AS
             SELECT ett.type AS type_id,
                ett.title AS type_title,
                et.title,
                et.email_template_id AS id,
                et.is_valid,
                et.email_template_id = ett.default_email_template_id AS is_default,
                et.published,
                et.event_id IS NULL AND et.event_owner_id IS NOT NULL AS all_events,
                false AS is_in_use,
                NULL::bigint AS usage_qty,
                ett.is_trigger,
                et.img_name,
                et.event_id,
                et.event_owner_id,
                et.email_template_group AS "group",
                etg.title AS group_title
             FROM email_template_type ett
             JOIN email_template et ON et.email_template_type = ett.type AND et.deleted IS NULL
             JOIN email_template_group etg ON ett.email_template_group = etg."group" AND et.email_template_group = etg."group"
             WHERE ett.is_trigger IS NOT TRUE AND ett.type = 'content'::text; 
        CREATE VIEW v_aem_basic_layouts AS
             SELECT NULL::text AS type_id,
                NULL::text AS type_title,
                et.title,
                et.email_template_id AS id,
                et.is_valid,
                false AS is_default,
                et.published,
                false AS all_events,
                true AS is_in_use,
                NULL::bigint AS usage_qty,
                false AS is_trigger,
                et.img_name,
                et.event_id,
                et.event_owner_id,
                et.email_template_group AS "group",
                NULL::text AS group_title
             FROM email_template et
             WHERE et.email_template_type = 'layout'::text AND et.deleted IS NULL; 
        CREATE VIEW v_aem_admin_templates AS
             SELECT t.type_id,
                t.type_title,
                t.title,
                t.id,
                t.is_valid,
                t.is_default,
                t.published,
                t.all_events,
                t.is_in_use,
                t.usage_qty,
                t.is_trigger,
                t.img_name,
                t.event_id,
                t.event_owner_id,
                t."group",
                t.group_title
             FROM v_aem_trigger_templates t
             WHERE t.is_default IS TRUE OR t.event_id IS NULL AND t.event_owner_id IS NULL
             UNION ALL
             SELECT m.type_id,
                m.type_title,
                m.title,
                m.id,
                m.is_valid,
                m.is_default,
                m.published,
                m.all_events,
                m.is_in_use,
                m.usage_qty,
                m.is_trigger,
                m.img_name,
                m.event_id,
                m.event_owner_id,
                m."group",
                m.group_title
             FROM v_aem_manual_mailing_templates m
             WHERE m.event_id IS NULL AND m.event_owner_id IS NULL
             UNION ALL
             SELECT v_aem_basic_layouts.type_id,
                v_aem_basic_layouts.type_title,
                v_aem_basic_layouts.title,
                v_aem_basic_layouts.id,
                v_aem_basic_layouts.is_valid,
                v_aem_basic_layouts.is_default,
                v_aem_basic_layouts.published,
                v_aem_basic_layouts.all_events,
                v_aem_basic_layouts.is_in_use,
                v_aem_basic_layouts.usage_qty,
                v_aem_basic_layouts.is_trigger,
                v_aem_basic_layouts.img_name,
                v_aem_basic_layouts.event_id,
                v_aem_basic_layouts.event_owner_id,
                v_aem_basic_layouts."group",
                v_aem_basic_layouts.group_title
             FROM v_aem_basic_layouts;
        CREATE VIEW v_aem_teams_payments_data AS
             SELECT p.purchase_id,
                p.stripe_charge_id,
                p.event_id,
                COALESCE(e.long_name, e.name) AS event_name,
                mc.club_name,
                array_to_html_list(array_agg(rt.team_name)::text[]) AS team_names,
                p.type AS payment_type,
                p.status AS payment_status,
                p.amount AS payment_amount,
                to_char(p.created, 'HH12:MI AM, Mon DD, YYYY'::text) AS payment_created_date,
                to_char(p.date_paid, 'HH12:MI AM, Mon DD, YYYY'::text) AS payment_date_paid,
                to_char(p.canceled_date, 'HH12:MI AM, Mon DD, YYYY'::text) AS payment_canceled_date,
                u1.first AS eo_first,
                u1.last AS eo_last,
                COALESCE(e.email, u1.email) AS eo_email,
                format_phone_number(u1.phone_mob::text) AS eo_phone,
                u2.first AS cd_first,
                u2.last AS cd_last,
                u2.email AS cd_email,
                format_phone_number(u2.phone_mob::text) AS cd_phone,
                p.roster_club_id
             FROM purchase p
                JOIN event e ON p.event_id = e.event_id
                JOIN roster_club rc ON rc.roster_club_id = p.roster_club_id
                JOIN master_club mc ON mc.master_club_id = rc.master_club_id
                JOIN purchase_team pt ON pt.purchase_id = p.purchase_id
                JOIN roster_team rt ON rt.roster_team_id = pt.roster_team_id AND rt.event_id = pt.event_id
                JOIN event_owner eo ON e.event_owner_id = eo.event_owner_id
                JOIN "user" u1 ON u1.user_id = eo.user_id
                JOIN club_owner co ON co.club_owner_id = mc.club_owner_id
                JOIN "user" u2 ON u2.user_id = co.user_id
             GROUP BY p.purchase_id, p.stripe_charge_id, p.event_id, e.long_name, e.name, mc.club_name, p.type, p.status, p.amount, p.created, p.date_paid, p.canceled_date, u1.first, u1.last, e.email, u1.email, u1.phone_mob, u2.first, u2.last, u2.email, u2.phone_mob, e.timezone;
        CREATE VIEW v_officials_notification_contact_provider AS
             SELECT w.event_id,
                w.official_admin_role_first,
                w.official_admin_role_last,
                w.official_admin_role_email,
                w.official_admin_role_phone,
                w.pick_up_order
             FROM ( SELECT r.event_id,
                       r.official_admin_role_first,
                       r.official_admin_role_last,
                       r.official_admin_role_email,
                       r.official_admin_role_phone,
                       r.pick_up_order,
                       row_number() OVER (PARTITION BY r.event_id ORDER BY r.pick_up_order) AS row_id
                      FROM ( SELECT eo.event_id,
                               u.first AS official_admin_role_first,
                               u.last AS official_admin_role_last,
                               u.email AS official_admin_role_email,
                               u.phone_mob AS official_admin_role_phone,
                               'A'::text AS pick_up_order
                              FROM event_official eo
                                JOIN official o ON eo.official_id = o.official_id
                                JOIN "user" u ON u.user_id = o.user_id
                             WHERE eo.head_official = true AND eo.is_email_contact_provider = true
                           UNION ALL
                            SELECT e.event_id,
                               u.first,
                               u.last,
                               COALESCE(e.email, u.email) AS email,
                               u.phone_mob,
                               'B'::text AS pick_up_order
                              FROM event e
                                JOIN event_owner eo ON e.event_owner_id = eo.event_owner_id
                                JOIN "user" u ON u.user_id = eo.user_id) r) w
             WHERE w.row_id = 1;
        CREATE VIEW v_head_referee_notification_receiver_data AS
             SELECT eo.event_id,
                u.first,
                u.last,
                u.email,
                u.phone_mob
             FROM event_official eo
               JOIN official o ON eo.official_id = o.official_id
               JOIN "user" u ON u.user_id = o.user_id
             WHERE eo.head_official = true AND eo.is_email_notifications_receiver = true
             UNION
             SELECT e.event_id,
                u.first,
                u.last,
                COALESCE(e.email, u.email) AS email,
                u.phone_mob
               FROM event e
                 JOIN event_owner eo ON e.event_owner_id = eo.event_owner_id
                 JOIN "user" u ON u.user_id = eo.user_id;
        CREATE VIEW v_swt_participant_age AS
             SELECT pt.purchase_id,
                    CASE
                        WHEN ec.name IS NULL THEN 'Ticket Type: '::text || et.label::text
                        ELSE 'Camp: '::text || ec.name
                    END AS name,
                COALESCE(ec.age_date, to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp without time zone) AS age_date,
                to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text) AS player_bithdate,
                date_part('year'::text, age(COALESCE(ec.age_date, to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp without time zone),
                    CASE COALESCE(p.tickets_additional ->> 'birthdate'::text, ''::text)
                        WHEN ''::text THEN COALESCE(ec.age_date, to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp without time zone)
                        ELSE to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text)::timestamp without time zone
                    END))::integer AS age,
                et.event_camp_id,
                'camp'::text AS age_type
               FROM purchase_ticket pt
                 JOIN purchase p ON pt.purchase_id = p.purchase_id
                 JOIN event e ON p.event_id = e.event_id
                 JOIN event_ticket et ON et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id
                 LEFT JOIN event_camp ec ON ec.event_camp_id = et.event_camp_id
             UNION
             SELECT pt.purchase_id,
                'Event: '::text || e.long_name::text AS name,
                to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text) AS age_date,
                to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text) AS player_bithdate,
                date_part('year'::text, age(to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp with time zone,
                    CASE COALESCE(p.tickets_additional ->> 'birthdate'::text, ''::text)
                        WHEN ''::text THEN to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)
                        ELSE to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text)
                    END::timestamp with time zone))::integer AS age,
                et.event_camp_id,
                'event'::text AS age_type
               FROM purchase_ticket pt
                 JOIN purchase p ON pt.purchase_id = p.purchase_id
                 JOIN event e ON p.event_id = e.event_id
                 JOIN event_ticket et ON et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id;
          
        CREATE VIEW v_officials_payout_creation_info AS 
             SELECT e.event_id,
                eo.event_owner_id,
                sa.stripe_account_id AS account_from,
                sa.secret_key AS sk_account_from,
                sa.public_key AS pk_account_from,
                sa.is_platform,
                u.user_id AS official_user_id,
                o.official_id,
                u.first AS official_first,
                u.last AS official_last,
                sao.stripe_account_id AS account_to,
                sao.secret_key AS sk_account_to,
                sao.public_key AS pk_account_to,
                sao.dashboard_url
               FROM stripe_account sa
                 JOIN event e ON sa.secret_key::text = e.stripe_teams_private_key::text
                 JOIN event_owner eo ON eo.event_owner_id = e.event_owner_id
                 JOIN event_official eof ON eof.event_id = e.event_id
                 JOIN official o ON o.official_id = eof.official_id
                 JOIN "user" u ON u.user_id = o.user_id
                 JOIN stripe_account sao ON sao.user_id = u.user_id AND sao.connected_to_account_id = sa.stripe_account_id::text
              WHERE sa.is_platform = true AND sao.account_type = 'express'::stripe_account_type;
        CREATE VIEW v_officials_payout_history AS
             SELECT st.id,
               st.date_sent,
               st.amount,
               st.description,
               v.event_owner_id,
               v.event_id,
               v.official_id
             FROM v_officials_payout_creation_info v
             JOIN stripe_transfer st ON v.event_id = st.event_id AND v.account_from::text = st.stripe_account_id AND v.account_to::text = st.destination_stripe_account_id;
    `)
};

exports.down = function (knex) {
    return knex.schema.raw(`
        DROP VIEW v_aem_admin_templates;
        DROP VIEW v_aem_trigger_templates;
        DROP VIEW v_aem_manual_mailing_templates;
        DROP VIEW v_aem_basic_layouts;
        DROP VIEW v_aem_teams_payments_data;
        DROP VIEW v_officials_notification_contact_provider;
        DROP VIEW v_head_referee_notification_receiver_data;
        DROP VIEW v_swt_participant_age;
        DROP VIEW v_officials_payout_history;
        DROP VIEW v_officials_payout_creation_info;
         ALTER TABLE "public"."event_note" ALTER COLUMN "note" TYPE VARCHAR(2044);
        ALTER TABLE "public"."event_ticket" ALTER COLUMN "description" TYPE VARCHAR(200);
        ALTER TABLE "public"."purchase_booth" ALTER COLUMN "notes" TYPE VARCHAR(2044);
        ALTER TABLE "public"."purchase_booth" ALTER COLUMN "description" TYPE VARCHAR(2044);
        ALTER TABLE "public"."event_booth" ALTER COLUMN "description" TYPE VARCHAR(2044);
        ALTER TABLE "public"."sponsor" ALTER COLUMN "company_description" TYPE VARCHAR(4096);
        ALTER TABLE "public"."sponsor" ALTER COLUMN "badge_names" TYPE VARCHAR(2044);
        ALTER TABLE "public"."sponsor" ALTER COLUMN "sponsor_title" TYPE VARCHAR(500);
        ALTER TABLE "public"."balance_transaction_account" ALTER COLUMN "automatic_transfer_id" TYPE VARCHAR(255);
        ALTER TABLE "public"."balance_transaction_account" ALTER COLUMN "id" TYPE VARCHAR(255);
        ALTER TABLE "public"."balance_transaction_account" ALTER COLUMN "source_id" TYPE VARCHAR(255);
        ALTER TABLE "public"."bracketteams" ALTER COLUMN "3rd" TYPE VARCHAR(2044);
        ALTER TABLE "public"."bracketteams" ALTER COLUMN "win" TYPE VARCHAR(2044);
        ALTER TABLE "public"."email_template" ALTER COLUMN "title" TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "address" TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "custom_housing_company" TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "email"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "host"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "hosting_org_address"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "hosting_org_city" TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "hosting_org_name"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "location"  TYPE VARCHAR(2000);
        ALTER TABLE "public"."event" ALTER COLUMN "long_name"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "payment_address"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "payment_city"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "payment_name"  TYPE VARCHAR(200);
        ALTER TABLE "public"."event" ALTER COLUMN "admin_security_pin" TYPE VARCHAR(2044);
        ALTER TABLE "public"."event" ALTER COLUMN "stripe_teams_private_key" TYPE VARCHAR(2044);
        ALTER TABLE "public"."event" ALTER COLUMN "stripe_tickets_private_key" TYPE VARCHAR(2044);
        ALTER TABLE "public"."event_chat" ALTER COLUMN "user_name" TYPE VARCHAR(255);
        ALTER TABLE "public"."event_official_schedule" ALTER COLUMN "match_name"  TYPE VARCHAR(2044);
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "account_email"  TYPE VARCHAR(2044);
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "secret_key" TYPE VARCHAR(2044);
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "public_key" TYPE VARCHAR(2044);
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "account_statement" TYPE VARCHAR(2044);
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "account_name" TYPE VARCHAR(2044);
        ALTER TABLE "public"."stripe_account" ALTER COLUMN "stripe_account_id" TYPE VARCHAR(2044);
        ALTER TABLE "public"."password_recovery" ALTER COLUMN "user_agent" TYPE VARCHAR(2044);
        ALTER TABLE "public"."roster_team" ALTER COLUMN "team_alert_note" TYPE VARCHAR(2044);
        ALTER TABLE "public"."division" ALTER COLUMN "color" TYPE VARCHAR(2044);
        ALTER TABLE "public"."master_staff" ALTER COLUMN "phone" TYPE VARCHAR(2044);
        CREATE VIEW v_aem_trigger_templates AS 
             SELECT ett.type AS type_id,
                ett.title AS type_title,
                et.title,
                et.email_template_id AS id,
                et.is_valid,
                et.email_template_id = ett.default_email_template_id AS is_default,
                et.published,
                et.event_id IS NULL AND et.event_owner_id IS NOT NULL AS all_events,
                COALESCE(usg.qty, 0::bigint) > 0 AS is_in_use,
                COALESCE(usg.qty, 0::bigint) AS usage_qty,
                ett.is_trigger,
                et.img_name,
                et.event_id,
                et.event_owner_id,
                et.email_template_group AS "group",
                etg.title AS group_title    
             FROM email_template_type ett
             JOIN email_template et ON et.email_template_type = ett.type AND et.email_template_group = ett.email_template_group AND et.deleted IS NULL
             LEFT JOIN email_template_group etg ON ett.email_template_group = etg."group"
             LEFT JOIN v_aem_template_usage usg ON usg.email_template_id = et.email_template_id AND usg.email_template_type = et.email_template_type AND usg.email_template_group = et.email_template_group
             WHERE ett.is_trigger IS TRUE AND ett.type <> 'content'::text;
        CREATE VIEW v_aem_manual_mailing_templates AS
             SELECT ett.type AS type_id,
                ett.title AS type_title,
                et.title,
                et.email_template_id AS id,
                et.is_valid,
                et.email_template_id = ett.default_email_template_id AS is_default,
                et.published,
                et.event_id IS NULL AND et.event_owner_id IS NOT NULL AS all_events,
                false AS is_in_use,
                NULL::bigint AS usage_qty,
                ett.is_trigger,
                et.img_name,
                et.event_id,
                et.event_owner_id,
                et.email_template_group AS "group",
                etg.title AS group_title
             FROM email_template_type ett
             JOIN email_template et ON et.email_template_type = ett.type AND et.deleted IS NULL
             JOIN email_template_group etg ON ett.email_template_group = etg."group" AND et.email_template_group = etg."group"
             WHERE ett.is_trigger IS NOT TRUE AND ett.type = 'content'::text; 
        CREATE VIEW v_aem_basic_layouts AS
             SELECT NULL::text AS type_id,
                NULL::text AS type_title,
                et.title,
                et.email_template_id AS id,
                et.is_valid,
                false AS is_default,
                et.published,
                false AS all_events,
                true AS is_in_use,
                NULL::bigint AS usage_qty,
                false AS is_trigger,
                et.img_name,
                et.event_id,
                et.event_owner_id,
                et.email_template_group AS "group",
                NULL::text AS group_title
             FROM email_template et
             WHERE et.email_template_type = 'layout'::text AND et.deleted IS NULL; 
        CREATE VIEW v_aem_admin_templates AS
             SELECT t.type_id,
                t.type_title,
                t.title,
                t.id,
                t.is_valid,
                t.is_default,
                t.published,
                t.all_events,
                t.is_in_use,
                t.usage_qty,
                t.is_trigger,
                t.img_name,
                t.event_id,
                t.event_owner_id,
                t."group",
                t.group_title
             FROM v_aem_trigger_templates t
             WHERE t.is_default IS TRUE OR t.event_id IS NULL AND t.event_owner_id IS NULL
             UNION ALL
             SELECT m.type_id,
                m.type_title,
                m.title,
                m.id,
                m.is_valid,
                m.is_default,
                m.published,
                m.all_events,
                m.is_in_use,
                m.usage_qty,
                m.is_trigger,
                m.img_name,
                m.event_id,
                m.event_owner_id,
                m."group",
                m.group_title
             FROM v_aem_manual_mailing_templates m
             WHERE m.event_id IS NULL AND m.event_owner_id IS NULL
             UNION ALL
             SELECT v_aem_basic_layouts.type_id,
                v_aem_basic_layouts.type_title,
                v_aem_basic_layouts.title,
                v_aem_basic_layouts.id,
                v_aem_basic_layouts.is_valid,
                v_aem_basic_layouts.is_default,
                v_aem_basic_layouts.published,
                v_aem_basic_layouts.all_events,
                v_aem_basic_layouts.is_in_use,
                v_aem_basic_layouts.usage_qty,
                v_aem_basic_layouts.is_trigger,
                v_aem_basic_layouts.img_name,
                v_aem_basic_layouts.event_id,
                v_aem_basic_layouts.event_owner_id,
                v_aem_basic_layouts."group",
                v_aem_basic_layouts.group_title
             FROM v_aem_basic_layouts;
        CREATE VIEW v_aem_teams_payments_data AS
             SELECT p.purchase_id,
                p.stripe_charge_id,
                p.event_id,
                COALESCE(e.long_name, e.name) AS event_name,
                mc.club_name,
                array_to_html_list(array_agg(rt.team_name)::text[]) AS team_names,
                p.type AS payment_type,
                p.status AS payment_status,
                p.amount AS payment_amount,
                to_char(p.created, 'HH12:MI AM, Mon DD, YYYY'::text) AS payment_created_date,
                to_char(p.date_paid, 'HH12:MI AM, Mon DD, YYYY'::text) AS payment_date_paid,
                to_char(p.canceled_date, 'HH12:MI AM, Mon DD, YYYY'::text) AS payment_canceled_date,
                u1.first AS eo_first,
                u1.last AS eo_last,
                COALESCE(e.email, u1.email) AS eo_email,
                format_phone_number(u1.phone_mob::text) AS eo_phone,
                u2.first AS cd_first,
                u2.last AS cd_last,
                u2.email AS cd_email,
                format_phone_number(u2.phone_mob::text) AS cd_phone,
                p.roster_club_id
             FROM purchase p
                JOIN event e ON p.event_id = e.event_id
                JOIN roster_club rc ON rc.roster_club_id = p.roster_club_id
                JOIN master_club mc ON mc.master_club_id = rc.master_club_id
                JOIN purchase_team pt ON pt.purchase_id = p.purchase_id
                JOIN roster_team rt ON rt.roster_team_id = pt.roster_team_id AND rt.event_id = pt.event_id
                JOIN event_owner eo ON e.event_owner_id = eo.event_owner_id
                JOIN "user" u1 ON u1.user_id = eo.user_id
                JOIN club_owner co ON co.club_owner_id = mc.club_owner_id
                JOIN "user" u2 ON u2.user_id = co.user_id
             GROUP BY p.purchase_id, p.stripe_charge_id, p.event_id, e.long_name, e.name, mc.club_name, p.type, p.status, p.amount, p.created, p.date_paid, p.canceled_date, u1.first, u1.last, e.email, u1.email, u1.phone_mob, u2.first, u2.last, u2.email, u2.phone_mob, e.timezone;
        CREATE VIEW v_officials_notification_contact_provider AS
             SELECT w.event_id,
                w.official_admin_role_first,
                w.official_admin_role_last,
                w.official_admin_role_email,
                w.official_admin_role_phone,
                w.pick_up_order
             FROM ( SELECT r.event_id,
                       r.official_admin_role_first,
                       r.official_admin_role_last,
                       r.official_admin_role_email,
                       r.official_admin_role_phone,
                       r.pick_up_order,
                       row_number() OVER (PARTITION BY r.event_id ORDER BY r.pick_up_order) AS row_id
                      FROM ( SELECT eo.event_id,
                               u.first AS official_admin_role_first,
                               u.last AS official_admin_role_last,
                               u.email AS official_admin_role_email,
                               u.phone_mob AS official_admin_role_phone,
                               'A'::text AS pick_up_order
                              FROM event_official eo
                                JOIN official o ON eo.official_id = o.official_id
                                JOIN "user" u ON u.user_id = o.user_id
                             WHERE eo.head_official = true AND eo.is_email_contact_provider = true
                           UNION ALL
                            SELECT e.event_id,
                               u.first,
                               u.last,
                               COALESCE(e.email, u.email) AS email,
                               u.phone_mob,
                               'B'::text AS pick_up_order
                              FROM event e
                                JOIN event_owner eo ON e.event_owner_id = eo.event_owner_id
                                JOIN "user" u ON u.user_id = eo.user_id) r) w
             WHERE w.row_id = 1;
        CREATE VIEW v_head_referee_notification_receiver_data AS
             SELECT eo.event_id,
                u.first,
                u.last,
                u.email,
                u.phone_mob
             FROM event_official eo
               JOIN official o ON eo.official_id = o.official_id
               JOIN "user" u ON u.user_id = o.user_id
             WHERE eo.head_official = true AND eo.is_email_notifications_receiver = true
             UNION
             SELECT e.event_id,
                u.first,
                u.last,
                COALESCE(e.email, u.email) AS email,
                u.phone_mob
               FROM event e
                 JOIN event_owner eo ON e.event_owner_id = eo.event_owner_id
                 JOIN "user" u ON u.user_id = eo.user_id;
        CREATE VIEW v_swt_participant_age AS
             SELECT pt.purchase_id,
                    CASE
                        WHEN ec.name IS NULL THEN 'Ticket Type: '::text || et.label::text
                        ELSE 'Camp: '::text || ec.name
                    END AS name,
                COALESCE(ec.age_date, to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp without time zone) AS age_date,
                to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text) AS player_bithdate,
                date_part('year'::text, age(COALESCE(ec.age_date, to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp without time zone),
                    CASE COALESCE(p.tickets_additional ->> 'birthdate'::text, ''::text)
                        WHEN ''::text THEN COALESCE(ec.age_date, to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp without time zone)
                        ELSE to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text)::timestamp without time zone
                    END))::integer AS age,
                et.event_camp_id,
                'camp'::text AS age_type
               FROM purchase_ticket pt
                 JOIN purchase p ON pt.purchase_id = p.purchase_id
                 JOIN event e ON p.event_id = e.event_id
                 JOIN event_ticket et ON et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id
                 LEFT JOIN event_camp ec ON ec.event_camp_id = et.event_camp_id
             UNION
             SELECT pt.purchase_id,
                'Event: '::text || e.long_name::text AS name,
                to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text) AS age_date,
                to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text) AS player_bithdate,
                date_part('year'::text, age(to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)::timestamp with time zone,
                    CASE COALESCE(p.tickets_additional ->> 'birthdate'::text, ''::text)
                        WHEN ''::text THEN to_date(e.tickets_settings ->> 'age_date'::text, 'MM/DD/YYYY'::text)
                        ELSE to_date(p.tickets_additional ->> 'birthdate'::text, 'MM/DD/YYYY'::text)
                    END::timestamp with time zone))::integer AS age,
                et.event_camp_id,
                'event'::text AS age_type
               FROM purchase_ticket pt
                 JOIN purchase p ON pt.purchase_id = p.purchase_id
                 JOIN event e ON p.event_id = e.event_id
                 JOIN event_ticket et ON et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id;
          
        CREATE VIEW v_officials_payout_creation_info AS 
             SELECT e.event_id,
                eo.event_owner_id,
                sa.stripe_account_id AS account_from,
                sa.secret_key AS sk_account_from,
                sa.public_key AS pk_account_from,
                sa.is_platform,
                u.user_id AS official_user_id,
                o.official_id,
                u.first AS official_first,
                u.last AS official_last,
                sao.stripe_account_id AS account_to,
                sao.secret_key AS sk_account_to,
                sao.public_key AS pk_account_to,
                sao.dashboard_url
               FROM stripe_account sa
                 JOIN event e ON sa.secret_key::text = e.stripe_teams_private_key::text
                 JOIN event_owner eo ON eo.event_owner_id = e.event_owner_id
                 JOIN event_official eof ON eof.event_id = e.event_id
                 JOIN official o ON o.official_id = eof.official_id
                 JOIN "user" u ON u.user_id = o.user_id
                 JOIN stripe_account sao ON sao.user_id = u.user_id AND sao.connected_to_account_id = sa.stripe_account_id::text
              WHERE sa.is_platform = true AND sao.account_type = 'express'::stripe_account_type;
        CREATE VIEW v_officials_payout_history AS
             SELECT st.id,
               st.date_sent,
               st.amount,
               st.description,
               v.event_owner_id,
               v.event_id,
               v.official_id
             FROM v_officials_payout_creation_info v
             JOIN stripe_transfer st ON v.event_id = st.event_id AND v.account_from::text = st.stripe_account_id AND v.account_to::text = st.destination_stripe_account_id;
    `)
};

