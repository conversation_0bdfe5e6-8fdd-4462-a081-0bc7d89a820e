
exports.up = function(knex) {
  return knex.schema.raw(`
    ALTER TABLE "public"."purchase_ticket" ADD COLUMN "ticket_coupon_id" INT DEFAULT NULL;
    CREATE INDEX "index_ticket_coupon_id" ON "public"."purchase_ticket" USING btree( "ticket_coupon_id" );
  `)
};

exports.down = function(knex) {
  return knex.schema.raw(`
    DROP INDEX IF EXISTS "public"."index_ticket_coupon_id";
    ALTER TABLE "public"."purchase_ticket" DROP COLUMN IF EXISTS "ticket_coupon_id";
  `)
};
