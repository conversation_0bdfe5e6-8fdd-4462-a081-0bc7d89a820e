
exports.up = function(knex) {
    return knex.raw(`
        INSERT INTO "public"."sport_variation" ("sport_id", "name", "sort_order")
        VALUES ((SELECT "s"."sport_id" FROM "public"."sport" AS "s" WHERE "s"."name" = 'Basketball'), 'Indoor', 1);
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        DELETE
        FROM "public"."sport_variation" AS "sv"
        WHERE "sv"."sport_id" = (SELECT "s"."sport_id" FROM "public"."sport" AS "s" WHERE "s"."name" = 'Basketball')
          AND "sv"."name" = 'Indoor';
    `)
};
