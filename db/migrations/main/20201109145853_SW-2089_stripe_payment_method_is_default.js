
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."stripe_payment_method" ADD COLUMN "is_default" BOOLEAN NOT NULL DEFAULT FALSE;
        
        CREATE UNIQUE INDEX "unique_stripe_payment_method_customer_id_is_default" 
        ON "public"."stripe_payment_method" (stripe_customer_id) 
        WHERE (is_default IS TRUE);
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP INDEX IF EXISTS "public"."unique_stripe_payment_method_customer_id_is_default";
    
        ALTER TABLE "public"."stripe_payment_method" DROP COLUMN "is_default";
    `)
};
