
exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "ticket_coupon_receiver" -----------------------------------------------------------------------
        CREATE TABLE "public"."ticket_coupon_receiver"
        (
            "ticket_coupon_receiver_id" INT GENERATED ALWAYS AS IDENTITY,
            "created"                   TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            "modified"                  TIMESTAMP WITH TIME ZONE DEFAULT NULL,
            "ticket_coupon_id"          INTEGER                                NOT NULL,
            "roster_team_id"            INTEGER,
            "email"                     TEXT,
            "first"                     TEXT,
            "last"                      TEXT,
            CONSTRAINT roster_team_id_or_email_ticket_coupon_receiver_check 
                CHECK (num_nonnulls(roster_team_id, email) = 1)
        );
        
        COMMENT ON COLUMN "public"."ticket_coupon_receiver"."ticket_coupon_id" IS 'Ticket Coupon ID (foreign key)';
        COMMENT ON COLUMN "public"."ticket_coupon_receiver"."roster_team_id" IS 'Roster Team ID (foreign key)';
        COMMENT ON COLUMN "public"."ticket_coupon_receiver"."email" IS 'Receiver Email';
        COMMENT ON COLUMN "public"."ticket_coupon_receiver"."first" IS 'Receiver First Name';
        COMMENT ON COLUMN "public"."ticket_coupon_receiver"."last" IS 'Receiver Last Name';
        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- CREATE UNIQUE INDICES ---------------------------------------------------------------------------------------
        CREATE UNIQUE INDEX ticket_coupon_id_email_ticket_coupon_receiver_unique
            ON ticket_coupon_receiver (ticket_coupon_id, email)
            WHERE email IS NOT NULL;
        
        CREATE UNIQUE INDEX ticket_coupon_id_roster_team_id_ticket_coupon_receiver_unique
            ON ticket_coupon_receiver (ticket_coupon_id, roster_team_id)
            WHERE roster_team_id IS NOT NULL;
        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- Add modified trigger to ticket_coupon_receiver table --------------------------------------------------------
        CREATE TRIGGER "update_ticket_coupon_receiver_modified"
            BEFORE UPDATE
            ON "public"."ticket_coupon_receiver"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        -- -------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."ticket_coupon_receiver";
    `)
};
