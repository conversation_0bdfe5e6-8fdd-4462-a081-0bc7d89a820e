
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TYPE import_process AS Enum( 'manual-vip-tickets' );
        CREATE TYPE import_status AS Enum( 'running', 'finished', 'error' );
        CREATE TABLE import
        (
            import_id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            created TIMESTAMP DEFAULT NOW() NOT NULL,
            modified TIMESTAMP DEFAULT NULL,
            process import_process NOT NULL,
            event_id INT NOT NULL,
            progress FLOAT DEFAULT NULL,
            status import_status DEFAULT 'running'::import_status,
            output JSONB DEFAULT NULL
        );
        
        COMMENT ON COLUMN import.progress IS 'In percents. 0 - 0%, 1 - 100%, NULL - progress cannot be estimated';
        
        CREATE TRIGGER update_import_modified
            BEFORE UPDATE
            ON "public"."import"
            FOR EACH ROW
        EXECUTE PROCEDURE update_modified_column();
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
          DROP TABLE IF EXISTS import;
          DROP TYPE IF EXISTS import_status;
          DROP TYPE IF EXISTS import_process;
    `)
};
