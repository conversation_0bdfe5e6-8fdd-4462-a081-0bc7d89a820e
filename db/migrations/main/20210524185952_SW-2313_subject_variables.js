
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add subject flag to Ticket Customers group -----------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "link",
            "title": "Receipt Link",
            "pattern": "{link}",
            "custom_action": true
          },
          {
            "field": "participant_name",
            "title": "Participant Name",
            "pattern": "{participant}",
            "is_available_for_subject": true
          },
          {
            "field": "camps_names_list",
            "title": "Bought Camps List",
            "pattern": "{camps}"
          },
          {
            "field": "tickets_names_list",
            "title": "Bought Ticket Types List",
            "pattern": "{tickets}"
          },
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "payer",
            "title": "Payer''''s Name",
            "pattern": "{payer}",
            "is_available_for_subject": true
          }
        ]'
        WHERE "group" = 'tickets';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Head Referee group ---------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "head_referee_name",
            "title": "Head Referee Name",
            "pattern": "{head_referee_name}",
            "is_available_for_subject": true
          },
          {
            "field": "head_referee_phone",
            "title": "Head Referee Phone",
            "pattern": "{head_referee_phone}"
          }
        ]'
        WHERE "group" = 'head.referee';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Officials group ------------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "official_first",
            "title": "Official First Name",
            "pattern": "{official_first}",
            "is_available_for_subject": true
          },
          {
            "field": "official_last",
            "title": "Official Last Name",
            "pattern": "{official_last}",
            "is_available_for_subject": true
          },
          {
            "field": "official_name",
            "title": "Official First and Last Name",
            "pattern": "{official_name}",
            "is_available_for_subject": true
          },
          {
            "field": "official_admin_role_first",
            "title": "Head Referee''s or Event Owner''s First",
            "pattern": "{official_admin_role_first}"
          },
          {
            "field": "official_admin_role_last",
            "title": "Head Referee''s or Event Owner''s Last",
            "pattern": "{official_admin_role_last}"
          },
          {
            "field": "official_admin_role_email",
            "title": "Head Referee''s or Event Owner''s Email",
            "pattern": "{official_admin_role_email}"
          },
          {
            "field": "official_admin_role_phone",
            "title": "Head Referee''s or Event Owner''s Phone",
            "pattern": "{official_admin_role_phone}"
          }
        ]'
        WHERE "group" = 'officials';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Teams & Clubs group --------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "club_name",
            "title": "Club Name",
            "pattern": "{club_name}",
            "is_available_for_subject": true
          },
          {
            "field": "receiver_first",
            "title": "Receiver First Name",
            "pattern": "{receiver_first}",
            "is_available_for_subject": true
          },
          {
            "field": "receiver_last",
            "title": "Receiver Last Name",
            "pattern": "{receiver_last}",
            "is_available_for_subject": true
          },
          {
            "field": "receiver_name",
            "title": "Receiver First and Last Name",
            "pattern": "{receiver_name}",
            "is_available_for_subject": true
          },
          {
            "field": "roster_teams",
            "title": "Team Names (separated by comma)",
            "pattern": "{teams}"
          },
          {
            "field": "teams_divs",
            "title": "Team Names with divisions (separated by comma)",
            "pattern": "{teams_divs}",
            "custom_action": true
          },
          {
            "field": "social_icons",
            "title": "Social Icons",
            "pattern": "{social_icons}",
            "custom_action": true
          },
          {
            "field": "facebook_icon",
            "title": "Facebook Icon",
            "pattern": "{facebook_icon}",
            "custom_action": true
          },
          {
            "field": "twitter_icon",
            "title": "Twitter Icon",
            "pattern": "{twitter_icon}",
            "custom_action": true
          },
          {
            "field": "instagram_icon",
            "title": "Instagram Icon",
            "pattern": "{instagram_icon}",
            "custom_action": true
          },
          {
            "field": "snapchat_icon",
            "title": "Snapchat Icon",
            "pattern": "{snapchat_icon}",
            "custom_action": true
          },
          {
            "field": "director_first",
            "title": "Director First Name",
            "pattern": "{director_first}",
            "is_hidden": true,
            "is_available_for_subject": true
          },
          {
            "field": "director_last",
            "title": "Director Last Name",
            "pattern": "{director_last}",
            "is_hidden": true,
            "is_available_for_subject": true
          },
          {
            "field": "director_name",
            "title": "Director First and Last Name",
            "pattern": "{director_name}",
            "is_hidden": true,
            "is_available_for_subject": true
          }
        ]'
        WHERE "group" = 'clubs';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Payments for teams group ---------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "club_name",
            "title": "Club Name",
            "pattern": "{club_name}",
            "is_available_for_subject": true
          },
          {
            "field": "team_names",
            "title": "Team Name(s)",
            "pattern": "{team_names}"
          },
          {
            "field": "payment_type",
            "title": "Payment Type",
            "pattern": "{payment_type}"
          },
          {
            "field": "payment_status",
            "title": "Payment Status",
            "pattern": "{payment_status}"
          },
          {
            "field": "payment_amount",
            "title": "Payment Amount",
            "pattern": "{payment_amount}"
          },
          {
            "field": "payment_created_date",
            "title": "Payment Created Date",
            "pattern": "{payment_created_date}"
          },
          {
            "field": "payment_date_paid",
            "title": "Payment Date Paid",
            "pattern": "{payment_date_paid}"
          },
          {
            "field": "payment_canceled_date",
            "title": "Payment Canceled Date",
            "pattern": "{payment_canceled_date}"
          },
          {
            "field": "eo_first",
            "title": "Event Owner First",
            "pattern": "{eo_first}",
            "is_available_for_subject": true
          },
          {
            "field": "eo_last",
            "title": "Event Owner Last",
            "pattern": "{eo_last}",
            "is_available_for_subject": true
          },
          {
            "field": "eo_email",
            "title": "Event Owner Email",
            "pattern": "{eo_email}"
          },
          {
            "field": "eo_phone",
            "title": "Event Owner Phone",
            "pattern": "{eo_phone}"
          },
          {
            "field": "cd_first",
            "title": "Club Director First",
            "pattern": "{cd_first}"
          },
          {
            "field": "cd_last",
            "title": "Club Director Last",
            "pattern": "{cd_last}",
            "is_available_for_subject": true
          },
          {
            "field": "cd_email",
            "title": "Club Director Email",
            "pattern": "{cd_email}",
            "is_available_for_subject": true
          },
          {
            "field": "cd_phone",
            "title": "Club Director Phone",
            "pattern": "{cd_phone}"
          },
          {
            "field": "failure_code",
            "title": "Failure Code",
            "pattern": "{failure_code}"
          },
          {
            "field": "failure_message",
            "title": "Failure Message",
            "pattern": "{failure_message}"
          }
        ]'
        WHERE "group" = 'teams.payments';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Staff group ----------------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "staff_first",
            "title": "Staff First Name",
            "pattern": "{staff_first}",
            "is_available_for_subject": true
          },
          {
            "field": "staff_last",
            "title": "Staff Last Name",
            "pattern": "{staff_last}",
            "is_available_for_subject": true
          },
          {
            "field": "staff_name",
            "title": "Staff First and Last Name",
            "pattern": "{staff_name}",
            "is_available_for_subject": true
          }
        ]'
        WHERE "group" = 'staff';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Event Owners group ---------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "first",
            "title": "Event Owner First Name",
            "pattern": "{eo_first}",
            "is_available_for_subject": true
          },
          {
            "field": "last",
            "title": "Event Owner Last Name",
            "pattern": "{eo_last}",
            "is_available_for_subject": true
          },
          {
            "field": "name",
            "title": "Event Owner First and Last Name",
            "pattern": "{eo_name}",
            "is_available_for_subject": true
          }
        ]'
        WHERE "group" = 'event.owners';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Camps group ----------------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "link",
            "title": "Receipt Link",
            "pattern": "{link}",
            "custom_action": true
          },
          {
            "field": "participant_name",
            "title": "Participant Name",
            "pattern": "{participant}",
            "is_available_for_subject": true
          },
          {
            "field": "camps_names_list",
            "title": "Bought Camps List",
            "pattern": "{camps}"
          },
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "payer",
            "title": "Payer''s Name",
            "pattern": "{payer}",
            "is_available_for_subject": true
          }
        ]'
        WHERE "group" = 'camps';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Custom List group ----------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "receiver_first",
            "title": "Receiver First Name",
            "pattern": "{receiver_first}",
            "is_available_for_subject": true
          },
          {
            "field": "receiver_last",
            "title": "Receiver Last Name",
            "pattern": "{receiver_last}",
            "is_available_for_subject": true
          }
        ]'
        WHERE "group" = 'custom_list';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Exhibitors group -----------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "company_name",
            "title": "Company Name",
            "pattern": "{company_name}",
            "is_available_for_subject": true
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          }
        ]'
        WHERE "group" = 'exhibitors';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Booths Payments group ------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "company_name",
            "title": "Company Name",
            "pattern": "{company_name}",
            "is_available_for_subject": true
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "link_to_invoice",
            "title": "Link To The Invoice",
            "pattern": "{link_to_invoice}"
          }
        ]'
        WHERE "group" = 'booths.payments';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add subject flag to Tickets Payments group -----------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
          },
          {
            "field": "amount_refunded",
            "title": "Amount Refunded",
            "pattern": "{amount_refunded}"
          },
          {
            "field": "date_refunded",
            "title": "Date Issued",
            "pattern": "{date_refunded}"
          },
          {
            "field": "ticket_code",
            "title": "Ticket Code",
            "pattern": "{ticket_code}"
          },
          {
            "field": "payer",
            "title": "Payer''s Name",
            "pattern": "{payer}",
            "is_available_for_subject": true
          },
          {
            "field": "eo_name",
            "title": "Event Owner First and Last Name",
            "pattern": "{eo_name}"
          },
          {
            "field": "eo_email",
            "title": "Event Owner Email",
            "pattern": "{eo_email}"
          },
          {
            "field": "eo_phone",
            "title": "Event Owner Phone",
            "pattern": "{eo_phone}"
          },
          {
            "field": "tickets_returned",
            "title": "Returned Tickets List",
            "pattern": "{tickets_returned}",
            "custom_action": true
          },
          {
            "field": "tickets_names_list",
            "title": "Bought Ticket Types List",
            "pattern": "{tickets}"
          },
          {
            "field": "camps_names_list",
            "title": "Bought Camps List",
            "pattern": "{camps}"
          },
          {
            "field": "receipt_link",
            "title": "List to Receipt",
            "pattern": "{receipt_link}",
            "custom_action": true
          },
          {
            "field": "purchase_date",
            "title": "Purchase Date",
            "pattern": "{purchase_date}"
          },
          {
            "field": "total_amount",
            "title": "Total Purchase Amount",
            "pattern": "{total_amount}"
          },
          {
            "field": "adjusted_total_amount",
            "title": "Adjusted Total Amount",
            "pattern": "{adjusted_total_amount}"
          },
          {
            "field": "payments_list_link",
            "title": "Link to Event Owner Purchases List",
            "pattern": "{payments_list_link}",
            "custom_action": true
          },
          {
            "field": "social_icons",
            "title": "Social Icons",
            "pattern": "{social_icons}",
            "custom_action": true
          },
          {
            "field": "facebook_icon",
            "title": "Facebook Icon",
            "pattern": "{facebook_icon}",
            "custom_action": true
          },
          {
            "field": "twitter_icon",
            "title": "Twitter Icon",
            "pattern": "{twitter_icon}",
            "custom_action": true
          },
          {
            "field": "instagram_icon",
            "title": "Instagram Icon",
            "pattern": "{instagram_icon}",
            "custom_action": true
          },
          {
            "field": "snapchat_icon",
            "title": "Snapchat Icon",
            "pattern": "{snapchat_icon}",
            "custom_action": true
          },
          {
            "field": "card_last_4",
            "title": "Last four digits card",
            "pattern": "{card_last_4}"
          },
          {
            "field": "eo_stripe_account",
            "title": "EO Stripe Account ID",
            "pattern": "{eo_stripe_account}"
          },
          {
            "field": "assigned_ticket_total_amount",
            "title": "Assigned Ticket Total Amount",
            "pattern": "{assigned_ticket_total_amount}"
          },
          {
            "field": "assigned_ticket_refunded_amount",
            "title": "Assigned Ticket Refunded Amount",
            "pattern": "{assigned_ticket_refunded_amount}"
          },
          {
            "field": "assigned_ticket_adjusted_total",
            "title": "Assigned Ticket Adjusted Total",
            "pattern": "{assigned_ticket_adjusted_total}"
          },
          {
            "field": "basic_tickets_names_list",
            "title": "Bought Basic Ticket Types List",
            "pattern": "{basic_tickets}"
          }
        ]'
        WHERE "group" = 'tickets.payments';
        -- ------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        -- Remove subject flag from Ticket Customers group ------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "link",
            "title": "Receipt Link",
            "pattern": "{link}",
            "custom_action": true
          },
          {
            "field": "participant_name",
            "title": "Participant Name",
            "pattern": "{participant}"
          },
          {
            "field": "camps_names_list",
            "title": "Bought Camps List",
            "pattern": "{camps}"
          },
          {
            "field": "tickets_names_list",
            "title": "Bought Ticket Types List",
            "pattern": "{tickets}"
          },
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "payer",
            "title": "Payer''''s Name",
            "pattern": "{payer}"
          }
        ]'
        WHERE "group" = 'tickets';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Head Referee group ----------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "head_referee_name",
            "title": "Head Referee Name",
            "pattern": "{head_referee_name}"
          },
          {
            "field": "head_referee_phone",
            "title": "Head Referee Phone",
            "pattern": "{head_referee_phone}"
          }
        ]'
        WHERE "group" = 'head.referee';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Officials group -------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "official_first",
            "title": "Official First Name",
            "pattern": "{official_first}"
          },
          {
            "field": "official_last",
            "title": "Official Last Name",
            "pattern": "{official_last}"
          },
          {
            "field": "official_name",
            "title": "Official First and Last Name",
            "pattern": "{official_name}"
          },
          {
            "field": "official_admin_role_first",
            "title": "Head Referee''s or Event Owner''s First",
            "pattern": "{official_admin_role_first}"
          },
          {
            "field": "official_admin_role_last",
            "title": "Head Referee''s or Event Owner''s Last",
            "pattern": "{official_admin_role_last}"
          },
          {
            "field": "official_admin_role_email",
            "title": "Head Referee''s or Event Owner''s Email",
            "pattern": "{official_admin_role_email}"
          },
          {
            "field": "official_admin_role_phone",
            "title": "Head Referee''s or Event Owner''s Phone",
            "pattern": "{official_admin_role_phone}"
          }
        ]'
        WHERE "group" = 'officials';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Teams & Clubs group ---------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "club_name",
            "title": "Club Name",
            "pattern": "{club_name}"
          },
          {
            "field": "receiver_first",
            "title": "Receiver First Name",
            "pattern": "{receiver_first}"
          },
          {
            "field": "receiver_last",
            "title": "Receiver Last Name",
            "pattern": "{receiver_last}"
          },
          {
            "field": "receiver_name",
            "title": "Receiver First and Last Name",
            "pattern": "{receiver_name}"
          },
          {
            "field": "roster_teams",
            "title": "Team Names (separated by comma)",
            "pattern": "{teams}"
          },
          {
            "field": "teams_divs",
            "title": "Team Names with divisions (separated by comma)",
            "pattern": "{teams_divs}",
            "custom_action": true
          },
          {
            "field": "social_icons",
            "title": "Social Icons",
            "pattern": "{social_icons}",
            "custom_action": true
          },
          {
            "field": "facebook_icon",
            "title": "Facebook Icon",
            "pattern": "{facebook_icon}",
            "custom_action": true
          },
          {
            "field": "twitter_icon",
            "title": "Twitter Icon",
            "pattern": "{twitter_icon}",
            "custom_action": true
          },
          {
            "field": "instagram_icon",
            "title": "Instagram Icon",
            "pattern": "{instagram_icon}",
            "custom_action": true
          },
          {
            "field": "snapchat_icon",
            "title": "Snapchat Icon",
            "pattern": "{snapchat_icon}",
            "custom_action": true
          },
          {
            "field": "director_first",
            "title": "Director First Name",
            "pattern": "{director_first}",
            "is_hidden": true
          },
          {
            "field": "director_last",
            "title": "Director Last Name",
            "pattern": "{director_last}",
            "is_hidden": true
          },
          {
            "field": "director_name",
            "title": "Director First and Last Name",
            "pattern": "{director_name}",
            "is_hidden": true
          }
        ]'
        WHERE "group" = 'clubs';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Payments for teams group ----------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "club_name",
            "title": "Club Name",
            "pattern": "{club_name}"
          },
          {
            "field": "team_names",
            "title": "Team Name(s)",
            "pattern": "{team_names}"
          },
          {
            "field": "payment_type",
            "title": "Payment Type",
            "pattern": "{payment_type}"
          },
          {
            "field": "payment_status",
            "title": "Payment Status",
            "pattern": "{payment_status}"
          },
          {
            "field": "payment_amount",
            "title": "Payment Amount",
            "pattern": "{payment_amount}"
          },
          {
            "field": "payment_created_date",
            "title": "Payment Created Date",
            "pattern": "{payment_created_date}"
          },
          {
            "field": "payment_date_paid",
            "title": "Payment Date Paid",
            "pattern": "{payment_date_paid}"
          },
          {
            "field": "payment_canceled_date",
            "title": "Payment Canceled Date",
            "pattern": "{payment_canceled_date}"
          },
          {
            "field": "eo_first",
            "title": "Event Owner First",
            "pattern": "{eo_first}"
          },
          {
            "field": "eo_last",
            "title": "Event Owner Last",
            "pattern": "{eo_last}"
          },
          {
            "field": "eo_email",
            "title": "Event Owner Email",
            "pattern": "{eo_email}"
          },
          {
            "field": "eo_phone",
            "title": "Event Owner Phone",
            "pattern": "{eo_phone}"
          },
          {
            "field": "cd_first",
            "title": "Club Director First",
            "pattern": "{cd_first}"
          },
          {
            "field": "cd_last",
            "title": "Club Director Last",
            "pattern": "{cd_last}"
          },
          {
            "field": "cd_email",
            "title": "Club Director Email",
            "pattern": "{cd_email}"
          },
          {
            "field": "cd_phone",
            "title": "Club Director Phone",
            "pattern": "{cd_phone}"
          },
          {
            "field": "failure_code",
            "title": "Failure Code",
            "pattern": "{failure_code}"
          },
          {
            "field": "failure_message",
            "title": "Failure Message",
            "pattern": "{failure_message}"
          }
        ]'
        WHERE "group" = 'teams.payments';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Staff group -----------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "staff_first",
            "title": "Staff First Name",
            "pattern": "{staff_first}"
          },
          {
            "field": "staff_last",
            "title": "Staff Last Name",
            "pattern": "{staff_last}"
          },
          {
            "field": "staff_name",
            "title": "Staff First and Last Name",
            "pattern": "{staff_name}"
          }
        ]'
        WHERE "group" = 'staff';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Event Owners group ----------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "first",
            "title": "Event Owner First Name",
            "pattern": "{eo_first}"
          },
          {
            "field": "last",
            "title": "Event Owner Last Name",
            "pattern": "{eo_last}"
          },
          {
            "field": "name",
            "title": "Event Owner First and Last Name",
            "pattern": "{eo_name}"
          }
        ]'
        WHERE "group" = 'event.owners';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Camps group -----------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "link",
            "title": "Receipt Link",
            "pattern": "{link}",
            "custom_action": true
          },
          {
            "field": "participant_name",
            "title": "Participant Name",
            "pattern": "{participant}"
          },
          {
            "field": "camps_names_list",
            "title": "Bought Camps List",
            "pattern": "{camps}"
          },
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "payer",
            "title": "Payer''s Name",
            "pattern": "{payer}"
          }
        ]'
        WHERE "group" = 'camps';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Custom List group -----------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "receiver_first",
            "title": "Receiver First Name",
            "pattern": "{receiver_first}"
          },
          {
            "field": "receiver_last",
            "title": "Receiver Last Name",
            "pattern": "{receiver_last}"
          }
        ]'
        WHERE "group" = 'custom_list';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Exhibitors group ------------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "company_name",
            "title": "Company Name",
            "pattern": "{company_name}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          }
        ]'
        WHERE "group" = 'exhibitors';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Booths Payments group -------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
          },
          {
            "field": "company_name",
            "title": "Company Name",
            "pattern": "{company_name}"
          },
          {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
          },
          {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
          },
          {
            "field": "link_to_invoice",
            "title": "Link To The Invoice",
            "pattern": "{link_to_invoice}"
          }
        ]'
        WHERE "group" = 'booths.payments';
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Remove subject flag from Tickets Payments group ------------------------------------------------------------
        UPDATE email_template_group
        SET variables = '[
          {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}"
          },
          {
            "field": "amount_refunded",
            "title": "Amount Refunded",
            "pattern": "{amount_refunded}"
          },
          {
            "field": "date_refunded",
            "title": "Date Issued",
            "pattern": "{date_refunded}"
          },
          {
            "field": "ticket_code",
            "title": "Ticket Code",
            "pattern": "{ticket_code}"
          },
          {
            "field": "payer",
            "title": "Payer''s Name",
            "pattern": "{payer}"
          },
          {
            "field": "eo_name",
            "title": "Event Owner First and Last Name",
            "pattern": "{eo_name}"
          },
          {
            "field": "eo_email",
            "title": "Event Owner Email",
            "pattern": "{eo_email}"
          },
          {
            "field": "eo_phone",
            "title": "Event Owner Phone",
            "pattern": "{eo_phone}"
          },
          {
            "field": "tickets_returned",
            "title": "Returned Tickets List",
            "pattern": "{tickets_returned}",
            "custom_action": true
          },
          {
            "field": "tickets_names_list",
            "title": "Bought Ticket Types List",
            "pattern": "{tickets}"
          },
          {
            "field": "camps_names_list",
            "title": "Bought Camps List",
            "pattern": "{camps}"
          },
          {
            "field": "receipt_link",
            "title": "List to Receipt",
            "pattern": "{receipt_link}",
            "custom_action": true
          },
          {
            "field": "purchase_date",
            "title": "Purchase Date",
            "pattern": "{purchase_date}"
          },
          {
            "field": "total_amount",
            "title": "Total Purchase Amount",
            "pattern": "{total_amount}"
          },
          {
            "field": "adjusted_total_amount",
            "title": "Adjusted Total Amount",
            "pattern": "{adjusted_total_amount}"
          },
          {
            "field": "payments_list_link",
            "title": "Link to Event Owner Purchases List",
            "pattern": "{payments_list_link}",
            "custom_action": true
          },
          {
            "field": "social_icons",
            "title": "Social Icons",
            "pattern": "{social_icons}",
            "custom_action": true
          },
          {
            "field": "facebook_icon",
            "title": "Facebook Icon",
            "pattern": "{facebook_icon}",
            "custom_action": true
          },
          {
            "field": "twitter_icon",
            "title": "Twitter Icon",
            "pattern": "{twitter_icon}",
            "custom_action": true
          },
          {
            "field": "instagram_icon",
            "title": "Instagram Icon",
            "pattern": "{instagram_icon}",
            "custom_action": true
          },
          {
            "field": "snapchat_icon",
            "title": "Snapchat Icon",
            "pattern": "{snapchat_icon}",
            "custom_action": true
          },
          {
            "field": "card_last_4",
            "title": "Last four digits card",
            "pattern": "{card_last_4}"
          },
          {
            "field": "eo_stripe_account",
            "title": "EO Stripe Account ID",
            "pattern": "{eo_stripe_account}"
          },
          {
            "field": "assigned_ticket_total_amount",
            "title": "Assigned Ticket Total Amount",
            "pattern": "{assigned_ticket_total_amount}"
          },
          {
            "field": "assigned_ticket_refunded_amount",
            "title": "Assigned Ticket Refunded Amount",
            "pattern": "{assigned_ticket_refunded_amount}"
          },
          {
            "field": "assigned_ticket_adjusted_total",
            "title": "Assigned Ticket Adjusted Total",
            "pattern": "{assigned_ticket_adjusted_total}"
          },
          {
            "field": "basic_tickets_names_list",
            "title": "Bought Basic Ticket Types List",
            "pattern": "{basic_tickets}"
          }
        ]'
        WHERE "group" = 'tickets.payments';
        -- ------------------------------------------------------------------------------------------------------------
    `)
};
