exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "ncsa_recruit_request" -------------------------------------------------------------------------
        CREATE TABLE "public"."ncsa_recruit_request"
            (
                "ncsa_recruit_request_id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                "created"                 TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
                "modified"                TIMESTAMP WITH TIME ZONE DEFAULT NULL,
                "event_id"                INTEGER NOT NULL,
                "request"                 JSONB NOT NULL,
                "response"                JSONB NOT NULL
            );
        ----------------------------------------------------------------------------------------------------------------
           
        --- Add modified trigger to ncsa_recruit_request table ---------------------------------------------------------
        CREATE TRIGGER "update_ncsa_recruit_request_modified"
            BEFORE UPDATE
            ON "public"."ncsa_recruit_request"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        ----------------------------------------------------------------------------------------------------------------
        `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."ncsa_recruit_request";
    `)
};
