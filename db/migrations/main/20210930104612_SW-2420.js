
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE OR REPLACE VIEW v_aem_teams_payments_data
        AS
        SELECT  p.purchase_id, p.stripe_charge_id, p.event_id,
                COALESCE(e.long_name, e.name) as event_name,
                mc.club_name as club_name,
                array_to_html_list(array_agg(rt.team_name )) as team_names,
                p.type as payment_type,
                p.status as payment_status,
                p.amount as payment_amount,
                to_char(p.created, 'HH12:MI AM, Mon DD, YYYY') as payment_created_date,
                to_char(p.date_paid, 'HH12:MI AM, Mon DD, YYYY') as payment_date_paid,
                to_char(p.canceled_date, 'HH12:MI AM, Mon DD, YYYY') as payment_canceled_date,
                u1.first as eo_first, u1.last as eo_last, COALESCE(e.email, u1.email) as eo_email,
                format_phone_number(u1.phone_mob) as eo_phone,
                u2.first as cd_first, u2.last as cd_last, u2.email as cd_email,
                format_phone_number(u2.phone_mob) as cd_phone,
                p.roster_club_id as roster_club_id,
                mc.administrative_email as administrative_club_email
        FROM purchase p JOIN event e ON p.event_id = e.event_id
                JOIN roster_club rc ON rc.roster_club_id = p.roster_club_id
                JOIN master_club mc on mc.master_club_id = rc.master_club_id
                JOIN purchase_team pt ON pt.purchase_id = p.purchase_id
                JOIN roster_team rt ON rt.roster_team_id = pt.roster_team_id AND rt.event_id = pt.event_id
                JOIN event_owner eo on e.event_owner_id = eo.event_owner_id
                JOIN public.user u1 ON u1.user_id = eo.user_id   
                JOIN club_owner co ON co.club_owner_id = mc.club_owner_id 
                JOIN public.user u2 ON u2.user_id = co.user_id    
        GROUP BY p.purchase_id, p.stripe_charge_id, p.event_id, e.long_name, e.name, mc.administrative_email,
                 mc.club_name, p.type, p.status, p.amount, p.created, p.date_paid,
                 p.canceled_date, u1.first, u1.last, e.email, u1.email, u1.phone_mob,
                 u2.first, u2.last, u2.email, u2.phone_mob, e.timezone;
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        CREATE OR REPLACE VIEW v_aem_teams_payments_data
        AS
        SELECT  p.purchase_id, p.stripe_charge_id, p.event_id,
                COALESCE(e.long_name, e.name) as event_name, 
                mc.club_name as club_name, 
                array_to_html_list(array_agg(rt.team_name )) as team_names, 
                p.type as payment_type, 
                p.status as payment_status,
                p.amount as payment_amount,
                to_char(p.created, 'HH12:MI AM, Mon DD, YYYY') as payment_created_date,
                to_char(p.date_paid, 'HH12:MI AM, Mon DD, YYYY') as payment_date_paid,
                to_char(p.canceled_date, 'HH12:MI AM, Mon DD, YYYY') as payment_canceled_date,
                u1.first as eo_first, u1.last as eo_last, COALESCE(e.email, u1.email) as eo_email, 
                format_phone_number(u1.phone_mob) as eo_phone,
                u2.first as cd_first, u2.last as cd_last, u2.email as cd_email,
                format_phone_number(u2.phone_mob) as cd_phone,
                p.roster_club_id as roster_club_id
        FROM purchase p JOIN event e ON p.event_id = e.event_id
                JOIN roster_club rc ON rc.roster_club_id = p.roster_club_id
                JOIN master_club mc on mc.master_club_id = rc.master_club_id
                JOIN purchase_team pt ON pt.purchase_id = p.purchase_id
                JOIN roster_team rt ON rt.roster_team_id = pt.roster_team_id AND rt.event_id = pt.event_id
                JOIN event_owner eo on e.event_owner_id = eo.event_owner_id
                JOIN public.user u1 ON u1.user_id = eo.user_id   
                JOIN club_owner co ON co.club_owner_id = mc.club_owner_id 
                JOIN public.user u2 ON u2.user_id = co.user_id    
        GROUP BY p.purchase_id, p.stripe_charge_id, p.event_id, e.long_name, e.name,
                 mc.club_name, p.type, p.status, p.amount, p.created, p.date_paid,
                 p.canceled_date, u1.first, u1.last, e.email, u1.email, u1.phone_mob,
                 u2.first, u2.last, u2.email, u2.phone_mob, e.timezone;
    `)
};
