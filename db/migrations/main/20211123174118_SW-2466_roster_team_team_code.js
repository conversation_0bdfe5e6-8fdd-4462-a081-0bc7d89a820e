
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE roster_team ADD COLUMN IF NOT EXISTS team_code TEXT DEFAULT NULL;

        COMMENT ON COLUMN roster_team.team_code 
            IS 'Manually added team code. Can be used as code for entering tickets buying page';
        
        ALTER TABLE roster_team ADD CONSTRAINT "unique_roster_team_team_code_event_id" UNIQUE( event_id, team_code );
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE roster_team DROP COLUMN IF EXISTS team_code;
    `)
};
