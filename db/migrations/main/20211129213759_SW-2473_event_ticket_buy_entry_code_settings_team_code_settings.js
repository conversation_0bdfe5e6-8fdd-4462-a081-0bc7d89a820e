
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE event_ticket_buy_entry_code_settings ADD COLUMN IF NOT EXISTS team_code_settings JSONB;

        COMMENT ON COLUMN event_ticket_buy_entry_code_settings.team_code_settings
            IS 'Here can be stored any settings related to team code source';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE event_ticket_buy_entry_code_settings DROP COLUMN IF EXISTS team_code_settings;
    `)
};
