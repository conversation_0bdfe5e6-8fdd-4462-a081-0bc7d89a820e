exports.up = function (knex) {
    return knex.schema.raw(`
        DROP INDEX "index_roster_athlete_deleted";
        CREATE INDEX "index_roster_athlete_deleted" ON "public"."roster_athlete" USING btree( "deleted", "event_id", "deleted_by_user" );
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
        DROP INDEX "index_roster_athlete_deleted";
        CREATE INDEX "index_roster_athlete_deleted" ON "public"."roster_athlete" USING btree( "deleted" );
    `);
};
