
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."purchase" ADD COLUMN IF NOT EXISTS "payment_intent_id" TEXT DEFAULT NULL;
        COMMENT ON COLUMN "public"."purchase"."payment_intent_id" IS 'Purchase and Payment Intent relation';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."purchase" DROP COLUMN IF EXISTS "payment_intent_id";
    `)
};
