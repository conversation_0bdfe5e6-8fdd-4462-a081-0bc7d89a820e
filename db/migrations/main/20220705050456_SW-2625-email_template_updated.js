
exports.up = function(knex) {
  return knex.schema.raw(String.raw`
    UPDATE email_template SET email_text = '{event_name}  Thank you for your purchase! This email contains confirmation of your purchase details.
    A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device along with a Government photo ID to gain entry. {tickets_names_list}
    Purchaser: {payer} Total Price: {total_amount}
    This payment will show up on your credit card statement as: {statement_decriptor}  List of all ticket holders on this purchase:
    (Click on a name to access each ticket if you do not receive the individual QR code ticket email) {tickets_links}
    Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy. {tickets_receipt_descr}  {social_icons}  © SportWrench Inc. 2021',
    email_html = '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><!--[if !mso]><!--><link href="https://fonts.googleapis.com/css$2family=Roboto" 
    rel="stylesheet" type="text/css"><!--<![endif]--><style>
    *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}@media (max-width:660px){.row-content{width:100%!important}.mobile_hide{display:none}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
    </style></head><body style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
    class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#333;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:15px;padding-bottom:15px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" 
    cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:Arial,sans-serif"><div class="txtTinyMce-wrapper" style="font-size:12px;font-family:Arial,''Helvetica Neue'',Helvetica,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:16px"><strong><span style="font-size:18px;">{event_name}&nbsp;</span></strong></p><p 
    style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px"><span style="font-size:16px;">Thank you for your purchase! This email contains confirmation of your purchase details.</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px">
    <span style="font-size:16px;">A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device <u><strong>along with a Government photo ID</strong></u> to gain entry.</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px">{tickets_names_list}</p><p style="margin:0;font-size:16px">
    <span style="font-size:16px;">Purchaser: {payer} </span></p><p style="margin:0;font-size:16px"><span style="font-size:16px;">Total Price: <strong>{total_amount}</strong></span></p><p style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px">
    <span style="font-size:16px;"><span style="font-size:14px;">This payment will show up on your credit card statement as: {statement_decriptor}&nbsp;</span><strong></strong></span></p><p style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px"><span style="font-size:16px;"><strong><span style="font-size:18px;">List of all ticket holders on this purchase:</span></strong> <br>
    <span style="font-size:14px;">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></span></p><p style="margin:0;font-size:16px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:center">{tickets_links}&nbsp;</p><p style="margin:0;font-size:16px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:left">
    Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.</p><p style="margin:0;font-size:16px;text-align:left;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:left">{tickets_receipt_descr}&nbsp;</p><p style="margin:0;font-size:16px;text-align:left;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p 
    style="margin:0;font-size:16px;text-align:center">{social_icons}&nbsp;</p><p style="margin:0;font-size:16px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:center">© SportWrench Inc. 2021</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
    bee_json = '{
        "page": {
        "body": {
            "type": "mailup-bee-page-proprerties",
            "content": {
            "style": {
                "color": "#000000",
                "font-family": "Tahoma, Verdana, Segoe, sans-serif"
            },
            "computedStyle": {
                "linkColor": "#0000FF",
                "messageWidth": "640px",
                "messageBackgroundColor": "#FFFFFF"
            }
            },
            "webFonts": [
            {
                "url": "https://fonts.googleapis.com/css$2family=Roboto",
                "name": "Roboto",
                "family": "''Roboto'', Tahoma, Verdana, Segoe, sans-serif",
                "fontName": "Roboto",
                "fontFamily": "''Roboto'', Tahoma, Verdana, Segoe, sans-serif"
            }
            ],
            "container": {
            "style": {
                "background-color": "#F7F7F7"
            }
            }
        },
        "rows": [
            {
            "type": "two-columns-4-8-empty",
            "uuid": "de77faba-c223-4f01-9b10-c7ac5ddc692b",
            "columns": [
                {
                "uuid": "80888d62-98f4-42bb-8b15-56fac019abc9",
                "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "15px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "15px",
                    "background-color": "transparent"
                },
                "modules": [
                    {
                    "type": "mailup-bee-newsletter-modules-text",
                    "uuid": "8f37f534-d236-42d1-ab29-fc309d22a2f3",
                    "descriptor": {
                        "text": {
                        "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Arial, ''Helvetica Neue'', Helvetica, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Arial, ''Helvetica Neue'', Helvetica, sans-serif;\"><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event Name\">{event_name}</code>&nbsp;</span></strong></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Thank you for your purchase! This email contains confirmation of your purchase details.</span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device <u><strong>along with a Government photo ID</strong></u> to gain entry.</span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">{tickets_names_list}</code></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">Purchaser:&nbsp;</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Payer''s Name\">{payer}</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">&nbsp;</code></span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">Total Price:&nbsp;</code><strong><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Total Purchase Amount\">{total_amount}</code></strong></span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">This payment will show up on your credit card statement as: {statement_decriptor}&nbsp;</span><strong></strong></span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\">List of all ticket holders on this purchase:</span></strong> <br><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></span></p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Links\">{tickets_links}</code>&nbsp;</p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event Disclaimer\">{tickets_receipt_descr}</code>&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Social Icons\">{social_icons}</code>&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\">© SportWrench Inc. 2021</p></div>",
                        "style": {
                            "color": "#555555",
                            "font-family": "Arial, ''Helvetica Neue'', Helvetica, sans-serif",
                            "line-height": "120%"
                        },
                        "computedStyle": {
                            "linkColor": "#0000FF"
                        }
                        },
                        "style": {
                        "padding-top": "10px",
                        "padding-left": "10px",
                        "padding-right": "10px",
                        "padding-bottom": "10px"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false
                        }
                    }
                    }
                ],
                "grid-columns": 12
                }
            ],
            "content": {
                "style": {
                "color": "#333",
                "width": "640px",
                "background-color": "#FFFFFF",
                "background-image": "none",
                "background-repeat": "no-repeat",
                "background-position": "top left"
                }
            },
            "container": {
                "style": {
                "background-color": "transparent",
                "background-image": "none",
                "background-repeat": "no-repeat",
                "background-position": "top left"
                }
            }
            }
        ],
        "title": "BF-basic-newsletter",
        "template": {
            "name": "template-base",
            "type": "basic",
            "version": "0.0.1"
        },
        "description": "BF-basic-newsletter"
        },
        "comments": {}
    }'
    where email_template_type = 'tickets.assigned.receipt'
    and email_template_group = 'tickets.payments'
  `)
};

exports.down = function(knex) {
    return knex.schema.raw(String.raw`
        UPDATE email_template SET email_text = '{event_name}  Thank you for your purchase! This email contains confirmation of your purchase details.
        A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device along with a Government photo ID to gain entry. {tickets_names_list}
        Purchaser: {payer} Total Price: {total_amount}
        List of all ticket holders on this purchase: (Click on a name to access each ticket if you do not receive the individual QR code ticket email) {tickets_links}  Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.
        {tickets_receipt_descr}  {social_icons}  © SportWrench Inc. 2021',
        email_html = '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><!--[if !mso]><!--><link href="https://fonts.googleapis.com/css$2family=Roboto" 
        rel="stylesheet" type="text/css"><!--<![endif]--><style>
        *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}@media (max-width:660px){.row-content{width:100%!important}.mobile_hide{display:none}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
        </style></head><body style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
        class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#333;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:15px;padding-bottom:15px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" 
        cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:Arial,sans-serif"><div class="txtTinyMce-wrapper" style="font-size:12px;font-family:Arial,''Helvetica Neue'',Helvetica,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:16px"><strong><span style="font-size:18px;">{event_name}&nbsp;</span></strong></p><p 
        style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px"><span style="font-size:16px;">Thank you for your purchase! This email contains confirmation of your purchase details.</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px">
        <span style="font-size:16px;">A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device <u><strong>along with a Government photo ID</strong></u> to gain entry.</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px">{tickets_names_list}</p><p style="margin:0;font-size:16px">
        <span style="font-size:16px;">Purchaser: {payer} </span></p><p style="margin:0;font-size:16px"><span style="font-size:16px;">Total Price: <strong>{total_amount}</strong></span></p><p style="margin:0;font-size:16px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px">
        <span style="font-size:16px;"><strong><span style="font-size:18px;">List of all ticket holders on this purchase:</span></strong> <br><span style="font-size:14px;">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></span></p><p style="margin:0;font-size:16px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:center">{tickets_links}&nbsp;</p><p 
        style="margin:0;font-size:16px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:left">Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.</p><p style="margin:0;font-size:16px;text-align:left;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:left">
        {tickets_receipt_descr}&nbsp;</p><p style="margin:0;font-size:16px;text-align:left;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:center">{social_icons}&nbsp;</p><p style="margin:0;font-size:16px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:center">© SportWrench Inc. 2021</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table>
        </td></tr></tbody></table><!-- End --></body></html>',
        bee_json = '{
        "page": {
            "body": {
            "type": "mailup-bee-page-proprerties",
            "content": {
                "style": {
                "color": "#000000",
                "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                },
                "computedStyle": {
                "linkColor": "#0000FF",
                "messageWidth": "640px",
                "messageBackgroundColor": "#FFFFFF"
                }
            },
            "webFonts": [
                {
                "url": "https://fonts.googleapis.com/css$2family=Roboto",
                "name": "Roboto",
                "family": "''Roboto'', Tahoma, Verdana, Segoe, sans-serif",
                "fontName": "Roboto",
                "fontFamily": "''Roboto'', Tahoma, Verdana, Segoe, sans-serif"
                }
            ],
            "container": {
                "style": {
                "background-color": "#F7F7F7"
                }
            }
            },
            "rows": [
            {
                "type": "two-columns-4-8-empty",
                "uuid": "de77faba-c223-4f01-9b10-c7ac5ddc692b",
                "columns": [
                {
                    "uuid": "80888d62-98f4-42bb-8b15-56fac019abc9",
                    "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "15px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "15px",
                    "background-color": "transparent"
                    },
                    "modules": [
                    {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "8f37f534-d236-42d1-ab29-fc309d22a2f3",
                        "descriptor": {
                        "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Arial, ''Helvetica Neue'', Helvetica, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Arial, ''Helvetica Neue'', Helvetica, sans-serif;\"><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event Name\">{event_name}</code>&nbsp;</span></strong></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Thank you for your purchase! This email contains confirmation of your purchase details.</span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device <u><strong>along with a Government photo ID</strong></u> to gain entry.</span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">{tickets_names_list}</code></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">Purchaser:&nbsp;</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Payer''s Name\">{payer}</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">&nbsp;</code></span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">Total Price:&nbsp;</code><strong><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Total Purchase Amount\">{total_amount}</code></strong></span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\">List of all ticket holders on this purchase:</span></strong> <br><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></span></p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Links\">{tickets_links}</code>&nbsp;</p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event Disclaimer\">{tickets_receipt_descr}</code>&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Social Icons\">{social_icons}</code>&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\">© SportWrench Inc. 2021</p></div>",
                            "style": {
                            "color": "#555555",
                            "font-family": "Arial, ''Helvetica Neue'', Helvetica, sans-serif",
                            "line-height": "120%"
                            },
                            "computedStyle": {
                            "linkColor": "#0000FF"
                            }
                        },
                        "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                        },
                        "computedStyle": {
                            "hideContentOnMobile": false
                        }
                        }
                    }
                    ],
                    "grid-columns": 12
                }
                ],
                "content": {
                "style": {
                    "color": "#333",
                    "width": "640px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                }
                },
                "container": {
                "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                }
                }
            }
            ],
            "title": "BF-basic-newsletter",
            "template": {
            "name": "template-base",
            "type": "basic",
            "version": "0.0.1"
            },
            "description": "BF-basic-newsletter"
        },
        "comments": {}
        }'
        where email_template_type = 'tickets.assigned.receipt'
        and email_template_group = 'tickets.payments'
    `)
};
