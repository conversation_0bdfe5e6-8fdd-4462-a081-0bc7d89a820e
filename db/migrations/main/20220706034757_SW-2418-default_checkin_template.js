exports.up = function (knex) {
  return knex.schema.raw(String.raw`
    -- Create teams.online-checkin.default template ------------------------------------------------------------
    WITH main_template AS (
        INSERT INTO public.email_template
        (email_html, email_subject, email_text,
        event_owner_id, recipient_type, sender_type, title, event_id, bee_json,
        img_name, email_template_type, is_valid, email_template_group, published,
        deleted)
        VALUES
        ('<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
        *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}@media (max-width:660px){.row-content{width:100%!important}.mobile_hide{display:none}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
        </style></head><body style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="html_block" width="100%" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center"><div style="font-weight: bold; font-size: 16px;">
          Thank you for completing Online Team Check In for {event_name}
        </div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content" 
        align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" 
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:16px;text-align:left">
        <span style><span style="font-size:16px;">You, {receiver_first} {receiver_last}, has been approved </span><span style="font-size:16px;">to pick up the athlete & coaches wristbands for the teams listed below.</span></span></p><p style="margin:0;font-size:16px;text-align:left">
        <span style="font-size:16px;"><span style>Make sure to check the event’s website for any additional information regarding Team Check In at</span><span style="color:#000000;"> </span><a href="{event_website}" target="_blank" style="text-decoration: underline; color: #0000FF;" rel="noopener">{event_website}</a></span></p></div></div></td></tr></table><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;text-align:center"><span style="font-size:16px;"><strong>Please bring your picture IDENTIFICATION and the QR CODE below to the Team Check In area.</strong></span></p></div></div></td></tr></table></td></tr></tbody>
        </table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="33.333333333333336%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td><td class="column column-2" width="33.333333333333336%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center">{qr_code_image}</p></div></div></td></tr></table></td><td class="column column-3" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px;text-align:center"><span style="font-size:12px;">Make sure to read If you don''t see a QR Image, click here: {description_link}</span></p><p
         style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:16.8px">&nbsp;</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0"><span style="font-size:16px;">You are currently assigned to pick up wristbands for these teams:</span></p></div></div></td></tr></table><table 
        class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px"><span style="font-size:16px;">{teams}</span></p></div></div></td></tr></table></td></tr></tbody></table>
        </td></tr></tbody></table><table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td><div align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0">
        <tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-7" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center"><span style="font-size:12px;">Copyright © SportWrench Inc. All rights reserved</span></p></div></div></td></tr></table>
        </td></tr></tbody></table></td></tr></tbody></table><table class="row row-8" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="width:100%;padding-right:0;padding-left:0"><div align="center" style="line-height:10px">
        <a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:72px;max-width:100%" width="72" alt="Logo" title="Logo"></a></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>', 
        'Wristband Pick Up for OnLine Team Check In, {event_name}', 
        'Thank you for completing Online Team Check In for {event_name}
        You, {receiver_first} {receiver_last}, has been approved to pick up the athlete & coaches wristbands for the teams listed below.Make sure to check the event’s website for any additional information regarding Team Check In at {event_website}Please bring your picture IDENTIFICATION and the QR CODE below to the Team Check In area.{qr_code_image}Make sure to read If you don''t see a QR Image, click here: {description_link} You are currently assigned to pick up wristbands for these teams:{teams} Copyright © SportWrench Inc. All rights reserved', null, null, null, 'Wristband Pick Up for Online Team Checkin', null,
        '{
          "page": {
            "body": {
              "type": "mailup-bee-page-proprerties",
              "content": {
                "style": {
                  "color": "#000000",
                  "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                },
                "computedStyle": {
                  "linkColor": "#0000FF",
                  "messageWidth": "640px",
                  "messageBackgroundColor": "#FFFFFF"
                }
              },
              "webFonts": [],
              "container": {
                "style": {
                  "background-color": "#F7F7F7"
                }
              }
            },
            "rows": [
              {
                "type": "one-column-empty",
                "uuid": "5e268b46-189c-4ef4-9f21-648799ab6503",
                "columns": [
                  {
                    "uuid": "a0c7b010-c0b4-4225-b527-ca892fe9332c",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-html",
                        "uuid": "0f73694d-4f62-43b6-bad4-6ce864691c2f",
                        "descriptor": {
                          "html": {
                            "html": "<div style=\"font-weight: bold; font-size: 16px;\">\n  Thank you for completing Online Team Check In for {event_name}\n</div>"
                          },
                          "style": {
                            "padding-top": "0px",
                            "padding-left": "0px",
                            "padding-right": "0px",
                            "padding-bottom": "0px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "33bdf240-17d4-4c78-b62c-70241fa85977",
                "columns": [
                  {
                    "uuid": "3f2f3752-5874-4116-ba1c-1cddb8f17bfd",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "d349a567-25b6-4522-ba84-************",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\"><span style=\"line-height: 14px;\" data-mce-style=\"line-height: 14px;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">You, {receiver_first} {receiver_last}, has been approved </span><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">to pick up the athlete &amp; coaches wristbands for the teams listed below.</span></span></p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><span style=\"line-height: 14px;\" data-mce-style=\"line-height: 14px;\">Make sure to check the event’s website for any additional information regarding Team Check In at</span><span style=\"color: #000000; line-height: 14px;\" data-mce-style=\"color: #000000; line-height: 14px;\"> </span><a href=\"{event_website}\" target=\"_blank\" style=\"text-decoration: underline;\" rel=\"noopener\">{event_website}</a></span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      },
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "53ad1a41-6df4-40f6-b1d0-34863b4adf35",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"text-align: center; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><strong>Please bring your picture IDENTIFICATION and the QR CODE below to the Team Check In area.</strong></span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "640px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "three-columns-empty",
                "uuid": "26cd2fd4-6362-42ac-941a-7d3f1be3dda6",
                "columns": [
                  {
                    "uuid": "350fc161-dfec-435b-886c-e6ee7521a1e2",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [],
                    "grid-columns": 4
                  },
                  {
                    "uuid": "7189a444-813f-435c-90cf-9be0b9093e3a",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "c3b85fa3-9ba5-4f5a-9f89-c2c515e08d16",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{qr_code_image}</p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 4
                  },
                  {
                    "uuid": "4b40a9ab-0ff0-4f3d-b627-383430dec7db",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [],
                    "grid-columns": 4
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "0cdebdb4-2e10-4fe2-9582-8830a415b375",
                "columns": [
                  {
                    "uuid": "bd198849-8bbb-47d9-8c4a-998ae246a2b8",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "1cfd14ed-2448-48bc-b67b-920294a3d341",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Make sure to read If you don''t see a QR Image, click here: {description_link}</span></p><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\">&nbsp;</p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "0px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "24ee9bd7-5f42-48a6-afb4-b7dc9fb5f8dd",
                "columns": [
                  {
                    "uuid": "b34ad298-18cc-46f5-ba8a-95e2f8486095",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "37984cb8-50a0-4c5d-93d8-7711f467fcd5",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">You are currently assigned to pick up wristbands for these teams:</span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "0px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      },
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "ac3b7bed-11b2-473e-bd2c-a103e8fb3f04",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">{teams}</span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "c103d342-8055-471f-85c1-5b3c8f667c37",
                "columns": [
                  {
                    "uuid": "9685cc38-2289-457c-a9ef-************",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-divider",
                        "uuid": "0e1de054-fcb2-4699-8d4a-8173e3e2483e",
                        "descriptor": {
                          "style": {
                            "padding-top": "5px",
                            "padding-left": "5px",
                            "padding-right": "5px",
                            "padding-bottom": "5px"
                          },
                          "divider": {
                            "style": {
                              "width": "100%",
                              "border-top": "1px dotted #BBBBBB"
                            }
                          },
                          "computedStyle": {
                            "align": "center"
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "640px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "e94962a3-df5e-4b44-bab0-0880a3a91eb7",
                "columns": [
                  {
                    "uuid": "ada10544-e964-4b5a-bd42-6ca0793fc1fe",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "59dcd9e0-3db7-44f3-a215-93e10a332128",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © SportWrench Inc. All rights reserved</span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "0px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "0px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false,
                            "hideContentOnDesktop": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "640px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "4cff0107-9682-47f7-bf36-1c7e87486f30",
                "columns": [
                  {
                    "uuid": "81aef7f5-ea61-4d42-9ed2-bff5e42d7362",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-image",
                        "uuid": "bba469f0-dc70-4317-a3b0-3dc87e809a69",
                        "descriptor": {
                          "image": {
                            "alt": "Logo",
                            "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                            "href": "https://sportwrench.com",
                            "width": "84px",
                            "height": "60px"
                          },
                          "style": {
                            "width": "100%",
                            "padding-top": "0px",
                            "padding-left": "0px",
                            "padding-right": "0px",
                            "padding-bottom": "0px"
                          },
                          "computedStyle": {
                            "class": "center",
                            "width": "72px"
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "640px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              }
            ],
            "title": "BF-basic-newsletter",
            "template": {
              "name": "template-base",
              "type": "basic",
              "version": "0.0.1"
            },
            "description": "BF-basic-newsletter"
          },
          "comments": {}
        }',null, 'teams.online-checkin.default', true, 'clubs', true, null)
            RETURNING email_template_id
      ), 
      insert_type AS (
          -- Create teams.online-checkin.default template type ------------------------------------------------------------
          INSERT INTO public.email_template_type
          (type, email_template_group, title, description, long_title, is_trigger,
              default_email_template_id)
              VALUES ('teams.online-checkin.default', 'clubs', 'Wristband Pick Up for Online Team Checkin',
              '<em>Wristband Pick Up for Online Team Checkin</em>', 'Wristband Pick Up for Online Team Checkin', true,
              (SELECT email_template_id
                  FROM main_template))
      )
  
      -- Create event email trigger for teams.online-checkin.default template type ------------------------------------------------------------
      INSERT
          INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
          VALUES ('teams.online-checkin.default', 'clubs', (SELECT email_template_id FROM main_template), 0);
  `);
};

exports.down = function (knex) {
  return knex.schema.raw(`
        DELETE FROM email_template WHERE email_template_type = 'teams.online-checkin.default';
        DELETE FROM event_email_trigger WHERE email_template_type = 'teams.online-checkin.default';
        DELETE FROM email_template_type WHERE type = 'teams.online-checkin.default';
    `);
};
