exports.up = function (knex) {
    return knex.schema.raw(String.raw`
      -- Delete old templates ---------------------------------------------------------------
      DELETE FROM email_template WHERE email_template_type = 'teams.online-checkin.primary-staff';
      DELETE FROM event_email_trigger WHERE email_template_type = 'teams.online-checkin.primary-staff';
      DELETE FROM email_template_type WHERE type = 'teams.online-checkin.primary-staff';
      -- Create teams.online-checkin.primary-staff template ------------------------------------------------------------
      WITH main_template AS (
        INSERT INTO public.email_template
                (email_html, email_subject, email_text,
                event_owner_id, recipient_type, sender_type, title, event_id, bee_json,
                img_name, email_template_type, is_valid, email_template_group, published,
                deleted)
        VALUES
                ('<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
        *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}@media (max-width:660px){.row-content{width:100%!important}.mobile_hide{display:none}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
        </style></head><body style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" 
        cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px;text-align:center">
        <strong><span style="font-size:16px;">On Line Team Check In Has Been Completed for {team_name} for the {event_name}</span></strong></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" 
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px">
        <span style="font-size:16px;">You, {receiver_name}, have been issued the QR code below for admission to the event. Please have this code available, along with a Government picture ID at the point of entry for athletes and coaches. This code will be scanned for access each entry and is not transferable to any other person.</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" 
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px;text-align:center"><strong><span style="font-size:18px;">{event_short_name}</span></strong></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
        <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td><td class="column column-2" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
        class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center">
        {qr_code_image}</p></div></div></td></tr></table></td><td class="column column-3" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px">
        <div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td 
        class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px;text-align:center"><strong><span style="font-size:18px;">{receiver_name}</span></strong></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px">
        <span style="font-size:16px;">Players in uniforms will be granted entry. Please check the team entry procedures at&nbsp;<a href="{event_website}" target="_blank" style="text-decoration: underline; color: #0000FF;" rel="noopener">event site</a>&nbsp;for the earliest time access to the courts will open to teams.</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-7" align="center" width="100%" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px">
        <span style="font-size:16px;">Be aware that spectators may not be allowed in at the same time and may have a separate entry location. If a separate entry point is designated for athletes and coaches, make sure to coordinate an appropriate plan with your team as parents will not be allowed to escort athletes through that entrance.</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-8" align="center" width="100%" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center"><span style="font-size:12px;">Copyright © SportWrench Inc. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="width:100%;padding-right:0;padding-left:0"><div align="center" style="line-height:10px">
        <a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:72px;max-width:100%" width="72" alt="Logo" title="Logo"></a></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
                        'Your Entry Pass for {event_name}',
                        'On Line Team Check In Has Been Completed for {team_name} for the {event_name}
        You, {receiver_name}, have been issued the QR code below for admission to the event. Please have this code available, along with a Government picture ID at the point of entry for athletes and coaches. This code will be scanned for access each entry and is not transferable to any other person.{event_short_name}
        {qr_code_image}
        {receiver_name}
        Players in uniforms will be granted entry. Please check the team entry procedures at event site for the earliest time access to the courts will open to teams.
        Be aware that spectators may not be allowed in at the same time and may have a separate entry location. If a separate entry point is designated for athletes and coaches, make sure to coordinate an appropriate plan with your team as parents will not be allowed to escort athletes through that entrance.Copyright © SportWrench Inc. All rights reserved', null, null, null, 'Primary Staff Online Checkin', null,
        '{
          "page": {
            "body": {
              "type": "mailup-bee-page-proprerties",
              "content": {
                "style": {
                  "color": "#000000",
                  "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                },
                "computedStyle": {
                  "linkColor": "#0000FF",
                  "messageWidth": "640px",
                  "messageBackgroundColor": "#FFFFFF"
                }
              },
              "webFonts": [],
              "container": {
                "style": {
                  "background-color": "#F7F7F7"
                }
              }
            },
            "rows": [
              {
                "type": "one-column-empty",
                "uuid": "ae6d36e7-ee4b-4e17-ac63-c3ec259c4327",
                "columns": [
                  {
                    "uuid": "0acd412b-25db-4ab7-afd4-23bd6e1d71bf",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "18120a1d-07ba-45c3-91aa-a6b6a6cee213",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\"><strong><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">On Line Team Check In Has Been Completed for {team_name} for the {event_name}</span></strong></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "0438b5a7-d6b5-440f-b3b9-371d7edd3801",
                "columns": [
                  {
                    "uuid": "9c375496-7932-4358-a611-1f0d5b9719eb",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "114e37b7-7448-4314-99cb-5580a1ea493d",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">You, {receiver_name}, have been issued the QR code below for admission to the event. Please have this code available, along with a Government picture ID at the point of entry for athletes and coaches. This code will be scanned for access each entry and is not transferable to any other person.</span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "f49ef65f-f2c7-4abe-a95c-11e81b6af3f0",
                "columns": [
                  {
                    "uuid": "84a338c9-7779-45d0-acaa-c52248decf61",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "bcd0644a-9028-4016-af72-1720fb196f7b",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\">{event_short_name}</span></strong></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "three-columns-empty",
                "uuid": "47f66b41-4f51-40ca-a872-0d32dd33db77",
                "columns": [
                  {
                    "uuid": "396bf85c-4ba8-4c29-8e62-0bee926c1bc5",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [],
                    "grid-columns": 4
                  },
                  {
                    "uuid": "470372ca-f7d7-4f9d-9f6d-ab21ab0d6ced",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "f45d7b40-74c6-41e8-a50f-29c4e98e7352",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{qr_code_image}</p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 4
                  },
                  {
                    "uuid": "e6bf9072-4c0e-45cf-99d9-c654f7de69bc",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [],
                    "grid-columns": 4
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "93c66530-9824-44c3-a61a-61a018506cb0",
                "columns": [
                  {
                    "uuid": "267d1cc3-610b-4c47-8a62-ad007a5175c7",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "a8702c7c-10fb-44a0-b455-61c325751136",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\">{receiver_name}</span></strong></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "48bce266-e197-4170-acf2-a00b57e7b494",
                "columns": [
                  {
                    "uuid": "8641fcb0-0677-4b34-b6f1-564c4c2b0eef",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "2ce43624-29bc-4329-85a5-a8f254894fd2",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Players in uniforms will be granted entry. Please check the team entry procedures at&nbsp;<a href=\"{event_website}\" target=\"_blank\" style=\"text-decoration: underline;\" rel=\"noopener\">event site</a>&nbsp;for the earliest time access to the courts will open to teams.</span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "1ad5eec0-ef39-4d9d-87cf-a4738dd6ec5f",
                "columns": [
                  {
                    "uuid": "df8d02de-e6f4-4a0a-a4b4-fd65d03deef7",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "2182191e-61ae-40ef-8621-e23e057fec13",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Be aware that spectators may not be allowed in at the same time and may have a separate entry location. If a separate entry point is designated for athletes and coaches, make sure to coordinate an appropriate plan with your team as parents will not be allowed to escort athletes through that entrance.</span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "10px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "10px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "500px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  },
                  "computedStyle": {
                    "rowColStackOnMobile": true,
                    "rowReverseColStackOnMobile": false
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "96a2f31e-a137-4d88-847d-f973c2d91beb",
                "columns": [
                  {
                    "uuid": "36bbe818-a9c9-412d-9397-8ad01dc8144f",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-text",
                        "uuid": "*************-4257-afb1-04ac4d5f4e5b",
                        "descriptor": {
                          "text": {
                            "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>",
                            "style": {
                              "color": "#555555",
                              "font-family": "inherit",
                              "line-height": "120%"
                            },
                            "computedStyle": {
                              "linkColor": "#0000FF"
                            }
                          },
                          "style": {
                            "padding-top": "0px",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "padding-bottom": "0px"
                          },
                          "computedStyle": {
                            "hideContentOnMobile": false,
                            "hideContentOnDesktop": false
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "640px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              },
              {
                "type": "one-column-empty",
                "uuid": "cb9a3203-2018-476c-bcc7-b24acd5960c9",
                "columns": [
                  {
                    "uuid": "9a68a078-3cee-4cf8-beb4-ae0fdc7369a7",
                    "style": {
                      "border-top": "0px solid transparent",
                      "border-left": "0px solid transparent",
                      "padding-top": "5px",
                      "border-right": "0px solid transparent",
                      "padding-left": "0px",
                      "border-bottom": "0px solid transparent",
                      "padding-right": "0px",
                      "padding-bottom": "5px",
                      "background-color": "transparent"
                    },
                    "modules": [
                      {
                        "type": "mailup-bee-newsletter-modules-image",
                        "uuid": "8cf230b9-b119-49d5-9777-8b561877ab8b",
                        "descriptor": {
                          "image": {
                            "alt": "Logo",
                            "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                            "href": "https://sportwrench.com",
                            "width": "84px",
                            "height": "60px"
                          },
                          "style": {
                            "width": "100%",
                            "padding-top": "0px",
                            "padding-left": "0px",
                            "padding-right": "0px",
                            "padding-bottom": "0px"
                          },
                          "computedStyle": {
                            "class": "center",
                            "width": "72px"
                          }
                        }
                      }
                    ],
                    "grid-columns": 12
                  }
                ],
                "content": {
                  "style": {
                    "color": "#000000",
                    "width": "640px",
                    "background-color": "#FFFFFF",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                },
                "container": {
                  "style": {
                    "background-color": "transparent",
                    "background-image": "none",
                    "background-repeat": "no-repeat",
                    "background-position": "top left"
                  }
                }
              }
            ],
            "title": "BF-basic-newsletter",
            "template": {
              "name": "template-base",
              "type": "basic",
              "version": "0.0.1"
            },
            "description": "BF-basic-newsletter"
          },
          "comments": {}
        }', null, 'teams.online-checkin.primary-staff', true, 'clubs', true, null)
        RETURNING email_template_id
                  ), insert_type AS
        (
        -- Create teams.online-checkin.primary-staff template type ------------------------------------------------------------
        INSERT INTO public.email_template_type
                (type, email_template_group, title, description, long_title, is_trigger,
                default_email_template_id)
        VALUES
                ('teams.online-checkin.primary-staff', 'clubs', 'Primary Staff Online Checkin',
                        '<em>Primary Staff Online Checkin</em>', 'Primary Staff Online Checkin', true,
                        (SELECT email_template_id
                        FROM main_template))
        )
        
        -- Create event email trigger for teams.online-checkin.primary-staff template type ------------------------------------------------------------
        INSERT
                      INTO public.event_email_trigger
                (email_template_type, email_template_group, email_template_id, event_id)
        VALUES
                ('teams.online-checkin.primary-staff', 'clubs', (SELECT email_template_id
                        FROM main_template), 0);
        
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
          DELETE FROM email_template WHERE email_template_type = 'teams.online-checkin.primary-staff';
          DELETE FROM event_email_trigger WHERE email_template_type = 'teams.online-checkin.primary-staff';
          DELETE FROM email_template_type WHERE type = 'teams.online-checkin.primary-staff';
      `);
};
