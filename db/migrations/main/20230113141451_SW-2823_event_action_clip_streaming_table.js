
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS event_action_clip_streaming
        (
            id                  INT GENERATED ALWAYS AS IDENTITY,
            created             TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            modified            <PERSON><PERSON><PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW(),
            event_id            INT UNIQUE                                NOT NULL,
            pin_codes_generated BOOLEAN                     DEFAULT FALSE NOT NULL,
            pin_codes_sent      BOOLEAN                     DEFAULT FALSE NOT NULL
        );
        
        COMMENT ON COLUMN "public"."event_action_clip_streaming"."pin_codes_generated" 
        IS 'Defines if Pin Codes for ACS has been generated';
        COMMENT ON COLUMN "public"."event_action_clip_streaming"."pin_codes_sent" 
        IS 'Defines if Pin Codes for ACS has been sent';
        
        CREATE TRIGGER update_event_action_clip_streaming_modified
            BEFORE
                UPDATE
            ON "public"."event_action_clip_streaming"
            FOR EACH ROW
        EXECUTE PROCEDURE update_modified_column();
        
        INSERT INTO event_action_clip_streaming (event_id, pin_codes_generated, pin_codes_sent) 
        VALUES (23017, TRUE, TRUE);
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS event_action_clip_streaming;
    `);
};
