exports.up = function(knex) {
    return knex.schema.raw(`
        -- payout_member_types enum --------------------------------------------------------------------------------------
        CREATE TYPE "public"."payout_member_types" AS ENUM ('officials', 'staff');
        COMMENT ON TYPE "public"."payout_member_types" IS 'Payout member types.';
        -- -----------------------------------------------------------------------------------------------------------------
        
        -- Add new column "member_type" to "official_payout" table -----------------------------------------------------
        ALTER TABLE "public"."official_payout" ADD COLUMN "member_type" payout_member_types DEFAULT 'officials' NOT NULL;
        COMMENT ON COLUMN "public"."official_payout"."member_type" IS 'Payout member type';
        -- --------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."official_payout" DROP COLUMN "member_type";
        DROP TYPE IF EXISTS "public"."payout_member_types";
    `);
};
