exports.up = function(knex) {
    return knex.schema.raw(`
    
        -- Add new column "official_additional_role_enable" to "event" table -----------------------------------------------------
        ALTER TABLE "public"."event" ADD COLUMN "official_additional_role_enable" BOOLEAN DEFAULT FALSE NOT NULL;
        COMMENT ON COLUMN "public"."event"."official_additional_role_enable" IS 'Official additional role for Event';
        -- --------------------------------------------------------------------------------------------------------------
        
        -- Create "public"."official_additional_role" table -----------------------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."official_additional_role"
        (
            "official_additional_role_id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            "created"               TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            "modified"              TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            "name"                  TEXT NOT NULL,
            "description"           TEXT DEFAULT NULL
        );
        
        COMMENT ON COLUMN "public"."official_additional_role"."name" IS 'Name of Official additional role';
        COMMENT ON COLUMN "public"."official_additional_role"."description" IS 'Description of Official additional role';
        
        CREATE TRIGGER "update_official_additional_role_modified"
            BEFORE UPDATE
            ON "public"."official_additional_role"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        
        INSERT INTO public.official_additional_role(official_additional_role_id, created, modified, name)
            VALUES (DEFAULT, DEFAULT, DEFAULT, 'Referee'),
                   (DEFAULT, DEFAULT, DEFAULT, 'Scorer'),
                   (DEFAULT, DEFAULT, DEFAULT, 'Dual Ref/Scorer'),
                   (DEFAULT, DEFAULT, DEFAULT, 'Asst Scorer'),
                   (DEFAULT, DEFAULT, DEFAULT, 'Line Judge');
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Create "public"."event_official_additional_role" table -----------------------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."event_official_additional_role"
        (
            "created"                       TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            "modified"                      TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            "event_official_id"             INTEGER NOT NULL,
            "official_additional_role_id"   INTEGER NOT NULL
        );
        
        CREATE INDEX "event_official_additional_role_event_official_id_index" ON "public"."event_official_additional_role" ("event_official_id");
    
        COMMENT ON COLUMN "public"."event_official_additional_role"."event_official_id" IS 'Event Official ID';
        COMMENT ON COLUMN "public"."event_official_additional_role"."official_additional_role_id" IS 'Official additional role ID';
        
        CREATE TRIGGER "update_event_official_additional_role_modified"
            BEFORE UPDATE
            ON "public"."event_official_additional_role"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        -- -------------------------------------------------------------------------------------------------------------
        
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."event_official_additional_role";
        DROP TABLE IF EXISTS "public"."official_additional_role";
        ALTER TABLE "public"."event" DROP COLUMN "official_additional_role_enable";
    `);
};
