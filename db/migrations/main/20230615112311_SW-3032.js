
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE SCHEMA IF NOT EXISTS payment_hub;
        CREATE TABLE payment_hub."event" (
            id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            order_uuid UUID,
            payment_uuid UUID,
            payload JSON NOT NULL,
            created TIMES<PERSON>MP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL
        );

        CREATE TABLE payment_hub."payment" (
            id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            order_uuid UUID,
            status TEXT,
            payment_uuid UUID,
            amount DECIMAL(10,2),
            currency VARCHAR(255),
            created TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL
        );


        --- Add modified trigger ---------------------------------------------------------
        CREATE TRIGGER "update_payment_hub_payment_modified"
            BEFORE UPDATE
            ON "payment_hub"."payment"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();

        CREATE TRIGGER "update_payment_hub_event_modified"
            BEFORE UPDATE
            ON "payment_hub"."event"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        ----------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS payment_hub."event";
        DROP TABLE IF EXISTS payment_hub."payment";
        DROP SCHEMA IF EXISTS payment_hub;
    `);
};
