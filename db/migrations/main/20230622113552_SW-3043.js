
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS "public"."athlete_role"
        (
            athlete_role_id INTEGER GENERATED BY DEFAULT AS IDENTITY UNIQUE,
            name                   TEXT NOT NULL UNIQUE,
            short_name             TEXT,
            description            TEXT
        );
        
        INSERT INTO "public"."athlete_role" (athlete_role_id, name, short_name) VALUES (0, 'player', 'P');
        INSERT INTO "public"."athlete_role" (name, short_name) VALUES ('team captain', 'TC');
        
        CREATE TYPE "public"."ethnicity" AS ENUM ('chinese');
        
        ALTER TABLE "public"."master_athlete"
            ADD COLUMN IF NOT EXISTS "role" INTEGER REFERENCES "public"."athlete_role" (athlete_role_id) DEFAULT 0;
        ALTER TABLE "public"."master_athlete"
            ADD COLUMN IF NOT EXISTS "ethnicity" ethnicity DEFAULT NULL;
        ALTER TABLE "public"."roster_athlete"
            ADD COLUMN IF NOT EXISTS "role" INTEGER REFERENCES "public"."athlete_role" (athlete_role_id) DEFAULT NULL;
        
        COMMENT ON COLUMN "public"."master_athlete"."role" IS 'Athlete role in club. Created for 9 Man Chinese events. See possible values in master_athlete_role table';
        COMMENT ON COLUMN "public"."master_athlete"."ethnicity" IS 'Athletes ethnicity. Values: chinese.';
        COMMENT ON COLUMN "public"."roster_athlete"."role" IS 'Athlete role on event. Created for 9 Man Chinese events. See possible values in master_athlete_role table';
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."master_athlete" DROP COLUMN IF EXISTS "role";
        ALTER TABLE "public"."master_athlete" DROP COLUMN IF EXISTS "ethnicity";
        ALTER TABLE "public"."roster_athlete" DROP COLUMN IF EXISTS "role";
        
        DROP TABLE IF EXISTS "public"."athlete_role";
        DROP TYPE IF EXISTS "public"."ethnicity";
    `)
};
