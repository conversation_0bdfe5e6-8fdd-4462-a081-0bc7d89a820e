
exports.up = function(knex) {
    return knex.schema.raw(`
        -- -- Add new values to ethnicity ENUM --------------------------------------------
            ALTER TYPE "public"."ethnicity" ADD VALUE IF NOT EXISTS 'other';
            ALTER TYPE "public"."ethnicity" ADD VALUE IF NOT EXISTS 'half chinese';
         -- -------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."master_team"
            ALTER COLUMN ethnicity TYPE TEXT;
        ALTER TABLE "public"."master_athlete"
            ALTER COLUMN ethnicity TYPE TEXT;
        
        DROP TYPE ethnicity;
        
        CREATE TYPE "public"."ethnicity" AS ENUM ('chinese');
        
        ALTER TABLE "public"."master_team"
            ALTER COLUMN ethnicity TYPE ethnicity USING (ethnicity::TEXT::ethnicity);
        ALTER TABLE "public"."master_athlete"
            ALTER COLUMN ethnicity TYPE ethnicity USING (ethnicity::TEXT::ethnicity);
    `)
};
