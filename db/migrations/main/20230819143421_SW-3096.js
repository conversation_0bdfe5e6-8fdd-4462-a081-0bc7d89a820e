
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add custom_form_field.variation column -------------------------------------------------------
        ALTER TABLE "public"."custom_form_field" ADD COLUMN IF NOT EXISTS "variation" TEXT DEFAULT NULL;
        
        UPDATE custom_form_field SET variation = 'email' WHERE label = 'Club Mailing Address';
        UPDATE custom_form_field SET variation = 'phone' WHERE label = 'Club Phone';
        UPDATE custom_form_field SET variation = 'url' WHERE label = 'Club Website';
        UPDATE custom_form_field SET variation = 'email' WHERE label = 'Club Email Address';
        -- ----------------------------------------------------------------------------------------------
        
        -- Add custom_form_field.help_text field --------------------------------------------------------
        ALTER TABLE "public"."custom_form_field"
         ADD COLUMN IF NOT EXISTS "help_text" TEXT DEFAULT NULL;
        
        UPDATE custom_form_field
        SET help_text = 'This email will be used for all communications, please enter an address that will be the main source of communication for the club'
        WHERE label = 'Club Email Address';
        -- ----------------------------------------------------------------------------------------------
        
        -- Add custom_form_field.label_hover_text column ------------------------------------------------
        ALTER TABLE "public"."custom_form_field"
            ADD COLUMN IF NOT EXISTS "label_hover_text" TEXT DEFAULT NULL;
        
        UPDATE custom_form_field
        SET label_hover_text = 'This email will be used for all communications, please enter an address that will be the main source of communication for the club'
        WHERE label = 'Club Email Address';
        -- ----------------------------------------------------------------------------------------------

        -- Add paragraph form fields instead of event form form_text field ------------------------------
        INSERT INTO "public"."custom_form_field_type" (type, description)
        VALUES ('paragraph', 'Any plain text');
        
        INSERT INTO custom_form_field AS cff (custom_form_event_id, custom_form_field_type_id, label)
        SELECT custom_form_event_id, cfft.custom_form_field_type_id, cfe.form_text
        FROM custom_form_event cfe
                 JOIN custom_form_field_type cfft ON cfft.type = 'paragraph'
        WHERE cfe.form_text IS NOT NULL;
        
        ALTER TABLE "public"."custom_form_event"
            DROP COLUMN IF EXISTS "form_text";
        -- ----------------------------------------------------------------------------------------------
        
        -- Set correct sorting for current SoCal Cup form fields ----------------------------------------
        UPDATE custom_form_field SET sort_order = 98 WHERE sort_order = 0;
        UPDATE custom_form_field SET sort_order = 0 WHERE label LIKE 'Welcome to the%';
        -- ----------------------------------------------------------------------------------------------
        
        -- Add signature_checkbox custom form field type ------------------------------------------------
        INSERT INTO "public"."custom_form_field_type" (type, description)
        VALUES ('signature_checkbox', 'Signature checkbox');
        
        UPDATE custom_form_field cff
        SET custom_form_field_type_id = types.custom_form_field_type_id
        FROM (SELECT cfft.custom_form_field_type_id
              FROM custom_form_field_type cfft
              WHERE cfft.type = 'signature_checkbox') types
        WHERE cff.label LIKE 'By signing below%';
        -- ----------------------------------------------------------------------------------------------
        
        -- Add custom_form_field settings field ---------------------------------------------------------
        ALTER TABLE custom_form_field ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}'::JSONB;
        UPDATE custom_form_field SET settings = '{"maxQtySelected":2}' WHERE label LIKE 'Please indicate your %';
        -- ----------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."custom_form_field" DROP COLUMN IF EXISTS "variation";
        ALTER TABLE "public"."custom_form_field" DROP COLUMN IF EXISTS "help_text";
        
        ALTER TABLE "public"."custom_form_event"
            ADD COLUMN IF NOT EXISTS "form_text" TEXT DEFAULT NULL;
        
        WITH paragraph_fields AS (SELECT cff.label, cff.custom_form_event_id, cff.custom_form_field_id
                                  FROM custom_form_field cff
                                           JOIN custom_form_field_type cfft ON cfft.type = 'paragraph' AND
                                                                               cff.custom_form_field_type_id =
                                                                               cfft.custom_form_field_type_id)
           , update_form_text AS (
            UPDATE custom_form_event AS cfe
                SET form_text =
                        (SELECT pf.label FROM paragraph_fields pf WHERE pf.custom_form_event_id = cfe.custom_form_event_id))
        DELETE
        FROM custom_form_field cff
        WHERE cff.custom_form_field_id IN (SELECT custom_form_field_id FROM paragraph_fields);
        
        DELETE
        FROM "public"."custom_form_field_type"
        WHERE type = 'paragraph';
        
        UPDATE custom_form_field SET sort_order = 0 WHERE sort_order = 98;
        
        UPDATE custom_form_field cff
        SET custom_form_field_type_id = types.custom_form_field_type_id
        FROM (SELECT cfft.custom_form_field_type_id
              FROM custom_form_field_type cfft
              WHERE cfft.type = 'checkbox') types
        WHERE cff.label LIKE 'By signing below%';
        
        DELETE FROM custom_form_field_type WHERE type = 'signature_checkbox';
        
        ALTER TABLE custom_form_field DROP COLUMN IF EXISTS settings;
    `);
};
