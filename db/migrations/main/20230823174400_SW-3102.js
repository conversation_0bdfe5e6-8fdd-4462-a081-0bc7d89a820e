
exports.up = function(knex) {
    return knex.schema.raw(`
        INSERT INTO "public"."custom_form_field_type" (type, description)
        VALUES ('signature', 'Signature field');
        INSERT INTO "public"."custom_form_field_type" (type, description)
        VALUES ('date', 'Datepicker field');
        
        ALTER TYPE "public"."custom_form_submitter_type" ADD VALUE 'purchase';
        ALTER TYPE "public"."custom_form_type" ADD VALUE 'so_cal_camps_commitment_form';
        ALTER TYPE "public"."custom_form_type" ADD VALUE 'so_cal_player_medical_release_form';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE
        FROM "public"."custom_form_field_type"
        WHERE type = 'signature';
        
        DELETE
        FROM "public"."custom_form_field_type"
        WHERE type = 'date';
    `)
};
