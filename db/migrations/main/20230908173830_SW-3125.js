
exports.up = function(knex) {
    return knex.raw(`
        UPDATE custom_form_field
        SET custom_form_field_type_id = (SELECT custom_form_field_type.custom_form_field_type_id
                                         FROM custom_form_field_type
                                         WHERE type = 'select'),
            settings                  = COALESCE(settings, '{}'::JSONB) ||
                                        '{"optionsQuery": "SELECT club_name as label FROM roster_club WHERE event_id = 23549 AND deleted IS NULL"}'
        WHERE custom_form_field.label = 'Club Name'
          AND custom_form_event_id IN (SELECT custom_form_event.custom_form_event_id
                                       FROM custom_form_event
                                       WHERE custom_form_event.type IN
                                             ('so_cal_player_medical_release_form', 'so_cal_camps_commitment_form'));
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        UPDATE custom_form_field
        SET custom_form_field_type_id = (SELECT custom_form_field_type.custom_form_field_type_id
                                         FROM custom_form_field_type
                                         WHERE type = 'text'),
            settings                  = NULL
        WHERE custom_form_field.label = 'Club Name'
          AND custom_form_event_id IN (SELECT custom_form_event.custom_form_event_id
                                       FROM custom_form_event
                                       WHERE custom_form_event.type IN
                                             ('so_cal_player_medical_release_form', 'so_cal_camps_commitment_form'))
    `)
};
