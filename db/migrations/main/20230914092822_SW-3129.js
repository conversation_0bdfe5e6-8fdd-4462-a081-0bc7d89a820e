
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS "public"."member_type_rules" (
            "member_type_rule_id"           INT GENERATED ALWAYS AS IDENTITY,
            "field_name"                    TEXT NOT NULL,
            "member_type"                   member_types,
            "rule_data"                     TEXT,
            "is_active"                     BOOLEAN DEFAULT TRUE NOT NULL,
            PRIMARY KEY("member_type_rule_id")
        );

        INSERT INTO "public"."member_type_rules" (field_name, member_type, rule_data) 
        VALUES 
        ('membership_definition_id', 'athlete', '11ecf894-ca81-5ac4-9dac-322aa482105c'),
        ('membership_definition_id', 'athlete', '11ecf894-66b4-9920-b723-42575c6b2dcb'),
        ('membership_definition_id', 'athlete', '11ecf89b-b6b1-484a-bdb2-322aa482105c'),
        ('membership_definition_id', 'athlete', '11eddf94-4a84-efd8-9b51-c2aba77d4df2'),
        ('membership_definition_id', 'staff', '11eddf97-7e18-649e-b1a5-c2aba77d4df2'),
        ('age_group_name', 'athlete', 'Junior'),
        ('age_group_name', 'athlete', 'JuniorF'),
        ('age_group_name', 'athlete', 'JuniorM'),
        ('age_group_name', 'athlete', 'JuniorL'),
        ('age_group_name', 'staff', 'Adult'),
        ('age_group_name', 'staff', 'AdultF'),
        ('age_group_name', 'staff', 'AdultM'),
        ('age_group_name', 'staff', 'AdultL')
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."member_type_rules";
    `);
};
