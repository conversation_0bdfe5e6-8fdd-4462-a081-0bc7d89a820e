
exports.up = function(knex) {
    return knex.schema.raw(`
        INSERT INTO "public"."member_type_rules" (field_name, member_type, rule_data) 
        VALUES 
        ('membership_definition_id', 'staff', '11ee2265-b572-3d96-a390-b63f8a7b39f5')
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM "public"."member_type_rules" 
        WHERE field_name = 'membership_definition_id' 
          AND member_type = 'staff' AND rule_data = '11ee2265-b572-3d96-a390-b63f8a7b39f5'
    `);
};
