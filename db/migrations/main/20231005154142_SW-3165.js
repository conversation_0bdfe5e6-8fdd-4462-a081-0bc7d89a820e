
exports.up = function(knex) {
    return knex.schema.raw(`alter type "public"."ths_teams_access_level" add value 'paid_ach_pending';`);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        alter table "public"."event"
            alter column "housing_teams_access_level" type text;
        
        drop type "public"."ths_teams_access_level";
        
        create type "public"."ths_teams_access_level" as Enum ( 'entered', 'paid', 'accepted' );
        
        alter table "public"."event"
            alter column "housing_teams_access_level" type "ths_teams_access_level" 
            using housing_teams_access_level::"ths_teams_access_level";
    `)
};
