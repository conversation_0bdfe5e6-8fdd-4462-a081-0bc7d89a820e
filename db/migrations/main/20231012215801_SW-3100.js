
exports.up = function(knex) {
    return knex.raw(`
        UPDATE purchase AS p
        SET collected_sw_fee = pt.collectedSwFee
        FROM
            (
                SELECT
                    pt.purchase_id AS purchase_id,
                    SUM(sw_fee) collectedSwFee,
                    e.event_id AS event_id
                FROM purchase_team pt
                    LEFT JOIN "purchase" p ON pt.purchase_id = p.purchase_id
                    LEFT JOIN "event" e ON p.event_id = e.event_id
                GROUP BY pt.purchase_id, e.event_id
            ) pt
        INNER JOIN "event" e ON e.event_id = pt.event_id
        WHERE pt.purchase_id = p.purchase_id
          AND p.is_payment IS TRUE
          AND p.type = 'check'::purchase_type
          AND p.payment_for::text = 'teams'::text
          AND (e.teams_sw_fee_payer = 'buyer'::payer_option
            OR (e.teams_sw_fee_payer = 'seller'::payer_option
                AND COALESCE((e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN, FALSE) = FALSE)
            )
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        UPDATE purchase AS p
        SET collected_sw_fee = 0
        WHERE p.is_payment IS TRUE
          AND p.type = 'check'::purchase_type
          AND p.payment_for::text = 'teams'::text
          AND p.collected_sw_fee <> 0
    `);
};
