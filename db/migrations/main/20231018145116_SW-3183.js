
exports.up = function(knex) {
    return knex.schema.raw(`
    CREATE TABLE "payment_hub"."customer"
    (
        "id"                            INT GENERATED ALWAYS AS IDENTITY,
        "created"                       TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        "modified"                      TIMES<PERSON>MP WITH TIME ZONE DEFAULT NULL,
        "user_id"                       INTEGER UNIQUE                         NOT NULL,
        "payment_hub_customer_id"       TEXT UNIQUE                            NOT NULL,
        "payment_hub_customer_object"   JSONB                                  NOT NULL
    );
    -- -----------------------------------------------------------------------------------------------------------------
    
    
    -- Comments --------------------------------------------------------------------------------------------------------
    COMMENT ON COLUMN "payment_hub"."customer"."user_id" IS 'User ID';
    COMMENT ON COLUMN "payment_hub"."customer"."payment_hub_customer_id" IS 'Payment Hub API Customer ID';
    COMMENT ON COLUMN "payment_hub"."customer"."payment_hub_customer_object" IS 'Payment Hub API Customer Object';
    -- -----------------------------------------------------------------------------------------------------------------
    
    
    -- Modified trigger ------------------------------------------------------------------------------------------------
    CREATE TRIGGER "update_payment_hub_customer_modified"
        BEFORE UPDATE
        ON "payment_hub"."customer"
        FOR EACH ROW
    EXECUTE PROCEDURE
        update_modified_column();
    -- -----------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "payment_hub"."customer";
    `);
};
