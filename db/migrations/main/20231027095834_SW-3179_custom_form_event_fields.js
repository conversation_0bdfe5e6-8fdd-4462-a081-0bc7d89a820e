
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Create new fields custom_form_event.name and custom_form_event.published ----------------------------------
        ALTER TABLE public.custom_form_event
            ADD COLUMN IF NOT EXISTS name      TEXT      DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS published TIMESTAMP DEFAULT NULL;
        -- -----------------------------------------------------------------------------------------------------------    
        
        -- Set initial value for existing forms ----------------------------------------------------------------------
        UPDATE public.custom_form_event
        SET published = created;
        -- -----------------------------------------------------------------------------------------------------------    

        -- Set initial custom_form_event for existing forms ----------------------------------------------------------
        UPDATE public.custom_form_event
        SET name = (CASE
                        WHEN type = 'so_cal_camps_commitment_form' THEN 'SoCal Camps Commitment Form'
                        WHEN type = 'so_cal_player_medical_release_form' THEN 'SoCal Player Medical Release Form'
                        WHEN type = 'so_cal_cup_form' THEN 'SoCal Cup Form'
                        WHEN type = 'so_cal_cup_seeding_request_form' THEN 'SoCal Cup Seeding Request Form'
            END);
        -- -----------------------------------------------------------------------------------------------------------    

        -- Add not NULL for custom_form_event.name field -------------------------------------------------------------
        ALTER TABLE public.custom_form_event
            ALTER COLUMN name SET NOT NULL;
        -- -----------------------------------------------------------------------------------------------------------  
        
        -- Transform current custom_form_event.type into new types ---------------------------------------------------
        ALTER TABLE "public"."custom_form_event" DROP CONSTRAINT IF EXISTS "custom_form_event_type_event_id_key";
        
        UPDATE public.custom_form_event
        SET type = (CASE
                        WHEN type IN ('so_cal_camps_commitment_form'::custom_form_type,
                                      'so_cal_player_medical_release_form'::custom_form_type,
                                      'so_cal_cup_seeding_request_form'::custom_form_type) THEN 'camps_purchase_page'::custom_form_type
                        WHEN type = 'so_cal_cup_form'::custom_form_type THEN 'team_assign_for_event'::custom_form_type END)
        WHERE type IN ('so_cal_camps_commitment_form'::custom_form_type, 'so_cal_player_medical_release_form'::custom_form_type,
                       'so_cal_cup_seeding_request_form'::custom_form_type, 'so_cal_cup_form'::custom_form_type);
        -- -----------------------------------------------------------------------------------------------------------  

        
        -- Remove deprecated custom_form_type ENUM values ------------------------------------------------------------
        ALTER TABLE public.custom_form_event
            ALTER COLUMN type TYPE TEXT;
        
        DROP TYPE custom_form_type;
        
        CREATE TYPE custom_form_type AS ENUM ('camps_purchase_page', 'team_assign_for_event');
        
        ALTER TABLE public.custom_form_event
            ALTER COLUMN type TYPE custom_form_type USING type::custom_form_type;  
        -- -----------------------------------------------------------------------------------------------------------  


        -- Do not allow to create multiple types for a single event if type = team_assign_for_event ------------------
        CREATE UNIQUE INDEX IF NOT EXISTS "custom_form_event_team_assign_for_event_type_unique"
            ON public.custom_form_event (type, event_id)
            WHERE type = 'team_assign_for_event';
        -- -----------------------------------------------------------------------------------------------------------
        
        -- Remove deprecated flags -----------------------------------------------------------------------------------
        UPDATE event
        SET tickets_settings = COALESCE(tickets_settings, '{}'::jsonb) - 'so_cal_camps_commitment_form_submitting_required'
        WHERE tickets_settings ->> 'so_cal_camps_commitment_form_submitting_required' IS NOT NULL;
        
        UPDATE event
        SET tickets_settings = COALESCE(tickets_settings, '{}'::jsonb) -
                               'so_cal_player_medical_release_form_submitting_required'
        WHERE tickets_settings ->> 'so_cal_player_medical_release_form_submitting_required' IS NOT NULL;
        
        UPDATE event
        SET tickets_settings = COALESCE(tickets_settings, '{}'::jsonb) - 'so_cal_cup_seeding_request_form_submitting_required'
        WHERE tickets_settings ->> 'so_cal_cup_seeding_request_form_submitting_required' IS NOT NULL;
        
        UPDATE event
        SET teams_settings = COALESCE(teams_settings, '{}'::jsonb) - 'so_cal_cup_form_submitting_required'
        WHERE teams_settings ->> 'so_cal_cup_form_submitting_required' IS NOT NULL;      
        -- -----------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        -- Return previous custom_form_type values -------------------------------------------------------------------
        UPDATE custom_form_event
        SET type = (
            CASE
                WHEN name = 'SoCal Camps Commitment Form' THEN 'so_cal_camps_commitment_form'
                WHEN name = 'SoCal Player Medical Release Form' THEN 'so_cal_player_medical_release_form'
                WHEN name = 'SoCal Cup Form' THEN 'so_cal_cup_form'
                WHEN name = 'SoCal Cup Seeding Request Form' THEN 'so_cal_cup_seeding_request_form'
                END
            );
        -- -----------------------------------------------------------------------------------------------------------  
 
        
        -- Return old flags --------------------------------------------------------------------
        UPDATE event e
        SET tickets_settings = COALESCE(tickets_settings, '{}'::jsonb) ||
                               '{"so_cal_camps_commitment_form_submitting_required":true}'
        WHERE EXISTS(SELECT 1
                     FROM custom_form_event cfe
                     WHERE cfe.event_id = e.event_id AND cfe.type = 'so_cal_camps_commitment_form');
        
        UPDATE event e
        SET tickets_settings = COALESCE(tickets_settings, '{}'::jsonb) ||
                               '{"so_cal_player_medical_release_form_submitting_required":true}'
        WHERE EXISTS(SELECT 1
                     FROM custom_form_event cfe
                     WHERE cfe.event_id = e.event_id
                       AND cfe.type = 'so_cal_player_medical_release_form');
        
        UPDATE event e
        SET tickets_settings = COALESCE(tickets_settings, '{}'::jsonb) ||
                               '{"so_cal_cup_seeding_request_form_submitting_required":true}'
        WHERE EXISTS(SELECT 1
                     FROM custom_form_event cfe
                     WHERE cfe.event_id = e.event_id AND cfe.type = 'so_cal_cup_seeding_request_form');
        
        UPDATE event e
        SET teams_settings = COALESCE(teams_settings, '{}'::jsonb) || '{"so_cal_cup_form_submitting_required":true}'
        WHERE EXISTS(SELECT 1 FROM custom_form_event cfe WHERE cfe.event_id = e.event_id AND cfe.type = 'so_cal_cup_form');
        -- -----------------------------------------------------------------------------------------------------------  

 
        -- Remove new custom_form_event columns ----------------------------------------------------------------------
        ALTER TABLE public.custom_form_event
            DROP COLUMN IF EXISTS name,
            DROP COLUMN IF EXISTS published;
        -- -----------------------------------------------------------------------------------------------------------  
       
       
        -- Drop unique custom_form_event_team_assign_for_event_type_unique ------------------------------------------- 
        DROP INDEX IF EXISTS "custom_form_event_team_assign_for_event_type_unique";
        -- -----------------------------------------------------------------------------------------------------------  
        
        -- Add unique custom_form_event_type_event_id_key ------------------------------------------------------------
        ALTER TABLE "public"."custom_form_event"
            ADD CONSTRAINT "custom_form_event_type_event_id_key" UNIQUE (event_id, type);
        -- -----------------------------------------------------------------------------------------------------------  
    `)
};

exports.config = { transaction: false };
