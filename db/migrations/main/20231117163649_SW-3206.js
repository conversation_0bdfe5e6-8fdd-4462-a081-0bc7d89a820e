
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."master_athlete"
            ADD COLUMN IF NOT EXISTS "district"                       TEXT DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS "aau_membership_id"              TEXT DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS "aau_membership_ending_year"     INT DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS "aau_jersey"                     VARCHAR(20) DEFAULT NULL;
        ALTER TABLE "public"."master_staff"
            ADD COLUMN IF NOT EXISTS "district"                       TEXT DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS "aau_membership_id"              TEXT DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS "aau_membership_ending_year"     INT DEFAULT NULL;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."master_athlete"
            DROP COLUMN IF EXISTS "district",
            DROP COLUMN IF EXISTS "aau_membership_id",
            DROP COLUMN IF EXISTS "aau_membership_ending_year",
            DROP COLUMN IF EXISTS "aau_jersey";
        ALTER TABLE "public"."master_staff"
            DROP COLUMN IF EXISTS "district",
            DROP COLUMN IF EXISTS "aau_membership_id",
            DROP COLUMN IF EXISTS "aau_membership_ending_year";
    `);
};
