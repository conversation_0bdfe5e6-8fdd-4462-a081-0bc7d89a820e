
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE tilled.refund (
            id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            tilled_refund_id TEXT NOT NULL,
            status TEXT NOT NULL,
            amount NUMERIC(8, 2) NOT NULL,
            tilled_balance_transaction_id TEXT NOT NULL,
            tilled_payment_intent_id TEXT NOT NULL,
            reason TEXT,
            metadata JSONB DEFAULT '{}'::jsonb NOT NULL,
            failure_code TEXT,
            failure_message TEXT,
            created TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL
        );
        CREATE UNIQUE INDEX idx_tilled_refund_tilled_refund_id ON tilled.refund(tilled_refund_id);

        CREATE TRIGGER "update_tilled_refund_modified"
            BEFORE UPDATE
            ON tilled.refund
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
    `);
};

exports.down = function(knex) {
  return knex.schema.raw(`
    DROP TABLE tilled.refund;
  `);
};
