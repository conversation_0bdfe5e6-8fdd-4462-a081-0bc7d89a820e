exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add "event_owner_id" column to "tilled.account" table -------------------------------------------------------
        ALTER TABLE tilled.account ADD COLUMN event_owner_id TEXT NOT NULL;

        -- Add "account_type" column to "tilled.account" table ---------------------------------------------------------
        ALTER TABLE tilled.account ADD COLUMN account_type TEXT NOT NULL;

        -- Change type of "tilled_tickets_account_id" column to text ---------------------------------------------------
        ALTER TABLE public.event ALTER COLUMN tilled_tickets_account_id TYPE TEXT USING tilled_tickets_account_id::text;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        -- Remove "event_owner_id" column -----------------------------------------------------------------------------
        ALTER TABLE tilled.account DROP COLUMN IF EXISTS event_owner_id;

        -- Remove "account_type" column -------------------------------------------------------------------------------
        ALTER TABLE tilled.account DROP COLUMN IF EXISTS account_type;

        -- Change type of "tilled_tickets_account_id" column to INT ---------------------------------------------------
        ALTER TABLE public.event ALTER COLUMN tilled_tickets_account_id TYPE TEXT USING tilled_tickets_account_id::INT;
    `);
};
