
exports.up = function(knex) {
    return knex.schema.raw(String.raw`
        INSERT INTO public.email_template (email_html, email_subject, email_text, title, bee_json, email_template_type,
                                           is_valid, email_template_group, published)
        VALUES ('<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><!--[if !mso]><!--><link href="https://fonts.googleapis.com/css?family=Lato"
        rel="stylesheet" type="text/css"><!--<![endif]--><style>
        *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:640px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
        </style></head><body style="background-color:#fff;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:20px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%"
        border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:116px"><img src="https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg" style="display:block;height:auto;border:0;width:100%" width="116" alt="Image" title="Image"></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody>
        </table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #222"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0"
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:16.8px"><span style="font-size:16px;">{event_name}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table
        class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:62px"><img
        src="https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/icon1.svg" style="display:block;height:auto;border:0;width:100%" width="62" alt="Image" title="Image"></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0"
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">Refunded:</span></p></div></div></td></tr></table><table class="text_block block-2"
        width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{amount_refunded}</span></p></div>
        </div></td></tr></table></td><td class="column column-2" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div
        style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;"><span style="font-size:16px;">Date</span> issued:</span></p></div></div></td></tr></table><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{date_refunded}</span></p></div></div></td></tr></table></td><td class="column column-3"
        width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;"><span style="font-size:16px;">Refunded</span> to:</span></p></div></div></td></tr></table><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">**** {card_last_4}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table>
        <table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:10px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="color:#000000;font-size:16px;">Attention!</span></p></div></div></td></tr></table><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td
         class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:18px;color:#000;line-height:1.5"><p style="margin:0;mso-line-height-alt:21px">
        <span style="font-size:14px;">Recently you issued a refund for your event {event_name} at SportWrench from your Stripe dashboard. Note that all refunds must be processed via SportWrench only.<br><br>When you issue a refund from your Stripe dashboard, SportWrench system may not be able to update all the data properly. As a result, teams statuses, statistics and accounting will contain incorrect data which may affect your payout process.<br><br>
        Please process all refunds from your SportWrench account in the future.<br></span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-7" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;background-color:#555;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#fff;line-height:1.2"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:16px;">PURCHASE SUMMARY</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody>
        </table><table class="row row-8" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Event</span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{event_name}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Teams<br>
        </span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{team_names}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-10" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Club Name<br>
        </span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{club_name} </span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-11" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Purchase date<br>
        </span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{payment_date_paid}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Total amount<br>
        </span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div
        style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{total_amount}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-13" align="center" width="100%" border="0" cellpadding="0" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;background-color:#555;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#fff;line-height:1.2"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:16px;">REFUND SUMMARY</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-14" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Refund date<br>
        </span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div
        style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{date_refunded}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-15" align="center" width="100%" border="0" cellpadding="0" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Amount refunded</span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{amount_refunded}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-16" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-17" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0"
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="66.66666666666667%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:20px;padding-right:20px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">
        <span style="font-size:16px;"><strong>ADJUSTED TOTAL</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:20px;padding-right:5px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:16px;">{adjusted_total_amount}</span> </strong></p>
        </div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-18" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td
        class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:10px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0"
        role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #222"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-19" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack"
        align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5"
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{facebook_icon}</p></div></div></td></tr></table></td><td class="column column-2" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{twitter_icon}</p></div></div></td></tr></table></td><td class="column column-3" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{instagram_icon}</p></div></div></td></tr></table></td><td class="column column-4" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{snapchat_icon}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-20" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td>
        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0"
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table>
        </div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-21" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1"
        width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
                'Important: Stripe refund for your SportWrench event {event_name} - Process violation', e'{event_name}Refunded:{amount_refunded}
        Date issued:{date_refunded}Refunded to:**** {card_last_4}
        Attention!
        Recently you issued a refund for your event {event_name} at SportWrench from your Stripe dashboard. Note that all refunds must be processed via SportWrench only.When you issue a refund from your Stripe dashboard, SportWrench system may not be able to update all the data properly. As a result, teams statuses, statistics and accounting will contain incorrect data which may affect your payout process.
        Please process all refunds from your SportWrench account in the future.PURCHASE SUMMARY
        Event{event_name}Teams
        {team_names}Club Name
        {club_name} Purchase date
        {payment_date_paid}Total amount
        {total_amount}REFUND SUMMARYRefund date
        {date_refunded}Amount refunded{amount_refunded}
        ADJUSTED TOTAL{adjusted_total_amount}
        {facebook_icon}{twitter_icon}{instagram_icon}{snapchat_icon}', 'Stripe dashboard EO refund', '{
            "page": {
              "body": {
                "type": "mailup-bee-page-properties",
                "content": {
                  "style": {
                    "color": "#000000",
                    "font-family": "''Lato'', Tahoma, Verdana, Segoe, sans-serif"
                  },
                  "computedStyle": {
                    "linkColor": "#71777D",
                    "messageWidth": "620px",
                    "messageBackgroundColor": "transparent"
                  }
                },
                "webFonts": [
                  {
                    "url": "https://fonts.googleapis.com/css?family=Lato",
                    "name": "Lato",
                    "fontFamily": "''Lato'', Tahoma, Verdana, Segoe, sans-serif"
                  }
                ],
                "container": {
                  "style": {
                    "background-color": "#FFFFFF"
                  }
                }
              },
              "rows": [
                {
                  "type": "one-column-empty",
                  "uuid": "7805f52d-87fe-4ad6-9502-4c5f7dc0480c",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "821d1436-94c1-4592-b403-0c080a0b6470",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "20px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "uuid": "99305df6-9e2d-4e63-a282-aae311c43fd5",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Image",
                              "src": "https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg",
                              "href": "",
                              "width": "116px",
                              "height": "40px",
                              "prefix": "",
                              "target": "_self"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": "116px",
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "bfbbb9b4-c050-4a0e-85a3-3edab911efc4",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "9ffa5d8d-384e-4648-8de9-e74db1f99d92",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "fa26dedf-7df2-4515-98b1-db90a4779de7",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "height": "0px",
                                "border-top": "1px solid #222222"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "a22f2253-231e-4a9a-b4d2-1898d1effdbb",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "3f5905fa-2234-45da-9d59-ddd51b24701f",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f4050145-66c0-45bc-88f7-2790ccf60198",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:left;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:left;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">{event_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "612d16d9-1c1d-4fd0-bc39-57618918b605",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "4abf5709-447c-4ab5-b960-9faf2fa20d15",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "uuid": "43fa98d1-d09f-483d-8d28-2beacab00978",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Image",
                              "src": "https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/icon1.svg",
                              "href": "",
                              "width": "62px",
                              "height": "53px",
                              "prefix": "",
                              "target": "_self"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": "62px"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "three-columns-empty",
                  "uuid": "784ec4e1-3975-4775-ad5c-3da8157fb21e",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "0557b350-9329-4120-b666-5e225eadf49c",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f2f4926b-130f-451d-b800-b4f7d029d050",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">Refunded:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "fd92a139-4dc2-4592-9e72-fab85ad1c49d",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">{amount_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    },
                    {
                      "uuid": "1a1c7349-a4d3-40ff-ae88-db8ddaaaecf0",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "e7b1e7ad-71e2-4a60-b88b-82868e362978",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">Date</span> issued:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "133421ea-5b0e-4ecd-93d4-d84831247025",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">{date_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    },
                    {
                      "uuid": "c160ca5a-12e4-4578-a6dd-52ffdb82ffaf",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "2da3b37c-aa0d-4014-8a96-2a1f7fcdea55",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">Refunded</span> to:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f2e13d71-cf5e-4564-9208-1df00779953e",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">**** {card_last_4}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "a1b31363-**************-7deefaae6899",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "f90da6a9-e2cc-4a80-b3ef-6c78df6cf1e6",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "10px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "bb933e8f-c523-4838-8162-060e92bef2e7",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"color:rgb(0,0,0);font-size:16px;line-height:19px;\" data-mce-style=\"color:rgb(0,0,0);font-size:16px;line-height:19px;\">Attention!</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "1e1f567b-a7fa-41e2-9a42-9aceb809cab5",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 18px; font-family: inherit;\" data-mce-style=\"font-size: 12px; line-height: 18px; font-family: inherit;\"><p style=\"word-break: break-word; line-height: 18px;\" data-mce-style=\"word-break: break-word; line-height: 18px;\"><span style=\"line-height: 21px; font-size: 14px;\" data-mce-style=\"line-height: 21px; font-size: 14px;\">Recently you issued a refund for your event {event_name} at SportWrench from your Stripe dashboard. Note that all refunds must be processed via SportWrench only.<br><br>When you issue a refund from your Stripe dashboard, SportWrench system may not be able to update all the data properly. As a result, teams statuses, statistics and accounting will contain incorrect data which may affect your payout process.<br><br>Please process all refunds from your SportWrench account in the future.<br></span></p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "150%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "9f11e40e-bfca-414d-a3ff-ee9b73a80c13",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "312527fb-7017-4b85-8444-b8e08c1f187f",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "#555555"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "968c5413-585f-401e-a567-9e6082b74063",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">PURCHASE SUMMARY</span></p></div>",
                              "style": {
                                "color": "#FFFFFF",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "82ff5e68-850c-4477-a6d3-52d4f0d82ec7",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "c737bd56-bf55-4286-9da3-ddcae69d7899",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "3806d1d2-1619-4945-af63-3c9dd18b9614",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Event</span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "7011e5e3-ea08-4197-814b-b9cf4eb49225",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "c87c9d0d-3043-45a4-a7e2-abdb7142eef3",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{event_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "47c440a0-d39e-49bc-8ef8-52f64ad46545",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "f77518f7-ebde-4551-9c96-368e13363000",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "0e7ba029-cf22-4324-bb52-56f1d2822c8f",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: inherit; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: inherit; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Teams<br></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "a43d7881-f39f-4040-9e99-b3f1c815c9cc",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "04186b56-5306-408b-a170-b3f0ed40088e",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: inherit; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: inherit; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"line-height: 16px; font-size: 14px;\" data-mce-style=\"line-height: 16px; font-size: 14px;\">{team_names}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "06045c4e-7722-4b70-b115-1ec42f1872ee",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "32656d49-3159-4ef8-aa57-d8ee99a9ac31",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "5a11daba-569d-40d8-8193-9e855c2baba2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: inherit; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: inherit; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Club Name<br></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "6742f4ef-4f48-4eb1-80f9-a3ce2b0f92d9",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "e0f5334e-51d7-4b95-8c0d-ca9fbb59d4e2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: inherit; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: inherit; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{club_name} </span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "2d3e213d-92fe-40a7-b1db-56a31c88a302",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "09a315fd-3b54-429c-8933-57f1b91a0cb9",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "8e6f38cd-5682-474d-b31c-3877b504e2b7",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Purchase date<br/></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "e45f0239-b727-411a-b5d8-9314c0c8f2d6",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "a7c82fb3-c3b3-4a4b-a3e8-c948971a6822",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{payment_date_paid}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "ded86472-eb16-49af-8fb8-00bf2cd85a43",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "8365b5f7-9a3f-4d2c-bb5c-b21e54654702",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "d42218a9-2307-4750-803e-a1787c2e28a0",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Total amount<br/></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "c9364c57-**************-0b912f494937",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "4446953f-169a-4c1c-b0cb-2145f7466ef9",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{total_amount}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "fbc39919-e458-44ec-becb-69158ca3c34c",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "873a4ff3-d608-430e-bd6a-8a6a29f9920d",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "#555555"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "42bb9534-d959-49d3-bc29-d4877b8c438d",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">REFUND SUMMARY</span></p></div>",
                              "style": {
                                "color": "#FFFFFF",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "bf4d9710-2acc-42e4-b251-b91bef157c8b",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "780af69e-8b5c-41ba-9465-781ad61acb54",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "53c5345f-411e-4596-a3a7-678e1406a0ba",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Refund date<br/></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "5f4e92ee-710b-4fb5-a876-8207815bfe7c",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "9381ef5a-85d7-4c67-94c3-4bc3ed1327f7",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{date_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "*************-4b58-b1e3-b66bf72b7212",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "cb69b8f4-2af5-4e61-876a-b7c03f1f16e3",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "1308faa4-42d8-4037-8641-b52787ff29c7",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Amount refunded</span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "32237c92-846d-4aa8-bad1-be4d3e00e53e",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "905f66e4-dd9d-4222-8c91-5837d85f985f",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{amount_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "cbf733dc-4d8b-4c84-83bf-c8155045f8ad",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "73907ab0-8033-4798-88ca-00ba1e58c420",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "ef33859b-42fd-4052-b6c4-c8aca7063262",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #CCCCCC"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "two-columns-8-4-empty",
                  "uuid": "eca042fa-76ea-4432-819d-82ca6690a1f3",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "18f9b3d7-9533-49e4-9d2e-74bebff4457d",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "b9bd6f55-8d36-4b12-9c6a-9a829ecd43a2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\"><strong>ADJUSTED TOTAL</strong></span></p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "20px",
                              "padding-right": "20px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 8
                    },
                    {
                      "uuid": "c193f235-1869-42ba-9e15-54a423cd37a6",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "3e08d41f-1e2a-4aec-bf7e-4873fe66f9a8",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">{adjusted_total_amount}</span> </strong></p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "20px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "404379a4-0d52-472a-a31c-79fb736ce265",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "dc5f82b4-3d95-41e3-b62a-52502783d157",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "10px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "d2dce48b-31ae-4a24-a7ae-384891371ea4",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "height": "0px",
                                "border-top": "1px solid #222222"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "four-columns-empty",
                  "uuid": "214d23ad-f0b9-4cf0-8543-d5a67c9daa0e",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "d2d81b20-37be-40e3-9a6f-05e538d5903e",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "ee805cda-7b05-431c-bcd9-5112c9680007",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{facebook_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "*************-40a3-b0c8-deeda6c148a5",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "293000cd-aaed-40b1-8ba5-2498d2014d04",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{twitter_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "3d05abe2-53a7-48d8-9fdc-c1fd597d5c29",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "c9f0af2d-28f6-46fd-92e1-f25ff61145c8",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{instagram_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "1d4f625b-6947-4527-84f3-86a1ddea56d0",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "41ab1628-58ec-4691-957b-6dcba030388e",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{snapchat_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "255397a7-0afc-41ff-b04c-68ea12eb79cf",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "f31ee056-37c8-4799-ad84-c94636659b26",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "b4ea6a30-7a30-4cd2-9c05-aef03dc1a852",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "20px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "20px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #CCCCCC"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "c2e6f64e-467b-4def-b29f-2c222c35f5a0",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "6f77ed02-aea8-4146-92da-a3b4c339ee10",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "ee73ee6d-67c9-419c-86e1-7948877704d3",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                }
              ],
              "title": "",
              "template": {
                "name": "template-base",
                "type": "basic",
                "version": "2.0.0"
              },
              "description": ""
            },
            "comments": {}
          }', 'refund.stripe.eo', true, 'teams.refunds', true);
        
        UPDATE email_template_type
        SET default_email_template_id = (SELECT email_template_id
                                         FROM email_template et
                                         WHERE et.email_template_type = 'refund.stripe.eo'
                                           and et.email_template_group = 'teams.refunds')
        WHERE type = 'refund.stripe.eo'
          AND email_template_group = 'teams.refunds';
        
        UPDATE event_email_trigger
        SET email_template_id = (SELECT email_template_id
                                 FROM email_template et
                                 WHERE et.email_template_type = 'refund.stripe.eo'
                                   and et.email_template_group = 'teams.refunds')
        WHERE email_template_type = 'refund.stripe.eo'
          AND email_template_group = 'teams.refunds';
          
          
          INSERT INTO public.email_template (email_html, email_subject, email_text, title, bee_json, email_template_type,
                                           is_valid, email_template_group, published)
        VALUES (e'<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><!--[if !mso]><!--><link href="https://fonts.googleapis.com/css?family=Lato"
        rel="stylesheet" type="text/css"><!--<![endif]--><style>
        *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:640px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
        </style></head><body style="background-color:#fff;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:20px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%"
        border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:116px"><img src="https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg" style="display:block;height:auto;border:0;width:100%" width="116" alt="Image" title="Image"></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody>
        </table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #222"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0"
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:16.8px"><span style="font-size:16px;">{event_name}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table
        class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:62px"><img
        src="https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/icon1.svg" style="display:block;height:auto;border:0;width:100%" width="62" alt="Image" title="Image"></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0"
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">Refunded:</span></p></div></div></td></tr></table><table class="text_block block-2"
        width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{amount_refunded}</span></p></div>
        </div></td></tr></table></td><td class="column column-2" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div
        style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;"><span style="font-size:16px;">Date</span> issued:</span></p></div></div></td></tr></table><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{date_refunded}</span></p></div></div></td></tr></table></td><td class="column column-3"
        width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;"><span style="font-size:16px;">Refunded</span> from Stripe acc:</span></p></div></div></td></tr></table><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{eo_stripe_account}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table>
        <table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:10px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:10px"><div
        style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:18px;color:#000;line-height:1.5"><p style="margin:0;mso-line-height-alt:21px"><span style="font-size:14px;">This email is to inform you that Event Owner {eo_name} issued a refund for the event {event_name} from the Stripe dashboard.&nbsp;<br><br>Please contact Event Owner to confirm required actions.<br><br>
        Also, ask EO to process all refunds from the SportWrench account in the future.</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-7" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr>
        <td class="pad"><div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-8" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td
        class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;background-color:#555;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#fff;line-height:1.2"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:16px;">PURCHASE SUMMARY</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p
        style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Event</span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{event_name}</span></p></div></div>
        </td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-10" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1"
        width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Teams<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{team_names}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-11" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p
        style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Club Name<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{club_name}</span></p></div></div></td>
        </tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Purchase date<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{payment_date_paid}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-13" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p
        style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Total amount<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{total_amount}</span></p></div></div>
        </td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-14" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1"
        width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;background-color:#555;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#fff;line-height:1.2"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:16px;">REFUND SUMMARY</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-15" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p
        style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Refund date<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{date_refunded}</span></p></div></div>
        </td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-16" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1"
        width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif">
        <div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Amount refunded</span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{amount_refunded}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-17" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table></div></td></tr></table>
        </td></tr></tbody></table></td></tr></tbody></table><table class="row row-18" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1"
        width="66.66666666666667%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:10px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:20px;padding-right:20px;padding-top:5px"><div
        style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:16px;"><strong>ADJUSTED TOTAL</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="33.333333333333336%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:10px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:20px;padding-right:5px;padding-top:5px"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:16px;">{adjusted_total_amount}</span> </strong></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-19" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;background-color:#555;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#fff;line-height:1.2"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:16px;">EVENT OWNER CONTACT DETAILS</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-20" align="center" width="100%" border="0" cellpadding="0" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:10px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Name</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:10px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{eo_name}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-21" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table
        class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Email</strong></span>
        </p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">{eo_email}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-22" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Phone</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">{eo_phone}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-23" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0"
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Stripe account<br></strong></span></p></div></div></td></tr></table></td><td class="column column-2"
        width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">{eo_stripe_account}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-24" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%"
        border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #000"><span>&#8202;</span></td>
        </tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-25" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px;margin:0 auto" width="620"><tbody><tr><td
        class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{facebook_icon}</p></div></div></td></tr></table></td><td class="column column-2" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{twitter_icon}</p></div></div></td></tr></table></td><td class="column column-3" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{instagram_icon}</p></div></div></td></tr></table></td><td class="column column-4" width="25%"
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{snapchat_icon}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-26" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td>
        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0"
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table>
        </div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-27" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px;margin:0 auto" width="620"><tbody><tr><td class="column column-1"
        width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class
        style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;mso-line-height-alt:14.399999999999999px">Copyright © 2019 SportWrench Inc., All rights reserved.</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
                'Important: Stripe refund for event {event_name} - Contact {eo_name}', e'{event_name}Refunded:{amount_refunded}
        Date issued:{date_refunded}Refunded from Stripe acc:{eo_stripe_account}
        This email is to inform you that Event Owner {eo_name} issued a refund for the event {event_name} from the Stripe dashboard. Please contact Event Owner to confirm required actions.
        Also, ask EO to process all refunds from the SportWrench account in the future.
        PURCHASE SUMMARYEvent{event_name}
        Teams{team_names}Club Name{club_name}
        Purchase date{payment_date_paid}Total amount{total_amount}
        REFUND SUMMARYRefund date{date_refunded}
        Amount refunded{amount_refunded}
        ADJUSTED TOTAL{adjusted_total_amount} EVENT OWNER CONTACT DETAILSName{eo_name}Email
        {eo_email}Phone{eo_phone}Stripe account{eo_stripe_account}
        {facebook_icon}{twitter_icon}{instagram_icon}{snapchat_icon}
        Copyright © 2019 SportWrench Inc., All rights reserved.', 'Stripe Dashboard EO Refund (to SW Admin)', '{
            "page": {
              "body": {
                "type": "mailup-bee-page-properties",
                "content": {
                  "style": {
                    "color": "#000000",
                    "font-family": "''Lato'', Tahoma, Verdana, Segoe, sans-serif"
                  },
                  "computedStyle": {
                    "linkColor": "#71777D",
                    "messageWidth": "620px",
                    "messageBackgroundColor": "transparent"
                  }
                },
                "webFonts": [
                  {
                    "url": "https://fonts.googleapis.com/css?family=Lato",
                    "name": "Lato",
                    "fontFamily": "''Lato'', Tahoma, Verdana, Segoe, sans-serif"
                  }
                ],
                "container": {
                  "style": {
                    "background-color": "#FFFFFF"
                  }
                }
              },
              "rows": [
                {
                  "type": "one-column-empty",
                  "uuid": "14f228f3-6870-4721-a67b-6e2e95bdfabe",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "e22589ca-45fd-4e7a-abc5-abab20278467",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "20px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "uuid": "4eb07ebb-33ce-42bc-8ccc-7064d10ffa09",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Image",
                              "src": "https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg",
                              "href": "",
                              "width": "116px",
                              "height": "40px",
                              "prefix": "",
                              "target": "_self"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": "116px",
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "0a75cf8f-b218-4561-bbd8-092c67e4aa50",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "17ef7f36-f8b1-4c07-b756-b6dd13b72250",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "ae069003-f09d-4d9e-b56b-d20b64cfe298",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "height": "0px",
                                "border-top": "1px solid #222222"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "20bedfac-e17b-41e9-bfb5-86ef8c4e497f",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "271e22b5-6520-4e02-a7de-8cc34758e0b2",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "fa5255eb-59ef-4e99-8359-27dde51fc70e",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:left;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:left;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">{event_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "94665bed-abe5-4d96-b750-106a6aea9cc2",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "c3c83e81-b966-46f0-9944-1cffb7bec219",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "uuid": "b92c7afb-aa13-4411-a893-ae8812711df3",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Image",
                              "src": "https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/icon1.svg",
                              "href": "",
                              "width": "62px",
                              "height": "53px",
                              "prefix": "",
                              "target": "_self"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": "62px"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "three-columns-empty",
                  "uuid": "1579fb66-d1ca-444a-8ff8-95ca548ea8ec",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "5a67dc1a-c07c-4a01-9c49-953e5b5ad740",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "5a3de55b-7160-47ea-a424-bbf7a11eed90",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">Refunded:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "ea9c202b-bd24-41a8-8063-f56c07b987ab",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">{amount_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    },
                    {
                      "uuid": "3171c580-294c-4820-bb1e-a93de7d5d408",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "1ad3825c-5ea3-438c-88f9-83386c013632",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">Date</span> issued:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "a8b40983-ba1c-44a3-adde-5982cbf7c374",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">{date_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    },
                    {
                      "uuid": "3595631d-ea19-40b1-bba3-bc84dfc2958c",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "d9fae57e-f152-467a-a387-c5ddf0276559",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">Refunded</span> from Stripe acc:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "2bf8586f-9d4a-4253-a0cf-59ef0741e909",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"line-height:19px;font-size:16px;\" data-mce-style=\"line-height:19px;font-size:16px;\">{eo_stripe_account}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "92996d45-5236-4756-b485-6986058609e5",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "cab76667-cdaf-4f4f-b55b-32f5bd3182db",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "10px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "c5757cfb-16ea-4b07-ac45-d0e1dc2dc079",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 18px; font-family: inherit;\" data-mce-style=\"font-size: 12px; line-height: 18px; font-family: inherit;\"><p style=\"word-break: break-word; line-height: 18px;\" data-mce-style=\"word-break: break-word; line-height: 18px;\"><span style=\"line-height: 21px; font-size: 14px;\" data-mce-style=\"line-height: 21px; font-size: 14px;\">This email is to inform you that Event Owner {eo_name} issued a refund for the event {event_name} from the Stripe dashboard.&nbsp;<br><br>Please contact Event Owner to confirm required actions.<br><br>Also, ask EO to process all refunds from the SportWrench account in the future.</span></p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "150%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "71805f4e-9f19-4456-a9fc-a6f48ab13c1c",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "fd041522-3b95-4717-bd07-b0d7e0a2ee90",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "1f425b78-4a86-4b22-9672-6652e7803255",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "d1e405ef-978e-4c6d-95af-96fe398855aa",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "#555555"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "5d0ad170-31fd-4c8e-bfa5-365c38407e9e",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">PURCHASE SUMMARY</span></p></div>",
                              "style": {
                                "color": "#FFFFFF",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "425fbb34-ce58-423d-8a12-eba883b1d1d2",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "3f4b99dc-25fb-4b55-802a-a45e8b5abf34",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "dc23d728-31a6-48ca-83b7-c223b024a22b",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Event</span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "c3a8e9ce-3d76-4a01-af0f-bfe0bc743494",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "8a6a8a67-db10-4702-95c7-42f50316c4b4",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{event_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "2c9382dc-b1a0-4e97-952f-4b10a04cfdad",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "9253d936-6798-4c20-8728-ef824e2dfb6f",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "20c4868d-2aac-4587-a587-6e5169376de4",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Teams<br/></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "120274de-c6b8-446a-a22d-7535756f02b7",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "df03cfd8-146c-4036-b763-970708b0706b",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: inherit; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: inherit; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"line-height: 16px; font-size: 14px;\" data-mce-style=\"line-height: 16px; font-size: 14px;\">{team_names}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "7dec15c4-a1ec-4c3d-a5f4-f39da828e004",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "82417767-c53b-4062-b69a-48f58209c74a",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "a5ce6a02-87a1-4a4e-a49d-ff191d3a0636",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: inherit; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: inherit; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Club Name<br></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "d510bc1f-41ce-482e-9702-3de63dfe8b02",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "90327429-fae8-47f6-9f68-ca60853eba11",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: inherit; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: inherit; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{club_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "9d4ab158-ea57-4771-841c-615dde6b2133",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "d3668242-b3d6-42d1-80d8-aa61adf0fd3a",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "df7e1660-d036-422b-8cf9-070b4a707ee2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Purchase date<br/></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "1009916b-122a-4c70-990c-63e174c0df42",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "adf5b780-dfb9-44fd-8549-e41edffc5c70",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{payment_date_paid}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "b8c669e3-b61c-44d1-85ae-b40053175ca9",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "7ba88255-d0e6-4122-9f93-437752fe29d5",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "e94ff043-320c-4c55-af75-61c83350e058",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Total amount<br/></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "d705f66f-482b-4e6f-867a-98b417b6eb33",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "fc0ddf55-1a78-4342-b44b-2a7227e845c4",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{total_amount}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "03a7f8af-5a35-47a6-b067-345b43e34b28",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "7df84d7f-cbec-4e74-8f05-d6a28db7e99b",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "#555555"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "272d0049-75c4-4331-9328-9a1ff46ce1cd",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">REFUND SUMMARY</span></p></div>",
                              "style": {
                                "color": "#FFFFFF",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "8eb9333b-d3cb-48a5-9012-088176c1ca38",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "1915089d-0bc3-438e-a02c-21cd975801ba",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "615c5995-5b17-4d4a-ac63-aedd616bffc0",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Refund date<br/></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "c67ffacc-fd1b-4b1a-a9bb-4f8fed6cd247",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "feff8273-4f27-471f-ae0c-23ddf31f5d46",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{date_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "c4cd7fa1-6718-46bb-b680-80c038776ff3",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "13d7236e-bbd2-43e5-8800-4040ba623b76",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "675eb00d-45fe-4dc2-813a-2fd7e30d6c2e",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">Amount refunded</span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "fa2c75e9-c576-4b36-a04c-8afc7a5366ad",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "58f1bc36-39b9-4231-bab7-2f818139c7e2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{amount_refunded}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "073412b0-f5dd-4f07-a7cd-5db80c215f8e",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "017af070-def8-4aab-ab05-c1ec9c4f0de8",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "5bcfca4f-26a6-40e0-bc94-e4a80e76f724",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #CCCCCC"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "two-columns-8-4-empty",
                  "uuid": "70030fc1-2fc0-4428-b414-b0f08de7abe4",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "7c1614ce-7223-4454-a819-4bf7547d1fee",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "10px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "74a7f312-febc-4f81-82c9-3cc4272e1fd5",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\"><strong>ADJUSTED TOTAL</strong></span></p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "20px",
                              "padding-right": "20px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 8
                    },
                    {
                      "uuid": "563322bb-1627-4ebf-87f7-dfd2bac08d3f",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "10px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "423b5ac5-ed22-4348-a51b-d3624f3af978",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><strong><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">{adjusted_total_amount}</span> </strong></p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "20px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "8936bcc0-c10a-427d-bc6c-b615c75d9f46",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "9950d0d5-fbab-49d7-b85f-da702f138ce9",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "#555555"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "b6ab3a94-8fc0-4f89-a65d-06d1d822f2c6",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;text-align:center;word-break:break-word;\"><span style=\"font-size:16px;line-height:19px;\" data-mce-style=\"font-size:16px;line-height:19px;\">EVENT OWNER CONTACT DETAILS</span></p></div>",
                              "style": {
                                "color": "#FFFFFF",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "c9a98523-9624-4b7f-b9a1-d38e249a5c3e",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "3113aef2-566b-483a-8ca9-076073d98fce",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "10px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "c2cce132-67a0-4ba4-bc46-65262a729bee",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\"><strong>Name</strong></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "8e6036e4-14c0-427e-acf1-753b10470bbe",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "10px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "c54591d9-599d-48af-907d-25b75f91aac0",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:12px;line-height:14px;word-break:break-word;\" data-mce-style=\"font-size:12px;line-height:14px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\">{eo_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "6adf4057-b50b-4de6-b391-821afc9f0453",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "82317b93-805b-4832-8f90-6bf518ec0e5e",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "9c7d4cd5-0e6e-4b0f-bf2b-aeec98c272a6",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\"><strong>Email</strong></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "41cea1ea-2550-427f-a04f-1cd6067bbd0a",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "6d65864e-94d8-4635-b8d0-a6ffdeed14c2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\">{eo_email}</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "e62b5fb4-2a83-4484-a8c4-2d633f80b1c7",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "c7667a5c-42a8-4de9-97d4-2b0d3f7d528e",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "b11cfe03-b426-472a-8b86-62bb0b61cfb8",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\"><strong>Phone</strong></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "80bb744a-9bf3-4716-8206-cb73a903582c",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "3e128238-f625-412a-81f9-c9199bf27e55",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\">{eo_phone}</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "f46cbeb6-c8f5-4dbc-8ba7-bca8969abd68",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "21fadb09-6ac2-40ba-966f-4de4611d049e",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "b80e7a15-0f9a-4be3-84ca-c07d5f40b0d5",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\"><span style=\"font-size:14px;line-height:16px;\" data-mce-style=\"font-size:14px;line-height:16px;\"><strong>Stripe account<br/></strong></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "34c6516d-a388-47f9-a4e9-9013fb7c63bc",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "45818e94-e81b-4d50-8f69-0a7ab9d029b1",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family:inherit;font-size:12px;line-height:14px;\" data-mce-style=\"font-family:inherit;font-size:12px;line-height:14px;\"><p style=\"font-size:14px;line-height:16px;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;word-break:break-word;\">{eo_stripe_account}</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "a72ee1f3-d3f8-457c-a832-32f36d4e5a2c",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "d859aa49-7ce3-4157-a95a-aa627e4f03e1",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "2e23c4ce-8c2a-4067-86ed-7174034697b0",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "20px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "20px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "height": "0px",
                                "border-top": "1px solid #000000"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "four-columns-empty",
                  "uuid": "5e1856ac-781e-4fc3-895c-2bd7e9b35ccf",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "fa79d13d-4395-4a91-8630-a6b54513bae6",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "a3da1cb2-97f4-42b2-9734-5cfe40da2169",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{facebook_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "21cab4f5-7e5c-4aa6-8124-d32169b34db3",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "5d1c205d-ee08-4011-8bb5-cde1cca2ada4",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{twitter_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "7e4d53cb-679c-4d05-aa3a-e64c4d5a72b8",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "620c9bba-25ef-4c72-98ef-83d49e45ac98",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{instagram_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "97453d2a-0d9b-486b-991f-91741e0c37f6",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "443ee213-3dec-4c6f-ac53-2aa92e7ceb6f",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\">{snapchat_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "3ed8e609-dc68-4acc-b2d9-c33c94d75e0d",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "d8b35571-4805-45f1-8308-e71ff15c56bf",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "7bbfde67-2ada-45b2-ad1d-6aa55645236c",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "20px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "20px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #CCCCCC"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "bb7385a8-9aa3-4372-a2b0-f0f12b24d80b",
                  "empty": false,
                  "locked": false,
                  "synced": false,
                  "columns": [
                    {
                      "uuid": "15772bdb-97a6-4a3d-a89c-5b8f60753881",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f24757b4-650e-4e8d-9692-a9e2d4e53dd2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"word-break:break-word;\">Copyright © 2019 SportWrench Inc., All rights reserved.</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "verticalAlign": "top",
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                }
              ],
              "title": "",
              "template": {
                "name": "template-base",
                "type": "basic",
                "version": "2.0.0"
              },
              "description": ""
            },
            "comments": {}
          }', 'refund.stripe.admin', true, 'teams.refunds', true);
        
        UPDATE email_template_type
        SET default_email_template_id = (SELECT email_template_id
                                         FROM email_template et
                                         WHERE et.email_template_type = 'refund.stripe.admin'
                                           and et.email_template_group = 'teams.refunds')
        WHERE type = 'refund.stripe.admin'
          AND email_template_group = 'teams.refunds';
        
        UPDATE event_email_trigger
        SET email_template_id = (SELECT email_template_id
                                 FROM email_template et
                                 WHERE et.email_template_type = 'refund.stripe.admin'
                                   and et.email_template_group = 'teams.refunds')
        WHERE email_template_type = 'refund.stripe.admin'
          AND email_template_group = 'teams.refunds';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM email_template 
        WHERE email_template_type IN ('refund.stripe.eo', 'refund.stripe.admin') 
            AND email_template_group = 'teams.refunds';
        
        UPDATE event_email_trigger SET email_template_id = 0 
        WHERE email_template_type IN ('refund.stripe.eo', 'refund.stripe.admin') 
            AND email_template_group = 'teams.refunds';
            
        UPDATE email_template_type SET default_email_template_id = 0
        WHERE type IN ('refund.stripe.eo', 'refund.stripe.admin') 
            AND email_template_group = 'teams.refunds';    
    `)
};
