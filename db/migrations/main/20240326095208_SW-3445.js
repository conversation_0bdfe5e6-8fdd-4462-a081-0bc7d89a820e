exports.up = function (knex) {
    return knex.schema.raw(`
        -- Add new columns: last_error_code, last_error_message, last_error_action, and last_error_image
        ALTER TABLE "public"."recognition_verification"
        ADD COLUMN "last_error_code" TEXT, 
        ADD COLUMN "last_error_message" TEXT, 
        ADD COLUMN "last_error_action" TEXT, 
        ADD COLUMN "last_error_image" TEXT;
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
        -- Remove newly added columns that are not needed in the original design
        ALTER TABLE "public"."recognition_verification" DROP COLUMN IF EXISTS "last_error_code"; 
        ALTER TABLE "public"."recognition_verification" DROP COLUMN IF EXISTS "last_error_message"; 
        ALTER TABLE "public"."recognition_verification" DROP COLUMN IF EXISTS "last_error_action"; 
        ALTER TABLE "public"."recognition_verification" DROP COLUMN IF EXISTS "last_error_image";
    `);
};
