exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add the new column without a default and update its value conditionally
        ALTER TABLE "public"."user" ADD COLUMN "recognition_verification_status" TEXT;

        -- Set the status based on the is_recognition_verified flag
        UPDATE "public"."user" 
        SET "recognition_verification_status" = CASE
            WHEN "is_recognition_verified" = TRUE THEN 'verified'
            ELSE 'requires_verification'
        END;

        -- Now, drop the old column
        ALTER TABLE "public"."user" DROP COLUMN "is_recognition_verified";
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        -- Add the is_recognition_verified column without a default and update its value conditionally
        ALTER TABLE "public"."user" ADD COLUMN "is_recognition_verified" BOOLEAN;

        -- Set the flag based on the recognition_verification_status
        UPDATE "public"."user" 
        SET "is_recognition_verified" = CASE
            WHEN "recognition_verification_status" = 'verified' THEN TRUE
            ELSE FALSE
        END;

        -- Add comment to the is_recognition_verified column
        COMMENT ON COLUMN "public"."user"."is_recognition_verified" IS 'Indicates if recognition is verified';

        -- Now, drop the new status column
        ALTER TABLE "public"."user" DROP COLUMN "recognition_verification_status";
    `);
};
