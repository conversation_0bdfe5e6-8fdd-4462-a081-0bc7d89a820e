
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."custom_payment" ADD COLUMN IF NOT EXISTS "stripe_payment_method_id" TEXT DEFAULT NULL;
        ALTER TABLE "public"."custom_payment" ALTER COLUMN "stripe_payment_intent_id" DROP NOT NULL;
        
        ALTER TYPE "public"."custom_payment_status" ADD VALUE 'requires_action' AFTER 'disputed';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."custom_payment" DROP COLUMN IF EXISTS "stripe_payment_method_id" ;
        ALTER TABLE "public"."custom_payment" ALTER COLUMN "stripe_payment_intent_id" SET NOT NULL;
        
        ALTER TABLE "public"."custom_payment"
            ALTER COLUMN "status" TYPE TEXT;
        
        DROP TYPE "public"."custom_payment_status";
        CREATE TYPE "public"."custom_payment_status" AS ENUM ('canceled', 'paid', 'pending', 'disputed');
        
        UPDATE "public"."custom_payment"
        SET "status" = 'pending'
        WHERE "status" = 'requires_action';
        
        ALTER TABLE "public"."custom_payment"
            ALTER COLUMN "status" TYPE "custom_payment_status" USING status::custom_payment_status;
    `)
};
