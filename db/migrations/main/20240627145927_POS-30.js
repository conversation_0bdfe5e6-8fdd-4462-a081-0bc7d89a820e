/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Create sales_hub_payment_provider_account table -------------------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."sales_hub_payment_provider_account"
        (
            "sales_hub_payment_provider_account_id" INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
            "created"                               TIMESTAMP DEFAULT NOW(),
            "modified"                              TIMESTAMP,
            "stripe_account_id"                     INT  NOT NULL,
            "payment_provider_account_id"           TEXT NOT NULL,
            CONSTRAINT "unique_sales_hub_payment_provider_account_stripe_account_id" UNIQUE ("stripe_account_id"),
            CONSTRAINT "unique_sales_hub_payment_provider_account_provider_account_id" 
                UNIQUE ("payment_provider_account_id")
        );
        
        COMMENT ON COLUMN "sales_hub_payment_provider_account".stripe_account_id
            IS 'SW Stripe Account ID';
        COMMENT ON COLUMN "sales_hub_payment_provider_account".payment_provider_account_id
            IS 'Sales Hub Payment Provider Account ID';
        -- -------------------------------------------------------------------------------------------------------------
        
        ALTER TABLE event_point_of_sales
            ADD COLUMN IF NOT EXISTS "payment_provider_account_id" TEXT DEFAULT NULL;
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS sales_hub_payment_provider_account;
        ALTER TABLE event_point_of_sales DROP COLUMN IF EXISTS "payment_provider_account_id";
    `)
};
