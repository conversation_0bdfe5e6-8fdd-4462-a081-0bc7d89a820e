/**
 * @param { import("knex").Knex } knex
 * @returns {Knex.SchemaBuilder}
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS "public"."sales_hub_product_event_ticket"
        (
            sales_hub_point_of_sales_product_id INT GENERATED ALWAYS AS IDENTITY,
            created                             TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            modified                            TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            sales_hub_product_id                TEXT NOT NULL UNIQUE,
            event_ticket_id                     INT  NOT NULL UNIQUE
        )
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns {Knex.SchemaBuilder}
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."sales_hub_product_event_ticket";
    `)
};
