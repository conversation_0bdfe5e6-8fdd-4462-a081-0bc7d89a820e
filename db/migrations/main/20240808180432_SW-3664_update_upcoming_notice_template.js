
exports.up = function(knex) {
    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text =
                'This email is to notify you that SportWrench has scheduled and will soon be processing event related charges for
                the {event_short_name}. You will receive an email confirming a successful charge for SportWrench Team Fees/Booth Fee on
                {team_charge_datetime}. Should any uncollected SportWrench Ticket Fees be owed, most likely as a result of cash sales for
                event tickets sold on site, the charge will occur on {ticket_charge_datetime}.
                These charges will be processed to the {payment_method} ending in {card_last_4}. If
                you wish to change your payment method, please log into your SportWrench account to change the payment
                method before the date and time listed above. To add or update your payment method, simply click on the
                Payment Cards button at the top of your My Events tab.
                The final amount charged for each fee may change from the date of this notice based on team status and ticket
                purchase activity. Payment due details can be found on the Accounting page for your event. We recommend you
                process any ticket refunds due prior to the charge date listed above as all SW Team Fees processed will be final
                Copyright © SportWrench Inc. {current_year}. All rights reserved',
            email_html =
                '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
                *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:660px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
                </style></head><body class="body" style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody>
                <tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
                class="html_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center">    <div style="text-align: left">
                        <p>
                            This email is to notify you that SportWrench has scheduled and will soon be processing event related charges for
                            the <b>{event_short_name}</b>. You will receive an email confirming a successful charge for <b>SportWrench Team Fees/Booth Fee</b> on
                            {team_charge_datetime}. Should any uncollected <b>SportWrench Ticket Fees</b> be owed, most likely as a result of cash sales for
                            event tickets sold on site, the charge will occur on {ticket_charge_datetime}.
                        </p>
                        <p>
                            These charges will be processed to the {payment_method} ending in {card_last_4}. If
                            you wish to change your payment method, please log into your SportWrench account to change the payment
                            method before the date and time listed above. To add or update your payment method, simply click on the
                            Payment Cards button at the top of your My Events tab.
                        </p>
                        <p>
                            The final amount charged for each fee may change from the date of this notice based on team status and ticket
                            purchase activity. Payment due details can be found on the Accounting page for your event. We recommend you
                            process any ticket refunds due prior to the charge date listed above as all SW Team Fees processed will be final
                        </p>
                    </div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
                cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" 
                cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
                <span style="font-size:12px;">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:70.4px"><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:100%" width="70.4" alt="Logo" 
                title="Logo" height="auto"></a></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
            bee_json =
                '{
                    "page": {
                        "body": {
                            "type": "mailup-bee-page-properties",
                            "content": {
                                "style": {
                                    "color": "#000000",
                                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                                },
                                "computedStyle": {
                                    "linkColor": "#0000FF",
                                    "messageWidth": "640px",
                                    "messageBackgroundColor": "#FFFFFF"
                                }
                            },
                            "webFonts": [],
                            "container": {
                                "style": {
                                    "background-color": "#F7F7F7"
                                }
                            }
                        },
                        "rows": [
                            {
                                "type": "one-column-empty",
                                "uuid": "db5d7102-0637-450a-8b23-8674482217c6",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "dedef322-7a4a-454d-a5a6-2d4041b3982b",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-html",
                                                "uuid": "959ddf8e-1299-4225-ba3b-7ce4a4bbc002",
                                                "locked": false,
                                                "descriptor": {
                                                    "html": {
                                                        "html": "<div style=\"text-align: left\"><p>This email is to notify you that SportWrench has scheduled and will soon be processing event related charges for the <b>{event_short_name}</b>. You will receive an email confirming a successful charge for <b>SportWrench Team Fees/Booth Fee</b> on {team_charge_datetime}. Should any uncollected <b>SportWrench Ticket Fees</b> be owed, most likely as a result of cash sales for event tickets sold on site, the charge will occur on {ticket_charge_datetime}.</p><p>These charges will be processed to the {payment_method} ending in {card_last_4}. If you wish to change your payment method, please log into your SportWrench account to change the payment method before the date and time listed above. To add or update your payment method, simply click on the Payment Cards button at the top of your My Events tab.</p><p>The final amount charged for each fee may change from the date of this notice based on team status and ticket purchase activity. Payment due details can be found on the Accounting page for your event. We recommend you process any ticket refunds due prior to the charge date listed above as all SW Team Fees processed will be final</p></div>"
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnAmp": false,
                                                        "hideContentOnHtml": false,
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "dc12a729-4e83-4a0a-b4b6-b0b8d104e030",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "54a3f753-ad77-48b7-ae45-1db752ccdf18",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-divider",
                                                "uuid": "4e96bc4a-e938-4e5b-b913-9bfdc6b8da0e",
                                                "locked": false,
                                                "descriptor": {
                                                    "style": {
                                                        "padding-top": "5px",
                                                        "padding-left": "5px",
                                                        "padding-right": "5px",
                                                        "padding-bottom": "5px"
                                                    },
                                                    "divider": {
                                                        "style": {
                                                            "width": "100%",
                                                            "border-top": "1px dotted #BBBBBB"
                                                        }
                                                    },
                                                    "computedStyle": {
                                                        "align": "center"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "91ed0510-590c-4673-b673-ae0ec0fbc3be",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "69ee26f6-3a60-40ef-9248-b6c2f50a9283",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-text",
                                                "uuid": "27048e93-148b-4862-b09e-d7801568e761",
                                                "locked": false,
                                                "descriptor": {
                                                    "text": {
                                                        "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"font-size:12px;line-height:14px;\" data-mce-style=\"font-size:12px;line-height:14px;\">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div>",
                                                        "style": {
                                                            "color": "#555555",
                                                            "font-family": "inherit",
                                                            "line-height": "120%"
                                                        },
                                                        "computedStyle": {
                                                            "linkColor": "#0000FF"
                                                        }
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "10px",
                                                        "padding-right": "10px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "06f311d8-354e-46d6-850b-091af8b03b57",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "ade101c1-f3c2-4bc6-88e2-9429e71d5413",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-image",
                                                "uuid": "07f13f2d-375b-44cd-8afb-c6316870ece8",
                                                "locked": false,
                                                "descriptor": {
                                                    "image": {
                                                        "alt": "Logo",
                                                        "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                                                        "href": "https://sportwrench.com",
                                                        "prefix": "",
                                                        "target": "_self",
                                                        "percWidth": 11
                                                    },
                                                    "style": {
                                                        "width": "100%",
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "class": "center",
                                                        "width": "70.4px"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            }
                        ],
                        "title": "BF-basic-newsletter",
                        "template": {
                            "name": "template-base",
                            "type": "basic",
                            "version": "2.0.0"
                        },
                        "description": "BF-basic-newsletter"
                    },
                    "comments": {}
                }'
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'upcoming_uncollected_fee_payments.notice');
    `);
};

exports.down = function(knex) {
    return knex.raw(String.raw`
        UPDATE email_template
        SET email_text =
                'This email is to notify you that SportWrench has scheduled and will soon be processing event related charges for
                the {event_short_name}. You will receive an email confirming a successful charge for SportWrench Team Fees/Booth Fee on
                {team_charge_datetime}. Should any uncollected SportWrench Ticket Fees be owed, most likely as a result of cash sales for
                event tickets sold on site, the charge will occur on {ticket_charge_datetime}.
                These charges will be processed to the {payment_method} ending in {card_last_4}. If
                you wish to change your payment method, please log into your SportWrench account to change the payment
                method before the date and time listed above. To add or update your payment method, simply click on the
                Payment Cards button at the top of your My Events tab.
                The final amount charged for each fee may change from the date of this notice based on team status and ticket
                purchase activity. Payment due details can be found on the Accounting page for your event. We recommend you
                process any ticket refunds due prior to the charge date listed above as all SW Team Fees processed will be final
                Copyright © SportWrench Inc. {season}. All rights reserved',
            email_html =
                '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
                *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:660px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
                </style></head><body class="body" style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody>
                <tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
                class="html_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center">    <div style="text-align: left">
                        <p>
                            This email is to notify you that SportWrench has scheduled and will soon be processing event related charges for
                            the <b>{event_short_name}</b>. You will receive an email confirming a successful charge for <b>SportWrench Team Fees/Booth Fee</b> on
                            {team_charge_datetime}. Should any uncollected <b>SportWrench Ticket Fees</b> be owed, most likely as a result of cash sales for
                            event tickets sold on site, the charge will occur on {ticket_charge_datetime}.
                        </p>
                        <p>
                            These charges will be processed to the {payment_method} ending in {card_last_4}. If
                            you wish to change your payment method, please log into your SportWrench account to change the payment
                            method before the date and time listed above. To add or update your payment method, simply click on the
                            Payment Cards button at the top of your My Events tab.
                        </p>
                        <p>
                            The final amount charged for each fee may change from the date of this notice based on team status and ticket
                            purchase activity. Payment due details can be found on the Accounting page for your event. We recommend you
                            process any ticket refunds due prior to the charge date listed above as all SW Team Fees processed will be final
                        </p>
                    </div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
                cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" 
                cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
                <span style="font-size:12px;">Copyright © SportWrench Inc. {season}. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:70.4px"><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:100%" width="70.4" alt="Logo" 
                title="Logo" height="auto"></a></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
            bee_json =
                '{
                    "page": {
                        "body": {
                            "type": "mailup-bee-page-properties",
                            "content": {
                                "style": {
                                    "color": "#000000",
                                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                                },
                                "computedStyle": {
                                    "linkColor": "#0000FF",
                                    "messageWidth": "640px",
                                    "messageBackgroundColor": "#FFFFFF"
                                }
                            },
                            "webFonts": [],
                            "container": {
                                "style": {
                                    "background-color": "#F7F7F7"
                                }
                            }
                        },
                        "rows": [
                            {
                                "type": "one-column-empty",
                                "uuid": "db5d7102-0637-450a-8b23-8674482217c6",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "dedef322-7a4a-454d-a5a6-2d4041b3982b",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-html",
                                                "uuid": "959ddf8e-1299-4225-ba3b-7ce4a4bbc002",
                                                "locked": false,
                                                "descriptor": {
                                                    "html": {
                                                        "html": "<div style=\"text-align: left\"><p>This email is to notify you that SportWrench has scheduled and will soon be processing event related charges for the <b>{event_short_name}</b>. You will receive an email confirming a successful charge for <b>SportWrench Team Fees/Booth Fee</b> on {team_charge_datetime}. Should any uncollected <b>SportWrench Ticket Fees</b> be owed, most likely as a result of cash sales for event tickets sold on site, the charge will occur on {ticket_charge_datetime}.</p><p>These charges will be processed to the {payment_method} ending in {card_last_4}. If you wish to change your payment method, please log into your SportWrench account to change the payment method before the date and time listed above. To add or update your payment method, simply click on the Payment Cards button at the top of your My Events tab.</p><p>The final amount charged for each fee may change from the date of this notice based on team status and ticket purchase activity. Payment due details can be found on the Accounting page for your event. We recommend you process any ticket refunds due prior to the charge date listed above as all SW Team Fees processed will be final</p></div>"
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnAmp": false,
                                                        "hideContentOnHtml": false,
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "dc12a729-4e83-4a0a-b4b6-b0b8d104e030",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "54a3f753-ad77-48b7-ae45-1db752ccdf18",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-divider",
                                                "uuid": "4e96bc4a-e938-4e5b-b913-9bfdc6b8da0e",
                                                "locked": false,
                                                "descriptor": {
                                                    "style": {
                                                        "padding-top": "5px",
                                                        "padding-left": "5px",
                                                        "padding-right": "5px",
                                                        "padding-bottom": "5px"
                                                    },
                                                    "divider": {
                                                        "style": {
                                                            "width": "100%",
                                                            "border-top": "1px dotted #BBBBBB"
                                                        }
                                                    },
                                                    "computedStyle": {
                                                        "align": "center"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "91ed0510-590c-4673-b673-ae0ec0fbc3be",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "69ee26f6-3a60-40ef-9248-b6c2f50a9283",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-text",
                                                "uuid": "27048e93-148b-4862-b09e-d7801568e761",
                                                "locked": false,
                                                "descriptor": {
                                                    "text": {
                                                        "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"font-size:12px;line-height:14px;\" data-mce-style=\"font-size:12px;line-height:14px;\">Copyright © SportWrench Inc. {season}. All rights reserved</span></p></div>",
                                                        "style": {
                                                            "color": "#555555",
                                                            "font-family": "inherit",
                                                            "line-height": "120%"
                                                        },
                                                        "computedStyle": {
                                                            "linkColor": "#0000FF"
                                                        }
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "10px",
                                                        "padding-right": "10px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "06f311d8-354e-46d6-850b-091af8b03b57",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "ade101c1-f3c2-4bc6-88e2-9429e71d5413",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-image",
                                                "uuid": "07f13f2d-375b-44cd-8afb-c6316870ece8",
                                                "locked": false,
                                                "descriptor": {
                                                    "image": {
                                                        "alt": "Logo",
                                                        "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                                                        "href": "https://sportwrench.com",
                                                        "prefix": "",
                                                        "target": "_self",
                                                        "percWidth": 11
                                                    },
                                                    "style": {
                                                        "width": "100%",
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "class": "center",
                                                        "width": "70.4px"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            }
                        ],
                        "title": "BF-basic-newsletter",
                        "template": {
                            "name": "template-base",
                            "type": "basic",
                            "version": "2.0.0"
                        },
                        "description": "BF-basic-newsletter"
                    },
                    "comments": {}
                }'        
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'upcoming_uncollected_fee_payments.notice');
    `);
};
