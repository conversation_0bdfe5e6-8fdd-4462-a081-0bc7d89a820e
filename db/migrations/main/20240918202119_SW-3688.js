/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        create table if not exists public.vertical_insurance_event
        (
            vertical_insurance_event_id integer generated always as identity,
            created                     TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified                    TIMESTA<PERSON> WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            event_type                  text  not null,
            policy_id                   text  not null,
            data                        jsonb not null
        );
        
        CREATE TRIGGER "update_vertical_insurance_event_modified"
            BEFORE UPDATE
            ON "public"."vertical_insurance_event"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        
        comment on column public.vertical_insurance_event.event_type is 'Vertical Insurance Event Type';
        
        comment on column public.vertical_insurance_event.policy_id is 'Vertical Insurance Policy ID';
        
        alter table public.vertical_insurance_event add constraint vertical_insurance_event_event_type_policy_id_unique
        unique (event_type, policy_id);
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        drop table if exists public.vertical_insurance_event;
    `)
};
