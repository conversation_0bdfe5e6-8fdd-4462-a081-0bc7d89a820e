exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "sales_hub_webhook_event" ---------------------------------------------------------------
        CREATE TABLE "public"."sales_hub_webhook_event"
            (
                "sales_hub_webhook_event_id" INT GENERATED ALWAYS AS IDENTITY,
                "id_at_sales_hub" TEXT UNIQUE,
                "type" TEXT NOT NULL,
                "data" JSONB NOT NULL,
                "created" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
                "modified" TIMESTAMP WITH TIME ZONE DEFAULT NULL

            );
            
        --- Add modified trigger to sales_hub_webhook_event table ---------------------------------------------------------
        CREATE TRIGGER "update_sales_hub_webhook_event_modified"
            BEFORE UPDATE
            ON "public"."sales_hub_webhook_event"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        ----------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."sales_hub_webhook_event";
    `);
};
