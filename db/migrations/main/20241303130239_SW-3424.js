
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TYPE "public"."checkin_action_types" AS ENUM('scan', 'reentry');

        CREATE TABLE IF NOT EXISTS "public"."online_checkin_api_history" (
            "online_checkin_api_history_id" INT GENERATED ALWAYS AS IDENTITY,
            "created"                       TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "modified"                      TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "event_id"                      INTEGER NOT NULL,
            "barcode"                       TEXT NOT NULL,
            "action_type"                   checkin_action_types,
            "scanner_name"                  TEXT,
            PRIMARY KEY("online_checkin_api_history_id")
        );
        
        CREATE INDEX IF NOT EXISTS "online_checkin_api_history_event_id_barcode_index" 
            ON "public"."online_checkin_api_history" ("event_id", "barcode");
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."online_checkin_api_history";
        DROP TYPE IF EXISTS "public"."checkin_action_types";
    `);
};
