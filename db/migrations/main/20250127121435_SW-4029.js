
exports.up = function(knex) {
    return knex.schema.raw(`
        INSERT INTO public.member_type_rules 
            (field_name, member_type, rule_data, is_active) 
        VALUES 
            ('membership_definition_id', 'athlete', '11efd48e-790b-7dcc-80a3-2213fd045229', true),
            ('membership_definition_id', 'athlete', '11efd48f-0789-8dc8-8a45-06e7c0fc8b64', true),
            ('membership_definition_id', 'staff', '11efcd86-931f-b75a-b832-f2a678bb4183', true);
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM "public"."member_type_rules" WHERE rule_data IN (
            '11efd48e-790b-7dcc-80a3-2213fd045229',
            '11efd48f-0789-8dc8-8a45-06e7c0fc8b64',
            '11efcd86-931f-b75a-b832-f2a678bb4183'
        );
    `)
};
