/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."user"
            ADD COLUMN IF NOT EXISTS "allow_login_as_cd" BOOLEAN NOT NULL DEFAULT FALSE;

        UPDATE "user"
        SET "allow_login_as_cd" = TRUE
        WHERE email IN (
                        '<EMAIL>'
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>'
            );
    `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."user" DROP COLUMN IF EXISTS "allow_login_as_cd";
    `);
};
