/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        alter table sportengine_adult_role
            rename column is_coach to is_impact;

        alter table sportengine_adult_role
            drop column if exists is_chaperone;
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        alter table sportengine_adult_role 
            rename column is_impact to is_coach;

        alter table sportengine_adult_role 
            add column if not exists is_chaperone boolean default false;
    `)
};
