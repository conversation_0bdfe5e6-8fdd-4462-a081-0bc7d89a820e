/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.raw(`
        INSERT INTO "public"."country" (code, name, calling_code) VALUES ('TW', 'Taiwan', 886);
    `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.raw(`
        DELETE FROM "public"."country" WHERE name = 'Taiwan';
    `);
};
