version: '3.7'
services:
  redis:
    image: redis:alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data
    networks:
      - techbridge
    env_file:
      - ./.env.development

  ua-scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 3000:3000
    depends_on:
      - redis
    volumes:
      - ./prisma:/app/prisma
      - ./src:/app/src
    networks:
      - techbridge
    env_file:
      - ./.env.development
volumes:
  redis_data:

networks:
  techbridge:
    name: techbridge
    driver: bridge


  