
class Service {
    constructor () {
        this.filtersData = {};
        this.selectionData = {};
    }

    get filters () {
        return this.filtersData;
    }

    get selection () {
        console.log(this.selectionData, 'get');
        return this.selectionData;
    }

    set filters (filters) {
        this.filtersData = angular.copy(filters);
    }

    set selection (selection) {
        this.selectionData = angular.copy(selection);
    }

    clearAll () {
        this.clearFilters();
        this.clearSelection();
    }

    clearFilters () {
        this.filtersData = {};
    }

    clearSelection () {
        this.selectionData = {};
    }

    getEventState (eventID) {
        return this.selectionData[eventID];
    }

    getTeamState (eventID, teamID) {
        return this.selectionData[eventID] && this.selectionData[eventID][teamID];
    }
}

angular.module('SportWrench').service('TeamsRegistrationStateService', Service);



