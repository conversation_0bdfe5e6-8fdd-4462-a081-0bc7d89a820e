

class Controller {
    constructor () {}

    $onInit () {
        this.eventFiltersOpen = true;
        this.teamsFiltersOpen = true;
    }

    onFilterChanged (filters) {
        this.filters = angular.extend(filters, this.filters);

        this.onUpdate({filters: this.filters});
    }

    eventFiltersCaret () {
        return this.eventFiltersOpen ? 'fa-caret-square-o-down' : 'fa-caret-square-o-right';
    }

    teamsFiltersCaret () {
        return this.teamsFiltersOpen ? 'fa-caret-square-o-down' : 'fa-caret-square-o-right';
    }

    toggleEventFilters () {
        this.eventFiltersOpen = !this.eventFiltersOpen;
    }

    toggleTeamsFilters () {
        this.teamsFiltersOpen = !this.teamsFiltersOpen;
    }
}


angular.module('SportWrench').component('clubBulkRegistrationFilters', {
    templateUrl: 'club/registration/filters/main/template.html',
    bindings: {
        filters: '<',
        onUpdate: '&',
        isHidden: '<'
    },
    controller: Controller
});
