angular.module('SportWrench')
    .controller('MinorTeamsController', MinorTeamsController);

function MinorTeamsController($scope, $rootScope, $stateParams, $uibModalInstance, teams, first, last) {

    if (!teams) $uibModalInstance.dismiss();

    const ROWS_PER_PAGE = 10;

    $scope.teams = [];

    // all but primary team
    teams.forEach(function(t) {
        if (!t.primary) $scope.teams.push(t);
    });

    if (!$scope.teams) $uibModalInstance.dismiss();

    init();

    function init() {
        // on load set current page to 1
        $scope.currentPage = 1;
        // total pages
        $scope.totalPages = Math.ceil($scope.teams.length / ROWS_PER_PAGE);
        // first page
        setShowPage($scope.currentPage);
        // title first and last names
        $scope.first_name = first;
        $scope.last_name = last;
    }

    function setShowPage(pageNumber) {
        $scope.showTeams = $scope.teams.slice((pageNumber - 1) * ROWS_PER_PAGE, pageNumber * ROWS_PER_PAGE);
    }

    $scope.shiftLeft = function () {
        if ($scope.currentPage > 1) {
            $scope.currentPage -= 1;
            setShowPage($scope.currentPage);
        }
    };

    $scope.shiftRight = function () {
        if ($scope.currentPage < $scope.totalPages) {
            $scope.currentPage += 1;
            setShowPage($scope.currentPage);
        }
    };

    $scope.close = function () {
        $uibModalInstance.dismiss();
    };

}
