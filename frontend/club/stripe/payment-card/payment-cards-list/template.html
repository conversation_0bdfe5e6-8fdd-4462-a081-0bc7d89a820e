<uib-alert type="info text-center" ng-if="!$ctrl.cards.length && !$ctrl.isLoading">No Cards found.</uib-alert>
<spinner active="$ctrl.isLoading"></spinner>
<ul class="list-group payment-cards-list" ng-if="$ctrl.cards.length && !$ctrl.isLoading">
    <li ng-class="{'list-group-item': true,  'list-group-item-success': card.is_default, 'pointer': !card.is_default }"
        ng-click="$ctrl.setDefault(card)"
        ng-repeat="card in $ctrl.cards track by $index">
        <div class="row">
            <div class="col-xs-2">
                <i class="fa fa-2x {{$ctrl.getCardBrandClass(card.card_brand)}}"></i>
            </div>
            <div class="col-xs-3 payment-cards-list-last4">**** {{$ctrl.getLast4(card)}}</div>
            <div class="col-xs-5 payment-cards-list-exp_month">
                <span class="pull-right">{{$ctrl.getLabel(card)}}</span>
            </div>
            <div class="col-xs-2 payment-cards-list-button" ng-click="$event.stopPropagation();">
                <a type="button"
                   class="glyphicon glyphicon-trash pointer pull-right"
                   ng-click="$ctrl.removePaymentMethod(card)"
                   ng-if="!card.disabledForRemove"
                ></a>
                <spinner size="1" active="card.disabledForRemove" class="pull-right"></spinner>
            </div>
        </div>
    </li>
</ul>
