angular.module('SportWrench')

.factory('lazyLoadService', function ($ocLazyLoad, _, $injector, $providerInjector, GMAPS_API_KEY) {
    return {
        xslxLoader: function () {
            return $ocLazyLoad.load({
                cache: true,
                files: [
                    'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.7.11/cpexcel.js',
                    'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.7.11/xlsx.core.min.js'
                ]
            });
        },
        fileUpload: function () {
            return $ocLazyLoad.load({
                name: 'angularFileUpload',
                files: [
                    'https://cdnjs.cloudflare.com/ajax/libs/angular-file-upload/2.3.4/angular-file-upload.min.js'
                ]
            });
        },
        loadCard: function () {
            return $ocLazyLoad.load('https://js.stripe.com/v3/');
        },
        loadReactEmailEditor: function () {
            return $ocLazyLoad.load('https://sw-files-dev.s3.us-east-1.amazonaws.com/react-email-module/static/<EMAIL>');
        },
        loadPlaid: function () {
            return $ocLazyLoad.load('https://cdn.plaid.com/link/v2/stable/link-initialize.js');
        },
        loadCKEditor: function () {
            return $ocLazyLoad.load({
                serie: true,
                cache: true,
                name: 'ngCkeditor',
                files: [
                    'https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.22.1/ckeditor.min.js',
                    'https://cdnjs.cloudflare.com/ajax/libs/ng-ckeditor/0.2.1/ng-ckeditor.min.js'
                ]
            });
        },
        loadGoogleMaps: function () {
            return $ocLazyLoad.load({
                name    : 'uiGmapgoogle-maps',
                files   : ['https://cdnjs.cloudflare.com/ajax/libs/angular-google-maps/2.3.4/angular-google-maps.min.js'],
                cache   : true
            })

            .then(function () {
                $providerInjector.get('uiGmapGoogleMapApiProvider').configure({
                    key         : GMAPS_API_KEY,
                    v           : '3.24',
                    libraries   : 'geometry'
                });
            });
        },

        loadBEEPlugin: function () {
            return $ocLazyLoad.load('https://app-rsrc.getbee.io/plugin/BeePlugin.js')
        },

        loadPaymentHub: function () {
            return $ocLazyLoad.load('https://cdn.jsdelivr.net/npm/payment-hub-sdk@1.1.6/dist/payment-hub-sdk.umd.min.js')
        },

        loadDragDrop: function() {
            return $ocLazyLoad.load([
                {
                    files: ['https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js'],
                    cache: true
                },
                {
                    name : 'ngDragDrop',
                    files: ['https://cdn.jsdelivr.net/npm/angular-dragdrop@1.0.13/src/angular-dragdrop.min.js'],
                    cache: true
                }
            ]);
        },

        loadMomentTimezone: function() {
            return $ocLazyLoad.load({
                files: ['https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.48/moment-timezone-with-data.js']
            })
        }
    };
});
