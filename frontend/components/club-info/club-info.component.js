angular.module('SportWrench').component('clubInfo', {
    templateUrl 	: 'components/club-info/club-info.html',
    bindings        : {
        club  : '<'
    },
    controller 		: ClubInfoController
});

function ClubInfoController() {
    let self = this;

    this.stateLabel = function () {
        let country = self.club && self.club.country;

        if (country === 'US') {
            return 'State'
        } else if (country === 'CA') {
            return 'Province'
        } else {
            return null;
        }
    }
}
