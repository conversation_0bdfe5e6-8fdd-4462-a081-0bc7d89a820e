
class Controller {

    constructor () {
        this.utils = {
            isOpen: false,
        }
    }

    onDateChanged () {
        if(!this.date) {
            this.value = null;
        } else {
            this.value = moment(this.date).format('MM/DD/YYYY');
        }
    }

    toggleOpen () {
        this.utils.isOpen = !this.utils.isOpen;
    }
}

angular.module('SportWrench').component('customFormFieldDate', {
    templateUrl: 'components/custom-form/fields/date/template.html',
    bindings: {
        value: '=',
        field: '<',
        fieldHasError: '&'
    },
    controller: Controller
});
