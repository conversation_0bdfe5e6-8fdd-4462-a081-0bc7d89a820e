angular.module('SportWrench').component('changeTeamPicker', {
	templateUrl : 'components/event-payments/items/team-picker/team-picker.html',
	bindings 	: {
		teamName 		: '<',
		paidAmount 		: '<',
		discount 		: '<',
		paymentType 	: '<',
		changePaidTeam 	: '&replaceTeam'
	},
	require: {
		paymentsCtrl: '^^paymentsDetails'
	},
	controller 	: [ChangeTeamPickerController]
});

function ChangeTeamPickerController () {

	this.teamsListOpened 	= false;
	this.teams 				= [];

	var isFirstLoad 		= true;

	var getTeams = function () {
		this.loading = true;

		this.paymentsCtrl.loadUnpaidTeams()
		.then(function (teams) {
			isFirstLoad 	= false;

			this.teams 		= teams;
			this.loading 	= false;
		}.bind(this))
	}.bind(this);

	this.loadTeamsList = function () {
		if (this.teamsListOpened) {
			getTeams();
		}
 	}

 	this.showTeamsList = function () {
 		return !this.loading && (this.teams.length > 0);
 	}

 	this.showErrorMsg = function () {
 		return !isFirstLoad && !this.loading && (this.teams.length === 0);
 	}

 	this.replaceTeam = function (rosterTeamID, teamName, discountDiff, regFee) {
 		this.loading = true;

 		this.changePaidTeam({ 
 			team_id 		: rosterTeamID, 
 			name 			: teamName, 
 			discount_diff 	: discountDiff, 
 			reg_fee 		: regFee 
 		})
 		.then(function() {
 			/* Close dropdown if it is opened */
 			if (this.teamsListOpened) {
 				this.teamsListOpened = !this.teamsListOpened
 			}
 		}.bind(this))
 		.finally(function  () {
 			this.loading = false; /* Actually, it is closed, so we can skip this operation */
 		}.bind(this))
 	}
}