angular.module('SportWrench').component('teamsPickerListItem', {
	templateUrl : 
			'components/event-payments/items/team-picker/teams-list/list-item/teams-list-item.html',
	bindings 	: {
		team 		: '<',
		amount 		: '<'
	},
	controller: [TeamsPickerListItemController]
});

function TeamsPickerListItemController () {

	this.teamNameCls = function () {
		return {
			'col-3-1 nowrap ellipsis' : this.team.not_available || (this.team.discount_diff > 0),
			'col-4' 				  : (this.team.discount_diff === 0)
		}
	}

}