angular.module('SportWrench').component('housingStatusFrom', {
    templateUrl: 'components/housing-status/housing-status-form.html',
    bindings: {
        team: '=',
        eventId: '<',
    },
    controller: ['HousingStatusFactory', 'teamHousingService', 'toastr', 'SAVED_MSG', '$rootScope', HousingStatusFromController ],
});

function HousingStatusFromController(HousingStatusFactory, teamHousingService, toastr, SAVED_MSG, $rootScope) {
    this.isUpdating = false;
    this.housingStatus = this.team.status_housing;
    this.notes = '';


    this.housingStatuses = HousingStatusFactory.getItemsForChange();

    this.hasChanges = () => {
        return this.housingStatus !== this.team.status_housing;
    };

    this.save = () => {
        this.isUpdating = true;
        teamHousingService.changeTeamStatus(this.eventId, this.team.roster_team_id, this.housingStatus, this.notes)
            .then(() => {
                this.team.status_housing = this.housingStatus;
                toastr.success(SAVED_MSG);
                this.notes = '';
                $rootScope.$broadcast('notes.updated');
                this.isUpdating = false;
            })
            .catch(err => {
                this.isUpdating = false;
            })
    };

    this.isFormVisible = () => {
        if(this.isUpdating) {
            return false;
        }
        if(this.isLocalClub()) {
            return false;
        }
        return true;
    };

    this.isLocalClub = () => {
        return this.team.is_local_club;
    };
}
