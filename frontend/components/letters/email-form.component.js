angular.module('SportWrench').component('emailForm', {
	templateUrl: 'components/letters/email-form.html',
	bindings: {
		onSubmit 	: '&',
		from 		: '<',
		showVars 	: '<'
	},
	controller: ['userService', 'toastr', function (userService, toastr) {
		var self = this;

		this.letter = {
			one_copy 	: true,
			from 		: this.from
		};

		this.sending = false;

		this.exampleEmail = '<EMAIL>';

		this.clear = function () {
			self.letter.letter_body = '';
		};

		this.submit = function () {
			if (self.letterForm.$invalid) {
				toastr.warning('Invalid Form Data');
				return;
			}

			self.sending = true;

			var letterCopy = angular.copy(self.letter);

			self.onSubmit({ letter: letterCopy })
			.then(function () {
				self.sending = true;
			}).catch(function () {
				self.letterForm.$setPristine();
				self.sending = false;
			});
		};

		this.getSubmitName = function () {
			return self.sending?'Sending ...':'Send';
		};

		this.disableBtns = function () {
			return self.sending;
		};

		this.$onInit = function () {
			userService.getUser(function (user) {
            	self.letter.bcc = user.email;
        	}); 
		};

		this.formControlClass = function (name) {
			return {
				'form-group' 	: true,
				'has-error' 	: (self.letterForm.$submitted && self.letterForm[name].$invalid)
			};
		};
	}]
});