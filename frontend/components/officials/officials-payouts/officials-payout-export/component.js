class Component {
    constructor($scope, $stateParams, $httpParamSerializerJQLike, $window, PAYMENT_OPTIONS, OFFICIALS_PAYOUT_MEMBER_TYPE) {
        this.$scope = $scope;
        this.eventID = $stateParams.event;
        this.$httpParamSerializerJQLike = $httpParamSerializerJQLike;
        this.$window = $window;
        this.PAYMENT_OPTIONS = PAYMENT_OPTIONS;
        this.OFFICIALS_PAYOUT_MEMBER_TYPE = OFFICIALS_PAYOUT_MEMBER_TYPE;

        this.$scope.modalTitle =
            `<div class="text-left"><strong>What type of export do you need?</strong></div>`;

        this.toolTipText = '';
    }

    $onInit () {
        this.exportTypes = [
            {
                label: `Export ${this.memberType} payouts table as spreadsheet`,
                query: {
                    type: 'officials',
                    data: { member_type: this.memberType },
                },
            },
            {
                label: `Export ${this.memberType} payouts for ArbiterPay Upload`,
                query: {
                    type: 'staffing',
                    data: {
                        payment_method: this.PAYMENT_OPTIONS.arbiterPay,
                        member_type: this.memberType
                    },
                },
                disabled: !this.allowedPaymentMethods[this.PAYMENT_OPTIONS.arbiterPay]
            }

        ];
        this.exportType = this.exportTypes[0];
        this.toolTipText = `You cannot select this file for export because ArbiterPay is not chosen as the
            ${this.isOfficialsType() ? 'Official' : 'Staff'} Payment Option in the Event Settings`;
    }

    get baseURL() {
        return '/api/event';
    }

    export () {
        const data = this.exportType.query.data;
        this.$window.open(`${this.baseURL}/${this.eventID}/${this.exportType.query.type}/payouts/export?${this.$httpParamSerializerJQLike({data})}`, '_blank');
        this.onClose();
    }

    isOfficialsType() {
        return this.memberType === this.OFFICIALS_PAYOUT_MEMBER_TYPE;
    }
}


angular.module('SportWrench').component('officialsPayoutExport', {
    templateUrl: 'components/officials/officials-payouts/officials-payout-export/template.html',
    bindings: {
        onClose: '&',
        allowedPaymentMethods: '<',
        memberType: '<',
    },
    controller: [
        '$scope',
        '$stateParams',
        '$httpParamSerializerJQLike',
        '$window',
        'PAYMENT_OPTIONS',
        'OFFICIALS_PAYOUT_MEMBER_TYPE',
        Component
    ],
})
