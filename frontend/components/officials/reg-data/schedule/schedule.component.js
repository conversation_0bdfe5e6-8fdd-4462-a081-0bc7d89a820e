angular.module('SportWrench').component('officialRegSchedule', {
	templateUrl: 'components/officials/reg-data/schedule/schedule.html',
	bindings: {
		dates: '<',
        scheduleAvailability: '<'
	},
    controller: [Component]
});

function Component() {
    this._avCache = {};

    this.getAvailability = function (dayIndex) {

        dayIndex = dayIndex + 1;

        return this._avCache[dayIndex] ||
            (this._avCache[dayIndex] = (this.scheduleAvailability['day_' + dayIndex] == 1)?'Yes':'No');
    };
}
