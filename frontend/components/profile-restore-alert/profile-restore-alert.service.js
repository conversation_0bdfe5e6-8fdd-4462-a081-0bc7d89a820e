class ProfileRestoreAlertService {
    constructor(userService) {
        this.storage = localStorage;
        this.userEmail = userService.getEmail();
    }

    saveUnsavedFieldsToLocalStorage(key, fields, modified) {
        localStorage.setItem(this.generateLocalStorageKey(key), JSON.stringify({
            fields,
            modified
        }))
    }

    removeUnsavedFieldsFromLocalStorage(key) {
        this.storage.removeItem(this.generateLocalStorageKey(key));
    }

    showAlert(key, modified) {
        const storage = JSON.parse(this.storage.getItem(this.generateLocalStorageKey(key)));

        if (!storage) {
            return false;
        }

        if (_.isEmpty(storage.fields)) {
            return false;
        }

        if (!modified && !storage.modified) {
            return true;
        }

        const updateModeRules = [
            modified,
            storage.modified,
            storage.modified >= modified
        ];

        return updateModeRules.every(rule => rule);
    }

    getUnsavedFieldsFromLocalStorage(key) {
        const storage = JSON.parse(this.storage.getItem(this.generateLocalStorageKey(key)));

        return storage && storage.fields || {};
    }

    joinUnsavedFieldsWithProfile(key, profile) {
        return Object.assign({}, profile, this.getUnsavedFieldsFromLocalStorage(key));
    }

    getUnsavedProfileFields(initProfile, currentProfile)  {
        const changedFields = {};

        if (_.isEqual(initProfile, currentProfile)) {
            return changedFields;
        }

        for (let key in currentProfile) {
            const currentField = currentProfile[key];
            const initField    = initProfile[key];

            if (_.isUndefined(currentField) || _.isNull(currentField)) {
                continue;
            }

            if (!_.isEqual(currentField, initField)) {
                changedFields[key] = currentField;
            }
        }

        return changedFields;
    }

    generateLocalStorageKey(key) {
        return `${key}-${this.userEmail}`;
    }
}

ProfileRestoreAlertService.$inject = ['userService'];

angular.module('SportWrench').service('ProfileRestoreAlertService', ProfileRestoreAlertService);
