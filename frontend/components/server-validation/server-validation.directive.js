angular.module('SportWrench')

.directive('serverValidation', function($rootScope) {
	return {
		restrict: 'A',
		link: function(scope, elem, attrs) {
			$rootScope.$on('serverValidationError', function(event, field, message) {
				var targetElem = elem.find("[name=" + field + "]");
				scope[attrs.name][field].$setValidity('required', false);
				targetElem.closest('.form-group').removeClass('has-success').addClass('has-error');
				var messageElem = '<span class="validation-invalid server-error">' + message + '</span>';
				if (targetElem.next().prop("tagName") === 'SPAN') {
					targetElem.next().append(messageElem);
				} else {
					targetElem.after(messageElem);
				}
			});
		}
	};
});