angular.module('SportWrench').component('tableFieldError', {
    templateUrl: 'components/table-field-error/table-field-error.html',
    bindings: {
        profileInfo: '<',
        role       : '<',
        field      : '<'
    },
    controller : TableFieldErrorController
});

TableFieldErrorController.$inject = ['SANCTIONING_CHECK_FIELDS_ALIASES', 'MBR_FIELD'];

function TableFieldErrorController(SANCTIONING_CHECK_FIELDS_ALIASES, MBR_FIELD) {
    const EXPIRED_FLAG  = 'expired';

    const FIELD_ALIASES = SANCTIONING_CHECK_FIELDS_ALIASES;

    this.getValue = function () {
        if(this.profileInfo[FIELD_ALIASES[this.field].manual_ok]) {
            return 'YES';
        }

        return this.profileInfo[FIELD_ALIASES[this.field].status];
    };

    this.showUSAVErrorNotification = function () {
        if(this.profileInfo[FIELD_ALIASES[this.field].manual_ok]) {
            return false;
        }

        return (this.showProfileCompletenessError()) ||
            this.profileInfo[FIELD_ALIASES[this.field].expiration] === EXPIRED_FLAG;
    };

    this.getUSAVErrorText = function () {
        if(this.showProfileCompletenessError()) {
            return `This ${this.role} has not updated their profile`
        }
    };

    this.getCaptionText = function () {
        if(this.profileInfo[FIELD_ALIASES[this.field].manual_ok]) {
            return;
        }

        if(this.profileInfo[FIELD_ALIASES[this.field].expiration]) {
            let text = this.profileInfo[FIELD_ALIASES[this.field].expiration] === EXPIRED_FLAG
                ? 'Expired' : 'Expires';

            return text + ' ' + this.profileInfo[FIELD_ALIASES[this.field].exp_date];
        }
    };

    this.showProfileCompletenessError = function () {
        return !this.profileInfo.profile_completed && this.field === MBR_FIELD;
    }
}
