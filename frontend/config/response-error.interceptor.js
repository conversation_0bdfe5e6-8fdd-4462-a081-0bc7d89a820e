angular.module('SportWrench').service('ResponseErrorInterceptor', [
	'$q', '$injector', 'INTERNAL_ERROR_MSG', 'APP_ROUTES', '$timeout', '$localStorage',
	'SERVER_UNAVAILABLE_MSG', '$location', '$rootScope', '$window', 'HOME_PAGE_URL',
	Interceptor
]);

function Interceptor ($q, $injector, INTERNAL_ERROR_MSG, APP_ROUTES, $timeout,
	$localStorage, SERVER_UNAVAILABLE_MSG, $location, $rootScope, $window, HOME_PAGE_URL
) {
    const redirectToHomePage = (response) => {
        
        const url = `${HOME_PAGE_URL}/login`;
        const w = $window.open(url, '_self');
        if (!w) {
            window.location = url;
        }
        
    };

	this.responseError =  function (resp) {
		var errors, toastr, msg, state, respStatus, respData, locationHeader;

       	if (resp) {
       		respStatus = resp.status;

       		respData = resp.data;

       	    toastr = $injector.get('toastr');
       	    state = $injector.get('$state');

       	    // This is used only by serverValidation directive which is used only once
       	    // TODO: remove the directive and this c
       	    if (respData && respData.invalidAttributes) {
                angular.forEach(respData.invalidAttributes, function (value, key) {
                    $rootScope.$emit('serverValidationError', key, value);
                });
            }

       	    if ((respStatus === 400) && respData) {

       	        if(respData.validationErrors) {
       	            errors = respData.validationErrors;

       	            errors.forEach(function (error) {
       	            	toastr.warning(error.message, 'Validation Failed');
       	            });

       	        } else if (respData.validation) {
       	            toastr.warning(respData.validation);
       	        }

       	    } else if (respStatus === 401) {
       	    	toastr.warning('Unauthorized');

       	    	redirectToHomePage(resp);

                $timeout(function () {
                    delete $localStorage.user;
                }, 10);
       	    } else if (respStatus === 403) {

       	    	locationHeader = resp.headers().location;

       	    	if (locationHeader) {
       	    		$timeout(function () {
       	    			$location.path(locationHeader);
       	    		});
       	    	} else {
       	    		toastr.warning(
	       	    		(respData && respData.validation) || 'You have no permission to access this page',
	                    'Access Denied'
	                );
       	    	}
       	    	
       	    } else if (respStatus === 404) {

       	    	toastr.warning('Resource not found', '404');

       	    } else if(respStatus === 500) {
       	        msg = (typeof respData === 'string')?respData:INTERNAL_ERROR_MSG;

       	        toastr.error(msg, 'Internal Error');
       	    } else if (respStatus === 502) {
       	    	toastr.info(SERVER_UNAVAILABLE_MSG, 'Server Error');
       	    }
       	}
        
        return $q.reject(resp);
	};

}
