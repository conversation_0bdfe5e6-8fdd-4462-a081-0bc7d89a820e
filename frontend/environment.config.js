/**
 * Environment Configuration for Frontend
 * 
 * This file defines AngularJS constants using environment variables
 * injected by WebPack's DefinePlugin. These replace the old env-config/index.js
 * files that were generated by Grunt.
 */

angular.module('SportWrench')

// Environment Indicator
.constant('ENV', process.env.ENV)

// URL Constants
.constant('HOME_PAGE_URL', process.env.HOME_PAGE_URL)
.constant('MAIN_APP_URL', process.env.MAIN_APP_URL)
.constant('ESW_URL', process.env.ESW_URL)
.constant('ESW_NEW_URL', process.env.ESW_NEW_URL)
.constant('SWT_URL', process.env.SWT_URL)
.constant('SCORES_APP_URL', process.env.SCORES_APP_URL)
.constant('SALES_HUB_URL', process.env.SALES_HUB_URL)

// Payment Hub Constants
.constant('PAYMENT_HUB_API_HOST', process.env.PAYMENT_HUB_API_HOST)
.constant('PAYMENT_HUB_PUBLISHABLE_KEY', process.env.PAYMENT_HUB_PUBLISHABLE_KEY);
