angular.module('SportWrench').component('customContactModal', {
    templateUrl : 'events/dashboard/aem/contact-lists/custom-contact-modal/custom-contact-modal.html',
    controller  : CustomContactModalController,
    bindings    : {
        contact : '<',
        close   : '&'
    }
});

CustomContactModalController.$inject = ['ContactListsService', '$stateParams', 'toastr'];

function CustomContactModalController(ContactListsService, $stateParams, toastr) {
    let eventID = $stateParams.event;
    let listID  = $stateParams.list;

    let self = this;

    if(!this.contact) {
        this.isUpdateMode   = false;
        this.modalTitle     = 'Create contact';

        this.contact = {
            first   : '',
            last    : '',
            email   : ''
        }
    } else {
        this.isUpdateMode   = true;
        this.modalTitle     = 'Update contact';
    }

    let saveMethod = this.isUpdateMode
        ? ContactListsService.updateRecipient.bind(ContactListsService, eventID, listID, this.contact.custom_recipient_id)
        : ContactListsService.addRecipient.bind(ContactListsService, eventID, listID);

    this.save = function () {
        return saveMethod(_.omit(this.contact, ['custom_recipient_id', 'custom_recipients_list_id']))
            .then(__onSuccess.bind(null, 'upsert'));
    };

    this.delete = function () {
        return ContactListsService.deleteRecipient(eventID, listID, self.contact.custom_recipient_id)
            .then(__onSuccess.bind(null, 'delete'));
    };

    function __onSuccess (action, contact) {
        //contact object returns only from "addRecipient" method
        if(!contact || _.isEmpty(contact)) {
            contact = self.contact;
        }

        toastr.success('Success');

        self.close({data: { contact, action }});
    }
}
