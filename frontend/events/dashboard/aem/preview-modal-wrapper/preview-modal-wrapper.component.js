angular.module('SportWrench').component('previewModalWrapper', {
    templateUrl: 'events/dashboard/aem/preview-modal-wrapper/preview-modal-wrapper.html',
    bindings: {
        link        : '<',
        data        : '<',
        templateId  : '<',
        group       : '<',
    },
    controller: [Component]
});

function Component() {
    this.INDEX_TABS = {
        SEND: 'send',
        PREVIEW: 'preview',
        STATISTIC: 'statistic',
    };

    this.NEWS_LETTERS_GROUP = 'newsletters';

    // default tab
    this.currentTab = this.INDEX_TABS.PREVIEW;

    if(_.isFunction(this.data)) {
        this.data().then(responseData => {
            this.emailPreviewData = responseData;
        })
    } else if(_.isObject(this.data)) {
        this.emailPreviewData = this.data;
    }

    this.onTabSelect = (indexTab) => {
        this.currentTab = indexTab;
    };

    this.showPreviewTab = () => {
        return this.currentTab === this.INDEX_TABS.PREVIEW
    };

    this.showSendTab = () => {
        return (this.currentTab === this.INDEX_TABS.SEND);
    };

    this.showEmailPreviewInfo = () => {
        return this.data;
    };
}
