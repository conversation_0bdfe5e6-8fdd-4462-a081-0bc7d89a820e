angular.module('SportWrench').component('aemTemplatePreview', {
	templateUrl 	: 'events/dashboard/aem/preview/preview.html',
	bindings 		: {
		link 	 		: '<'
	},
	controller 		: ['$element', '$timeout', AEMTemplatePreviewController]
});

function setIFrameHeight ($element, $timeout) {
	return $timeout(function () {
		let iFrameElem = $element.find('iframe');
		let marginBottom = 20,
            maxHeightForShortText = 150
		iFrameElem.load(function () {
			var iFrameContentHeight = this.contentWindow.document.body.offsetHeight > maxHeightForShortText
                ? this.contentWindow.document.body.offsetHeight + marginBottom
                : this.contentWindow.document.body.offsetHeight ;

			iFrameElem.css('height', iFrameContentHeight + 'px')
		});
	})
}
function setIFrameBordersForText () {
    let iframe = document.getElementsByTagName('iframe')[0];
    iframe.onload = () => {
        let iframeDoc = iframe.contentWindow.document;
        iframeDoc.body.innerHTML = iframeDoc.body.innerHTML + '<style>p{word-break: break-all;}</style>';
    }
}


function AEMTemplatePreviewController ($element, $timeout) {
	
	setIFrameHeight($element, $timeout);
    setIFrameBordersForText();

}
