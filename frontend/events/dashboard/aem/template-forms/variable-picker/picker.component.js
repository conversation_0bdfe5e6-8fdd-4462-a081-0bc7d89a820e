angular.module('SportWrench').component('aemVariablePicker', {
	templateUrl 	: 'events/dashboard/aem/template-forms/variable-picker/picker.html',
	bindings 			: {
		tmplGroup 		: '<',
		dropdownWidth 	: '<',
		onVarPicked 	: '&',
		variables 		: '<?'
	},
	controllerAs 	: '$ctrl',
	controller 		: ['AEMService', '$timeout', '$element', AEMVariablePickerController]
})

function AEMVariablePickerController (AEMService, $timeout, $element) {
	let isFirstLoadAttempt = true;

	this.templateSubjectVariables = angular.isDefined(this.variables) ? angular.copy(this.variables) : [];

	this.loading = false;

	this.isDrowDownOpened = false;

	this.showList = function () {
		return !this.loading && (this.templateSubjectVariables.length > 0)
	}

	this.showMsg = function () {
		return !isFirstLoadAttempt && !this.loading && (this.templateSubjectVariables.length === 0);
	}

	this.loadVariables = function () {
		if (this.templateSubjectVariables.length > 0 || !this.isDrowDownOpened) {
			return;
		}

		this.loading = true;

		AEMService.getTemplateGroupVariables(this.tmplGroup)
		.then((variables) => {
			this.templateSubjectVariables = AEMService.filterSubjectVariables(variables);
			this.variables = variables;
		})
		.finally(() => {
			this.loading = false;
			isFirstLoadAttempt = false;
		})
	}

	this.onItemClick = function (pattern) {
		this.onVarPicked({ pattern: pattern });
	}

	this.$onChanges = function (changes) {

        let widthValue = changes.dropdownWidth && changes.dropdownWidth.currentValue;

		if (widthValue) {
			angular.element($element.find('.dropdown-menu')[0]).css('width', widthValue + 'px');
		}
	}
}
