angular.module('SportWrench').directive('aemTmplsListItem', function () {
    return {
        templateUrl         : 'events/dashboard/aem/templates/templates-list/list-item/tmpls-list-item.html',
        bindToController    : {
            template: '<'
        },
        replace             : true,
        require             : {
            ListCtrl: '^aemTmplsList'
        },
        controllerAs        : '$ctrl',
        controller          : ['UtilsService', 'AEMService', 'toastr', '$uibModal',  AEMTmplsListItemController]
    }
});

function AEMTmplsListItemController (UtilsService, AEMService, toastr, $uibModal) {
    this.$onInit = function () {
        this.isAdmin = (this.ListCtrl.userRole === AEMService.ADMIN_USER_ROLE);
    }


    this.rowClass = function () {

        let tmpl = this.template;

        return {
            'list-group-item'   : true,
            'disabled'          : !tmpl.published,
            'pointer'           : tmpl.is_valid,
            'clearfix'          : true,
            'xs-aem-btn'        : true
        }
    }

    this.previewTmpl = function () {
        if (!this.template.is_valid) {
            return;
        }

        this.ListCtrl.onPreview({ item: this.template });
    }


    this.tmplTypeClass = function () {
        let type = this.template.type_id;

        return {
            'label'                     : true,
            'label-info'                : (type !== AEMService.BASIC_LAYOUT_TYPE),
            'label-default badge-dark'  : (type === AEMService.BASIC_LAYOUT_TYPE)
        }
    } 


    this.getTriggerAssignmentCls = function () {
        let isInUse = this.template.is_in_use;

        return UtilsService.isLiveIconClass(isInUse);
    }


    this.isTrigger = function () {
        return Boolean(this.template.is_trigger);
    }

    this.isDupInProgress = function () {
        return this.ListCtrl.isDupInProgress(this.template.id);
    }

    this.duplicate = function () {
        if (this.isAdmin) {
            return this.ListCtrl.duplicate(this.template);
        }

        this.openDuplicateModal();
    }

    this.assign = function () {
        return this.ListCtrl.onTmplAssign({ item: this.template });
    }

    this.edit = function () {
        return this.ListCtrl.onTmplEdit({ item: this.template })
    }

    this.showEditBtn = function () {
        return !this.template.is_default || this.isAdmin;
    }

    this.showRemoveBtn = function () {
        return (
            this.isAdmin || 
            (this.template.type_id === AEMService.BASIC_LAYOUT_TYPE)
        )
    }

    this.showAssignBtn = function () {
        /**
         * Admin can not assign any template, this is for EO only:
         *     Admin can not specify an event to assign a template to it.
         */
        return (!this.isAdmin && this.template.is_trigger && !this.template.is_in_use);
    }

    this.showTogglePublishBtn = function () {
        return (this.isAdmin && this.template.is_valid);
    }

    this.togglePublishBtnText = function () {
        return (this.template.published ? 'Un-publish' : 'Publish');
    }

    this.onTogglePublishClick = function () {
        return this.ListCtrl.toggleTmplPublish(this.template)
        .then(() => {
            this.template.published = !this.template.published;

            toastr.success(`Template ${this.template.published ? 'published' : 'un-published'}`);
        });
    }

    this.togglePublishIconCls = function () {
        return { 
            fa              : true, 
            'fa-times'      : this.template.published, 
            'fa-check'      : !this.template.published 
        }
    }

    /* NOTE: confirmation components drops "this" reference */
    this.removeTemplate = function (confirmResp) {
        if (!confirmResp) {
            return;
        }

        return this.ListCtrl.removeTemplate(this.template)
    }.bind(this);

    this.openDuplicateModal = () => {
        $uibModal.open({
            template: `<duplicate-email-template
                template="template"
                on-edit="onEdit(item)"
                on-load-data="onLoadData()"
                on-close="close()"
                >
                </duplicate-email-template>`,
            controller: ['$scope', ($scope) => {
                $scope.template = this.template;

                $scope.onEdit = (item) => {
                    this.ListCtrl.onTmplEdit({ item })
                };

                $scope.onLoadData = () => {
                    this.ListCtrl.onLoadData();
                };

                $scope.close = () => {
                    $scope.$dismiss();
                };
            }]
        })
    }
}
