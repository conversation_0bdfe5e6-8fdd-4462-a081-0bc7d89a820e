angular.module('SportWrench').factory('ClubInvoiceFiltersFactory', function () {
    return {
        getTypes: function () {
            return [
                {
                    id: 'card',
                    name: 'Card',
                    class: 'fa fa-credit-card'
                }, {
                    id: 'check',
                    name: 'Check',
                    class: 'fa fa-pencil-square-o'
                }, {
                    id: 'ach',
                    name: 'ACH',
                    class: 'fa fa-university'
                }
            ]
        },
        getStatuses: function () {
            return [
                {
                    id: 'paid',
                    name: 'Paid',
                    class: 'glyphicon glyphicon-ok-sign green'
                }, {
                    id: 'refunded',
                    name: 'Refunded',
                    class: 'glyphicon glyphicon-repeat red'
                }, {
                    id: 'pending',
                    name: 'Pending',
                    class: 'glyphicon glyphicon-minus-sign blue'
                }, {
                    id: 'canceled',
                    name: 'Canceled',
                    class: 'glyphicon glyphicon-remove-circle red'
                }, {
                    id: 'disputed',
                    name: 'Disputed',
                    class: 'glyphicon glyphicon-minus-sign red'
                }, {
                    id: 'not_paid',
                    name: 'Not Pa<PERSON>',
                    class: 'glyphicon glyphicon-minus-sign red'
                }
            ]
        }
    }
});
