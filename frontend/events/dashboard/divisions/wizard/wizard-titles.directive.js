angular.module('SportWrench').directive('wizardTitles', function () {
    return {
        restrict: 'E',
        require: '^divisionsWirard',
        scope: {
            titles: '=',
            selection: '='
        },
        templateUrl: 'events/dashboard/divisions/wizard/wizard-titles.html',
        link: function (scope, elem, attrs, ctrl) {
            scope.utils = {}

            scope.dataChanged = function () {
                for(var i = 0, l = scope.titles.length, q = 0; i < l; ++i) {
                    if(scope.selection[scope.titles[i].id]) q++
                }
                ctrl.setTitlesCount(q)
            }
        }
    }
});
