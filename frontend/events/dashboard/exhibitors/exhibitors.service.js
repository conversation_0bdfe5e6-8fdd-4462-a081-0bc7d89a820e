class ExhibitorsService {
    constructor($http, $uibModal, PAYMENT_STATUS, PAYMENT_TYPE, UtilsService, OTHER_BOOTH_LABEL) {
        this.$http = $http;
        this.$uibModal = $uibModal;
        this.PAYMENT_STATUS = PAYMENT_STATUS;
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.OTHER_BOOTH_LABEL = OTHER_BOOTH_LABEL;
        this.UtilsService = UtilsService;
    }

    get baseURL() {
        return '/api/event';
    }

    // TODO: create common method 'request' for get/post/put
    get(url) {
        return this.$http.get(url)
            .then(({ data }) => data)
            .catch(({ data }) => Promise.reject(data));

    }

    post(url, data) {
        return this.$http.post(url, data)
            .then(({ data }) => data)
            .catch(({ data }) => Promise.reject(data));
    }

    getExhibitors(eventID) {
        return this.get(`${this.baseURL}/${eventID}/exhibitors`);
    }

    getExhibitorProfileInfo({ eventID, exhibitorID }) {
        return this.get(`${this.baseURL}/${eventID}/exhibitor/${exhibitorID}/info`);
    }

    getExhibitorRegistrationInfo(eventId, exhibitorId) {
        return this.get(`${this.baseURL}/${eventId}/exhibitor/${exhibitorId}`);
    }

    changePaymentType(type, data, eventID) {
        return this.post(`/api/event/${eventID}/sales/booths/pay/${type}/change`, { payment: data });
    }

    updateExhibitorRegistrationInfo(eventID, exhibitorID, data) {
        return this.$http.put(`${this.baseURL}/${eventID}/exhibitor/${exhibitorID}`, data);
    }

    createExhibitorRegistrationInfo(eventID, exhibitorID, data) {
        return this.$http.post(`${this.baseURL}/${eventID}/exhibitor/${exhibitorID}`, data);
    }

    makeRefund(purchaseID, data, eventID) {
        return this.$http.post(`/api/event/${eventID}/sales/payment/${purchaseID}/refund`, data)
    }

    makeVoid(purchaseID, data, eventID) {
        return this.$http.post(`/api/event/${eventID}/sales/payment/${purchaseID}/void`, data)
    }

    makeReceive(purchaseID, data, eventID) {
        return this.$http.post(`/api/event/${eventID}/sales/payment/${purchaseID}/receive`, data)
    }

    openExhibitorInfoModal({ eventID, companyName, exhibitorID, onSave, isApplyMode }) {
        return this.$uibModal.open({
            size: 'md',
            template: `
                <exhibitor-apply-form
                    event-id="eventID"
                    company-name="companyName"
                    on-close="$close(withReload)"
                    exhibitor-id="exhibitorID"
                    is-apply-mode="isApplyMode"
                    on-save-exhibitor="onSave(eventID, exhibitorID, data)"
                >
                </exhibitor-apply-form>
            `,
            controller: ['$scope', function($scope) {
                $scope.eventID = eventID;
                $scope.companyName = companyName;
                $scope.exhibitorID = exhibitorID;
                $scope.isApplyMode = isApplyMode;
                $scope.onSave = onSave;
            }]
        }).result
    }

    getUserExhibitors(eventID) {
        return this.get(`${this.baseURL}/${eventID}/exhibitors/list`);
    }

    createExhibitor(eventID, data) {
        return this.$http.post(`${this.baseURL}/${eventID}/exhibitor`, data)
    }

    openCreateExhibitorModal(onSearch) {
        return this.$uibModal.open({
            size: 'md',
            template: `
                <create-exhibitor on-search="onSearch(search)" on-close="$close(withReload)"></create-exhibitor>
            `,
            controller: ['$scope', function($scope) {
                $scope.onSearch = onSearch;
            }]
        }).result
    }

    isRegenerateInvoiceMode(exhibitorPaymentData, initExhibitorPaymentData) {
        const areEventBoothsChanged = !_.isEqual(
            _.sortBy(initExhibitorPaymentData.selectedBooths, 'id'),
            _.sortBy(exhibitorPaymentData.selectedBooths, 'id')
        );

        const areOtherBoothsChanged = !_.isEqual(
            _.sortBy(initExhibitorPaymentData.otherBooths, 'title', 'fee'),
            _.sortBy(exhibitorPaymentData.otherBooths, 'title', 'fee')
        );

        return [
            areEventBoothsChanged || areOtherBoothsChanged,
            exhibitorPaymentData.purchase_status === this.PAYMENT_STATUS.PENDING,
            exhibitorPaymentData.purchase_type === this.PAYMENT_TYPE.PENDING_PAYMENT,
        ].every(rule => rule);
    }
    
    getBoothID({ title, id }) {
        return title === this.OTHER_BOOTH_LABEL ? null : id;
    }

    generateBoothsForRequest(registrationData) {
        const { selectedBooths = [], otherBooths = [] } = registrationData;

        const formattedBooths = [].concat(otherBooths); // add other booths

        /* add event booths */
        const groupedSelectedBooths = _.groupBy(selectedBooths, (booth) => booth.id);
        const eventBoothsIds = Object.keys(groupedSelectedBooths);

        eventBoothsIds.forEach(id => {
            const booths = groupedSelectedBooths[id];
            const booth = booths[0];

            formattedBooths.push({
                quantity: booths.length,
                event_booth_id: this.getBoothID(booth),
                amount: this.UtilsService.approxNumber(booths.length * booth.amount),
                fee: this.UtilsService.approxNumber(booth.fee || booth.amount),
                title: booth.title,
                description: booth.description,
                // event booths fields must match the other booths fields
                booth_label: '',
                notes: '',
            })
        });

        return formattedBooths;
    }

    getBoothsTotal(registrationData) {
        const { selectedBooths = [], otherBooths = [] } = registrationData;

        let total = 0;

        if (!_.isEmpty(selectedBooths)) {
            total += this.UtilsService.sumByField(selectedBooths, 'amount');
        }

        if (!_.isEmpty(otherBooths)) {
            total += this.UtilsService.sumByField(otherBooths, 'amount');
        }

        return this.UtilsService.approxNumber(total);
    }

    hasOtherBoothsWithoutFee(booths) {
        if (_.isEmpty(booths)) {
            return false;
        }

        return booths.some(booth => !booth.fee || booth.fee <= 0);
    }

    hasOtherBoothsWithoutDescription(booths) {
        if (_.isEmpty(booths)) {
            return false;
        }

        return booths.some(booth => !booth.description || booth.description.trim() === '');
    }

    isEventDatesSelected(eventDates) {
        return !_.isEmpty(eventDates) && Object.values(eventDates).includes(true);
    }

    isPurchaseDataRequired(registrationData) {
        if(_.isEmpty(registrationData)) {
            return false;
        }

        const rules = [
            this.isEventDatesSelected(registrationData.event_dates),
            !_.isEmpty(registrationData.selectedBooths),
            !_.isEmpty(registrationData.otherBooths),
            !_.isEmpty(registrationData.comment)
        ]

        return rules.some(rule => rule);
    }

    validateExhibitorPaymentData(exhibitorPaymentData) {
        if (!this.isEventDatesSelected(exhibitorPaymentData.event_dates)) {
            throw {validation: 'At least one event date must be selected'};
        }

        if( this.hasOtherBoothsWithoutDescription(exhibitorPaymentData.otherBooths)) {
            throw {validation: 'All other booths must have a description'};
        }

        if (this.hasOtherBoothsWithoutFee(exhibitorPaymentData.otherBooths)) {
            throw {validation: 'All other booths must have a fee'};
        } else if (this.getBoothsTotal(exhibitorPaymentData) <= 0) {
            throw {validation: 'At least one booth must be added'};
        }
    }
}

ExhibitorsService.$inject = ['$http', '$uibModal', 'PAYMENT_STATUS', 'PAYMENT_TYPE', 'UtilsService', 'OTHER_BOOTH_LABEL'];

angular.module('SportWrench').service('ExhibitorsService', ExhibitorsService);
