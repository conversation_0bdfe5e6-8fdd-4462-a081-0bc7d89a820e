class ExhibitorPaymentsListComponent {
    constructor(ExhibitorsService, ExhibitorsPaymentsService, $stateParams, EXHIBITOR_LIST_DATES_FORMAT) {
        this.ExhibitorsService = ExhibitorsService;
        this.ExhibitorsPaymentsService = ExhibitorsPaymentsService;
        this.$stateParams = $stateParams;
        this.eventId = $stateParams.event;
        this.EXHIBITOR_LIST_DATES_FORMAT = EXHIBITOR_LIST_DATES_FORMAT;
    }

    $onChanges({payments}) {
        if (_.isEmpty(payments.previousValue) && !_.isEmpty(payments.currentValue)) {
            this._openExhibitorModalInstantly();
        }
    }

    openUpdateExhibitorPaymentModal(payment) {
        this.ExhibitorsPaymentsService.openExhibitorPaymentModal({
            eventId: this.eventId,
            exhibitorId: payment.exhibitor_id,
            eventExhibitorInvoiceId: payment.event_exhibitor_invoice_id,
            companyName: payment.company_name,
        }).finally(() => this.onReloadTable());
    }

    _openExhibitorModalInstantly () {
        let stateParamsPurchaseID =
            this.$stateParams.openPaymentModal && this.$stateParams.openPaymentModal.purchase_id;

        if(!_.isEmpty(this.payments) && stateParamsPurchaseID) {
            let payment = this.payments.filter(payment =>
                payment.purchase_id === this.$stateParams.openPaymentModal.purchase_id
            )[0];

            if(payment) {
                this.openUpdateExhibitorPaymentModal(payment);
            }
        }
    }
}

angular.module('SportWrench').component('exhibitorPaymentsList', {
    templateUrl: 'events/dashboard/exhibitors/payments/exhibitor-payments-list/exhibitor-payments-list.html',
    bindings: {
        payments: '<',
        onReloadTable: '&',
    },
    controller: [
        'ExhibitorsService', 'ExhibitorsPaymentsService', '$stateParams', 'EXHIBITOR_LIST_DATES_FORMAT',
        ExhibitorPaymentsListComponent
    ],
});
