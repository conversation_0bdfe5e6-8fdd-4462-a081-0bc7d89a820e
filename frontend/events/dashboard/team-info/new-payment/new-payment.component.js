angular.module('SportWrench').component('eoPayment', {
	templateUrl 	: 'events/dashboard/team-info/new-payment/new-payment.html',
	bindings 		: {
		rosterClubID 	: '<rosterClub',
		eventID 		: '<event',
		onSaved 		: '&?'
	},
	controller 		: ['rosterTeamService', 'eventDashboardService', 'purchaseService' ,EoPaymentController]
})

function EoPaymentController (rosterTeamService, eventDashboardService, purchaseService) {
	var eventInfo = eventDashboardService.getEvent();
    var checkData = null;

	this.teams = rosterTeamService.getUnpaidTeams(this.eventID, { roster_club: this.rosterClubID });

    this.event = {
		payment_name 			: eventInfo.payment_name,
		payment_city 			: eventInfo.payment_city,
		payment_address 		: eventInfo.payment_address,
		payment_state 			: eventInfo.payment_state,
		payment_zip 			: eventInfo.payment_zip,
		reg_fee 				: eventInfo.reg_fee,
		card_surcharge 			: 0, /* We do not support card and ach here */
		ach_surcharge 			: 0, /* We do not support card and ach here */
		check 					: eventInfo.teams_checks_allowed,
	}

	this.disableCheckboxes = false;
	this.enableDiscountInputs = true;
	this.check = {};

	this.createPayment = function (payment) {
		return purchaseService.savePurchase(
			this.eventID,
			angular.extend({ roster_club_id: this.rosterClubID }, checkData, payment)
		).then(function (resp) {
			if (angular.isFunction(this.onSaved)) {
				this.onSaved({teams: payment.receipt});
			}

			return resp.data && resp.data.id;
		}.bind(this))
	}

	this.saveCheckData = function (check) {
		checkData = check;
	}
}
