class ChangePaymentStatusFormComponent {
    constructor(toastr, TEAM_STATUS) {
        this.$toastr = toastr;

        this.data = {};
        
        this.STATUSES = [
            { id: TEAM_STATUS.PAYMENT.PAID, label: 'Paid' },
            { id: TEAM_STATUS.PAYMENT.NONE, label: 'Not Paid' },
        ];
    }

    submit() {
        if (this.statusForm.$invalid) {
            this.$toastr.warning('Invalid Form Data');
            
            return;
        }

        this.onSubmit({ data: this.data }).catch(function () {
            this.statusForm.$setPristine();
        });
    };
}

angular.module('SportWrench').component('changePaymentStatusForm', {
    templateUrl: 'events/dashboard/teams/actions/change-payment-status-form/change-payment-status-form.html',
    bindings: {
        onSubmit: '&',
        disableSave: '<'
    },
    controller: ['toastr', 'TEAM_STATUS', ChangePaymentStatusFormComponent],
})