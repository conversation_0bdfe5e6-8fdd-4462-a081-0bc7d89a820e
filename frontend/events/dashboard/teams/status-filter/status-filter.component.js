angular.module('SportWrench').component('teamStatusPicker', {
    templateUrl     : 'events/dashboard/teams/status-filter/status-filter.html',
    bindings        : {
        items                   : '<',
        onSelect                : '&',
        lb                      : '@',
        pickerName              : '@',
        iconClass               : '@icon',
        size                    : '@',
        groupHeaderItemsLimit   : '@groupLimit',
        registerOnClearHandler  : '&?regOnClear',
        initValues              : '<?init'
    },
    transclude: {
        info: '?div'
    },
    controller: ['_', '$timeout', '$element', '$window', function (_, $timeout, $element, $window) {
        var self = this;
        var selectionTimeout;
        var pickItemDelay = 500;
        var mediaQueryList;
        var infoSlotElem = angular.element($element).find('[ng-transclude="info"]');

        this.isOpen = false;

        this.pickAllItem = {
            id      : null,
            name    : 'Clear All'
        };

        this.opt = {};
        this.pickedItemsQty = 0;

        this.hiddenItemsQty = false;

        this.hasInfo = function () {
            /* 
            * For now $transclude.isSlotFilled() can detect slot presence only during compilation time,
            * so it does not support dyncamic slot's content. That's why we need to check the quantity of slot's child
            * elements
            */
            return (infoSlotElem.children().length > 0);
        };

        this.getPickerTitle = function () {
            return self.pickerName;
        };

        this.dropPick = function () {
            self.clear();

            self.onSelect({ selection: [] });
        };

        this.pick = function () {
            var pickedItems = getPicked(self.opt);

            self.pickedItemsQty = pickedItems.length;

            cancelTimer();

            selectionTimeout = $timeout(function () {
                self.onSelect({ selection: pickedItems });
            }, pickItemDelay);
        };

        this.getItemClass = function (item) {
            if(!item.id) {
                return 'font-bold';
            } else {
                return (self.opt[item.id])?'bg-info':item.item_class;
            }
        };

        this.getHeaderItemTitle = function (item) {
            return !!item.short
                ?self.getItemName(item.short)
                :self.getItemName(item.name).substring(0, 1);
        };

        this.getItemName = function (name) {
            return _.isFunction(name)?name():name;
        };

        this.getPickerClass = function () {
            return {
                'form-group multiselect-filter form-group--custom': true,
                'large'         : (self.size === 'large'),
                'middle'        : (self.size == 'middle'),
                'middle-large'  : (self.size == 'middleLarge'),
                'increase-mb'   : self.hasInfo()
            };
        };

        this.getHeaderItems = function () {
            var limit = +self.groupHeaderItemsLimit;


            var limitedItems = _.filter(self.items, function(item){
                return self.opt[item.id];
            });

            if(limit) {

                limitedItems = limitedItems.slice(0, limit);

                self.hiddenItemsQty = (self.pickedItemsQty - limitedItems.length);

                return limitedItems;
            } else {

                self.hiddenItemsQty = 0;

                return limitedItems;
            }
        };

        this.clear = function () {
            var opts = self.opt;

            Object.keys(self.opt).forEach(function (key) {
                opts[key] = false;
            });

            self.isOpen = false;

            self.pickedItemsQty = 0;
        };

        this.$onInit = function () {
            /*
            * Notes:
            * The handler registration logic can be implemened by calling
            * parent component controller's action.
            * But for now we do not have parent component, so the "handler registrator"
            * have to be passed to the component.
            */
            if(self.registerOnClearHandler) {
                self.registerOnClearHandler({ handler: self.clear });
            }

            /* In case we want to initially pick some items */
            if(self.initValues && self.initValues.length) {
                self.initValues.forEach(function (itemID) {
                    self.opt[itemID] = true;
                });

                self.pickedItemsQty = self.initValues.length;
            }
        };

        this.$postLink = function () {
            mediaQueryList = $window.matchMedia('(max-width: 767px)');
            mediaQueryList.addListener(mqListener);
            mqListener(mediaQueryList);
        };

        this.$onDestroy = function () {
            console.log('onDestroy');
            cancelTimer();
            mediaQueryList.removeListener(mqListener);
        };

        function getPicked (selection) {
            return _.filter(Object.keys(selection), function (itemID) {
                return !!selection[itemID];
            });
        }

        function mqListener (mql) {
            if(mql.matches) {
                $element.off('mouseenter', mouseenterListener);
                $element.off('mouseleave', mouseleaveListener);
            } else {
                $element.on('mouseenter', mouseenterListener);
                $element.on('mouseleave', mouseleaveListener);
            }
        }

        function mouseenterListener () {
            $timeout(function () {
                self.isOpen = true;
            });
        }

        function mouseleaveListener () {
            $timeout(function () {
                self.isOpen = false;
            });
        }

        function cancelTimer () {
            if(selectionTimeout) {
                $timeout.cancel(selectionTimeout);
            }
        }
    }]
});
