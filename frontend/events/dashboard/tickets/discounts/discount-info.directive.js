angular.module('SportWrench').directive('discountInfo', function (
	ticketsService, toastr, $state, APP_ROUTES, DONE_MSG
) {
	return  {
		restrict: 'E',
		scope: {
			discountId 		: '@discount',
			eventId 		: '@event'
		},
		templateUrl: 'events/dashboard/tickets/discounts/discount-info.html',
		link: function (scope) {
			scope.utils = {
				loading: false,
				isUpdated: false
			};

			scope.discountInfo = {};

			scope.sendFormData = function () {
				return ticketsService.saveDiscountInfo(
					scope.eventId, scope.discountId, 
					_.omit(scope.discountInfo, 'payments', 'notified_at', 'amount', 'is_free')
				).then(function () {
					toastr.success('Successfully updated');
					
				})
			}

			scope.removeDiscount = function () {
				return ticketsService.removeDiscount(scope.eventId, scope.discountId)
				.then(function () {
					toastr.success(DONE_MSG)
					scope.$parent.$close(true);
				})
			}

			scope.showList = function () {
				return ((scope.discountInfo.payments && (scope.discountInfo.payments.length > 0)) 
																						&& (!scope.utils.loading))
			}

			scope.showNoRowsMsg = function () {
				return !scope.utils.loading && !(scope.discountInfo.payments && scope.discountInfo.payments.length)
			}

			scope.openPaymentInfo = function (barcode) {
				$state.go(APP_ROUTES.EO.TICKETS_PAYMENTS, { barcode: barcode })
			}

			function __loadDiscountInfo () {
				scope.utils.loading = true;
				ticketsService.loadDiscountInfo(scope.eventId, scope.discountId)
				.success(function (data) {
					scope.utils.code = data.discount.code;
					scope.discountInfo = data.discount;
				})
				.finally(function () {
					scope.utils.loading = false;
				})
			}

			__loadDiscountInfo();
		}
	}
})