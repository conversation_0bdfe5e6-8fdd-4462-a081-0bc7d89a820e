angular.module('SportWrench').directive('resultAlert', function () {
    return {
        restrict: 'E',
        scope: {
            type: '=',
            msg: '=',
            dismissTime: '='
        },
        template:
            '<uib-alert type="{{type || \'success\'}}" ng-if="msg" dismiss-on-timeout="{{dismissTime || 20000}}" close="close()">{{msg}}</uib-alert>',
        link: function (scope) {
            scope.close = function () {
                scope.msg = undefined;
            }
        }
    }
});
