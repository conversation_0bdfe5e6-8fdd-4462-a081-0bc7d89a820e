angular.module('SportWrench').controller('Event.Tabs.TicketsSettingsController', TicketsController);


function TicketsController (
    $scope, toastr, ticketsService, $stateParams, INTERNAL_ERROR_MSG, $filter, $state,
    $sce, DateService, $location, UtilsService, $timeout, $q, eventDashboardService,
    SAVED_MSG, EventOwnerService, userService, ticketsAdditionalService, eventUserService, FreeTicketService,
    TicketBuyEntryCodeService, FEE_PAYER, PAYMENT_PROVIDER, PAYMENT_ACCOUNT_TYPE, ENV
) {
    var dateFilter      = $filter('date'),
    currencyFilter      = $filter('currency'),
    defaultDateFormat   = 'MM/dd/yyyy HH:mm+00',
    defaultPublished,
    defaultVisible;

    const currentEvent = eventDashboardService.getEvent();

    $scope.stripeStatementMaxLength = 22;
    $scope.tilledStatementMaxLength = 20;

    $scope.tickets      = [];
    $scope.event        = {};
    $scope.camps        = [];
    $scope.data         = { errors: [] };
    $scope.utils        = { 
        errors              : [],
        pickerDateStart     : {},
        pickerDateEnd       : {},
        pickerStartOpened   : false,
        pickerEndOpened     : false,
        guruPassCodesSaved  : false,
        allDataLoaded       : false,
        pageErrors          : {
            purchaseDates: []
        },
        buyer               : FEE_PAYER.BUYER,
        defaultPayer        : FEE_PAYER.SELLER,
        stripeStatement     : $scope.$parent.event.stripe_statement,
        ticketing_mode      : null,
        statementDescriptorForm: null,
    };
    $scope.additional   = {};
    $scope.infoData     = {};
    $scope.ticketingModeOptions = [
        {label: 'Basic Ticket', value: false},
        {label: 'Assigned Ticket', value: true},
    ];
    validateSettings();

    loadData()
    .then(function (responses) {
        var eventData      = (responses[0] && responses[0].event),
            ticketsData    = (responses[0] && responses[0].tickets),
            addsData       = (responses[1] && responses[1].data),
            infoData       = (responses[2] && responses[2].data),
            stripeAccs     = (responses[3]),
            tilledAccounts = (responses[4]);

        $scope.utils.initStatement  =  $scope.isStripeProvider() ? eventData.tickets_stripe_statement : eventData.tickets_tilled_statement;
        $scope.utils.initStripeKey  = eventData.account_id;
        $scope.utils.initTilledAccountId = eventData.tilled_account_id;
        $scope.utils.ticketing_mode = eventData.ticketing_mode;

        $scope.tickets          = ticketsData;
        $scope.event            = eventData;
        $scope.stripeAccounts   = stripeAccs;
        $scope.tilledAccounts   = tilledAccounts;
        defaultPublished        = $scope.event.tickets_published;
        defaultVisible          = $scope.event.tickets_visible;
        $scope.utils.start      = angular.copy($scope.event.purchase_date_start);
        $scope.utils.end        = angular.copy($scope.event.purchase_date_end);
        $scope.additional       = addsData.additional;
        $scope.infoData         = infoData;

        if(responses[0].camps) {
            $scope.camps = responses[0].camps;
        }

        if (eventData.block_tickets_keys_edit) {
            var name = '';
            $scope.stripeAccounts.forEach(function (acc) {
                if (acc.account_id === eventData.account_id) {
                    name = $scope.accLabel(acc);
                }
            });
            if (!name) name = 'N/A';

            $scope.stripeAccName = name;

            const activeTilledAccount = $scope.tilledAccounts.find((acc)=> acc.tilled_account_id === eventData.tilled_account_id);

            $scope.tilledAccountName = activeTilledAccount ? $scope.tilledAccountLabel(activeTilledAccount) : 'N/A';
        } else {
            $scope.stripeAccounts = UtilsService.filterHiddenAccounts($scope.stripeAccounts, eventData.account_id);
        }
    })
    .then(function () {
        $scope.utils.allDataLoaded = true;
    })
    .then(getTicketsDirectLink)
    .then(null, function (data) {
        $scope.utils.allDataLoaded = true;
        if(data.validation) toastr.error(data.validation)
    });

    $scope.hasAccess = function () {
        const allowRoles = [
            userService.hasAccess(currentEvent),
            !eventUserService.isCoEventOwner,
        ];

        return allowRoles.every(item => item);
    };

    $scope.updateTicket = function (ticket, cb) {
        ticketsService.updateTicket($stateParams.event, ticket)
        .then(function (resp) {
            toastr.success(SAVED_MSG)
            cb(null, resp.data);
        }).catch(function (resp) {
            var errorMsg = resp.data && resp.data.validation || INTERNAL_ERROR_MSG;
            cb(errorMsg);
        })
    }

    $scope.accLabel = function (acc) {
        return (acc.is_test?'TEST: ':'') + acc.title + ' (' + acc.email + ')';
    };

    $scope.tilledAccountLabel = function (acc) {
        return (acc.is_test?'TEST: ':'') + acc.account_name + ' (' + acc.account_email + ')';
    };

    $scope.saveTicket = function (ticket, cb) {
        ticketsService.createTicket($stateParams.event, ticket).then(function (response) {
            toastr.success(SAVED_MSG)
            cb(null, response.data);
        }).catch(function (resp) {
            var errorMsg = resp.data && resp.data.validation || INTERNAL_ERROR_MSG;
            cb(errorMsg);
        });
    }

    $scope.getPaymentFeePayer = function () {
        return $scope.isStripeProvider() ? $scope.event.stripe_fee_payer :  $scope.event.tilled_fee_payer;
    }

    $scope.saveStripeTicketsKey = function () {
        ticketsService.saveStripeTicketsKey($stateParams.event, {account_id: $scope.event.account_id}
        ).then(function () {
            $scope.utils.initStripeKey = $scope.event.account_id;
        }).then(function () {
            toastr.success('Updated');
        })
    }

    $scope.hasPaymentAccountChanged = function () {
        if($scope.isStripeProvider()) {
            return $scope.utils.initStripeKey !== $scope.event.account_id;
        }

        return $scope.utils.initTilledAccountId !== $scope.event.tilled_account_id;
    }

    $scope.saveTilledAccount = function () {
        ticketsService.saveTilledAccount($stateParams.event, { tilled_account_id: $scope.event.tilled_account_id }
            ).then(function () {
                $scope.utils.initTilledAccountId = $scope.event.tilled_account_id;
            }).then(function () {
                toastr.success('Updated');
            })
    }

    $scope.savePaymentAccount = function () {
        if($scope.isStripeProvider()) {
            return $scope.saveStripeTicketsKey()
        }

        return $scope.saveTilledAccount()
    }

    $scope.saveTicketsChange = function (change, cb) {
        ticketsService.createPriceChange($stateParams.event, change).then(function success () {
            return cb();
        }, function error (response) {
            return cb((response.data && response.data.validation) || INTERNAL_ERROR_MSG);
        });
    }

    $scope.updateTicketsChange  = function (data) {
        return ticketsService.updatePriceChange($stateParams.event, data)
    }

    $scope.setTicketsPublished = function () {
        ticketsService.publishTickets($stateParams.event, {
            tickets_published   : $scope.event.tickets_published,
            tickets_visible     : $scope.event.tickets_visible
        }).then(function () {
            getTicketsDirectLink();   
            defaultPublished = $scope.event.tickets_published;
            defaultVisible = $scope.event. tickets_visible;
        }, function () {
            $scope.event.tickets_published = defaultPublished;
            $scope.event.tickets_visible = defaultVisible;
        });
    }

    $scope.updateTicketsSettings = function(settings) {
        ticketsService.updateTicketsSettings($stateParams.event, settings)
            .then(({data}) => {
                $scope.event.ticketing_mode = data.require_recipient_name_for_each_ticket;
                $scope.utils.ticketing_mode = data.require_recipient_name_for_each_ticket;
                $scope.event.require_coupon = data.require_coupon;
                $scope.event.require_covid_test_for_each_ticket = data.require_covid_test_for_each_ticket;
                $scope.event.use_vertical_insurance = data.use_vertical_insurance;
                $scope.event.allow_point_of_sales = data.allow_point_of_sales;
                eventDashboardService.updateEvent(
                    _.pick($scope.event, [
                        'ticketing_mode',
                        'require_coupon',
                    ])
                );
                toastr.success('Updated!');

                $state.transitionTo($state.current, $stateParams, {
                    reload: true,
                    inherit: false,
                    notify: true
                });
            })
    }

    $scope.updateCouponSettings = async function (settings) {
        try {
            await TicketBuyEntryCodeService.upsert($stateParams.event, settings);

            $scope.event.coupons_settings = settings;

            toastr.success('Saved');
        } catch (err) {
            toastr.warning('Failed');
        }
    };

    $scope.showSalesHubModeSwitcher = function () {
        return $scope.isAssignedTicketsMode() && ENV === 'development';
    }

    $scope.showTicketCouponsSettings = function () {
        return $scope.isAssignedTicketsMode() &&
            $scope.utils.allDataLoaded &&
            $scope.event &&
            $scope.event.coupons_settings &&
            $scope.event.coupons_settings.is_active;
    }

    $scope.saveTicketingMode = function () {
        $scope.updateTicketsSettings({
            require_recipient_name_for_each_ticket: $scope.event.ticketing_mode,
        })
    }

    $scope.isAssignedTicketsMode = function () {
        return $scope.utils.ticketing_mode === true;
    }

    $scope.pointOfSalesModeEnabled = () => $scope.event.allow_point_of_sales === true;

    $scope.showVerticalInsuranceSettings = function () {
        return false; // can switch later. SHOULD BE TURNED ON ONLY ON ASSIGNED TICKETS
    }

    $scope.showTicketingModeSaveButton = function () {
        return ($scope.utils.ticketing_mode !== $scope.event.ticketing_mode) && $scope.utils.allDataLoaded;
    }

    $scope.hideSalesSaveButton = function () {
        var uStart = $scope.utils.start && $scope.utils.start.getTime(),
            eStart = $scope.event.purchase_date_start && $scope.event.purchase_date_start.getTime(),
            uEnd = $scope.utils.end && $scope.utils.end.getTime(),
            eEnd =  $scope.event.purchase_date_end && $scope.event.purchase_date_end.getTime();
        return (uStart === eStart) && (uEnd === eEnd);
    }

    $scope.saveSalesDates = function (start, end) {
        $scope.utils.errors.length = 0;
        $scope.utils.pageErrors.purchaseDates = [];

        __validateSalesDates(start, end);
        validateEventDateEnd();

        if($scope.utils.errors.length || !__noPageErrors()) {
            return;
        }

        var startFormatted  = dateFilter(start, defaultDateFormat),
            endFormatted    = dateFilter(end, defaultDateFormat);
        ticketsService.saveSalesDates($stateParams.event, startFormatted, endFormatted)
        .success(function () {
            $scope.event.purchase_date_start = angular.copy(start);
            $scope.event.purchase_date_end = angular.copy(end);
            validateSettings();
        })
        .error(function (data) {
            $scope.utils.errors = [];
            $scope.utils.errors.push({ text: (data.validation || INTERNAL_ERROR_MSG) });
        });
    }

    var __noPageErrors = function () {
        var count = 0;
        angular.forEach($scope.utils.pageErrors, function (type) {
            count += type.length;
        });
        return count === 0;
    };

    $scope.removePriceChange = function (change, cb) {
        ticketsService.removePriceChange($stateParams.event, +change)
        .then(function success (resp) {
            $scope.event.changes = {};
            var keys = Object.keys(resp.data.changes);
            for(var i = 0; i < keys.length; ++i) {
                $scope.event.changes[keys[i]] 
                    = DateService.normalizeStr(resp.data.changes[keys[i]]);
            }            
            $scope.tickets = resp.data.tickets;
            return cb();
        }, function error (resp) {
            if(resp.data && resp.data.validation)
                $scope.utils.errors = resp.data.validation;
            return cb();
        })
    }

    $scope.errorText = function (text) {
        return $sce.trustAsHtml(text);
    }

    $scope.updateAdditional = function (callback) {
        ticketsAdditionalService.updateTicketsAdditionalFields($stateParams.event, { 
            additional: $scope.additional
        }).then(function (data) {
            if(callback) return callback(null, data);
        }).catch(function (data) {
            if(callback) return callback(data);
        })
    }

    $scope.createAdditional = function (data, callback) {
        ticketsAdditionalService.saveTicketsAdditional($stateParams.event, data).then(function (data) {
            toastr.success('New Field Added')
            if (callback) return callback(null, data)
        }).catch(function (data) {
            if (callback) return callback(data)
        });
    }

    $scope.savePasscodes = function () {
        ticketsService.savePasscodes($stateParams.event, {
            tickets_purchase_passcode   : $scope.event.purchase_passcode,
            tickets_refund_passcode     : $scope.event.refund_passcode
        }).success(function () {
            $scope.utils.guruPassCodesSaved = true;
            $timeout(function () {
                $scope.utils.guruPassCodesSaved = false;
            }, 1000);
        }).error(function (data) {            
            $scope.utils.errors = [];
            $scope.utils.errors.push({ text: (data.validation || INTERNAL_ERROR_MSG) });
        })
    }

    $scope.saveInfoData = function (data, callback) {
        ticketsService.saveText($stateParams.event, data)
        .success(function () {
            if(!_.isUndefined(data.kiosk_description)) {
                $scope.infoData.kiosk_description = data.kiosk_description;
            }
            if(callback) return callback();
        }).error(function (data) {
            let validationError = data.validation || (data.validationErrors && data.validationErrors[0].message);

            if(callback) return callback(validationError || INTERNAL_ERROR_MSG)
        })
    }

    $scope.isStripeProvider = function () {
        return $scope.event.payment_provider === PAYMENT_PROVIDER.STRIPE
    }

    $scope.getStatement = function () {
        if($scope.isStripeProvider()) {
            return $scope.event.tickets_stripe_statement;
        }

        return $scope.event.tickets_tilled_statement;
    }

    $scope.hasStatementChanged = function () {
        return $scope.utils.initStatement !== $scope.getStatement()
    }

    $scope.saveStatement = function () {
        if($scope.utils.statementDescriptorForm.$invalid) {
            return;
        }

        if($scope.isStripeProvider()) {
            return $scope.saveStripeStatement();
        }

        return $scope.saveTilledStatement();
    }

    $scope.saveStripeStatement = function () {
        var statement = ($scope.event.tickets_stripe_statement === '')
                            ?null
                            :$scope.event.tickets_stripe_statement

        ticketsService.saveStripeStatement($stateParams.event, statement)
            .then(function () {
                $scope.utils.initStatement = $scope.event.tickets_stripe_statement;

                toastr.success('Updated');
            })
    }

    $scope.saveTilledStatement = function () {
        ticketsService.saveTilledStatement($stateParams.event, $scope.event.tickets_tilled_statement)
            .then(function () {
                $scope.utils.initStatement = $scope.event.tickets_tilled_statement;

                toastr.success('Updated');
            })
    }

    $scope.getFee = function () {
        return $scope.event.sw_fee
                    ?currencyFilter($scope.event.sw_fee)
                    :'N/A'
    }

    $scope.showFreeTicketButton = function() {
        const rules = [
            $scope.event.is_tickets_purchase_open,
            $scope.event.ticketing_mode === true,
            $scope.event.enable_free_tickets,
        ];

        return rules.every(rule => rule);
    }

    $scope.openGenerateFreeTicketModal = function() {
        let isWeekendExist = false;

        $scope.tickets.forEach(ticket => {
            if (ticket.ticket_type === 'weekend') {
                isWeekendExist = true;
            }
        })

        const ticketType = isWeekendExist ? 'weekend': 'daily';
        FreeTicketService.openGenerateFreeTicketModal(ticketType);
    }

    function validateSettings() {
        var eventID = $stateParams.event;

        ticketsService.validateSettings(eventID)
            .then(function (resp) {
                var respErrors 	= resp.data && resp.data.errors;

                respErrors 	= _.isArray(respErrors) ? respErrors : [];

                if (!currentEvent.live_to_public && (currentEvent.tickets_visible || currentEvent.tickets_published)) {
                    var msg =
                            'To make Tickets Live enable ' +
                            '"Make Event Live to Public" checkbox ' +
                            '<a href="#/event/' + eventID + '/update">here</a>';

                    respErrors.push({
                        message: msg
                    });
                }

                $scope.data.errors = respErrors;
            });
    }

    function getTicketsDirectLink () {
        let showLink = ($scope.event.tickets_published || $scope.event.tickets_visible);
        const showSalesHubLink = $scope.event.allow_point_of_sales && $scope.isAssignedTicketsMode();

        if(showLink) {
            $scope.utils.link = showSalesHubLink
                ?  UtilsService.getTicketsSalesHubLink({
                    salesHubPosID: $scope.event.sales_hub_point_of_sale_id,
                })
                :  UtilsService.getTicketsDirectLink(
                    $scope.event.event_tickets_code,
                    currentEvent.require_tickets_names,
                    currentEvent.use_merchandise_sales
                );
        } else
            $scope.utils.link = null;
    }

    function loadData () {
        return $q.all([__ticketsList(), __loadAdditional(), __receiptData(), __loadStripeAccs(), __loadTilledAccs()]);
    }

    function __ticketsList () {
        // "02/26/2016 00:00+00" - price change
        var defer = $q.defer();
        ticketsService.ticketsList($stateParams.event).then(function (data) {       
            if(data.event.purchase_date_start)
                data.event.purchase_date_start 
                    = DateService.parseDate(data.event.purchase_date_start, data.event.date_format);
            if(data.event.purchase_date_end) {
                data.event.purchase_date_end 
                    = DateService.parseDate(data.event.purchase_date_end, data.event.date_format);
            }
            defer.resolve(data);
        }).then(null, function (data) {
            defer.reject(data)
        })
        return defer.promise;
    }

    function __loadAdditional () {
        return ticketsAdditionalService.getAdditionalFields($stateParams.event);
    }

    function __receiptData () {
        return ticketsService.loadEmailData($stateParams.event);
    }

    function __loadStripeAccs() {
        return EventOwnerService.getEOAccounts($stateParams.event, PAYMENT_ACCOUNT_TYPE.TICKETS);
    }

    function __loadTilledAccs() {
        return EventOwnerService.getEOTilledAccounts($stateParams.event, PAYMENT_ACCOUNT_TYPE.TICKETS);
    }

    function __validateSalesDates(start, end) {
        if(!end) {
           handleDateError('Tickets Purchase End date can not be empty.');
        }
        if(!start) {
            handleDateError('Tickets Purchase Start date can not be empty.')
        }
        if(end && start && end < start) {
            handleDateError(`Tickets Purchase End date can't be earlier then Tickets Purchase Start date.`)
        }
    }

    function validateEventDateEnd() {
        const FORMAT        = 'YYYY-MM-DD HH:mm';
        const UI_FORMAT     = 'MM/DD YYYY, hh:mm A';

        const ticketPurchaseDateStart   = formatDate($scope.utils.start, FORMAT);
        const ticketPurchaseDateEnd     = formatDate($scope.utils.end, FORMAT);
        const eventDateEnd              = formatDate($scope.event.date_end,FORMAT, true);

        if (ticketPurchaseDateStart > eventDateEnd) {
            handleDateError(`Tickets Purchase Start date can't be later then Event End date (${formatDate($scope.event.date_end, UI_FORMAT, true)})`);
        }

        if (ticketPurchaseDateEnd > eventDateEnd) {
            handleDateError(`Tickets Purchase End date can't be later then Event End date (${formatDate($scope.event.date_end, UI_FORMAT, true)})`)
        }
    }

    function handleDateError(text) {
        $scope.utils.pageErrors.purchaseDates.push({ text });
    }

    function formatDate(date, format, useUTC) {
        const _moment = useUTC ? moment.utc : moment;

        return _moment(date).format(format);
    }

}
