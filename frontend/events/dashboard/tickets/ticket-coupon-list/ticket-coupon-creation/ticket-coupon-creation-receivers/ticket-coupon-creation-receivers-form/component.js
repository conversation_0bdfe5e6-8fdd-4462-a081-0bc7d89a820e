
class Controller {
    constructor (toastr) {
        this.toastr = toastr;
    }

    $onInit () {
        this.receiver = {};
    }

    fieldHasError (fieldName) {
        return this.couponReceiverForm.$submitted && this.couponReceiverForm[fieldName].$invalid;
    }

    disableSubmit () {
        return this.couponReceiverForm.$invalid;
    }

    submit () {
        if(this.couponReceiverForm.$invalid) {
            this.toastr.warning('Invalid form');
            return;
        }

        this.onSave({ receiver: this.receiver })
            .catch(err => {
                if(err.validation) {
                    this.toastr.warning(err.validation.message);

                    this.couponReceiverForm[err.validation.field].$invalid = true;
                }
            })
    }

    close () {
        this.onClose();
    }
}

Controller.$inject = ['toastr'];

angular.module('SportWrench').component('ticketCouponCreationReceiversForm', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-creation/ticket-coupon-creation-receivers/ticket-coupon-creation-receivers-form/template.html',
    bindings: {
        onSave: '&',
        onClose: '&'
    },
    controller: Controller
});
