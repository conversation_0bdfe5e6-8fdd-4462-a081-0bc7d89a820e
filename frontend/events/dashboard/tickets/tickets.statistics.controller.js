angular.module('SportWrench').controller('Event.Tabs.TicketsStatistics', TicketsStatistics)

function TicketsStatistics ($scope, loadData, $stateParams, INTERNAL_ERROR_MSG, ticketsService) {
    $scope.stats = loadData;

    $scope.reloadReport = function (from, to) {
        if($scope.error) {
            $scope.error = null;
        }

        return ticketsService.statistics($stateParams.event, {
            from    : from,
            to      : to
        }).then(function (resp) {
            $scope.stats = resp.data;
            return resp.data;
        });
    }

    $scope.exportReport = function (from, to) {
        ticketsService.statisticsExport($stateParams.event, {from, to});
    };
}
