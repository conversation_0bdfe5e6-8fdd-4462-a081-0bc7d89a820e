angular.module('SportWrench').component('virtualTransfersList', {
    templateUrl: 'events/dashboard/transfers/general/virtual-transfers/virtual-transfers-list.html',
    bindings: {
        transfers   : '<',
        sign        : '@'
    },
    controller: ['$filter', VirtualTransfersController]
});

function VirtualTransfersController ($filter) {

    var currency = $filter('currency');

    this.showAmount = function (amount) {

        if (this.sign !== 'plus') {
            amount = (amount * -1);
        }
        
        return currency(amount, '');
    }
}
