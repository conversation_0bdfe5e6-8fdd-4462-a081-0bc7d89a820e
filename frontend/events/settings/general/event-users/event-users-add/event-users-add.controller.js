angular
    .module('SportWrench')
    .controller('AddEventUserContoller', AddEventUser);

AddEventUser.$inject = ['$stateParams', '$state', '$uibModalInstance', '$scope', 'eventUsersService', '$rootScope'];

function AddEventUser($stateParams, $state, $uibModalInstance, $scope, eventUsersService, $rootScope) {
    let eventID             = $stateParams.event;
    $scope.user             = null;
    $scope.userPermissions  = {};

    $scope.search           = '';
    $scope.users            = {};
    $scope.warning          = {
        toManyResults       : false,
        noResults           : false
    };
    $scope.rowQtyLimit      = 0;
    $scope.loading          = {
        error       : false,
        errMsg      : ''
    };

    $scope.showUsersList = true;

    $scope.showError = function (err) {
        $scope.loading.errMsg = err;
        $scope.loading.error  = true;
    };

    $scope.openPermissions = function (selectedUser) {
        $scope.showUserPermissions = true;
        $scope.showUsersList       = false;

        $scope.user = selectedUser;
    };

    $scope.closePermissions = function () {
        $scope.showUserPermissions = false;
        $scope.showUsersList       = true;
        $scope.user                = null;
        $scope.userPermissions     = {};
    };

    $scope.addEventUser = function () {
        let permissions = _.pick($scope.userPermissions, (item) => item === true);
        let user        = $scope.user;

        eventUsersService.addEventUserByEmail(eventID, user.email, permissions)
            .then(function () {

                user.eventid        = Number(eventID);
                user.permissions    = permissions;

                $rootScope.$emit('eventUserAdded', user);
                $scope.$dismiss();
            })
            .catch(function (err) {
                $scope.loading.errMsg = (err.data && err.data.validation)
                    ? err.data.validation
                    : 'Internal Server Error.'
            });
    };
}
