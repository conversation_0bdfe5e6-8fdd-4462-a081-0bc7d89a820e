angular.module('SportWrench').component('statementDescriptorInput', {
    templateUrl: 'events/settings/general/forms/inputs/statement-descriptor-input.html',
    bindings: {
        ngModel: '=',
        hasError: '<',
        isRequired: '<',
        maxLength: '<',
    },
    controller: [
        function () {
            this.$onInit = function () {
                const STRIPE_STATEMENT_MAX_LENGTH = 22;

                if (!this.maxLength) {
                    this.maxLength = STRIPE_STATEMENT_MAX_LENGTH;
                }
            };
        },
    ],
});

