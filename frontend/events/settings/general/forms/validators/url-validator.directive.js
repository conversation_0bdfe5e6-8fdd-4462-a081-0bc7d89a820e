angular.module('SportWrench').directive('urlValidator', function (URL_PATTERN) {    
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.url = function (modelValue) {
                if(ctrl.$isEmpty(modelValue)) return true;
                if(URL_PATTERN.test(modelValue)) return true;
                return false;
            }
        }
    }
})
