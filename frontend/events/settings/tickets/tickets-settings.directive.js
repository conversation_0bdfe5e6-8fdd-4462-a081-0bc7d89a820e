angular.module('SportWrench').directive('ticketsSettings', function (EventSettingsService) {
    return {
        restrict: 'E',
        scope: {},
        templateUrl: 'events/settings/tickets/tickets-settings.html',
        require: '^eventSettings',
        link: function (scope, attrs, elem, ctrl) {
            var mode = ctrl.getMode(),
                eventId = ctrl.getEventId();
            scope.booths = [];
            // scope.$watch(function () {
            //     return ctrl.isLocationsActive();
            // }, function (activate) {
            //     if(activate === true && mode === 'update') {
            //         EventSettingsService.loadLocations(eventId)
            //     }
            // }) 
        }
    }
})
