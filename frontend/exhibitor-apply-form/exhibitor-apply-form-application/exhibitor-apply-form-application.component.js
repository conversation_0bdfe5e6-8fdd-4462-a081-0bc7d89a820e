class ExhibitorApplyFormApplicationComponent {
    constructor(ExhibitorsService, INTERNAL_ERROR_MSG, EVENT_DATES_FORMAT, OTHER_BOOTH,
                DEFAULT_OTHER_BOOTH, $interval, toastr) {
        this.ExhibitorsService = ExhibitorsService;
        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;
        this.EVENT_DATES_FORMAT = EVENT_DATES_FORMAT;
        this.OTHER_BOOTH = OTHER_BOOTH;
        this.DEFAULT_OTHER_BOOTH = DEFAULT_OTHER_BOOTH;
        this.$interval = $interval;
        this.toastr = toastr;

        this.loading = {
            inProcess: true,
            error: '',
        }

        this.selectedBooth = null;
        this.selectedBooths = [];
        this.otherBooths = [];
    }

    async $onInit() {
        await this.loadExhibitorRegistrationInfo();
    }

    async loadExhibitorRegistrationInfo() {
        try {
            const exhibitorRegistrationInfo =
                await this.ExhibitorsService.getExhibitorRegistrationInfo(this.eventId, this.exhibitorId);

            this.allowedVendorTypes = exhibitorRegistrationInfo.allowed_vendor_types;
            this.registrationInfo = _.omit(exhibitorRegistrationInfo, 'allowed_vendor_types');

            this.registrationInfo.selectedBooths = this.selectedBooths;
            this.registrationInfo.otherBooths = this.otherBooths;
            this.registrationInfo.comment = '';
        } catch(error) {
            this.loading.error = error && error.validation ? error.validation : this.INTERNAL_ERROR_MSG;
        } finally {
            this.loading.inProcess = false;
        }
    }

    addBooth() {
        if (!this.selectedBooth) {
            return;
        }

        if (this.selectedBooth.id === this.OTHER_BOOTH.id) {
            this.addOtherBooth();
        } else {
            this.addEventBooth();
        }
    }

    addOtherBooth() {
        this.otherBooths.push(Object.assign({}, this.DEFAULT_OTHER_BOOTH));
    }

    addEventBooth() {
        this.selectedBooths.push(JSON.parse(angular.toJson(this.selectedBooth)));
    }

    removeBooth(index) {
        this.selectedBooths.splice(index, 1);
    }

    getBoothsTotal() {
        this.total = this.ExhibitorsService.getBoothsTotal(this.registrationInfo);

        return this.total;
    }

    isPurchaseDataRequired() {
        return this.ExhibitorsService.isPurchaseDataRequired(this.registrationInfo);
    }

    isEventDatesEmpty() {
        const {event_dates = {}} = this.registrationInfo || {};

        return !this.ExhibitorsService.isEventDatesSelected(event_dates);
    }

    hasOtherBoothsWithoutFee() {
        const {otherBooths = []} = this.registrationInfo || {};

        return this.ExhibitorsService.hasOtherBoothsWithoutFee(otherBooths);
    }

    hasOtherBoothsWithoutDescription() {
        const {otherBooths = []} = this.registrationInfo || {};

        return this.ExhibitorsService.hasOtherBoothsWithoutDescription(otherBooths);
    }

    isBoothsEmpty() {
        return this.getBoothsTotal() <= 0;
    }
}

angular.module('SportWrench').component('exhibitorApplyFormApplication', {
    templateUrl: 'exhibitor-apply-form/exhibitor-apply-form-application/exhibitor-apply-form-application.html',
    bindings: {
        total: '=',
        eventId: '<',
        exhibitorId: '<',
        registrationInfo: '=',
        isApplyMode: '<',
    },
    controller: [
        'ExhibitorsService',
        'INTERNAL_ERROR_MSG',
        'EVENT_DATES_FORMAT',
        'OTHER_BOOTH',
        'DEFAULT_OTHER_BOOTH',
        '$interval',
        'toastr',
        ExhibitorApplyFormApplicationComponent
    ]
});
