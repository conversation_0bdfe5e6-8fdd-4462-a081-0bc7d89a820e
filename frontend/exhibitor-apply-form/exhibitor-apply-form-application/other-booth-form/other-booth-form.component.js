class OtherBoothFormComponent {
    constructor(UtilsService) {
        this.UtilsService = UtilsService;
    }

    generateAmount() {
        this.info.amount = this.UtilsService.approxNumber(this.info.fee * this.info.quantity);
    }
}

angular.module('SportWrench').component('otherBoothForm', {
    templateUrl: 'exhibitor-apply-form/exhibitor-apply-form-application/other-booth-form/other-booth-form.html',
    bindings: {
        info: '=',
    },
    controller: [
        'UtilsService', OtherBoothFormComponent
    ]
});
