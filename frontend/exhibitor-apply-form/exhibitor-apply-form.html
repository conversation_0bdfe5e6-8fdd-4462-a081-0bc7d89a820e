<div class="row">
    <div class="col-sm-12">
        <div class="modal-header">
            <h4 class="text-center">{{$ctrl.companyName}}</h4>
        </div>
    </div>
    <div class="col-sm-12">
        <div class="modal-body">
            <uib-tabset active="$ctrl.currentTab">
                <uib-tab
                        index="$ctrl.tabs.EXHIBITOR_INFO"
                        select="$ctrl.onTabSelect($ctrl.tabs.EXHIBITOR_INFO)"
                >
                    <uib-tab-heading>
                        Exhibitor Info
                    </uib-tab-heading>
                    <div ng-if="$ctrl.loadTabContent($ctrl.tabs.EXHIBITOR_INFO)">
                        <exhibitor-apply-form-info
                            event-id="$ctrl.eventId"
                            exhibitor-id="$ctrl.exhibitorId"
                        >
                        </exhibitor-apply-form-info>
                    </div>
                </uib-tab>
                <uib-tab
                        index="$ctrl.tabs.APPLICATION_INFO"
                        select="$ctrl.onTabSelect($ctrl.tabs.APPLICATION_INFO)"
                >
                    <uib-tab-heading>
                        Application Info
                    </uib-tab-heading>
                    <div ng-if="$ctrl.loadTabContent($ctrl.tabs.APPLICATION_INFO)">
                        <exhibitor-apply-form-application
                            event-id="$ctrl.eventId"
                            exhibitor-id="$ctrl.exhibitorId"
                            registration-info="$ctrl.registrationInfo"
                            total="$ctrl.total"
                            is-apply-mode="$ctrl.isApplyMode"
                            is-form-submitted="$ctrl.isFormSubmitted"
                        >
                        </exhibitor-apply-form-application>
                    </div>
                </uib-tab>
            </uib-tabset>
        </div>
    </div>
    <div class="col-sm-12">
        <div class="modal-footer">
            <button class="btn btn-default" ng-click="$ctrl.onClose()">Close</button>
            <button class="btn btn-primary"
                    ng-if="$ctrl.currentTab !== $ctrl.tabs.EXHIBITOR_INFO"
                    ng-click="$ctrl.saveApplication()">Save</button>
        </div>
    </div>
</div>
