class ExhibitorEventReceiptFormComponent {
    constructor(ExhibitorReceiptsService, ExhibitorsService, ExhibitorService, ExhibitorsPaymentsService, UtilsService, INTERNAL_ERROR_MSG, PAYMENT_STATUS,
                PAYMENT_TYPE, EVENT_DATES_FORMAT, APPLICATION_STATUS, _) {
        this.ExhibitorReceiptsService = ExhibitorReceiptsService;
        this.ExhibitorsService = ExhibitorsService;
        this.ExhibitorService = ExhibitorService;
        this.ExhibitorsPaymentsService = ExhibitorsPaymentsService;
        this.UtilsService = UtilsService;

        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;
        this.PAYMENT_STATUS = PAYMENT_STATUS;
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.EVENT_DATES_FORMAT = EVENT_DATES_FORMAT;
        this.APPLICATION_STATUS = APPLICATION_STATUS;

        this.loading = {
            inProcess: true,
            error: ''
        };

        this.selectedBooth = null;
        this.selectedBooths = [];
    }

    async $onInit() {
        if (!this.receiptId) {
            await this.getRegistrationData();
        } else {
            await this.getReceiptData();
        }
    }

    async getRegistrationData() {
        try {
            const exhibitorRegistrationInfo = await this.ExhibitorService.getRegistrationInfo(this.eventId)
            this.isCreateMode = true;
            this.exhibitorReceiptData = Object.assign({}, exhibitorRegistrationInfo);

            this.selectedBooths = [];
            this.exhibitorReceiptData.selectedBooths = this.selectedBooths;

            this.initExhibitorReceiptData = JSON.parse(JSON.stringify(this.exhibitorReceiptData));
        } catch (error) {
            this.loading.error = error && error.validation ? error.validation : this.INTERNAL_ERROR_MSG;
        } finally {
            this.loading.inProcess = false;
        }
    }

    async getReceiptData() {
        try {
            const {data: exhibitorReceiptInfo = {}} = await this.ExhibitorReceiptsService.getReceiptInfo(
                this.eventId,
                this.receiptId
            );

            this.exhibitorReceiptData = Object.assign({}, exhibitorReceiptInfo);

            this.selectedBooths = exhibitorReceiptInfo.chosen_event_booths || [];
            this.exhibitorReceiptData.selectedBooths = this.selectedBooths;

            this.initExhibitorReceiptData = JSON.parse(JSON.stringify(this.exhibitorReceiptData));
        } catch (error) {
            this.loading.error = error && error.validation ? error.validation : this.INTERNAL_ERROR_MSG;
        } finally {
            this.loading.inProcess = false;
        }
    }

    getBoothsTotal() {
        let total = 0;

        if (!_.isEmpty(this.selectedBooths)) {
            total += this.calculateTotal(this.selectedBooths, 'amount');
        }

        this.total = this.UtilsService.approxNumber(total);

        return this.total;
    }

    calculateTotal(collection, field) {
        return collection.reduce((acc, item) => acc + item[field], 0);
    }

    addBooth() {
        if (!this.selectedBooth) {
            return;
        }

        this.selectedBooths.push(JSON.parse(angular.toJson(this.selectedBooth)));
    }

    removeBooth(index) {
        this.selectedBooths.splice(index, 1);
    }

    isReceiptEditable() {
        return this.isCreateMode || this.ExhibitorsPaymentsService.isPendingPayment(this.exhibitorReceiptData);
    }

    async $onChanges(changes) {
        if (changes.eventId && changes.eventId.currentValue !== changes.eventId.previousValue && !this.exhibitorReceiptData) {
            await this.getRegistrationData();
        }
    }

    isEventDatesEmpty() {
        const {event_dates = {}} = this.exhibitorReceiptData || {};

        return !this.ExhibitorsService.isEventDatesSelected(event_dates);
    }

    isApplicationApproved() {
        return this.exhibitorReceiptData && this.exhibitorReceiptData.status === this.APPLICATION_STATUS.APPROVED;
    }
}

angular.module('SportWrench').component('exhibitorEventReceiptForm', {
    templateUrl: 'exhibitor/exhibitor-event-receipt/exhibitor-event-receipt-form/exhibitor-event-receipt-form.html',
    bindings: {
        eventId: '<',
        receiptId: '<',
        exhibitorReceiptData: '=',
        initExhibitorReceiptData: '=',
        total: '=',
        onModalClose: '&',
    },
    controller: [
        'ExhibitorReceiptsService',
        'ExhibitorsService',
        'ExhibitorService',
        'ExhibitorsPaymentsService',
        'UtilsService',
        'INTERNAL_ERROR_MSG',
        'PAYMENT_STATUS',
        'PAYMENT_TYPE',
        'EVENT_DATES_FORMAT',
        'APPLICATION_STATUS',
        '_',
        ExhibitorEventReceiptFormComponent
    ]
});
