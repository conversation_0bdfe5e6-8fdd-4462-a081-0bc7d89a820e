<div class="row">
    <div class="col-sm-12">
        <div class="modal-header">
            <h4 class="text-center">{{$ctrl.eventName}}</h4>
        </div>
    </div>
    <div class="col-sm-12">
        <div class="modal-body">
            <uib-tabset active="$ctrl.currentTab">
                <uib-tab
                    index="$ctrl.tabs.EVENT_INFO"
                    select="$ctrl.onTabSelect($ctrl.tabs.EVENT_INFO)"
                >
                    <uib-tab-heading>
                        Event Info
                    </uib-tab-heading>
                    <div ng-if="$ctrl.loadTabContent($ctrl.tabs.EVENT_INFO)">
                        <exhibitor-event-info
                           event-id="$ctrl.eventId"
                        >
                        </exhibitor-event-info>
                    </div>
                </uib-tab>
                <uib-tab
                    ng-if="$ctrl.showApplicationTab()"
                    index="$ctrl.tabs.REGISTRATION_INFO"
                    select="$ctrl.onTabSelect($ctrl.tabs.REGISTRATION_INFO)"
                >
                    <uib-tab-heading>
                        Application Info
                    </uib-tab-heading>
                    <div ng-if="$ctrl.loadTabContent($ctrl.tabs.REGISTRATION_INFO)">
                        <exhibitor-event-registration-form
                            event-id="$ctrl.eventId"
                            registration-data="$ctrl.registrationData"
                            total="$ctrl.total"
                            is-view-mode="$ctrl.isViewMode"
                            is-apply-mode="$ctrl.isApplyMode"
                        >
                        </exhibitor-event-registration-form>
                    </div>
                </uib-tab>
            </uib-tabset>
        </div>
    </div>
    <div class="col-sm-12">
        <div class="modal-footer">
            <button class="btn btn-default" ng-click="$ctrl.onCloseModal()">{{$ctrl.getCloseBtnName()}}</button>
            <async-button ng-if="$ctrl.showSubmitButton()" class="spacer-xs-l" title="{{$ctrl.getSubmitBtnName()}}" btn-class="btn btn-primary" on-click="$ctrl.onApply()"></async-button>
        </div>
    </div>
</div>
