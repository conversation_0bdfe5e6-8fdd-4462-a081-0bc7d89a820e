angular.module('SportWrench').service('ExhibitorReceiptsService', ['$http', '$uibModal', ExhibitorReceiptsService]);

function ExhibitorReceiptsService ($http, $uibModal) {
    this.$http = $http;
    this.$uibModal = $uibModal;
}

ExhibitorReceiptsService.prototype.getReceiptsList = function () {
    return this.$http.get('/api/sponsor/payments')
        .then(response => response.data && response.data.payments);
};

ExhibitorReceiptsService.prototype.getReceiptInfo = function (eventId, receiptId) {
    return this.$http.get(`/api/sponsor/event/${eventId}/receipts/${receiptId}`);
};

ExhibitorReceiptsService.prototype.updateReceipt = function (eventId, receiptId, receiptData) {
    return this.$http.put(`/api/sponsor/event/${eventId}/receipts/${receiptId}`, receiptData);
};

ExhibitorReceiptsService.prototype.getShortEventsData = function () {
    return this.$http.get('/api/sales/events/published/short')
        .then(response => response.data && response.data.events);
};

ExhibitorReceiptsService.prototype.getPaymentBooths = function (purchaseID) {
    return this.$http.get('/api/sales/payments/' + purchaseID)
        .then(response => response.data && response.data.booth_payments);
};

ExhibitorReceiptsService.prototype.payReceipt = function (type, payment, eventID) {
    return this.$http.post(`/api/event/${eventID}/exhibitor/pay/${type}`, { payment })
};

ExhibitorReceiptsService.prototype.createReceipt = function (eventId, receiptData) {
    return this.$http.post(`/api/sponsor/event/${eventId}/receipts`, receiptData);
};

ExhibitorReceiptsService.prototype.openExhibitorEventReceiptModal = function (payment, isCreateMode = false) {
    const {
        eventId,
        purchaseId: receiptId,
        companyName,
        events
    } = payment || {};

    return this.$uibModal.open({
        size: 'md',
        template: `
            <exhibitor-event-receipt
                event-id="eventId"
                receipt-id="receiptId"
                company-name="companyName"
                is-create-mode="isCreateMode"
                events="events"
                on-close="$close(withReload)"
            >
            </exhibitor-event-receipt>
        `,
        controller: ['$scope', function($scope) {
            $scope.eventId = eventId;
            $scope.receiptId = receiptId;
            $scope.companyName = companyName;
            $scope.isCreateMode = isCreateMode;
            $scope.events = events || [];
        }]
    }).result
}
