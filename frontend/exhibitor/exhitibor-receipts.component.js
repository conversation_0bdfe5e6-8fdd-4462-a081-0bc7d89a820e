angular.module('SportWrench').component('exhibitorReceipts', {
    templateUrl 	: 'exhibitor/receipts.html',
    controller 		: Exhibitor<PERSON>eceiptsController
});

ExhibitorReceiptsController.$inject = ['$stateParams', 'ExhibitorReceiptsService'];

function ExhibitorReceiptsController($stateParams, ExhibitorReceiptsService) {
    let self = this;

    this.$onInit = function () {
        loadData();
    };

    this.filters = $stateParams.filters || {};

    this.loadBooths = function (purchaseID) {
        return ExhibitorReceiptsService.getPaymentBooths(purchaseID);
    };

    function loadData() {
        self.events         = [];
        self.payments       = [];
        self.booth_payments = [];

        return Promise.all([
            ExhibitorReceiptsService.getReceiptsList(),
            ExhibitorReceiptsService.getShortEventsData()
        ]).then(result => {
            self.payments   = result[0];
            self.events     = result[1];
        })
    }
}
