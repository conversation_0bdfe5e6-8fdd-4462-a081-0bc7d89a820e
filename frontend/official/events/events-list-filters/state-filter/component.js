
class Controller {
    constructor (geoService) {
        this.geoService = geoService;
    }

    $onInit () {
        this.states = [];

        this.__loadStates();
    }

    async __loadStates () {
        let states = await this.geoService.getStates();

        if(_.isEmpty(states)) {
            return;
        }

        this.states = states.map(s => {
            return {
                id: s.state,
                name: s.name,
                short: s.state
            }
        });

        this.states = _.sortBy(this.states, 'name');
    }

    filterEntry (selection) {
        this.onUpdate({ value: selection });
    }

    addClearFilterHandler () {
        this.onUpdate({ value: [] });
    }
}


Controller.$inject = ['geoService'];

angular.module('SportWrench').component('officialEventsStateFilter', {
    templateUrl: 'official/events/events-list-filters/state-filter/template.html',
    bindings: {
        filter: '<',
        onUpdate: '&'
    },
    controller: Controller
});
