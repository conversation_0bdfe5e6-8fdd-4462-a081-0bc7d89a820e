angular.module('SportWrench')

.controller('OfficialMenuController', OfficialMenuController);

function OfficialMenuController (
    $scope, $rootScope, $state, officialService, officialData, APP_ROUTES, USER_ROLE,
    EVENTS_WHERE_OFFICIAL_PAYOUTS_HIDDEN_FROM_HO
) {
    $scope.official = officialData.official[0] || {};
    $scope.managing = {};
    $scope.season   = officialData.season;

    $scope.showUSAVCodeValidationError = false;

	$scope.tabs = [{
        name        : 'Staff / Official Profile', 
        state       : APP_ROUTES.OF.INFO, 
        isVisible   : _visible, 
        disabled    : _disabled 
    },{ 
        name        : 'Events', 
        state       : APP_ROUTES.OF.EVENTS, 
        isVisible   : officialExists, 
        disabled    : officialMissing
    }, {
        name        : 'Officiating Events Schedule',
        state       : APP_ROUTES.OF.SCHEDULE,
        isVisible   : officialExists,
        disabled    : _disabled
    }, { 
        name        : 'Event Managing', 
        state       : APP_ROUTES.OF.MANAGE_EVENT, 
        isVisible   : showEventManagingTab, 
        disabled    : _disabled,
        stateParams : function () { return { event: $scope.managing.event } }
    }, {
        name        : 'My Staff Events',
        state       : APP_ROUTES.OF.STAFF_EVENTS,
        isVisible   : showMyStaffEventsTab,
        disabled    : _disabled,
    },{
        name        : 'Officials Payouts',
        state       : APP_ROUTES.OF.OFFICIALS_PAYOUTS,
        isVisible   : showOfficialPayoutsTab,
        disabled    : _disabled,
        stateParams : function () { return { event: $scope.managing.event } }
    }];

	$scope.officialRole = USER_ROLE.OFFICIAL;

	function validateSeasonInUSAVCode () {
        $scope.showUSAVCodeValidationError = !officialService.USAVCodeSeasonIsValid(
            $scope.official.usav_num, $scope.season
        );
    }

    function _visible () {
        return true;
    }

    function _disabled () {
        return false;
    }

    function officialExists () {
        return !_.isEmpty($scope.official);
    }

	function officialMissing () {
		return _.isEmpty($scope.official);
	}

	function showMyStaffEventsTab () {
        return $scope.official && $scope.official.has_approved_staff_regs;
    }

    function showEventManagingTab() {
        return $scope.managing.event;
    }

    function showOfficialPayoutsTab() {
        return $scope.managing.event &&
            !EVENTS_WHERE_OFFICIAL_PAYOUTS_HIDDEN_FROM_HO.includes(Number($scope.managing.event));
    }

    $scope.$on('off.event.manage', function (e, eventId) {
        $scope.managing.event = eventId;
    });

    $rootScope.$on('off.reload', function () {
        officialService.officialRes().find().then(function (data) {
            $scope.official = data.official[0] || {};

            validateSeasonInUSAVCode();
        });
    });

    validateSeasonInUSAVCode();
}
