angular.module('SportWrench').component('staffEventsItem', {
    templateUrl: 'official/staff-events/staff-events-item/staff-events-item.html',
    bindings: {
        event: '<',
    },
    controller: ['$state', 'APP_ROUTES', 'moment', Component],
});

function Component($state, APP_ROUTES, moment) {
    this.showEventInfo = () => {
        $state.go(APP_ROUTES.OF.STAFF_EVENTS_INFO, {
            event: this.event.event_id,
        })
    };

    this.showRegistrationInfo = () => {
        $state.go(APP_ROUTES.OF.STAFF_EVENTS_CHECKIN, {
            event: this.event.event_id,
        })
    };

    this.convertToUTC = (date) => {
        return moment.utc(date).toDate();
    };

    this.convertToDate = (date) => {
        return moment(date).toDate();
    }
}
