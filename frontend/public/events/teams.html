<p class="lead pointer"><a ui-state="states.events"><i class="fa fa-chevron-left"></i> {{data.event_name}}</a></p>
<div class="row row-space">
    <div class="col-sm-3">
        <div class="form-group">
            <div class="search-box search-input">
                <sw-searchbox
                    css="search_teams white-ro"
                    input-model="search"
                    placeholder="Search..."
                    reload-time="300"
                    reload="_search()"
                ></sw-searchbox>
            </div>
        </div>
    </div>
    <div class="col-sm-5 center-form-text">
        <div class="form-inline">
            <team-status-picker
                items="entry_statuses"
                on-select="filterStatus('entry', selection)"
                lb="entry"
                picker-name="Entry"
                icon="fa fa-list-alt big-icon"
            ></team-status-picker>
            <team-status-picker
                items="payment_statuses"
                on-select="filterStatus('payment', selection)"
                lb="payment"
                picker-name="Payment"
                icon="fa fa-money big-icon"
            >
            </team-status-picker>
            <team-status-picker
                items="housing_statuses"
                on-select="filterStatus('housing', selection)"
                lb="housing"
                icon="all-teams-icon glyphicon glyphicon-home"
                picker-name="Housing"
                size="middle"
            >
            </team-status-picker>
        </div>
    </div>
    <div class="col-sm-2">
        <button ng-click="exportTeamList()" class="btn btn-default to-right">Export team list</button>
    </div>
    <div class="col-sm-2 center-form-text">
        <a ng-click="toPrevPage();">
            <i class="fa fa-chevron-left"></i>
        </a>
        <span>{{ range.from }} - {{ range.to }}</span>&nbsp;of&nbsp;
        <span class="badge badge-dark">{{total_teams}}</span>
        <a ng-click="toNextPage();">
            <i class="fa fa-chevron-right"></i>
        </a>
    </div>
</div>

<div class="row row-space">
    <div class="col-sm-12">
        <div class="table-responsive">
            <table class="table text-center" sticky-header>
                <thead>
                    <tr>
                        <td class="col-1">
                            <a ng-click="orderTeamsBy('ps');" href="">Payment Status</a>
                            <reverse-arrow show="{{order_by.column == 'ps'}}" reverse="order_by.asc"></reverse-arrow> 
                        </td>
                        <td class="col-2">
                            <a ng-click="orderTeamsBy('es');" href="">Entry Status</a>
                            <reverse-arrow show="{{order_by.column == 'es'}}" reverse="order_by.asc"></reverse-arrow> 
                        </td>
                        <td ng-click="orderTeamsBy('hs')" ng-if="!showReservationsColumns()" class="col-2">
                            <a href="">Housing Status</a>
                            <reverse-arrow show="{{order_by.column === 'hs'}}" reverse="order_by.asc"></reverse-arrow>
                        </td>
                        <td ng-if="showReservationsColumns()" class="pointer" style="width: 40px;" title="Total Tentative (or better) Nights" ng-click="orderTeamsBy('t');">
                            <a href="">T</a>
                            <reverse-arrow show="{{order_by.column == 't'}}" reverse="order_by.asc"></reverse-arrow> 
                        </td>
                        <td ng-if="showReservationsColumns()" class="pointer" style="width: 40px;" title="Total Accepted (or better) Nights" ng-click="orderTeamsBy('a');">
                            <a href="">A</a>
                            <reverse-arrow show="{{order_by.column == 'a'}}" reverse="order_by.asc"></reverse-arrow> 
                        </td>
                        <td ng-if="showReservationsColumns()" class="pointer" style="width: 40px;" title="Total Confirmed Nights" ng-click="orderTeamsBy('c');">
                            <a href="">C</a>
                            <reverse-arrow show="{{order_by.column == 'c'}}" reverse="order_by.asc"></reverse-arrow> 
                        </td>
                        <td ng-if="showReservationsColumns()" class="pointer" style="width: 40px;" title="Original Nights Booked" ng-click="orderTeamsBy('m');">
                           <a href="">O</a>
                           <reverse-arrow show="{{order_by.column == 'm'}}" reverse="order_by.asc"></reverse-arrow> 
                        </td>
                        <td class="col-3">
                            <a ng-click="orderTeamsBy('name');" href="">Team Name</a>
                            <reverse-arrow show="{{order_by.column == 'name'}}" reverse="order_by.asc"></reverse-arrow>
                        </td>
                        <td class="col-3">
                            <a ng-click="orderTeamsBy('division');" href="">Division</a>
                            <reverse-arrow show="{{order_by.column == 'division'}}" reverse="order_by.asc"></reverse-arrow>
                        </td>
                        <td class="col-5">
                            <a ng-click="orderTeamsBy('code');" href="">USAV Code</a>
                            <reverse-arrow show="{{order_by.column == 'code'}}" reverse="order_by.asc"></reverse-arrow>
                        </td>
                        <td class="col-6">
                            <a ng-click="orderTeamsBy('club');" href="">Club Name</a>
                            <reverse-arrow show="{{order_by.column == 'club'}}" reverse="order_by.asc"></reverse-arrow>
                        </td>
                    </tr>
                </thead>
                <tbody class="pointer">
                    <tr ng-repeat="team in teams track by $index" ng-click="showTeamInfo(team)">
                        <td class="col-1" ng-click="$event.stopPropagation()">
                            <span uib-tooltip="{{team.date_paid}}">
                                <status-paid status="{{team.status_paid}}"></status-paid>
                            </span>
                        </td>
                        <td class="col-2" ng-click="$event.stopPropagation()">
                            <span uib-tooltip="{{team.status_date}}">
                                <status-entry status="{{team.status_entry}}"></status-entry>
                            </span>
                        </td>
                        <td ng-if="!showReservationsColumns()" class="col-2" ng-click="$event.stopPropagation()">
                            <span uib-tooltip="{{getStatusHousingTooltip(team)}}">
                                <status-housing status="{{team.status_housing}}"></status-housing>
                            </span>
                        </td>
                        <td ng-if="showReservationsColumns()" ng-bind="team.total_tentative"></td>
                        <td ng-if="showReservationsColumns()" ng-bind="team.total_accepted"></td>
                        <td ng-if="showReservationsColumns()" ng-bind="team.total_confirmed"></td>
                        <td ng-if="showReservationsColumns()" ng-bind="team.max_total_accepted"></td>
                        <td class="col-3" ng-bind="team.team_name"></td>
                        <td class="col-3" ng-bind="team.division"></td>
                        <td class="col-5" ng-bind="team.organization_code"></td>
                        <td class="col-6" ng-bind="team.club_name"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
