angular.module('SportWrench').directive('exhibitorsPurchaserInfo',
    ['eventDashboardService', 'INTERNAL_ERROR_MSG', '$uibModal', PurchaserInfo]
);

function PurchaserInfo (eventDashboardService, INTERNAL_ERROR_MSG, INVALID_FORM_ERROR_MSG) {
	return {
		restrict 		: 'E',
		scope 			: {
			payment 	    : '=',
            openTicketModal : '&'
		},
		require 		: '^exhibitorsTicketsModal',
        templateUrl     : 'sales/tickets/exhibitors/tickets-modal/exhibitors-purchaser-info.html',
		link: function (scope, elem, attrs, ctrl) {
		    scope.showPurchaserInfo = scope.payment.type !== "waitlist";

		    getPurchaseInfo();

            const errorCallback = function (resp) {
                if(resp && resp.data) {
                    const alert = formatErrorAlert(resp);
                    scope.resultAlert.msg = alert.message;
                    scope.resultAlert.type = alert.type;
                }
            }

            scope.getPhoneMask = function () {
                if(scope.payment && scope.payment.phone) {
                    if(scope.payment.phone.length === 11) {
                        return '9 (*************';
                    }
                }

                return '(*************';
            }

			scope.dismissAlertTime = 20000;
			scope.resultAlert = {
				dismissTime: 20 * 1000
			};
			scope.resend = function (type) {
				if((!type || type === 'phone') && !scope.payment.phone) return;
                if((!type || type === 'email') && !scope.payment.email) return;

				ctrl.resendReceipt(type)
				.then(function () {
					scope.resultAlert.msg = 'Successfully sent';
                    scope.resultAlert.type = 'success';
				}, errorCallback)
			}

            scope.onKeypress = function (e) {
                if(e.charCode === 13) {
                    scope.update();
                }
            };

            scope.update = function () {
				ctrl.updatePayerData({
					first 		: scope.payment.first,
                    last 		: scope.payment.last,
                    email 		: scope.payment.email,
                    phone 		: scope.payment.phone,
                    zip 		: scope.payment.zip,
                    additional 	: scope.payment.additional_fields
				}).then(function () {
                    scope.resultAlert.msg = 'Successfully updated';
                    scope.resultAlert.type = 'success';
				}, errorCallback);
            }

            scope.hasAdditionalFields = function () {
                return !_.isEmpty(scope.payment.event_fields)
            }

            scope.openTicketReceipt = function (barcode) {
                scope.openTicketModal({barcode});
            };

            function getPurchaseInfo() {
                if (scope.payment.purchaser_info) {
                    scope.purchaserInfo = scope.payment.purchaser_info;
                } else {
                    const {
                        first,
                        last,
                        email,
                        phone,
                    } = scope.payment;

                    scope.purchaserInfo = { first, last, email, phone };
                }

                scope.ticketsInPayment = scope.payment.tickets_in_payment;
            }
        }
    }

    function formatErrorAlert(resp) {
        const alert = {};
        if(resp.status >= 400 && resp.status < 500) {
            alert.type = 'warning';
            if(resp.data.validation) {
                alert.message = resp.data.validation;
            }
            else if(resp.data.validationErrors) {
                alert.message = resp.data.validationErrors.map(
                    e => e.message
                ).join('\n')
            }
            else {
                alert.message = INVALID_FORM_ERROR_MSG;
            }
        }
        else {
            alert.type = 'danger';
            alert.message = INTERNAL_ERROR_MSG;
        }
        return alert;
    }
}
