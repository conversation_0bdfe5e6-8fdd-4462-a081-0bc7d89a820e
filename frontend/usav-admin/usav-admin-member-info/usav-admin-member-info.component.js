angular.module('SportWrench').component('usavAdminMemberInfo', {
    templateUrl 	: 'usav-admin/usav-admin-member-info/usav-admin-member-info.html',
    bindings        : {
        id: '<',
        role: '<',
        onClose: '&',
    },
    controller 		: UsavAdminMemberInfoController
});

function UsavAdminMemberInfoController(UsavAdminService, UtilsService) {
    this.info = null;
    this.memberRole = null;

    this.$onInit = function () {
        this.memberRole = UtilsService.capitalizeFirstLetter(this.role);

        if (this.role === 'athlete') {
            loadAthleteInfo();
        } else if (this.role === 'staff') {
            loadStaffInfo();
        }
    };

    const loadAthleteInfo = () => {
        UsavAdminService.getAthleteInformation(this.id)
            .then(response => this.info = response);
    };

    const loadStaffInfo = () => {
        UsavAdminService.getStaffInformation(this.id)
            .then(response => this.info = response);
    };

    this.close = function () {
        this.onClose();
    };

    this.isAthlete = function() {
        return this.role === 'athlete';
    };

    this.isStaff = function() {
        return this.role === 'staff';
    }
}

UsavAdminMemberInfoController.$inject = ['UsavAdminService', 'UtilsService'];
