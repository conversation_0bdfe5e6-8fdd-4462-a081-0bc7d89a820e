import "angular-loading-bar/build/loading-bar.min.css" // bower angular-loading-bar@0.7.1 -> npm angular-loading-bar@0.7.1
import "font-awesome/css/font-awesome.css" // bower font-awesome@4.7.0 -> npm font-awesome@4.7.0
import "ng-table/dist/ng-table.css" // bower ng-table@0.5.5 -> npm ng-table@0.5.4
import "angular-toastr/dist/angular-toastr.css" // bower angular-toastr@1.5.0 -> npm angular-toastr@1.5.0
import "ui-select/dist/select.css" // bower angular-ui-select@0.19.8 -> npm ui-select@0.19.8
import "angular-bootstrap-colorpicker/css/colorpicker.min.css" // bower angular-bootstrap-colorpicker@3.0.32 -> npm angular-bootstrap-colorpicker@3.0.32

import "../assets/js/polyfills/babel-polyfill.js"
import "../assets/js/polyfills/array-includes.js"
import "../assets/js/polyfills/string-includes.js"

import 'angular-sanitize' // bower angular-sanitize@1.2.32 -> npm angular-sanitize@1.2.32
import 'angular-ui-router' // bower angular-ui-router@0.3.1 -> npm angular-ui-router@0.3.1
import 'ngstorage' // bower ngstorage@0.3.11 -> npm ngstorage@0.3.11
import "../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js"
import "angular-ui-utils/modules/mask/mask.js" // bower angular-ui-utils @0.1.1 -> npm angular-ui-utils@0.1.1
import "oclazyload" // bower oclazyload@0.4.2 -> npm oclazyload@0.4.2
import 'angular-loading-bar' // bower angular-loading-bar@0.7.1 -> npm angular-loading-bar@0.7.1
import 'sticky-table-headers' // bower StickyTableHeaders@0.1.24 -> npm sticky-table-headers@0.1.24
import 'ng-table/dist/ng-table.js' // bower ng-table@0.5.5 -> npm ng-table@0.5.4
import 'angular-clipboard' // bower angular-clipboard@1.7.0 -> npm angular-clipboard@1.6.2
import 'ng-infinite-scroll/build/ng-infinite-scroll.js'; // bower ngInfiniteScroll@1.0.0 -> npm ng-infinite-scroll@1.0.0
import "bootstrap-ui-datetime-picker" // bower bootstrap-ui-datetime-picker@2.4.3 -> npm bootstrap-ui-datetime-picker@2.4.3
import 'angular-animate' // bower angular-animate@1.3.20 -> npm angular-animate@1.3.20
import 'angular-toastr' // bower angular-toastr@1.5.0 -> npm angular-toastr@1.5.0
import 'ui-select' // bower angular-ui-select@0.19.8 -> npm ui-select@0.19.8
import 'angular-bootstrap-colorpicker' // bower angular-bootstrap-colorpicker@3.0.32 -> npm angular-bootstrap-colorpicker@3.0.32
import 'angularjs-dropdown-multiselect'; // bower angularjs-dropdown-multiselect@2.0.0-beta.10 -> npm angularjs-dropdown-multiselect@2.0.0-beta.10
import "../assets/js/signature_pad/signature_pad.js"
import 'angular-simple-logger' // bower angular-simple-logger@0.1.7 -> npm angular-simple-logger@0.1.7
