import "angular-loading-bar/build/loading-bar.min.css" //import "../assets/bower_components/angular-loading-bar/build/loading-bar.min.css"
import "font-awesome/css/font-awesome.css" //import "../assets/bower_components/font-awesome/css/font-awesome.css"
import "ng-table/dist/ng-table.css" // import "../assets/bower_components/ng-table/dist/ng-table.css"
import "angular-toastr/dist/angular-toastr.css" // import "../assets/bower_components/angular-toastr/dist/angular-toastr.css"
import "ui-select/dist/select.css" // import "../assets/bower_components/angular-ui-select/dist/select.css"
import "angular-bootstrap-colorpicker/css/colorpicker.min.css" // import "../assets/bower_components/angular-bootstrap-colorpicker/css/colorpicker.min.css"

import "../assets/js/polyfills/babel-polyfill.js"
import "../assets/js/polyfills/array-includes.js"
import "../assets/js/polyfills/string-includes.js"

import 'angular-sanitize' // import "../assets/bower_components/angular-sanitize/angular-sanitize.min.js"
import 'angular-ui-router' // import "../assets/bower_components/angular-ui-router/release/angular-ui-router.min.js"
import 'ngstorage' // import "../assets/bower_components/ngstorage/ngStorage.min.js"
import "../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js"
import "../assets/bower_components/angular-ui-utils/mask.min.js"
import "../assets/bower_components/oclazyload/dist/ocLazyLoad.min.js" // import 'oclazyload' !!!!!!
import 'angular-loading-bar' // import "../assets/bower_components/angular-loading-bar/build/loading-bar.min.js"
import 'sticky-table-headers' // import "../assets/bower_components/StickyTableHeaders/js/jquery.stickytableheaders.min.js" !!!
import 'ng-table/dist/ng-table.js' // import "../assets/bower_components/ng-table/dist/ng-table.min.js"
import 'angular-clipboard' // import "../assets/bower_components/angular-clipboard/angular-clipboard.js"
import "../assets/bower_components/ngInfiniteScroll/build/ng-infinite-scroll.min.js" // import 'ng-infinite-scroll' // !!!!
import "../assets/bower_components/bootstrap-ui-datetime-picker/dist/datetime-picker.js" // !!!!
import 'angular-animate' // import "../assets/bower_components/angular-animate/angular-animate.min.js"
import 'angular-toastr' // import "../assets/bower_components/angular-toastr/dist/angular-toastr.tpls.min.js"
import 'ui-select' // import "../assets/bower_components/angular-ui-select/dist/select.js"
import 'angular-bootstrap-colorpicker' // import "../assets/bower_components/angular-bootstrap-colorpicker/js/bootstrap-colorpicker-module.min.js"
import 'angularjs-dropdown-multiselect'; // import "../assets/bower_components/angularjs-dropdown-multiselect/dist/angularjs-dropdown-multiselect.min.js"
import "../assets/js/signature_pad/signature_pad.js"
import 'angular-simple-logger' // import "../assets/bower_components/angular-simple-logger/dist/angular-simple-logger.min.js"
