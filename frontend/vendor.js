import "../assets/bower_components/angular-loading-bar/build/loading-bar.min.css"
import "../assets/bower_components/angular/angular-csp.css"
import "../assets/bower_components/font-awesome/css/font-awesome.css"
import "../assets/bower_components/ng-table/dist/ng-table.css"
import "../assets/bower_components/angular-toastr/dist/angular-toastr.css"
import "../assets/bower_components/angular-ui-select/dist/select.css"
import "../assets/bower_components/angular-bootstrap-colorpicker/css/colorpicker.min.css"

import "../assets/js/polyfills/babel-polyfill.js"
import "../assets/js/polyfills/array-includes.js"
import "../assets/js/polyfills/string-includes.js"
import "../assets/js/signature_pad/signature_pad.js"

import "../assets/bower_components/angular-sanitize/angular-sanitize.min.js"
import "../assets/bower_components/angular-ui-router/release/angular-ui-router.min.js"
import "../assets/bower_components/ngstorage/ngStorage.min.js"
import "../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js"
import "../assets/bower_components/angular-ui-utils/mask.min.js"
import "../assets/bower_components/oclazyload/dist/ocLazyLoad.min.js"
import "../assets/bower_components/angular-loading-bar/build/loading-bar.min.js"
import "../assets/bower_components/StickyTableHeaders/js/jquery.stickytableheaders.min.js"
import "../assets/bower_components/ng-table/dist/ng-table.min.js"
import "../assets/bower_components/angular-clipboard/angular-clipboard.js"
import "../assets/bower_components/ngInfiniteScroll/build/ng-infinite-scroll.min.js"
import "../assets/bower_components/bootstrap-ui-datetime-picker/dist/datetime-picker.js"
import "../assets/bower_components/angular-animate/angular-animate.min.js"
import "../assets/bower_components/angular-toastr/dist/angular-toastr.tpls.min.js"
import "../assets/bower_components/angular-ui-select/dist/select.js"
import "../assets/bower_components/angular-bootstrap-colorpicker/js/bootstrap-colorpicker-module.min.js"
import 'angularjs-dropdown-multiselect'; // import "../assets/bower_components/angularjs-dropdown-multiselect/dist/angularjs-dropdown-multiselect.min.js"
import "../assets/bower_components/angular-simple-logger/dist/angular-simple-logger.min.js"


// /**
//  * Vendor Dependencies for Frontend
//  * This file imports all vendor dependencies to create vendor bundles
//  */
//
// // Core AngularJS
// import 'angular';
// import 'angular-animate';
// import 'angular-cookies';
// import 'angular-resource';
// import 'angular-route';
// import 'angular-sanitize';
//
// // UI and Routing
// import 'angular-ui-router';
// import 'angular-ui-bootstrap';
//
// // UI Components
// import 'ng-table/dist/ng-table.js';
// import 'angular-toastr';
// import 'ui-select';
// import 'angular-bootstrap-colorpicker';
// import 'angularjs-dropdown-multiselect';
//
// // Utility Libraries
// import 'jquery';
// import 'underscore';
// import 'moment';
// import 'moment-timezone';
//
// // Additional Angular modules
// import 'angular-file-upload';
// import 'angular-clipboard';
// import 'angular-loading-bar';
// import 'ngstorage';
// import 'oclazyload';
// import 'angular-simple-logger';
// import 'ng-infinite-scroll';
//
// // Vendor CSS
// import 'font-awesome/css/font-awesome.css';
// // import 'angular-loading-bar/build/loading-bar.min.css';
// // import 'ng-table/dist/ng-table.css';
// // import 'angular-toastr/dist/angular-toastr.css';
// // import 'ui-select/dist/select.css';
// // import 'angular-bootstrap-colorpicker/css/colorpicker.min.css';
