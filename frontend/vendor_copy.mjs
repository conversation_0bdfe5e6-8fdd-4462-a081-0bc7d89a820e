/**
 * Vendor Dependencies for Frontend
 * This file imports all vendor dependencies following the EXACT original order from assets/index.html
 * Each npm import is followed by its commented bower equivalent for easy debugging
 */

// ===== CSS IMPORTS (in original order) =====

// 1. Angular Loading Bar CSS
import "angular-loading-bar/build/loading-bar.min.css"
// import "../assets/bower_components/angular-loading-bar/build/loading-bar.min.css"

// 2. Angular CSP CSS (no npm equivalent)
// import "../assets/bower_components/angular/angular-csp.css"

// 3. Font Awesome CSS
import "font-awesome/css/font-awesome.css"
// import "../assets/bower_components/font-awesome/css/font-awesome.css"

// 4. ng-table CSS
import "ng-table/dist/ng-table.css"
// import "../assets/bower_components/ng-table/dist/ng-table.css"

// 5. Angular Toastr CSS
import "angular-toastr/dist/angular-toastr.css"
// import "../assets/bower_components/angular-toastr/dist/angular-toastr.css"

// 6. Angular UI Select CSS
import "ui-select/dist/select.css"
// import "../assets/bower_components/angular-ui-select/dist/select.css"

// 7. Angular Bootstrap Colorpicker CSS
import "angular-bootstrap-colorpicker/css/colorpicker.min.css"
// import "../assets/bower_components/angular-bootstrap-colorpicker/css/colorpicker.min.css"

// ===== POLYFILLS AND CUSTOM SCRIPTS (keep as-is) =====

// Polyfills (keep original paths)
import "../assets/js/polyfills/babel-polyfill.js"
import "../assets/js/polyfills/array-includes.js"
import "../assets/js/polyfills/string-includes.js"

// Custom UI Bootstrap (keep original path)
import "../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js"

// ===== JAVASCRIPT IMPORTS (in original order from assets/index.html) =====

// Core dependencies first (from original HTML script order)
// jQuery (foundation library)
// import 'jquery'
// import "../assets/bower_components/jquery/jquery.min.js"

// Underscore (utility library)
// import 'underscore'
// import "../assets/bower_components/underscore/underscore-min.js"

// Angular Core
// import 'angular'
// import "../assets/bower_components/angular/angular.min.js"

// 1. Angular Sanitize
// import 'angular-sanitize'
import "../assets/bower_components/angular-sanitize/angular-sanitize.min.js"

// 2. Angular UI Router
// import 'angular-ui-router'
import "../assets/bower_components/angular-ui-router/release/angular-ui-router.min.js"

// 3. ngStorage
// import 'ngstorage'
import "../assets/bower_components/ngstorage/ngStorage.min.js"

// Custom UI Bootstrap (keep original path)
// import "../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js" // Already imported above

// Moment.js (date/time library)
// import 'moment'
// import "../assets/bower_components/moment/min/moment.min.js"

// 4. Angular UI Utils (mask) - keeping bower version (deprecated on npm)
import "../assets/bower_components/angular-ui-utils/mask.min.js"

// 5. ocLazyLoad
// import 'oclazyload'
import "../assets/bower_components/oclazyload/dist/ocLazyLoad.min.js"

// 6. Angular Loading Bar
// import 'angular-loading-bar'
import "../assets/bower_components/angular-loading-bar/build/loading-bar.min.js"

// 7. StickyTableHeaders
// import 'sticky-table-headers'
import "../assets/bower_components/StickyTableHeaders/js/jquery.stickytableheaders.min.js"

// 8. ng-table
// import 'ng-table/dist/ng-table.js'
import "../assets/bower_components/ng-table/dist/ng-table.min.js"

// 9. Angular Clipboard
// import 'angular-clipboard'
import "../assets/bower_components/angular-clipboard/angular-clipboard.js"

// 10. ngInfiniteScroll
// import 'ng-infinite-scroll'
import "../assets/bower_components/ngInfiniteScroll/build/ng-infinite-scroll.min.js"

// 11. Bootstrap UI DateTime Picker - keeping bower version (not available on npm)
import "../assets/bower_components/bootstrap-ui-datetime-picker/dist/datetime-picker.js"

// 12. Angular Animate
// import 'angular-animate'
import "../assets/bower_components/angular-animate/angular-animate.min.js"

// 13. Angular Toastr
// import 'angular-toastr'
import "../assets/bower_components/angular-toastr/dist/angular-toastr.tpls.min.js"

// 14. Angular UI Select
// import 'ui-select'
import "../assets/bower_components/angular-ui-select/dist/select.js"

// 15. Angular Bootstrap Colorpicker
// import 'angular-bootstrap-colorpicker'
import "../assets/bower_components/angular-bootstrap-colorpicker/js/bootstrap-colorpicker-module.min.js"

// 16. AngularJS Dropdown Multiselect
import 'angularjs-dropdown-multiselect'
// import "../assets/bower_components/angularjs-dropdown-multiselect/dist/angularjs-dropdown-multiselect.min.js"

// 17. Angular Simple Logger
// import 'angular-simple-logger'
import "../assets/bower_components/angular-simple-logger/dist/angular-simple-logger.min.js"
