angular.module('SportWrench').directive('spinner', [function(){
    return {
        restrict: 'E',
        scope: {
            active      : '&',
            size        : '@?',
            pos         : '@?',
            isInline    : '&?'
        },
        replace: true,
        templateUrl: 'components/spinner/spinner.html',
        link: function(scope, elem, attrs) {
            scope.inline = function () {
                if(attrs.isInline && scope.isInline())
                    return 'inline-block'
            }
        }
    };
}]);
