angular.module('SportWrench').controller('CardPaymentsController', CardPaymentsController);

function CardPaymentsController ($scope, $http, $uibModal) {
    $scope.data = {
        events: $scope.$parent.events,
        payments: [],
        stop_checking: false,
        checking_running: false
    };

    var _events_watch = $scope.$watch(function () {
        return $scope.$parent.events
    }, function (newVal, oldVal) {
        if(newVal !== oldVal) {
            $scope.data.events = newVal;
            _events_watch();
        }        
    });

    $scope.load_payments = function () {
        var url = '/api/admin/payments?' +
                        $.param({event: $scope.data.selected_event, type: 'card'});   
        $http({
            method: 'GET',
            url: url
        }).success(function (data) {
            $scope.data.payments = data.payments;
        })
    }

    $scope.stop_queries = function () {
        $scope.data.stop_checking = true;
    }

    $scope.open_errors_modal = function (payment) {
        console.log(payment);
        $uibModal.open({
            templateUrl: 'public/payments/payment.errors.modal.html',
            controller: function ($scope, $uibModalInstance, $http) {
                $scope.payment = payment;

                $scope.closeModal = function () {
                    $uibModalInstance.close();
                }    

                $scope.formatJson = function (data) {
                    var _res;
                    try {
                        _res = JSON.stringify(data, null, ' ');
                    } catch (e) {
                        _res = data
                    } finally {
                        return _res;
                    }
                }            
            }
        })
    }

    $scope.run_check = function () {
        $scope.data.checking_running = true;
        $scope.data.stop_checking = false;
      
      _check_payment(0, function () {
        $scope.data.checking_running = false;
      });

      function _check_payment (index, cb) {
        if(index >= $scope.data.payments.length)
            return cb();
        if($scope.data.stop_checking)
            return cb();
        $http.get('/api/admin/payment/' + $scope.data.payments[index].id + '/card/check')
            .success(function (data) {
                if(data.errors && data.errors.length > 0) {
                    $scope.data.payments[index].errors = data.errors;
                }                
            }).error(function () {
                console.log('tratata');
            }).finally(function () {
                $scope.data.payments[index].checked = true;
                _check_payment(++index, cb);
            });
      }
    }

    $scope.$on('$destroy', function() {
        $scope.data.stop_checking = true;
    });
}
