angular.module('SportWrench').component('clubTeamsList', {
    templateUrl : 'components/club-teams/club-teams.html',
    bindings 	: {
        teams: '<'
    },
    controller : ClubTeamsListController
});

function ClubTeamsListController() {

    this.$onChanges = function (changes) {
        if(changes.teams && changes.teams.currentValue !== changes.teams.previousValue) {
            this.teams = changes.teams.currentValue;
        }
    }
}
