angular.module('SportWrench')

.filter("LCdate",function(){
    return function (date, format, parse) {
        if(!date) {
            return;
        }
        parse = parse || "";
        format = format || "";
        var result = moment(date).format(format);
        if (result === "Invalid date" && isNaN(+date) === false) {
        	var result = moment(+date).format(format);
        }
        return result;
    };
});
