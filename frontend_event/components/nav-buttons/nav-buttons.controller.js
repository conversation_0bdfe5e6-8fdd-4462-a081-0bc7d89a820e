angular.module('SportWrench')

.controller('NavButtonsCtrl', function($rootScope, $scope, $state, $stateParams, $localStorage) {
    $scope.toState = function (name) {
        if ($stateParams.event) {
            $state.go(name, { event: $stateParams.event });
        } else if ($localStorage.events && $localStorage.events.length && $localStorage.lastEventName) {
            for (var i = $localStorage.events.length - 1; i >= 0; i--) {
                if ($localStorage.events[i].name == $localStorage.lastEventName) {
                    $state.go(name, { event: $localStorage.events[i].event_id });
                    break;
                }
            };
        } else {
            $state.go('events');
        }
    }
});
