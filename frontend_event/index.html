<!doctype html>
<html class="no-js" xmlns="http://www.w3.org/1999/html" data-ng-app="SportWrench">
<head>
    <meta charset="utf-8"/>
    <title>SportWrench - The Home of Sporting Events</title>
    <meta name="description" content="SportWrench - The Home of Sporting Events"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1">
    <link rel="stylesheet" href="bower_components/angular/angular-csp.css" />
    <link rel="stylesheet" href="bower_components/font-awesome/css/font-awesome.css" />
    <link rel="stylesheet" href="bower_components/angular-loading-bar/build/loading-bar.min.css" />
    <link rel="stylesheet" href="bower_components/angular-toastr/dist/angular-toastr.css" />
    <link rel="stylesheet" href="main.css"/>
</head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-16KVVKWFYJ"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-16KVVKWFYJ', {'send_page_view': false});
</script>
<body data-ng-controller="MainController" ng-cloak ng-class="{'inin':true}">
    <div ng-class="{'navbar': true, 'navbar-default': true, 'navbar--m0':true, 'navbar-fixed-top':false}">
        <div class="container">
            <div class="navbar-header">
                <button type="button" class="mobile-menu navbar-toggle" data-toggle="collapse" data-target=".navbar-ex1-collapse" ng-click="isCollapsed = !isCollapsed">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <div class="esw-state">
                    <a ng-click="goBack()" class="btn btn-default"><i class="fa fa-chevron-left"></i></a>
                    <ui-breadcrumbs class="sw_header_breadcrumbs"
                        displayname-property="data.displayName"
                        template-url="components/breadcrumbs/uiBreadcrumbs.html"
                        abstract-proxy-property="data.breadcrumbProxy">
                    </ui-breadcrumbs>
                    <a ng-click="openFavorites()" class="btn btn-default" style="float:left; margin-right:5px;"><i ng-class="{'red-star': !favorites.length}" class="fa fa-star"></i></a>
                    <div class="tap-star" ng-if="!favorites.length">Tap <i class="fa fa-star red-star"></i> to save page.</div>
                </div>
            </div>
            <nav collapse="isCollapsed" class="collapse navbar-collapse navbar-ex1-collapse">
                <ul class="nav navbar-nav navbar-right" ng-controller="NavButtonsCtrl">
                    <li>
                        <a ng-click="toState('events.event.schedule')" ng-show="$root.isPrivateLink()" ui-sref-active="active"><i class="fa fa-calendar"></i> Court Grid</a>
                    </li>
                    <li>
                        <a ng-click="toState('events.event.clubs')" ng-show="$root.isPrivateLink() && $root.hasClubs()" ui-sref-active="active"><i class="fa fa-users"></i> Clubs</a>
                    </li>
                    <li>
                        <a ng-click="toState('events.event.divisions')" ng-show="$root.isPrivateLink()" ui-sref-active="active"><i class="fa fa-th"></i> Divisions</a>
                    </li>
                    <li>
                        <a ui-sref="events" ui-sref-active="active"><i class="fa fa-search"></i> Events</a>
                    </li>
                    <li>
                        <a href="{{HOME_PAGE_URL}}/#/"><i class="fa fa-home"></i> SW Home</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div 
        class="block-height-min" ng-class="{'container': isNotFullPage(), 'container bracket-container': isPoolBracketPage()}"
        ui-view="rootView"
        autoscroll="true"
        >
    </div>

    <confirmation visible="{{$root.confirm.getVisibility()}}" message="{{$root.confirm.getMessage()}}" agree="$root.confirm.confirm()" cancel="$root.confirm.cancel()" class="notification-holder"></confirmation>

    <div ng-controller="notificationCtrl" class="notification-holder">
        <alert ng-repeat="notification in notifications" type="{{notification.type}}" close="closeNotification($index)">{{notification.message}}</alert>
    </div>
        
    <footer class="container-fluid">
        <div class="container">
            <div class="copyright pull-left">&copy; SportWrench Inc. {{year}}. All rights reserved</div>
        </div>
    </footer>

    <script src="bower_components/underscore/underscore-min.js"></script>
    <script src="bower_components/jquery/jquery.js"></script>
    <script src="js/jquery-ui/jquery-ui.min.js"></script>
    <script src="bower_components/angular/angular.js"></script>
    <script src="bower_components/angular-resource/angular-resource.min.js"></script>
    <script src="bower_components/angular-sanitize/angular-sanitize.js"></script>
    <script src="bower_components/angular-cookies/angular-cookies.js"></script>
    <script src="bower_components/angular-ui-router/release/angular-ui-router.js"></script>
    <script src="bower_components/ngstorage/ngStorage.js"></script>
    <script src="bower_components/angular-bootstrap/ui-bootstrap-tpls.js"></script>
    <script src="bower_components/angular-utils-ui-breadcrumbs/uiBreadcrumbs.js"></script>
    <script src="bower_components/moment/min/moment.min.js"></script>
    <script src="bower_components/angular-loading-bar/build/loading-bar.min.js"></script>
    <script src="bower_components/angular-dragdrop/src/angular-dragdrop.min.js"></script>
    <script src="bower_components/ngSelectable/src/ngSelectable.js"></script>
    <script src="bower_components/angular-clipboard/angular-clipboard.js"></script>
    <script src="bower_components/angular-toastr/dist/angular-toastr.tpls.min.js"></script>
    <script src="js/validation/angular-validation.js"></script>
    <script src="js/validation/angular-validation-rule.js"></script>
    <script src="main.js"></script>
</body>
</html>
