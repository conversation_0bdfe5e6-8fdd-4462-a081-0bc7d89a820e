angular.module('SportWrench')

.controller('MainController', MainController);

function MainController(
    $scope, $window, $state, $stateParams, $sce, $localStorage, UtilsService,
    $rootScope, $location, $anchorScroll, $timeout, $modal, eswService, HOME_PAGE_URL
) {
    $scope.breadCrumbs = {};
    $scope.isCollapsed = true;
    $scope.showClubsMenu = true;
    $scope.year = new Date().getUTCFullYear();
    var prevState = {
        from: {},
        fromParams: {}
    };

    $rootScope.$storage = $localStorage.$default({
        favorites: []
    });

    $rootScope.$stateParams = $stateParams;
    $rootScope.$state = $state;

    $scope.favorites = $rootScope.$storage.favorites;

    $rootScope.courtFilters = {
        event_id: null,
        event_day: null,
        start_hour: 0,
        hours_count: 3,
        division: null
    };

    $rootScope.openFavorites = function() {
        $modal.open({
            templateUrl: 'public/favorites/favorites.html',
            controller: 'Public.Events.Favorites'
        });
    };

    $rootScope.goBack = function() {
        $window.history.back();
        $state.reload();
    };

    $scope.$on('$stateChangeSuccess', function ($event, to, toParams, from, fromParams) {
        prevState = {
            from: from,
            fromParams: fromParams
        };
        $scope.isCollapsed = true;
    });

    $rootScope.isPoolBracketPage = function () {
        if ($window.innerWidth > 500) {
            return false;
        }

        return $state.current.name.indexOf('brackets') != -1 || 
               $state.current.name.indexOf('pooldetails') != -1;
    };

    $scope.isEventAvailable = function () {
        return $stateParams.event ? true : false;//$localStorage.lastEventName ? true : false;
    };

    $scope.sheduleAvailable = function() {
        try {
            return $localStorage.lastEvent.schedule_published ? true : false;
        } catch (e) {
            return false;
        }
    };

    $scope.clubMenuAvailable = function () {
        return $scope.isEventAvailable() && $scope.showClubsMenu;
    }

    $scope.scrollTo = function(id) {
        console.log(id);
        $location.url('/#' + id);
        $anchorScroll();
    }

    $scope.bcClick = function(state) {
        $state.go(state, {event: $stateParams.event}, {
            reload: true,
            inherit: true,
            notify: true
        });
    }

    $scope.isNotFullPage = function () {
        var fullPagesList = ['events.event.schedule'];
        return fullPagesList.indexOf($state.current.name) === -1;
    }

    $scope.HTMLTrust = function(expression) {
        return $sce.trustAsHtml(expression);
    }

    $rootScope.$on('eventDataLoaded', function (e, eventData) {
        if(eventData && eventData.registration_method !== 'club') {
            $scope.showClubsMenu = false;
        } else {
            $scope.showClubsMenu = true;
        }

        if(eventData && eventData.tickets_published) {
            $scope.showTicketsLink = true;
            $scope.ticketsLink = UtilsService.getTicketsDirectLink(eventData.tickets_code);
        }
    });

    $scope.modalAlert = {};

    $rootScope.HOME_PAGE_URL = HOME_PAGE_URL;

    $rootScope.confirmation = function(msg, agree, cancel, visibility) {
        var _confirm = function() {
            _showConfirmDialog = false;
            if(agree) {
                agree();
            }
        };
        var _abort = function() {
            _showConfirmDialog = false;
            if(cancel) {
                cancel();
            }
        };
        var _showConfirmDialog = visibility || true;
        var _message = $sce.trustAsHtml(msg);

        $rootScope.confirm = {
            show: function() {
                _showConfirmDialog = true;
            },
            hide: function() {
                _showConfirmDialog = false;
            },
            confirm: function() {
                _confirm();
            },
            cancel: function() {
                _abort();
            },
            getMessage: function() {
                return _message;
            },
            getVisibility: function() {
                return _showConfirmDialog;
            }
        }
    }
    $rootScope.confirm = {};

    $rootScope.isPrivateLink = function () {
        return eswService.isESWId($stateParams.event);
    }
    $rootScope.hasClubs = function() {
        return $rootScope.currentEvent && $rootScope.currentEvent.has_clubs;
    }
}
