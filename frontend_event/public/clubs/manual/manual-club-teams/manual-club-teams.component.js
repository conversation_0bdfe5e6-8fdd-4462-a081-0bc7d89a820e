angular.module('SportWrench').component('manualClubTeams', {
    templateUrl : 'public/clubs/manual/manual-club-teams/manual-club-teams.html',
    controller : ManualClubTeamsController
});

ManualClubTeamsController.$inject = ['$rootScope', 'ManualClubTeamsService', '$stateParams'];

function ManualClubTeamsController($rootScope, ManualClubTeamsService, $stateParams) {
    this.teams = undefined;

    this.$onInit = function () {
        let eventID = $stateParams.event;
        let clubName = $stateParams.club;

        this.clubName = clubName;

        ManualClubTeamsService.getTeams(eventID, clubName).then(({teams}) => this.teams = teams);
    }
}
