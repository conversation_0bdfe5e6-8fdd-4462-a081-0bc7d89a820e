angular.module('SportWrench')

.controller('Public.Events.TeamRosterController',
    ['$scope', '$rootScope', '$stateParams', 'eswService', 'currentTeam', '$state', 'SANCTIONING_BODY', TeamRosterController]);

function TeamRosterController($scope, $rootScope, $stateParams, eswService, currentTeam, $state, SANCTIONING_BODY) {
    $scope.team = currentTeam;

    $rootScope.pageTitle = "Team - " + currentTeam.team_name + " Roster";
    $scope.title = currentTeam.team_name;

    const sportSanctioning = SANCTIONING_BODY[$rootScope.currentEvent.sport_sanctioning];

    $scope.eventHasAAUSanctioning = sportSanctioning === SANCTIONING_BODY.AAU;
}
