const TeamsPaymentSessionService = require("../../api/services/payment/teams/_TeamsPaymentSessionService");

const {PAYMENT_SESSION_STATUS} = require("../../api/constants/payments");
const {STATUS: PAYMENT_INTENT_STATUS} = require("../../api/constants/stripe/payment-intent");

module.exports.run = async function run() {
    const pendingPaymentSessions = await getPendingPaymentSessions();

    await Promise.all(pendingPaymentSessions.map(async ({provider_payment_intent_id, user_id}) => {
        const paymentSessionIsDeletable = await isPaymentSessionDeletable(provider_payment_intent_id);

        if(paymentSessionIsDeletable) {
            await TeamsPaymentSessionService.stripe.removePaymentSession({
                provider_payment_intent_id,
                user_id,
                tr: Db,
            })
        } else {
            loggers.errors_log.error(`Payment session with payment intent ${provider_payment_intent_id} was not removed after 30 minutes.`);
        }
    }))
}

async function getPendingPaymentSessions() {
    const query = knex('payment_session as ps')
        .select({
            user_id: 'ps.user_id',
            provider_payment_intent_id: 'ps.provider_payment_intent_id',
        })
        .where(knex.raw(`ps.created < (NOW() - INTERVAL '30 min')`))
        .where('ps.status', PAYMENT_SESSION_STATUS.PENDING);

    const {rows = []} = await Db.query(query);

    return rows;
}

async function isPaymentSessionDeletable(paymentIntentId) {
    try {
        const paymentIntent = await StripeService.paymentCard.stripeService.getPaymentIntent(paymentIntentId);

        if (_.isEmpty(paymentIntent)) {
            return false;
        }

        const notDeletableStatuses = [PAYMENT_INTENT_STATUS.PROCESSING, PAYMENT_INTENT_STATUS.SUCCEEDED];

        return !notDeletableStatuses.includes(paymentIntent.status);
    } catch (error) {
        return false;
    }
}
