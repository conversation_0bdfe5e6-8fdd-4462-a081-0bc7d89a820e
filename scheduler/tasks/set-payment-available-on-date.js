'use strict';

/**
 * covered 😄👍
 * 
 * The script finds the date a Stripe-charge will become available on the Stripe Balance and sets 
 * the date to a corresponding "puchase" row ("stripe_balance_available" column)
 *
 * Algorithm: 
 * 1. Find "purchase" rows for paid Stripe-charges with "puchase"."stripe_balance_available" = NULL
 *      (NOTE: only current-season purchase via Stripe Connect) 
 * 2. Make Stripe-API request to retrive the availability date 
 * 3. Set the date to "puchase"."stripe_balance_available"
 * 4. Repeat from the 1st step until all the rows have "puchase"."stripe_balance_available" set
 *
 * Detailed research on how to retrieve the availability date:
 *  https://docs.google.com/document/d/1jraYIeFE7UdogGUZswBFTKFSGEVVxn53X_bNaGsCD88/edit#heading=h.uuwptzrstk6m
 */

const co        = require('co');
const assert    = require('assert');

const { current: SW_SEASON } = require('../../config/sw_season').sw_season;

function findAndSetAvailableOnDate (purchase) {
    return Promise.resolve().then(() => {

        let { charge_id: chargeID } = purchase;

        return StripeService.webhook.charge.getChargeAvailableOnDate(chargeID)
        .then(availableOnDate => {

            assert(Number.isInteger(availableOnDate) && availableOnDate > 0, 
                                                'Expecting "availableOnDate" to be an integer');

            return PaymentService.setPaymentAvailableOnDate(chargeID, availableOnDate);

        })
    });
}

/**
 * Retrieve the date the season starts, e.g.:
 *
 * season = 2018
 * yyyy = 2018-1=2017
 * min date = yyyy-09-01 = 2017-09-01
 * 
 */
function getSeasonStartDate () {
    return `${SW_SEASON - 1}-09-01`;
}

function run () {
    return co(function* () {

        const MIN_DATE      = getSeasonStartDate();
        /**
         * Stripe API allows to make ~100 req. per sercond
         */
        const ROWS_LIMIT = 50;

        let totalUpdatedRowsQty = 0;

        let offset = 0;

        while (true) {
            let purchaseRows = 
               yield PaymentService.findPaymentsToSetAvailability(MIN_DATE, ROWS_LIMIT, offset); /* jshint ignore:line */

            if (purchaseRows.length === 0) {
                break;
            }

            let res = yield Promise.all(purchaseRows.map(findAndSetAvailableOnDate)); /* jshint ignore:line */
            /* [n, n, n] -> ∑n */
            let updatedRows = res.reduce((sum, updQty) => (sum + updQty), 0);

            totalUpdatedRowsQty += updatedRows;

            offset += ROWS_LIMIT;
        }

        return totalUpdatedRowsQty;
    });
}

module.exports.run = run;
