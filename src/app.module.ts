import { ExpressAdapter } from '@bull-board/express';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullModule as BullMQModule } from '@nestjs/bullmq';
import { Module, ValidationPipe } from '@nestjs/common';
import { APP_PIPE } from '@nestjs/core';
import { FirebaseModule } from 'libs/nestjs/firebase/firebase.module';

import { AuthModule } from './auth/auth.module';
import { FirebaseConfig } from './config/firebase.config';
import { RedisConfig } from './config/redis.config';
import { MOBILE, SCHEDULER } from './constants/constants';
import { ApiModule } from './core/api/api.module';
import { BullModule } from './core/bull/bull.module';
import { CourtModule } from './core/court/court.module';
import { EventsModule } from './core/event/event.module';
import { MatchModule } from './core/match/match.module';
import { ParsingModule } from './core/parsing/parsing.module';
import { ParsingRuleModule } from './core/parsing-rule/parsing-rule.module';
import { ParsingTemplateModule } from './core/parsing-template/parsing-template.module';
import { ScheduleModule } from './core/schedule/schedule.module';
import { SchedulerModule } from './core/scheduler/scheduler.module';
import { SchedulerLogModule } from './core/scheduler-log/scheduler-log.module';
import { TeamModule } from './core/team/team.module';
import { WebScraperModule } from './core/web-scraper/web-scraper.module';
import { PrismaModule } from './prisma';
import { ServicesModule } from './services/services.module';

@Module({
  imports: [
    EventsModule,
    ApiModule,
    ServicesModule,
    SchedulerModule,
    ParsingModule,
    BullMQModule.forRootAsync({
      useFactory: () => ({
        connection: {
          host: RedisConfig.REDIS_HOST,
          port: RedisConfig.REDIS_PORT,
          password: RedisConfig.REDIS_PASSWORD,
        },
      }),
    }),
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter,
    }),
    FirebaseModule.forRoot({
      keyFilename: FirebaseConfig.SCHEDULER_FIREBASE_CONFIG_FILEPATH,
      databaseUrl: FirebaseConfig.SCHEDULER_FIREBASE_DATABASE_URL,
      instanceName: SCHEDULER,
    }),
    FirebaseModule.forRoot({
      keyFilename: FirebaseConfig.MOBILE_FIREBASE_CONFIG_FILEPATH,
      databaseUrl: FirebaseConfig.MOBILE_FIREBASE_DATABASE_URL,
      instanceName: MOBILE,
    }),
    TeamModule,
    MatchModule,
    CourtModule,
    PrismaModule,
    SchedulerLogModule,
    AuthModule,
    ScheduleModule,
    WebScraperModule,
    BullModule,
    ParsingTemplateModule,
    ParsingRuleModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        // disableErrorMessages: true,
        transform: true,
        whitelist: true,
      }),
    },
  ],
})
export class AppModule {}
