import { ApiConfig, ServerConfig } from '../config';

export const API_ROUTES = {
  UA: {
    POST_SCHEDULE: `${ApiConfig.API_BASE_URL}/ua-api/${
      ServerConfig.PROJECT_NAME === 'CP' ? 'v1' : 'v4'
    }/schedule/`,
    GRAPH_QL: 'https://dev.uastage.com/ua-provider-sync/graphql',
  },
  SW: {
    JOURNAL_COUNT: 'https://sportwrench.com/api/swb/v1/journal_count/',
    PROVIDER_DATA: (providerId: string, gender: string) => `https://sportwrench.com/api/ua/export/${providerId}/${gender}/schedule`,
  },
  AES: {
    PROVIDER_DATA_STAGING: (providerId: string) => `https://results.aes-staging.com/api/universityathlete/${providerId}?ResultsOnly=true`,
    PROVIDER_DATA_DEV: (providerId: string) => `https://results.advancedeventsystems.com/api/universityathlete/${providerId}?ResultsOnly=true`,
    PROVIDER_DATA_JSON:
      'https://www.advancedeventsystems.com/EventResults/UniversityAthleteEventDetails.ashx?ResultsOnly&E_ID=',
    COURT_SCHEDULES: (token: string, date: string) => `https://results.advancedeventsystems.com/api/event/${token}/courts/${date}/-0`,
    TEAMS: (providerId: string) => `https://advancedeventsystems.com/api/landing/events/${providerId}/teams`,
    EVENT: (providerId: string) => `https://advancedeventsystems.com/api/landing/events/${providerId}`,
    EVENT_DATES: (token: string) => `https://results.advancedeventsystems.com/api/event/${token}/utilizeddates`,
  },
  VBS: {
    PROVIDER_DATA: (providerId: string) => `https://api.vbschedule.com/partners/schedule/events/${providerId}/matches?limit=32700`,
    EVENT_DATA: (providerId: string) => `https://api.vbschedule.com/results/event/${providerId}`,
    PUB_PROVIDER_DATA: (providerId: string, date: string) => `https://api.vbschedule.com/results/event/${providerId}/grid/${date}`,
  },
  TM: {
    EVENT_DATA: (providerId: string) => `https://tm2sign.com/api/public/events/${providerId}`,
    PUB_PROVIDER_DATA: (providerId: string, date: string) => ` https://tm2sign.com/api/public/scheduler-matches-grid?event_id=${providerId}&date=${date}`,
    COURTS: (providerId: string) => `https://tm2sign.com/api/public/scheduler-court-locations?filter[event_id]=${providerId}&include[]=schedulerCourts`,
    DIVISIONS: (providerId:string) => `https://tm2sign.com/api/public/event-divisions?filter[event_id]=${providerId}&include[]=schedulerEventDivisionProfile`,
  },
  PG: {
    TOKEN: 'https://tpa.perfectgame.org/grantarea',
    PROVIDER_DATA: (providerId: string, eventType: string) => `https://tpa.perfectgame.org/api/event/getevents?id=${providerId}&type=${eventType}&schedule=true&teams=false`,
    EVENTS: 'https://tpa.perfectgame.org/api/event/getevents',
  },
  TMCH: {
    PROVIDER_DATA: 'https://www.tourneymachine.com/public/mobile/WebApp/index.aspx?IDTournament=',
    PUBLIC_PROVIDER_DATA: 'https://tourneymachine.com/Public/Results/Tournament.aspx?IDTournament=',
  },
  CP: {
    PROVIDER_DATA: (
      sheetName: string,
      sheetId: string = '1-4qdwaf0AsF_19VUtUV_ETnPTNGB5fXomTzu7mKWDUI',
    ) => `https://docs.google.com/spreadsheets/d/${sheetId}/gviz/tq?tqx=out:csv&sheet=${sheetName}`,
  },
  UAR: {
    CLEAR_SCHEDULE_CACHE: (eventId: number) => `${ApiConfig.API_BASE_URL}/ua-api/v5/list/schedule?event=${eventId}&clearcache=1`,
  },
  BALLER_TV: {
    EVENT_DATA: (providerId: string, provider: string) => `https://www.ballertv.com/api/ua/streams?partner_event_id=${providerId}&scheduling_partner=${provider}`,
  },
};

export const HEADERS = {
  UA: {
    SW_JOURNAL: {
      Authorization: `Basic ${Buffer.from('USAV:demo').toString('base64')}`,
      'User-Agent': `UA Sch ${ServerConfig.MAJOR_VERSION}.${ServerConfig.MINOR_VERSION}.${ServerConfig.BUG_VERSION}`,
      'x-cf-pass': 'sw2023',
    },
    POST_SCHEDULE: {
      'Content-Type': 'application/json; charset=utf-8',
      'x-ua-dev': 'UA2023',
    },
  },
  AES: {
    STAGING: {
      'x-api-key': '3qaZaWA?JyhbZDLwtAKbN5BKWmwxXijaT/Z155Xr68GG?r28NLeUgupKR1Y27/0o',
    },
    DEV: {
      'x-api-key': 'pu7pUIhvqYybfmlLmdfhdL21t?UyQQvUrc0/xE!QS15nrTckTuDGHY!CS5kB!jxr',
    },
  },
  VBS: {
    Accept: 'application/json',
    Authorization: 'Bearer 2FakCszM1dBP7fMtAox5YvhJRcJyRRRh',
  },
  PG: {
    TOKEN: {
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    PROVIDER_DATA: (token) => ({
      Accept: 'text/html,application/json',
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36',
      Authorization: `Bearer ${token}`,
    }),
    EVENTS: (token) => ({
      Accept: 'text/html,application/json',
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36',
      Authorization: `Bearer ${token}`,
    }),
  },
  BALLER_TV: {
    'X-BALLERTV-API-KEY': '49f2bfb3366f700339096e9b16ce7af0f4066e41bc7b4152b0153e7a16fb153a',
  },
};

export const API_ARGS = {
  GS: {
    PROVIDER_DATA: ['page=1', 'per_page=5000'],
  },
  EE: {
    PROVIDER_DATA: (eventId: string) => [`eventid=${eventId}`, 'page=1', 'pagesize=5000'],
    TEAMS: (eventId: string) => [`eventid=${eventId}`],
  },
};
