import { capitalize } from 'lodash';

import { ProviderEnum } from './providers';

export const messages = {
  errors: {
    fetchProviderData: (provider: ProviderEnum) => `Failed to fetch ${provider} provider data`,
    fetchBallerTvData: 'Failed to fetch BallerTv data',
    fetchSWJournalCount: 'Failed to fetch SW journal count',
    fetchEvent: 'Failed to fetch event',
    fetchPGEvents: 'Failed to fetch PG events',
    fetchPGToken: 'Failed to fetch PG token',
    postSchedule: (isV4?: boolean) => `Failed to post schedule ${isV4 ? 'v4' : ''}`,
    eventNotFound: (eventId: number) => `Event with id ${eventId} not found`,
    eventEnabled: (eventId: number) => `Event with id ${eventId} is enabled for the parsing`,
    eventDisabled: (eventId: number) => `Event with id ${eventId} is disabled for the parsing`,
    invalidDatePeriod: 'Start date should be less than end date',
    unknownProvider: (provider: string) => `Unknown provider: ${provider}`,
    empty: (entity: string) => `${capitalize(entity)} is empty`,
    noGraphQLAccess: 'No graphQL access code',
    noGraphQLEvent: 'No graphQL event',
    noSuggestion: 'There is nothing to suggest',
    someEventsNotFound: (eventIds: number[]) => `The following event IDs do not exist: ${eventIds.join(', ')}`,
    someEventsEnabled: (eventIds: number[]) => `The following event IDs are enabled for the parsing: ${eventIds.join(', ')}`,
    requiredRuleTypeParam: (...params: string[]) => `${params.join(', ')} ${
      params.length > 1 ? 'are' : 'is'
    } required for chosen ruleType or invalid`,
    notFound: (entity: string, id: number) => `${entity} with id ${id} not found`,
    scraping: 'Could not webscrape the data',
    sameEnablePublication: 'This enablePublication is already assigned to the event',
    savingSchedule: 'Error saving the schedule',
    duplicateMatch: (matchId) => `Duplicate match in schedule with match_id ${matchId}  `,
    matchNotAdded: (id: string) => `[${new Date().toLocaleString()}] ${id} match is not added to the schedule`,
    fetchTeamCodes: (provider: ProviderEnum) => `Failed to fetch ${provider} team codes`,
    fetchAesEvent: 'Failed to fetch AES Pub event',
    fetchAesEventDates: 'Failed to fetch AES Pub event dates',
    clearCache: 'Failed to clear schedule cache',
    fetchVbsEvent: 'Failed to fetch Vbs Pub event',
    fetchProviderEvent: (provider:ProviderEnum) => `Failed to fetch ${provider} event`,
    fetchTmCourts: 'Failed to fetch TM courts',
    fetchTmDivisions: 'Failed to fetch TM divisions',
    fetchEePubTeams: 'Failed to fetch EE pub teams',
    fetchEePubEvents: 'Failed to fetch EE pub events',
  },
  jobLogs: {
    eventNotFound: 'Event not found',
    eventProvider: (provider: string) => `Event provider: ${provider}`,
    noEventProvider: 'Skipping Event as it has no provider',
    buildSchedule: 'Building event schedule',
    postSchedule: 'Posting event schedule',
    noUpdates: 'No updates found',
    noProviderData: 'No provider data',
    noTeamsData: 'No teams data',
    noScheduleData: 'No schedule generated',
    eventSaved: 'Event data saved',
    error: 'Error while parsing the data',
    add: (id: number) => `[${new Date().toLocaleString()}] adding ${id} event to the queue`,
  },
};
