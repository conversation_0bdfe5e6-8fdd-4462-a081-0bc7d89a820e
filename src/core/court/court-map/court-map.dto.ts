import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
export class PutCourtMapDto {
  @ValidateNested({ each: true })
  @Type(() => UpdateCourtMapDto)
  courtMap: UpdateCourtMapDto[];
}

export class UpdateCourtsVenueDto {
  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  schedulerCourtMapIds: number[];

  @IsNotEmpty()
  @IsNumber()
  schedulerCourtSummaryId: number;
}

export class PutCourtMapSummaryDto {
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => UpdateCourtMapSummaryDto)
  courtSummary: UpdateCourtMapSummaryDto[];
}

export class UpdateCourtMapSummaryDto {
  @IsNumber()
  schedulerCourtSummaryId: number;

  @IsOptional()
  @IsNumber()
  offset?: number;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  outPrefix?: string;

  @IsOptional()
  @IsNumber()
  count?: number;
}

export class OneCourtMapDto {
  @IsString()
  pattern: string;

  @IsNumber()
  @IsOptional()
  offsetValue?: number;

  @IsNumber()
  @IsOptional()
  startNumber?: number;

  @IsString()
  @IsOptional()
  outPrefix: string;

  @IsString()
  @IsOptional()
  schedulerCourtSummaryId?: number;
}

export class UpdateCourtMapDto extends PartialType(
  OmitType(OneCourtMapDto, ['pattern']),
) {
  @IsNumber()
  schedulerCourtMapId: number;
}

export class CourtMapDto {
  @ApiProperty()
  schedulerCourtMapId: number;

  @ApiProperty()
  tournamentId: number;

  @ApiProperty()
  dateCreated: Date;

  @ApiProperty()
  dateModified: Date;

  @ApiProperty()
  pattern: string | null;

  @ApiProperty()
  offsetValue: number | null;

  @ApiProperty()
  startNumber: number | null;

  @ApiProperty()
  outPrefix: string | null;
}

export class CourtDetails {
  @ApiProperty()
  schedulerCourtMapId: number;

  @ApiProperty()
  pattern: string;
}

export class SummaryCourtMap {
  @ApiProperty()
  schedulerCourtSummaryId: number;

  @ApiProperty()
  prefix: string;

  @ApiProperty()
  outPrefix: string;

  @ApiProperty()
  count: number;

  @ApiProperty()
  offset: number;
}

export class ReplaceOutPrefixDto {
  @IsString()
  @IsNotEmpty()
  searchPattern: string;

  @IsString()
  @IsNotEmpty()
  replacePattern: string;
}
