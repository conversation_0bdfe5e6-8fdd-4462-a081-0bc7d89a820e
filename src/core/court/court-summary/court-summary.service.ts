import { Injectable, NotFoundException } from '@nestjs/common';
import { SchedulerCourtMap } from '@prisma/client';
import _ from 'lodash';

import { messages } from '@/server/constants/messages';

import { CourtSummaryRepository } from './court-summary.repository';
import { EventRepository } from '../../event/event.repository';
import {
  OneCourtMapDto,
  ReplaceOutPrefixDto,
  UpdateCourtMapSummaryDto,
} from '../court-map/court-map.dto';
import { CourtMapService } from '../court-map/court-map.service';
import {
  CreateCourtSummaryAndMaps,
  SchedulerCourtSummaryWithMaps,
} from '../court.interface';

@Injectable()
export class CourtSummaryService {
  constructor(
    private readonly courtSummaryRepo: CourtSummaryRepository,
    private readonly eventRepo: EventRepository,
    private readonly courtMapService: CourtMapService,
  ) {}

  public async getCourtMapSummary(eventId: number) {
    const courtSummary = await this.courtSummaryRepo.getCourtSummary(eventId);

    return courtSummary.map((oneCourtSummary) => {
      const leastCourtNumber = this.getLeastCourtNumber(
        oneCourtSummary.SchedulerCourtMaps,
        oneCourtSummary.outPrefix,
      );

      return {
        schedulerCourtSummaryId: oneCourtSummary.schedulerCourtSummaryId,
        outPrefix: oneCourtSummary.outPrefix,
        count: oneCourtSummary.SchedulerCourtMaps.filter((map) => map.isOld === 'n').length,
        prefix: oneCourtSummary.locationName,
        offset: oneCourtSummary.offset,
        leastCourtNumber,
      };
    }).filter((summary) => summary.count > 0);
  }

  public async updateCourtSummary(
    eventId: number,
    courtSummary: UpdateCourtMapSummaryDto[],
  ) {
    const event = await this.eventRepo.getEvent(eventId);
    if (!event) {
      throw new NotFoundException(messages.errors.eventNotFound(eventId));
    }
    await Promise.all(
      courtSummary.map(async ({ schedulerCourtSummaryId, ...updateData }) => this.updateOne(schedulerCourtSummaryId, updateData)),
    );

    const shouldUpdateMapOrders = courtSummary.some(
      (summary) => summary.offset !== undefined,
    );

    if (shouldUpdateMapOrders) {
      const sortedCourtSummary = await this.courtSummaryRepo.getCourtSummary(eventId);

      let currentOffset = 1;
      const courtMapUpdates = sortedCourtSummary
        .flatMap((summary) => summary.SchedulerCourtMaps.filter((map) => map.isOld === 'n').map((map) => {
          const mapUpdateData = {
            schedulerCourtMapId: map.schedulerCourtMapId,
            offsetValue: currentOffset,
          };
          currentOffset += 1;

          if (mapUpdateData.offsetValue === map.offsetValue) {
            return null;
          }
          return mapUpdateData;
        }))
        .filter(Boolean);
      if (courtMapUpdates.length > 0) await this.courtMapService.updateCourtMaps(eventId, courtMapUpdates);
    }
    return this.courtSummaryRepo.getCourtSummary(
      eventId,
      _.map(courtSummary, (summary) => summary.schedulerCourtSummaryId),
    );
  }

  public generateCourtSummary(
    courts: (OneCourtMapDto & {
      schedulerCourtMapId?: number;
      schedulerCourtSummaryId?: number | null;
    })[],
  ) {
    const distinctCourts = new Map<
    string,
    {
      count: number;
      outPrefix: string;
      courts:(OneCourtMapDto & {
        schedulerCourtMapId?: number;
        schedulerCourtSummaryId?: number | null;
      })[];
      offset: number;
    }
    >();
    courts.forEach((court) => {
      const prefix = this.getSummaryCourtPrefix(court.pattern).trim();

      const entry = distinctCourts.get(prefix) || {
        count: 0,
        outPrefix: this.getSummaryCourtOutPrefix(court.outPrefix),
        courts: [],
        offset: distinctCourts.size + 1,
      };

      entry.count += 1;
      entry.courts.push(court);

      distinctCourts.set(prefix, entry);
    });
    return Array.from(distinctCourts.entries()).map(
      ([summaryPrefix, groupedCourtDetails]) => ({
        locationName: summaryPrefix,
        ...groupedCourtDetails,
      }),
    );
  }

  public async findAndReplaceSummaryPrefix(
    eventId: number,
    { searchPattern, replacePattern }: ReplaceOutPrefixDto,
  ) {
    const matchedCourtSummary = await this.courtSummaryRepo.findCourtSummaryByOutPrefix(
      eventId,
      searchPattern,
    );

    if (!_.isEmpty(matchedCourtSummary)) {
      await this.updateSummaryAndCourtMapOutPrefix(
        matchedCourtSummary,
        searchPattern,
        replacePattern,
      );
    }

    return this.getCourtMapSummary(eventId);
  }

  public async createOneCourtSummaryAndMaps(
    tournamentId: number,
    { courts, ...courtSummary }: CreateCourtSummaryAndMaps,
  ) {
    const createCourtMapsData = courts.map((court) => ({ ...court, tournamentId }));

    return this.courtSummaryRepo.createOneCourtSummaryAndMaps(
      tournamentId,
      courtSummary,
      createCourtMapsData,
    );
  }

  private async updateSummaryAndCourtMapOutPrefix(
    summary: SchedulerCourtSummaryWithMaps[],
    find: string,
    replace: string,
  ) {
    await this.updateSummaryOutPrefixes(summary, find, replace);

    const courtMaps = summary.flatMap(
      ({ SchedulerCourtMaps: summaryCourtMaps }) => summaryCourtMaps,
    );
    const filteredCourtMaps = courtMaps.filter((map) => map.outPrefix.includes(find));

    await this.courtMapService.updateOutPrefix(filteredCourtMaps, find, replace);
  }

  private async updateSummaryOutPrefixes(
    summary: SchedulerCourtSummaryWithMaps[],
    find: string,
    replace: string,
  ) {
    return Promise.all(
      summary.map(async ({ schedulerCourtSummaryId, outPrefix }) => this.courtSummaryRepo.updateSummaryOutPrefix(
        schedulerCourtSummaryId,
        outPrefix,
        find,
        replace,
      )),
    );
  }

  private getSummaryCourtOutPrefix(outPrefix: string) {
    return this.stripFinalDigitsFromString(outPrefix);
  }

  private getSummaryCourtPrefix(courtName: string) {
    const hasCaret = courtName.includes('^');
    let result: string;
    if (!hasCaret) {
      result = this.stripFinalDigitsFromString(courtName);
      //  result = result.replace(/[0-9]+/g, '.*');
    } else {
      const [venue, field] = courtName.split('^', 2);

      // const transformedVenue = venue.replace(/[0-9]+/g, '.*');
      const transformedField = this.stripFinalDigitsFromString(field);

      result = `${venue}.${transformedField}`;
    }

    result = result.replace(/\.\*\.\*/g, '.*');
    result = result.replace(/\.\*\./g, '.*');

    return result;
  }

  private async updateOne(
    summaryId: number,
    updateData: { offset?: number; outPrefix?: string },
  ) {
    const { outPrefix: initialValue, SchedulerCourtMaps: courtMaps } = await this.courtSummaryRepo.findCourtSummaryById(summaryId);
    const { outPrefix: finalValue } = await this.courtSummaryRepo.updateOneCourtSummary(
      summaryId,
      updateData,
    );
    if (updateData.outPrefix !== undefined) {
      const filteredCourtMaps = courtMaps.filter((map) => map.outPrefix.includes(initialValue));

      if (filteredCourtMaps.length > 0) {
        await this.courtMapService.updateOutPrefix(
          filteredCourtMaps,
          initialValue,
          finalValue,
        );
      }
    }
  }

  private stripFinalDigitsFromString(str: string): string {
    let result: string;

    // First, check if the string ends with digits (+ optional letter)
    if (/[0-9]+[a-zA-Z]?$/.test(str) && !/^[0-9]+[a-zA-Z]?$/.test(str)) {
      // Remove trailing digits (+ optional letter)
      result = str.replace(/[0-9]+[a-zA-Z]?$/, '');
    } else {
      // Otherwise, check for digits in the middle of the string
      // This regex matches digits  surrounded by non-digits
      const middleDigitsRegex = /([^0-9]+)\d+([^0-9]+)/;
      const middleDigitsMatch = str.match(middleDigitsRegex);

      if (middleDigitsMatch) {
        // If digits are found in the middle, remove them
        result = str.replace(/\d+/, '');
      } else {
        // Otherwise, remove leading digits (+ optional letter)
        result = str.replace(/^\d+/, '');
      }
    }

    // Replace multiple whitespaces with a single space and trim
    result = result.replace(/\s+/g, ' ').trim();

    return result;
  }

  private getLeastCourtNumber(courtMaps: SchedulerCourtMap[], summaryOutPrefix: string) {
    const numbers = courtMaps
      .map((map) => {
        if (!map.outPrefix.startsWith(summaryOutPrefix)) {
          return null;
        }

        const numberStr = map.outPrefix.replace(summaryOutPrefix, '').trim();
        const number = parseInt(numberStr, 10);
        if (Number.isNaN(number)) {
          return null;
        }

        return number;
      })
      .filter((num) => num !== null);

    return numbers.length > 0 ? Math.min(...numbers) : null;
  }
}
