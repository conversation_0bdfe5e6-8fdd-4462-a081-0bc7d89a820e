import { Prisma } from '@prisma/client';

import { OneCourtMapDto } from './court-map/court-map.dto';

export interface CraeteCourtSummary {
  locationName: string;
  count: number;
  outPrefix: string;
  offset: number;
}

export type CreateCourtSummaryAndMaps = CraeteCourtSummary & {
  courts: (OneCourtMapDto & {
    schedulerCourtMapId?: number;
    schedulerCourtSummaryId?: number | null;
  })[];
};

export type SchedulerCourtSummaryWithMaps = Prisma.SchedulerCourtSummaryGetPayload<{
  include: { SchedulerCourtMaps: true };
}>;
