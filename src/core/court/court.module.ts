import { forwardRef, Module } from '@nestjs/common';

import { PrismaModule } from '@/server/prisma';

import { CourtMapController } from './court-map/court-map.controller';
import { CourtMapRepository } from './court-map/court-map.repository';
import { CourtMapService } from './court-map/court-map.service';
import { CourtSummaryController } from './court-summary/court-summary.controller';
import { CourtSummaryRepository } from './court-summary/court-summary.repository';
import { CourtSummaryService } from './court-summary/court-summary.service';
import { CourtService } from './court.service';
import { EventsModule } from '../event/event.module';

@Module({
  imports: [PrismaModule, forwardRef(() => EventsModule)],
  controllers: [CourtMapController, CourtSummaryController],
  providers: [CourtSummaryService, CourtMapRepository, CourtSummaryRepository, CourtMapService, CourtService],
  exports: [CourtSummaryService, CourtMapService, CourtService, CourtMapRepository],
})
export class CourtModule {}
