import { ApiProperty } from '@nestjs/swagger';

import { DetailedEvent, LimitedEvent } from './interfaces/event.interface';
import { UnknownTeam } from '../team/team.interface';

export class Event implements DetailedEvent {
  public id: number;
  public tournament_id: number;
  public name: string;
  public provider: string;
  public provider_tournament_id: string;
  public start_date: string;
  public end_date: string;
  public city: string;
  public state: string;
  public timezone: string;
  public sport: string;
  public gender: string;
  public access_code: string;
  public ncsa_event: string;
  public min_age: number;
  public max_age: number;
  public first_day: number;
  public max_asc: number;
  public hours_offset: number;
  public last_update?: string;
  public last_update_secs?: number;
  public team_md5?: string;
  public evt_md5?: string;
  public schedule_md5?: string;
  public match_count?: string;
  public unknown_count?: number;
  public extra_count?: number;
  public schedule_length?: string;
  public unknown_teams?: UnknownTeam[];
  public aes_event_url?: string;
  constructor(event: DetailedEvent) {
    Object.assign(this, event);
  }
}

export class LimitedEventDto implements LimitedEvent {
  @ApiProperty()
  public tournamentId: number;

  @ApiProperty()
  public name: string;

  @ApiProperty()
  public startDate: Date;

  @ApiProperty()
  public endDate: Date;

  @ApiProperty()
  public sport: string;

  @ApiProperty()
  public teams: number;

  @ApiProperty()
  public colleges: number;

  @ApiProperty()
  public licenses: number;
}

export class SchedulerEvent {
  @ApiProperty()
  public tournament_id: number;

  @ApiProperty()
  public name: string;

  @ApiProperty()
  public sport: string;

  @ApiProperty()
  public sport_id: number;

  @ApiProperty()
  public gender: string;

  @ApiProperty()
  public provider_name: string;

  @ApiProperty()
  public event_owner: string | null;

  @ApiProperty()
  public event_owner_id: string | null;

  @ApiProperty()
  public schedule_provider: string;

  @ApiProperty()
  public schedule_provider_tournament_id: string;

  @ApiProperty()
  public last_update: string;

  @ApiProperty()
  public matches: string;

  @ApiProperty()
  public status: string;

  @ApiProperty()
  public start_date: string;

  @ApiProperty()
  public end_date: string;

  @ApiProperty()
  public state: string;

  @ApiProperty()
  public timezone: string;

  @ApiProperty()
  public access: string;

  @ApiProperty()
  public team_hash: string;

  @ApiProperty()
  public schedule_hash: string;

  @ApiProperty()
  public event_hash: string;

  @ApiProperty()
  public error_message: string;

  @ApiProperty()
  public schedule_length: string;

  @ApiProperty()
  public max_asc: number;

  @ApiProperty()
  public first_day: number;

  @ApiProperty()
  public hours_offset: number;
}
