import { forwardRef, Module } from '@nestjs/common';

import { PrismaModule } from '@/server/prisma';

import { EventController } from './event.controller';
import { EventRepository } from './event.repository';
import { EventService } from './event.service';
import { BullModule } from '../bull/bull.module';
import { ScheduleModule } from '../schedule/schedule.module';

@Module({
  imports: [PrismaModule, BullModule, forwardRef(() => ScheduleModule)],
  controllers: [EventController],
  providers: [EventService, EventRepository],
  exports: [EventService, EventRepository],
})
export class EventsModule {}
