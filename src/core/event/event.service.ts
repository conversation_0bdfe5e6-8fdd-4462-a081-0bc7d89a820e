import { InjectQueue } from '@nestjs/bullmq';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { RuleType, Tournament } from '@prisma/client';
import { Queue } from 'bullmq';
import _ from 'lodash';

import { messages } from '@/server/constants/messages';
import { stateToTimeZone } from '@/server/utils/state-to-timezone';

import { EventMapper } from './event.mapper';
import { Event } from './event.model';
import { EventRepository } from './event.repository';
import { SchedulerEvent } from './interfaces/event.interface';
import { PARSING_QUEUE } from '../parsing/parsing.constants';
import { ParsingRuleDto } from '../parsing-template/dto/create-parsing-template.dto';
import { ScheduleRepository } from '../schedule/schedule.repository';

@Injectable()
export class EventService {
  constructor(
    private readonly eventRepo: EventRepository,
    @InjectQueue(PARSING_QUEUE) private readonly parsingQueue: Queue,
    private readonly scheduleRepo: ScheduleRepository,
  ) {}

  public async getSchedulerEvents(
    sportId: number,
    availableSports: number[],
  ): Promise<SchedulerEvent[]> {
    const schedulerEvents = await this.eventRepo.getSchedulerEvents(
      sportId,
      availableSports,
    );
    return schedulerEvents.map((event) => ({
      tournament_id: event.tournament_id,
      name: EventMapper.stripMonth(event.name),
      sport: event.sport,
      sport_id: event.sports_id,
      provider_name: event.provider,
      gender: event.gender,
      event_owner: event.event_owner,
      event_owner_id: event.event_owner_id,
      schedule_provider: EventMapper.getSchProviderName(event),
      schedule_provider_tournament_id: EventMapper.getSchProviderTournamentId(event),
      last_update: event.last_update,
      matches: event.match_count,
      status: event.parsing_status,
      start_date: new Date(event.datestart).toISOString().split('T')[0],
      end_date: new Date(event.dateend).toISOString().split('T')[0],
      state: event.state,
      timezone: stateToTimeZone(event.state || ''),
      access: event.ncsa_access_code,
      team_hash: event.scheduler_hash_team,
      publication_enabled: event.scheduler_enable_publication,
      schedule_hash: event.scheduler_hash_schedule,
      event_hash: event.scheduler_hash_event,
      error_message: event.error_message,
      schedule_length: event.schedule_length,
      max_asc: 0,
      first_day: 1,
      hours_offset: 0,
    }));
  }

  public async getAndFormatEvent(eventId: number): Promise<Event> {
    const detailedEvent = await this.getEventFromUa(eventId);
    const eventHashes = await this.eventRepo.getEventHashes(eventId);
    const formattedEvent = EventMapper.mapEvent(detailedEvent, eventHashes);
    return new Event(formattedEvent);
  }

  public async getEventFromUa(eventId: number) {
    return this.eventRepo.getApiEvent(eventId);
  }

  public async enableSchedulerAndBulkQueue(
    eventIds: number[],
    rules?: ParsingRuleDto[],
  ) {
    const events = await this.eventRepo.getEventsByIds(eventIds);

    this.ensureAllEventsExist(eventIds, events);
    this.ensureNoEventsAreAlreadyEnabled(events);

    await Promise.all(
      eventIds.map(async (eventId) => this.enableEventScheduler(eventId, rules)),
    );

    const jobsData = eventIds.map((eventId) => ({
      name: 'parse',
      data: {
        eventId,
        forceUpdate: true,
        post: true,
      },
    }));

    await this.parsingQueue.addBulk(jobsData);
  }

  public async enableSchedulerAndQueue(eventId: number, rules?: ParsingRuleDto[]) {
    const event = await this.getEventOrThrow(eventId);
    if (event.schedulerEnabled) {
      throw new BadRequestException(messages.errors.eventEnabled(eventId));
    }

    await this.enableEventScheduler(eventId, rules);

    await this.parsingQueue.add('parse', { eventId, forceUpdate: true, post: true });
  }

  public async addEventToParsing(eventId: number) {
    const event = await this.getEventOrThrow(eventId);
    if (!event.schedulerEnabled) {
      throw new BadRequestException(messages.errors.eventDisabled(eventId));
    }

    await this.parsingQueue.add('parse', { eventId, forceUpdate: true, post: true });
  }

  public async removeEventFromParsing(eventId: number) {
    const event = await this.getEventOrThrow(eventId);
    if (!event.schedulerEnabled) {
      throw new BadRequestException(messages.errors.eventDisabled(eventId));
    }
    await this.eventRepo.setEventSchedulerEnabled(eventId, false);
  }

  public async canAccessEventMaps(eventId: number) {
    const event = await this.eventRepo.getEvent(eventId);
    if (!event) {
      throw new NotFoundException(messages.errors.eventNotFound(eventId));
    }
    const scheduleMatches = await this.scheduleRepo.getAll(eventId);
    return { accessGranted: scheduleMatches.length > 0 };
  }

  public async getEventSchedulerConfig(eventId: number) {
    const event = await this.getEventOrThrow(eventId);
    return JSON.parse(<string>event.schedulerConfig);
  }

  public async updateEventSchedulerConfig(eventId: number, rules: ParsingRuleDto[]) {
    const event = await this.getEventOrThrow(eventId);
    if (_.isEmpty(rules)) {
      throw new BadRequestException(messages.errors.empty('rules'));
    }

    return this.eventRepo.setEventParserConfig(event.tournamentId, JSON.stringify(rules));
  }

  public async setPublication(eventId: number, enablePublication: 'y' | 'n') {
    const event = await this.getEventOrThrow(eventId);
    if (event.schedulerEnablePublication === enablePublication) {
      throw new BadRequestException(messages.errors.sameEnablePublication);
    }

    await this.eventRepo.updateEventScheduleDateModified(eventId);

    return this.eventRepo.updatePublication(eventId, enablePublication);
  }

  private async getEventOrThrow(eventId: number): Promise<Tournament> {
    const event = await this.eventRepo.getEvent(eventId);
    if (!event) {
      throw new NotFoundException(messages.errors.eventNotFound(eventId));
    }
    return event;
  }

  private async enableEventScheduler(eventId: number, rules?: ParsingRuleDto[]) {
    await this.eventRepo.setEventSchedulerEnabled(eventId, true);

    const defaultRules: ParsingRuleDto[] = [
      { ruleType: RuleType.before_event, frequency: 30, dayOfWeek: [0, 1, 2, 3, 4, 5, 6] },
      { ruleType: RuleType.during_event, frequency: 7, dayOfWeek: [0, 1, 2, 3, 4, 5, 6] },
    ];

    const effectiveRules = rules && rules.length > 0 ? rules : defaultRules;

    await this.eventRepo.setEventParserConfig(eventId, JSON.stringify(effectiveRules));
  }

  // private getStatusesUntilError(statuses: { parsingStatus: ParsingStatus }[]) {
  //   const errorIndex = statuses.findIndex((status) => status.parsingStatus === 'error');
  //   const result = errorIndex === -1 ? statuses : statuses.slice(0, errorIndex + 1);
  //   return _.map(result, (status) => status.parsingStatus);
  // }

  private ensureAllEventsExist(eventIds: number[], events: Tournament[]) {
    if (eventIds.length !== events.length) {
      const foundIds = events.map((event) => event.tournamentId);
      const missingIds = eventIds.filter((id) => !foundIds.includes(id));

      throw new NotFoundException(messages.errors.someEventsNotFound(missingIds));
    }
  }

  private ensureNoEventsAreAlreadyEnabled(events: Tournament[]): void {
    const enabledEventIds = events
      .filter((event) => event.schedulerEnabled)
      .map((event) => event.tournamentId);

    if (enabledEventIds.length > 0) {
      throw new BadRequestException(messages.errors.someEventsEnabled(enabledEventIds));
    }
  }
}
