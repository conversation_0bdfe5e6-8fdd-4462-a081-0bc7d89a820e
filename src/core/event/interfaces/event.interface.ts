export interface LimitedEvent {
  tournamentId: number;
  name: string;
  startDate: Date;
  endDate: Date;
  sport: string;
  teams: number;
  colleges: number;
  licenses: number;
}

export interface FetchEventResponse {
  tournament_id: number;
  name: string;
  provider: string;
  provider_tournament_id: string;
  datestart: string;
  dateend: string;
  min_age: number;
  max_age: number;
  has_alpha_courts: string;
  showcase_event: string;
  city: string;
  state: string;
  schedule_provider_type: string;
  schedule_provider_tournament_id: string;
  sport: string;
  gender: string;
  ncsa_access_code: string;
  ncsa_event_id: string;
  scheduler_hash_team?: string;
  scheduler_hash_schedule?: string;
  scheduler_hash_event?: string;
  last_update?: string;
  match_count?: string;
  parsing_status?: string;
  sports_id?: number;
  error_message?: string;
  schedule_length?: string;
  event_owner?: string;
  event_owner_id?: number;
  aes_event_url?: string;
  scheduler_enable_publication?: 'y' | 'n';
}

export interface DetailedEvent {
  id: number;
  tournament_id: number;
  name: string;
  provider: string;
  provider_tournament_id: string;
  start_date: string;
  end_date: string;
  city: string;
  state: string;
  timezone: string;
  sport: string;
  gender: string;
  access_code: string;
  ncsa_event: string;
  min_age: number;
  max_age: number;
  first_day: number;
  max_asc: number;
  hours_offset: number;
  last_update?: string;
  last_update_secs?: number;
  team_md5?: string;
  evt_md5?: string;
  schedule_md5?: string;
  match_count?: string;
  unknown_count?: number;
  extra_count?: number;
  parsing_status?: string;
  aes_event_url?: string;
}

export interface SchedulerEvent {
  tournament_id: number;
  name: string;
  sport: string;
  sport_id: number;
  gender: string;
  provider_name: string;
  event_owner: string | null;
  event_owner_id: number | null;
  schedule_provider: string;
  schedule_provider_tournament_id: string;
  last_update: string;
  matches: string;
  status: string;
  start_date: string;
  end_date: string;
  state: string;
  timezone: string;
  access: string;
  team_hash: string;
  schedule_hash: string;
  event_hash: string;
  max_asc: number;
  first_day: number;
  hours_offset: number;
  publication_enabled: 'y' | 'n';
}
