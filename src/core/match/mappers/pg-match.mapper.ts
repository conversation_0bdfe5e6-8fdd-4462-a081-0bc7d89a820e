import { Injectable } from '@nestjs/common';

import { StorageService } from '@/server/services/storage.service';
import { getDigitsFromString } from '@/server/utils/get-digits-from-string';
import { getEventIsoDates } from '@/server/utils/get-event-iso-dates';

import { OneCourtMapDto } from '../../court/court-map/court-map.dto';
import { Event } from '../../event/event.model';
import { PgMatch } from '../../parsing/parsing.interface';
import { OneTeamMapDto } from '../../team/dto/team-map.dto';
import { Match } from '../match.model';
import { CourtUtilsService } from '../utils/court-utils.service';
import { isUaTeamMapped } from '../utils/is-ua-team-mapped';

@Injectable()
export class PgMatchMapper {
  constructor(
    private readonly storageService: StorageService,
    private readonly courtUtils: CourtUtilsService,
  ) {}

  public mapMatch(
    resultMatch: Match,
    event: Event,
    match: PgMatch,
    maps: { teamsMap: OneTeamMapDto[]; courtsMap: OneCourtMapDto[] },
  ) {
    resultMatch.event = event.tournament_id.toString();
    resultMatch.div = match.division ?? resultMatch.div;

    this.setMatchDates(resultMatch, match, event);
    if (!resultMatch.date_time || !resultMatch.day) return;

    this.setMatchCourt(resultMatch, match, event, maps.courtsMap);
    this.setMatchTeams(resultMatch, match, event, maps.teamsMap);
    this.setMatchId(resultMatch, match);
  }

  public isGenderMatching(event: Event, match: PgMatch) {
    const eventGender = event.gender;
    const matchGender = match.division.includes('Boys')
      ? 'M'
      : match.division.includes('Girls')
        ? 'F'
        : undefined;

    return eventGender === matchGender || !matchGender;
  }

  private setMatchId(resultMatch: Match, match: PgMatch) {
    resultMatch.match_id = `${resultMatch.event}_${match.tournamentGameId}`;
  }

  private setMatchTeams(
    resultMatch: Match,
    match: PgMatch,
    event: Event,
    teamsMap: OneTeamMapDto[],
  ) {
    const teams = [
      { id: match.homeTeamId, key: 'team_1_id', name: match.homeTeam },
      { id: match.visitorTeamId, key: 'team_2_id', name: match.awayTeam },
    ];

    teams.forEach((team) => {
      if (team.id) {
        this.processTeam(team, resultMatch, event, teamsMap);
      }
    });
  }

  private processTeam(
    team: { id: number; key: string; name?: string },
    resultMatch: Match,
    event: Event,
    teamsMap: OneTeamMapDto[],
  ) {
    const teamMap = teamsMap.find((map) => map.providerTeamId === team.id.toString());
    if (teamMap) {
      this.processTeamByMapData(teamMap, resultMatch, team.key);
    } else {
      this.processTeamByUaData(team, resultMatch, event, teamsMap);
    }
  }

  private processTeamByUaData(
    team: { id: number; key: string; name?: string },
    resultMatch: Match,
    event: Event,
    teamsMap: OneTeamMapDto[],
  ) {
    const uaTeam = this.storageService.uaTeams.find(
      (element) => element.providerTeamId === team.id.toString(),
    );
    if (uaTeam && !isUaTeamMapped(uaTeam.uaId, teamsMap)) {
      resultMatch[team.key] = uaTeam.uaId;
      resultMatch.div = uaTeam.uaDiv;
      uaTeam.uaMatchCount += 1;
      if (!this.storageService.matchedTeams.has(uaTeam.uaId)) {
        this.storageService.matchedTeams.set(uaTeam.uaId, {
          teamMasterId: Number(uaTeam.uaId),
          providerTeamId: team.id.toString(),
          providerDiv: resultMatch.div,
          providerTeamName: team.name ?? null,
        });
      }
    } else {
      this.logUnknownTeam(team, resultMatch, event);
    }
  }

  private processTeamByMapData(teamMap: OneTeamMapDto, resultMatch: Match, teamKey: string) {
    const teamMasterId = teamMap.teamMasterId;

    resultMatch[teamKey] = teamMap.teamMasterId;
    const uaTeam = this.storageService.uaTeams.find(
      (element) => element.uaId === teamMasterId.toString(),
    );
    if (uaTeam) {
      resultMatch.div = uaTeam.uaDiv;
      uaTeam.uaMatchCount += 1;
    }
  }

  private logUnknownTeam(
    team: { id: number; key: string; name?: string },
    resultMatch: Match,
    event: Event,
  ) {
    const divAge = getDigitsFromString(resultMatch.div);
    if (
      !this.storageService.unknownTeams.has(team.id.toString())
      && (divAge === '' || (divAge >= event.min_age.toString() && divAge <= '22'))

    ) {
      // const logData = `${match.fieldName} ${match.fieldNumber}\t${match.date.substring(
      //   5,
      //   10,
      // )} ${match.time}\t${resultMatch.div || 'div?'}\t${team.id}`;
      this.storageService.unknownTeams.set(team.id.toString(), {
        providerTeamId: team.id.toString(),
        providerDiv: resultMatch.div,
        providerTeamName: team.name ?? null,
      });
    } else if (
      !this.storageService.unknownTeams.has(team.id.toString())
      && !this.storageService.notTakenTeams.has(team.id.toString())
    ) {
      this.storageService.notTakenTeams.add(team.id.toString());
    }
  }

  private setMatchCourt(
    resultMatch: Match,
    match: PgMatch,
    event: Event,
    courtsMap: OneCourtMapDto[],
  ) {
    const court = this.getCourtName(match);

    if (!court) return;

    const courtNumber = this.courtUtils.getCourtNumber(event, court, courtsMap);
    const sortOrder: string = courtNumber.toString().padStart(3, '0');
    resultMatch.court = this.courtUtils.alphaCourt(courtNumber, event, courtsMap);
    resultMatch.sort_order = sortOrder;
  }

  private getCourtName(match: PgMatch): string | undefined {
    if (match.fieldName) {
      const court = match.fieldName.trim();
      const fieldNumber = match.fieldNumber;

      if (fieldNumber && !court.includes(fieldNumber)) {
        return `${court} ${fieldNumber}`.trim();
      }

      return court;
    }

    if (match.ballparkName) {
      return match.ballparkName;
    }
    return undefined;
  }

  private setMatchDates(resultMatch: Match, match: PgMatch, event: Event) {
    if (!match.date) {
      return;
    }

    const [year, month, day] = match.date
      .slice(0, 10)
      .split('-')
      .map((el) => parseInt(el, 10));

    const time = match.time ?? '00:00';
    const timeParts = time.split(':');

    let hours = parseInt(timeParts[0], 10);
    const mins = parseInt(timeParts[1].slice(0, 2), 10);

    if (time.includes('PM') && hours < 12) {
      hours += 12;
    }

    const dtObj = new Date(Date.UTC(year, month - 1, day, hours, mins));
    const dateString = dtObj.toISOString().slice(0, 19).replace('T', ' ');
    resultMatch.date_time = dateString;

    const eventDates = getEventIsoDates(event);
    const isoDate = dateString.substring(0, 10);
    const pos = eventDates.indexOf(isoDate) + 1;
    resultMatch.day = pos > 0 ? pos.toString() : undefined;
  }
}
