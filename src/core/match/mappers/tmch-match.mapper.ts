import { Injectable } from '@nestjs/common';

import { StorageService } from '@/server/services/storage.service';
import { getEventIsoDates } from '@/server/utils/get-event-iso-dates';

import { OneCourtMapDto } from '../../court/court-map/court-map.dto';
import { Event } from '../../event/event.model';
import { TmchDivision, TmchFacility, TmchMatch } from '../../parsing/parsing.interface';
import { OneTeamMapDto } from '../../team/dto/team-map.dto';
import { Match } from '../match.model';
import { cleanUpPool } from '../utils/cleanup-pool';
import { CourtUtilsService } from '../utils/court-utils.service';
import { isUaTeamMapped } from '../utils/is-ua-team-mapped';

@Injectable()
export class TmchMatchMapper {
  constructor(
    private readonly storageService: StorageService,
    private readonly courtUtils: CourtUtilsService,
  ) {}

  public mapMatch(
    resultMatch: Match,
    event: Event,
    match: TmchMatch,
    maps: { teamsMap: OneTeamMapDto[]; courtsMap: OneCourtMapDto[] },
    {
      facilities,
      divisions,
    }: { facilities: Record<string, TmchFacility>; divisions: Record<string, TmchDivision> },
  ) {
    resultMatch.event = event.tournament_id.toString();
    this.setMatchDiv(resultMatch, match, divisions);

    if (!resultMatch.div) return;
    this.setMatchDates(resultMatch, match.S, event);

    if (!resultMatch.date_time || !resultMatch.day) return;

    this.setMatchCourt(resultMatch, match.IDF, event, maps.courtsMap, facilities);
    this.setMatchTeams(resultMatch, match, maps.teamsMap);
    this.setMatchResults(resultMatch, match);
    this.loadPoolData(resultMatch, match);
    this.setMatchId(resultMatch, match);
  }

  private setMatchDiv(
    resultMatch: Match,
    match: TmchMatch,
    divisions: Record<string, TmchDivision>,
  ) {
    for (const division of Object.values(divisions)) {
      const { Name: divName, IDGames: idGamesItem } = division;
      if (
        typeof divName !== 'string'
        || divName.length === 0
        || !Array.isArray(idGamesItem)
        || idGamesItem.length === 0
      ) continue;

      if (idGamesItem.indexOf(Number(match.matchId)) === -1) continue;

      if (
        divName.includes('U-12')
        || divName.includes('U-13')
        || divName.includes('Co-Ed')
        || (divName.includes('U-14') && divName.includes('7v7'))
      ) {
        return;
      }

      resultMatch.div = divName.replace(/Girls/i, '').replace(/Boys/i, '').trim();
      return;
    }
  }

  private setMatchId(resultMatch: Match, match: TmchMatch) {
    resultMatch.match_id = `${resultMatch.event}_${match.matchId}`;
  }

  private loadPoolData(resultMatch: Match, match: TmchMatch) {
    let poolMatchCount: number;
    const poolName = match.GN ? cleanUpPool(match.GN) : '';
    const poolKey = resultMatch.div + poolName;
    if (!this.storageService.mPoolData.has(poolKey)) {
      poolMatchCount = 1;
      this.storageService.mPoolData.set(poolKey, poolMatchCount);
    } else {
      poolMatchCount = this.storageService.mPoolData.get(poolKey) + 1;
      this.storageService.mPoolData.set(poolKey, poolMatchCount);
    }
  }

  private setMatchResults(resultMatch: Match, match: TmchMatch) {
    if (!match.F) {
      return;
    }
    const score1: number = match.S1;
    const score2: number = match.S2;
    resultMatch.scores = `${score1}-${score2}`;
    resultMatch.winning_team = score1 > score2 ? '1' : score2 > score1 ? '2' : '0';
  }

  private setMatchTeams(resultMatch: Match, match: TmchMatch, teamsMap: OneTeamMapDto[]) {
    const teams = [
      { id: match.IDT1, name: match.T1, key: 'team_1_id' },
      { id: match.IDT2, name: match.T2, key: 'team_2_id' },
    ];

    teams.forEach((team) => {
      if (team.id) {
        this.processTeam(team, resultMatch, teamsMap);
      }
    });
  }

  private processTeam(
    team: { id: string; name: string; key: string },
    resultMatch: Match,
    teamsMap: OneTeamMapDto[],
  ) {
    const { id: teamId, key: teamKey } = team;

    if (teamId.length === 0 || teamId === '0' || teamId === 'null') {
      return;
    }

    const teamMap = teamsMap.find((map) => map.providerTeamId === team.id)
      || [...this.storageService.matchedTeams.values()].find((map) => map.providerTeamId === team.id);
    if (teamMap) {
      this.processTeamByMapData(teamMap, resultMatch, teamKey);
    } else {
      this.processTeamByUaData(team, resultMatch, teamsMap);
    }
  }

  private processTeamByUaData(
    team: { id: string; name: string; key: string },
    resultMatch: Match,
    teamsMap: OneTeamMapDto[],
  ) {
    const uaTeam = this.findUaTeam(team.id, team.name, resultMatch);

    if (
      uaTeam
      && !isUaTeamMapped(uaTeam.uaId, teamsMap)
      && !this.storageService.matchedTeams.has(uaTeam.uaId)
    ) {
      resultMatch[team.key] = uaTeam.uaId;
      uaTeam.uaMatchCount += 1;
      this.storageService.matchedTeams.set(uaTeam.uaId, {
        teamMasterId: Number(uaTeam.uaId),
        providerTeamId: team.id,
        providerTeamName: this.stripPrefix(team.name),
        providerDiv: resultMatch.div,
      });
      // resultMatch.div = uaTeam.uaDiv;
    } else {
      this.logUnknownTeam(team, resultMatch);
    }
  }

  private processTeamByMapData(teamMap: OneTeamMapDto, resultMatch: Match, teamKey: string) {
    const teamMasterId = teamMap.teamMasterId;
    resultMatch[teamKey] = teamMasterId;

    const uaTeam = this.storageService.uaTeams.find(
      (element) => element.uaId === teamMasterId.toString(),
    );

    if (uaTeam) {
      // resultMatch.div = uaTeam.uaDiv;
      uaTeam.uaMatchCount += 1;
    }
  }

  private logUnknownTeam(team: { id: string; name: string }, resultMatch: Match) {
    if (!this.storageService.unknownTeams.has(team.id)) {
      // const dateTime = resultMatch.date_time || '';

      // const logData = `${resultMatch.court}\t${dateTime.slice(6)}>\t${team.id}\t${
      //   resultMatch.div || 'div?'
      // }\t${this.stripPrefix(team.name)}`;

      this.storageService.unknownTeams.set(team.id, {
        providerTeamId: team.id,
        providerTeamName: this.stripPrefix(team.name),
        providerDiv: resultMatch.div,
      });
    }
  }

  private findUaTeam(teamId: string, teamName: string, resultMatch: Match) {
    const formattedTeamName = this.stripPrefix(teamName);
    const divName = `${resultMatch.div}|${formattedTeamName}`;
    const nameDiv = `${formattedTeamName} ${resultMatch.div}`;

    return (
      this.storageService.uaTeams.find((element) => element.providerTeamId === teamId)
      ?? this.storageService.uaTeams.find((element) => element.uaDivName === divName)
      ?? this.storageService.uaTeams.find((element) => element.uaName === nameDiv)
      ?? this.storageService.uaTeams.find((element) => element.uaName === formattedTeamName)
    );
  }

  private setMatchCourt(
    resultMatch: Match,
    idf: string,
    event: Event,
    courtsMap: OneCourtMapDto[],
    facilities: Record<string, TmchFacility>,
  ) {
    if (idf) {
      const facility = facilities[idf];
      const courtName = facility.DisplayName ?? '';
      const courtNumber = this.courtUtils.getCourtNumber(event, courtName, courtsMap);
      const sortOrder: string = courtNumber.toString().padStart(3, '0');
      resultMatch.court = this.courtUtils.alphaCourt(courtNumber, event, courtsMap);
      resultMatch.sort_order = sortOrder;
    }
  }

  // private getCourtNumber(facility: TmchFacility, event: Event, courtsMap: OneCourtMapDto[]) {
  //   const court = facility.DisplayName ?? '';
  //   const court2 = facility.Name ?? '0';
  //   const court3 = `${court}^${court2}`;
  //   const courtNumber = this.courtUtils.getCourtNumber(event, court, courtsMap);
  //   const courtNumber2 = this.courtUtils.getCourtNumber(event, court2, courtsMap);
  //   const courtNumber3 = this.courtUtils.getCourtNumber(event, court3, courtsMap);

  //   return courtNumber3 > 0
  //     ? courtNumber3
  //     : courtNumber2 > 0 && courtNumber === 0
  //       ? courtNumber2
  //       : courtNumber;
  // }

  private setMatchDates(resultMatch: Match, matchStartDate: string, event: Event) {
    if (matchStartDate) {
      const dateString = matchStartDate.replace('T', ' ');
      resultMatch.date_time = dateString;
      const eventDates = getEventIsoDates(event);
      const isoDate = dateString.substring(0, 10);
      const pos = eventDates.indexOf(isoDate) + 1;
      resultMatch.day = pos > 0 ? pos.toString() : undefined;
    }
  }

  private stripPrefix(teamName: string) {
    const noSuffix = teamName.replace(/\s*\[\[.*\]\]$/, '');
    return noSuffix.replace(/^\[\[.*\]\]\s*/, '').trim();
  }
}
