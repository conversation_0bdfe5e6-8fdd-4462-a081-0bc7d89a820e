export function cleanUpPool(pool: string) {
  let result = pool
    .replace(/Ch/g, 'CH')
    .replace(/Re/g, 'RE')
    .replace(/Con/g, 'CON')
    .replace(/Cross/g, 'X')
    .replace(/Xover/g, 'X')
    .replace(/M[0-9]+$/, '');

  const re = /^(G?1[0-9][A-QS-Z ]?)R[1-9]/;
  const reMatch = re.exec(pool);
  if (reMatch) {
    const strip = reMatch[1];
    result = pool.replace(strip, '');
  }

  result = pool.replace(/[^0-9A-Z]/g, '');

  // Use a max of 28 letters for pool name
  if (result.length > 28) {
    result = pool.substring(0, 28);
  }

  return result;
}
