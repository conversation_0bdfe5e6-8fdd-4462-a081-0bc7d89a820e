import { Injectable, NotFoundException } from '@nestjs/common';

import { messages } from '@/server/constants/messages';

import { ParsingRuleDto } from './dto/create-parsing-template.dto';
import { ParsingTemplateRepository } from './parsing-template.repository';
import { ParsingRuleRepository } from '../parsing-rule/parsing-rule.repository';
import { ParsingRuleService } from '../parsing-rule/parsing-rule.service';

@Injectable()
export class ParsingTemplateService {
  constructor(
    private readonly parsingRuleService: ParsingRuleService,
    private readonly parsingTemplateRepo: ParsingTemplateRepository,
    private readonly parsingRuleRepo: ParsingRuleRepository,
  ) {}

  public async createTemplateWithRules(name: string, rules: ParsingRuleDto[]) {
    const processedRules = rules.map((rule) => this.parsingRuleService.processRule(rule));

    await this.parsingTemplateRepo.createTemplateWithRules(name, processedRules);
  }

  public async getParsingTemplates() {
    const templates = await this.parsingTemplateRepo.getAll();
    return templates.map((template) => ({
      name: template.name,
      schedulerParsingTemplateId: template.schedulerParsingTemplateId,
      rules: template.SchedulerParsingRules,
    }));
  }

  public async getTemplateById(templateId: number) {
    const template = await this.parsingTemplateRepo.getOneById(templateId);
    if (!template) {
      throw new NotFoundException(messages.errors.notFound('template', templateId));
    }
    return {
      name: template.name,
      schedulerParsingTemplateId: template.schedulerParsingTemplateId,
      rules: template.SchedulerParsingRules,
    };
  }

  public async addRule(templateId: number, rule: ParsingRuleDto) {
    const template = await this.parsingTemplateRepo.getOneById(templateId);
    if (!template) {
      throw new NotFoundException(messages.errors.notFound('template', templateId));
    }

    const processedRule = this.parsingRuleService.processRule(rule);

    await this.parsingRuleRepo.createOne(templateId, processedRule);
  }
}
