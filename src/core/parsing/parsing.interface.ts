import { Match } from '../match/match.model';

export interface ParsingJobData {
  eventId: number;
  forceUpdate: boolean;
  post: boolean;
}

export interface FetchedTeam {
  team_master_id: number;
  provider_team_id: string;
  team_name: string;
  master_team_name: string;
  schedule_name: string;
  age: number;
  division: string;
  organization_code: string;
  master_organization_code: string;
}

export interface SWJournalCount {
  count: number;
}

export interface AesProviderData {
  Results: {
    Matches: AesMatch[];
  };
}

export type AesTeams = {
  teamId: number;
  name: string;
  clubId: number;
  clubName: string;
  teamCode: string;
  acceptedTypeId: number;
  acceptedType: {
    orderIndex: number;
    isEligible: boolean;
    hidden: boolean;
    isEligibleForOfficials: boolean;
    isOkToPayEligible: boolean;
    value: number;
    displayName: string;
  };
  eventDivisionAssignmentId: number;
  divisionDescription: string;
}[];

export type EeProviderData = EeMatch[];

export interface EeMatch {
  Id: number;
  Type: number;
  Date: string;
  Time: string;
  Division: {
    Id: number;
    Name: string;
    Order: number;
    Event: object; // Use a more specific type if the Event structure is known
  };
  VenueCourt: {
    Id: number;
    Court: { Name: string }; // Use a more specific type if the Court structure is known
    Venue: {
      Abbr: string;
    }; // Use a more specific type if the Venue structure is known
  };
  AwayTeam: EeTeam;
  HomeTeam: EeTeam;
  BracketName: string;
  Number: number;
  BracketId: number;
  VideoUrl: string;
  Round: number;
}
export interface EeTeam {
  Name: string;
  PoolNumber: number;
  Score: number;
  TeamId: number;
  BracketName: string;
  BracketId: number;
  WinnerGameNumber: number;
  ParentTeamId: number;
}

export interface VbsProviderData {
  data: VbsMatch[];
}
export interface PgProviderData {
  schedule: PgMatch[];
}

export interface TmchProviderData {
  GameDictionary: Record<number, TmchMatch>;
  FacilityDictionary: Record<string, TmchFacility>;
  DivisionDictionary: Record<string, TmchDivision>;
}

export interface GsProviderData {
  matches: GsMatch[] | GsPubMatch[];
}

export interface GsMatch {
  id: number;
  event_name: string;
  type: string;
  event_start_date: string;
  event_end_date: string;
  event_year: string;
  group_id: number;
  allow_streaming: boolean;
  gender: string;
  age: number;
  group_name: string;
  group_tier: number;
  scoring_format: string;
  bracket_id: number;
  bracket_name: string;
  match_type: string;
  match_number: number;
  match_date: string;
  local_match_date: string | null;
  iso_match_date: string;
  time: string;
  local_time: string | null;
  iso_time: string;
  end_time: string | null;
  local_end_time: string | null;
  iso_end_time: string | null;
  duration: string | null;
  venue_timezone: string | null;
  venue_timezone_tz: string;
  created_at: string;
  updated_at: string;
  status: string;
  home_team_reg_id: number;
  home_team_id: number;
  home_team_reg_full_name: string;
  home_team_reg_short_name: string;
  home_team_name: string;
  home_team_short_name: string;
  home_team_state: string;
  home_club_id: number;
  home_club_name: string;
  home_club_url: string;
  home_score: number | null;
  away_team_reg_id: number;
  away_team_id: number;
  away_team_reg_full_name: string;
  away_team_reg_short_name: string;
  away_team_name: string;
  away_team_short_name: string;
  away_team_state: string;
  away_club_id: number;
  away_club_name: string;
  away_club_url: string;
  away_score: number | null;
  venue_id: number | null;
  pitch_id: number | null;
  venue_name: string | null;
  venue_address1: string | null;
  venue_address2: string | null;
  venue_city: string | null;
  venue_country: string | null;
  venue_state: string | null;
  zip: string | null;
  logo: string | null;
  latitude: number | null;
  longitude: number | null;
  pitch_name: string | null;
  surface: string | null;
  playoff_tier: string;
  playoff_tier_full_name: string;
  playoff_home_team: string;
  playoff_away_team: string;
  tier_index: number | null;
  match_index: number | null;
  home_playoff_team_bracket_id: number | null;
  away_playoff_team_bracket_id: number | null;
  playoff_element_id: number | null;
  playoff_format_id: number | null;
  playoff_format_name: string | null;
}

export interface GsPubMatch {
  matchId: string;
  local_match_date: string;
  local_time: string;
  home_team: string;
  away_team: string;
  home_team_reg_id: string;
  away_team_reg_id: string;
  home_score: number | null;
  away_score: number | null;
  venue_name: string;
  pitch_name: string;
  group_name: string;
  gender: string;
}
export interface TmchDivision {
  Name: string;
  IDGames: number[];
}

export interface TmchFacility {
  IDFacility: string;
  IDComplex: string;
  Name: string;
  DisplayName: string;
  Notes: string;
  Sort: number;
}

export interface TmchMatch {
  IDG: string;
  GN: string;
  PID4: string;
  IDT1: string;
  T1: string;
  IDT2: string;
  T2: string;
  IDT3: string;
  T3: string;
  S: string; // Assuming it is a date-time string
  T: string; // Assuming it is a time string
  IDF: string;
  S1: number;
  S2: number;
  SS: string;
  F: boolean;
  N: string;
  ISID: string;
  BT1S: string;
  BT1SP: string;
  BT1SGN: string;
  BT2S: string;
  BT2SP: string;
  BT2SGN: string;
  LP: number;
  TP: number;
  W: number;
  H: number;
  BR: boolean;
  BL: boolean;
  SL: boolean;
  VURL: string;
  Team1Exhibition: boolean;
  Team2Exhibition: boolean;
  matchId?: string;
}

export interface PgMatch {
  eventId: number;
  tournamentGameId: number;
  gameNumber: number;
  pool: string;
  homeTeamId: number;
  visitorTeamId: number;
  date: string;
  ballparkId: number;
  ballparkName: string;
  fieldId: number;
  fieldName: string;
  fieldNumber: string;
  time: string;
  division: string;
  awayTeam?: string;
  homeTeam?: string;
}

export interface VbsMatch {
  id: string;
  eventId: string;
  eventName: string;
  divisionId: string;
  divisionName: string;
  type: string;
  timezone: string;
  startTime: string;
  startTimeInEventTimezone: string;
  completedTime: string;
  completedTimeInEventTimezone: string;
  trackingLabel: string;
  teamOneName: string;
  teamOneIdentifier: string;
  teamOneTrackingLabel: string;
  teamTwoName: string;
  teamTwoIdentifier: string;
  teamTwoTrackingLabel: string;
  courtName: string;
  courtLocation: string;
  updatedAt: string;
  isTrashed: boolean;
}
export interface VbsPubMatch {
  id: string;
  divisionName: string;
  courtName: string;
  courtLocation: string;
  teamOneName: string;
  teamOneIdentifier: string;
  teamTwoName: string;
  teamTwoIdentifier: string;
  startTime: number;
  timezone: string;
}

export interface VbsPubProviderData {
  data: VbsPubMatch[];
}

export interface AesMatch {
  Division?: string;
  FirstTeamId: number;
  SecondTeamId: number;
  PlayId: number;
  PlayName: string;
  WorkTeamId: number;
  WorkTeamName: string;
  Outcome: string;
  Score: string;
  MatchEndDateTime: string;
  MatchId: number | string;
  FirstTeamName: string;
  FirstTeamClubName: string;
  SecondTeamName: string;
  SecondTeamClubName: string;
  CourtName: string;
  DivisionName: string;
  MatchStartDateTime: string;
  CourtId: number;
  FirstTeamCode?: string;
  SecondTeamCode?: string;
}

export interface Schedule {
  schedule: {
    match: Match[];
  };
}

export interface PgToken {
  token: string;
  expirationDate: Date;
}

export interface FetchedPgToken {
  access_token: string;
  expires_in: number;
}

export type TmProviderData = TmMatch[];

export type CpDocProviderData = string[][];

export type SwProviderData = SwMatch[];

export interface SwMatch {
  match_uuid: string; // UUID format
  event: number; // Event ID
  match_id: string; // Composite Match ID
  div: string; // Division (e.g., "G15N")
  day: number; // Day number (e.g., 4)
  date_time: string; // Date and time in "YYYY-MM-DD HH:MM:SS" format
  court: number; // Court number (e.g., 3)
  court_alpha: string; // Court identifier as a string (e.g., "3")
  pool: string; // Pool identifier (e.g., "R3S")
  team1_roster_id: number; // Team 1 Roster ID
  team2_roster_id: number; // Team 2 Roster ID
  ref_roster_id: number; // Referee Roster ID (0 if not assigned)
  team_1_name: string; // Team 1 Name (e.g., "Great Plains Girls 15U Blue")
  team_2_name: string; // Team 2 Name (e.g., "OK All Star 15 1")
  master_team_id_1: number; // Master Team ID for Team 1
  master_team_id_2: number; // Master Team ID for Team 2
  ref_name: string | null; // Referee Name (null if not assigned)
  match_type: string; // Type of match (e.g., "3 of 5")
}

export interface TmMatch {
  provider_match_id: number;
  match_time: string | number;
  court_name: string;
  court_location: string;
  first_provider_team_id: number;
  first_team_name: string;
  first_team_division: string | null;
  second_provider_team_id: number;
  second_team_name: string;
  second_team_division: string | null;
  timezone?: string;
}

export interface AesPubMatch {
  firstTeam: string;
  firstTeamId?: number;
  firstTeamCode?: string;
  secondTeam: string;
  secondTeamId?: number;
  secondTeamCode?: string;
  courtName: string;
  divisionName: string;
  matchId: number | string;
  dateTimestamp: number;
}

export type AesPubProviderData = AesPubMatch[];

export interface TmchPubMatch {
  datetime: string;
  court: string;
  team_1_id: string;
  team_1_name: string;
  team_1_score: string;
  team_2_score: string;
  team_2_id: string;
  team_2_name: string;
  division: string;
}

export type TmchPubProviderData = TmchPubMatch[];

export type ProviderData =
  | TmProviderData
  | GsProviderData
  | EeProviderData
  | TmchProviderData
  | PgProviderData
  | AesProviderData
  | VbsProviderData
  | CpDocProviderData
  | AesPubProviderData
  | SwProviderData
  | VbsPubProviderData
  | TmchPubProviderData
  | string;
