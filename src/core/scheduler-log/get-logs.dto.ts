import { DatePeriodQueryDto } from '@/server/dto/date-period.query.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { ParsingStatus } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, Min } from 'class-validator';

export class GetLogsDto extends DatePeriodQueryDto {
  @IsEnum(ParsingStatus)
  @IsOptional()
  @ApiPropertyOptional({
    enum: ParsingStatus,
    description: 'Status of parsing',
  })
  status?: ParsingStatus;

  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  @Min(1)
  limit?: number = 50;

  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  @Min(1)
  page?: number = 1;
}
