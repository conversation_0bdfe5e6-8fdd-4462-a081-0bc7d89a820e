import { Module } from '@nestjs/common';

import { PrismaModule } from '@/server/prisma';

import { SchedulerLogController } from './scheduler-log.controller';
import { SchedulerLogRepository } from './scheduler-log.repository';
import { SchedulerLogService } from './scheduler-log.service';

@Module({
  imports: [PrismaModule],
  providers: [SchedulerLogRepository, SchedulerLogService],
  exports: [SchedulerLogRepository, SchedulerLogService],
  controllers: [SchedulerLogController],
})
export class SchedulerLogModule {}
