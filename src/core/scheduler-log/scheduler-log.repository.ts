import { Injectable } from '@nestjs/common';
import { ParsingStatus } from '@prisma/client';

import { PrismaService } from '@/server/prisma';

import { Event } from '../event/event.model';

type SchedulerLogUpdateData = {
  parsingStatus: 'success' | 'error' | 'no_updates' | 'no_schedule' | 'no_provider_data';
  endDatetime?: Date;
  extraCount?: number;
  matchCount?: string;
  unknownCount?: number;
  errorMessage?: string;
  scheduleLength?: string;
  unknownTeams?: string;
};
@Injectable()
export class SchedulerLogRepository {
  constructor(private readonly prisma: PrismaService) {}

  public async getSchedulerLogs(
    status: ParsingStatus,
    pagination: { limit: number; page: number },
    datePeriod: { startDate: Date; endDate: Date },
    eventId?: number,
  ) {
    const offset = (pagination.page - 1) * pagination.limit;
    if (!datePeriod.startDate) {
      datePeriod.startDate = new Date(
        new Date(datePeriod.endDate).setMonth(datePeriod.endDate.getMonth() - 2),
      );
    }

    return this.prisma.schedulerLog.findMany({
      where: {
        parsingStatus: status,
        startDatetime: { gte: datePeriod.startDate, lte: datePeriod.endDate },
        tournamentId: eventId,
      },
      skip: offset,
      take: pagination.limit,
      orderBy: { startDatetime: 'desc' },
    });
  }

  public async getEventLastParsingDate(tournamentId: number) {
    const log = await this.prisma.schedulerLog.findFirst({
      where: {
        tournamentId,
        parsingStatus: {
          in: ['success', 'no_updates', 'error', 'no_schedule', 'no_provider_data'],
        },
      },
      orderBy: { startDatetime: 'desc' },
    });

    return log?.startDatetime;
  }

  public async createLog(tournamentId: number) {
    return this.prisma.schedulerLog.create({
      data: {
        startDatetime: new Date(),
        tournamentId,
        parsingStatus: 'in_process',
      },
    });
  }

  public async updateLog(logId: number, updateData: SchedulerLogUpdateData) {
    return this.prisma.schedulerLog.update({
      where: { schedulerLogId: logId },
      data: {
        endDatetime: new Date(),
        ...updateData,
      },
    });
  }

  public async logSuccess(logId: number, event: Event) {
    return this.updateLog(logId, {
      parsingStatus: 'success',
      extraCount: event.extra_count,
      matchCount: event.match_count,
      unknownCount: event.unknown_count,
      scheduleLength: event.schedule_length,
      unknownTeams:
        event.unknown_teams.length > 0 ? JSON.stringify(event.unknown_teams) : undefined,
    });
  }

  public async logFail(logId: number, errorMessage: string) {
    return this.updateLog(logId, {
      parsingStatus: 'error',
      errorMessage,
    });
  }

  public async logNoUpdates(logId: number) {
    return this.updateLog(logId, {
      parsingStatus: 'no_updates',
    });
  }

  public async logNoSchedule(logId: number, event: Event) {
    return this.updateLog(logId, {
      parsingStatus: 'no_schedule',
      scheduleLength: event.schedule_length,
      extraCount: event.extra_count,
      matchCount: event.match_count,
      unknownCount: event.unknown_count,
    });
  }

  public async logNoProviderData(logId: number) {
    return this.updateLog(logId, {
      parsingStatus: 'no_provider_data',
    });
  }
}
