import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { eachLimit } from 'async';

import { StreamService } from './stream.service';
import { EventMapper } from '../event/event.mapper';
import { EventRepository } from '../event/event.repository';

@Injectable()
export class StreamCron {
  constructor(
    private readonly streamService: StreamService,
    private readonly eventRepo: EventRepository,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  public async syncStreams() {
    const activeEvents = await this.eventRepo.getActiveEvents();

    await eachLimit(activeEvents, 3, async (event) => {
      console.log(`Syncing event ${event.tournament_id}`, new Date().toISOString());
      await this.streamService.syncStreams({
        provider: EventMapper.getSchProviderName(event),
        provider_tournament_id: EventMapper.getSchProviderTournamentId(event),
        tournament_id: event.tournament_id,
      });
      console.log(`Done event ${event.tournament_id}`);
    });
  }
}
