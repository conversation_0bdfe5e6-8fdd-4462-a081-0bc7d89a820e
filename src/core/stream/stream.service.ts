import { Injectable } from '@nestjs/common';
import { Prisma, StreamStatus } from '@prisma/client';

import { ServerConfig } from '@/server/config/server.config';
import { FirebaseService } from '@/server/services/firebase.service';
import { extractProvider } from '@/server/utils/extract-provider';

import { BallerTvStream } from './stream.interface';
import { StreamRepository } from './stream.repository';
import { ApiService } from '../api/api.service';
import { ScheduleRepository } from '../schedule/schedule.repository';

type NewGameVideo = {
  tournamentId: number;
  scheduleMatchId: number;
  title: string;
  providerUrl: string;
  scores: string;
};

@Injectable()
export class StreamService {
  constructor(
    private readonly apiService: ApiService,
    private readonly streamRepository: StreamRepository,
    private readonly scheduleRepository: ScheduleRepository,
    private readonly firebaseService: FirebaseService,
  ) {}

  public async syncStreams({
    provider_tournament_id,
    provider,
    tournament_id,
  }: {
    provider_tournament_id: string;
    provider: string;
    tournament_id: number;
  }) {
    try {
      if (!['AES'].includes(extractProvider(provider))) {
        return;
      }
      const [streamData, eventMatches] = await Promise.all([
        this.apiService.getBallerTvData(provider_tournament_id, extractProvider(provider)),
        this.scheduleRepository.getAllEventMatchesWithStreams(tournament_id),
      ]);

      const streams = streamData.filter(
        (stream) => (stream.stream_state === StreamStatus.live
            || stream.stream_state === StreamStatus.replay)
          && stream.sd_stream_url
          && stream.partner_game_id !== null,
      );
      if (streams.length === 0) {
        return;
      }
      console.log('streams got', new Date().toISOString());

      const streamMap = new Map<string, BallerTvStream>(streams.map((s) => [s.partner_game_id, s]));
      const createVideos: NewGameVideo[] = [];
      const updateMatches: (Prisma.ScheduleMatchUpdateInput & {
        shedule_match_id: number;
      })[] = [];
      const updateScores: { gameVideoId: number; scores: string }[] = [];
      const fbData = new Map<string, any>();
      for (const match of eventMatches) {
        const partnerId = this.getPartnerGameId(match.match_id, provider);
        const stream = streamMap.get(partnerId);
        // → Create
        if (!match.game_video_id && stream) {
          const scores = this.getBallerTvScores(stream);
          createVideos.push({
            tournamentId: tournament_id,
            scheduleMatchId: match.shedule_match_id,
            title: `${match.team_1_name || 'Team 1'} vs ${match.team_2_name || 'Team 2'}`,
            providerUrl: stream.sd_stream_url,
            scores,
          });
          updateMatches.push({
            shedule_match_id: match.shedule_match_id,
            stream_url: stream.sd_stream_url,
            stream_status: stream.stream_state,
          });
          fbData.set(`${match.shedule_match_id}`, {
            stream_url: stream.sd_stream_url,
            stream_status: stream.stream_state,
            scores,
          });
          continue;
        }

        // → Update
        if (match.game_video_id && stream) {
          const scores = this.getBallerTvScores(stream);
          if (match.scores !== scores) {
            updateScores.push({ gameVideoId: match.game_video_id, scores });
            fbData.set(`${match.shedule_match_id}/scores`, scores);
          }
          if (match.stream_status !== stream.stream_state) {
            updateMatches.push({
              shedule_match_id: match.shedule_match_id,
              stream_status: stream.stream_state,
            });
            fbData.set(`${match.shedule_match_id}/stream_status`, stream.stream_state);
          }
          continue;
        }

        if (match.game_video_id && !stream && match.stream_status === StreamStatus.live) {
          updateMatches.push({
            shedule_match_id: match.shedule_match_id,
            stream_status: StreamStatus.replay,
          });
          fbData.set(`${match.shedule_match_id}/stream_status`, StreamStatus.replay);
        }
      }
      console.log('fbData', fbData.size);
      console.log('updateMatches', updateMatches.length, updateMatches.slice(0, 10));
      console.log('createVideos', createVideos.length, createVideos.slice(0, 10));
      console.log('updateScores', updateScores.length, updateScores.slice(0, 10));
      await Promise.all([
        createVideos.length && this.streamRepository.createGameVideos(createVideos),
        updateScores.length && this.streamRepository.updateGameVideos(updateScores),
        updateMatches.length && this.scheduleRepository.updateMany(updateMatches),
        fbData.size && this.saveFbData(tournament_id, fbData),
      ]);

      await this.syncAthletesStreams(tournament_id);
    } catch (error) {
      console.error(`Event ${tournament_id} - BallerTV integration failed`);
      console.error(error);
    }
  }

  public async syncGameVideoTitles(eventId: number) {
    await this.streamRepository.syncGameVideoTitles(eventId);
  }

  private getPartnerGameId(matchId: string, provider: string) {
    switch (provider) {
      case 'AES':
        return matchId.split('_')[1];
      case 'PLACEHOLDER':
        return matchId.split('_')[1];
      default:
        return null;
    }
  }

  private getBallerTvScores(stream: BallerTvStream) {
    const [teamA, teamB] = stream.teams;
    const scoresA = teamA.set_scores;
    const scoresB = teamB.set_scores;
    const len = Math.min(scoresA.length, scoresB.length);

    let hasAnyScore = false;
    const parts: string[] = [];

    for (let i = 0; i < len; i += 1) {
      const a = scoresA[i];
      const b = scoresB[i];
      if (a !== 0 || b !== 0) {
        hasAnyScore = true;
      }
      parts.push(`${a}-${b}`);
    }

    return hasAnyScore ? parts.join(',') : null;
  }

  private async syncAthletesStreams(tournamentId: number) {
    await this.streamRepository.syncGameVideoAthletes(tournamentId);
  }

  private async saveFbData(
    tournamentId: number,
    data:
    | Map<string, string>
    | Map<number, { stream_url: string; stream_status: StreamStatus; scores: string }>,
  ) {
    const ref = `${ServerConfig.NODE_ENV}/events/${tournamentId}/baller-tv`;
    await this.firebaseService.updateData(ref, Object.fromEntries(data));
  }
}

//   private async syncAthletesStreams({ tournament_id }: { tournament_id: number }) {
//     const matchesWithStreams = await this.scheduleRepository.getMatchesWithStreams(tournament_id);
//     if (!matchesWithStreams.length) return;

//     const rosterIds = new Set<number>();
//     const gameVideoIds: number[] = [];
//     for (const m of matchesWithStreams) {
//       if (m.team_1_roster_id) rosterIds.add(m.team_1_roster_id);
//       if (m.team_2_roster_id) rosterIds.add(m.team_2_roster_id);
//       if (m.game_video_id) gameVideoIds.push(m.game_video_id);
//     }
//     const [athletes, existingLinks] = await Promise.all([
//       this.athleteRepository.getAthletesByTeamRosterIds(Array.from(rosterIds)),
//       this.streamRepository.getEventGameVideoAthletes(gameVideoIds),
//     ]);

//     const athletesByRoster = new Map<number, number[]>();
//     for (const { team_roster_id: rosterId, athlete_master_id: masterId } of athletes) {
//       if (!masterId) continue;

//       if (athletesByRoster.has(rosterId)) {
//         athletesByRoster.get(rosterId).push(masterId);
//       } else {
//         athletesByRoster.set(rosterId, [masterId]);
//       }
//     }
//     if (athletesByRoster.size === 0) return;

//     const existingSet = new Set<string>();
//     for (const ev of existingLinks) {
//       existingSet.add(`${ev.gameVideoId}_${ev.athleteMasterId}`);
//     }

//     const toCreate: GameVideoAthletePayload[] = [];

//     for (const match of matchesWithStreams) {
//       for (const rosterId of [match.team_1_roster_id, match.team_2_roster_id]) {
//         const masters = athletesByRoster.get(rosterId);
//         if (!masters) continue;

//         for (const masterId of masters) {
//           const key = `${match.game_video_id}_${masterId}`;
//           if (!existingSet.has(key)) {
//             toCreate.push({
//               gameVideoId: match.game_video_id,
//               athleteMasterId: masterId,
//             });
//             existingSet.add(key);
//           }
//         }
//       }
//     }

//     if (toCreate.length) {
//       await this.streamRepository.createGameVideoAthletes(toCreate);
//     }
//   }

//  private async createStreams(
//     { tournament_id, provider }: { tournament_id: number; provider: string },
//     streamMap: Map<string, BallerTvStream>,
//     eventMatches: ScheduleMatchWithStreams[],
//   ) {
//     const newFbData = new Map<number, StreamFbData>();

//     const matchesWithoutStreams = eventMatches.filter((match) => !match.game_video_id);
//     if (matchesWithoutStreams.length === 0) {
//       return newFbData;
//     }

//     const scheduleMatchUpdates: (Prisma.ScheduleMatchUpdateInput & {
//       shedule_match_id: number;
//     })[] = [];
//     const newGameVideos: NewGameVideo[] = [];

//     for (const match of matchesWithoutStreams) {
//       const partnerId = this.getPartnerGameId(match.match_id, provider);
//       const stream = streamMap.get(partnerId);
//       if (!stream) continue;

//       const scores = this.getBallerTvScores(stream);

//       newFbData.set(match.shedule_match_id, {
//         stream_url: stream.sd_stream_url,
//         stream_status: stream.stream_state,
//         scores,
//       });
//       scheduleMatchUpdates.push({
//         shedule_match_id: match.shedule_match_id,
//         stream_url: stream.sd_stream_url,
//         stream_status: stream.stream_state,
//       });
//       newGameVideos.push({
//         tournamentId: tournament_id,
//         scheduleMatchId: match.shedule_match_id,
//         title: `${match.team_1_name || 'Team 1'} vs ${match.team_2_name || 'Team 2'}`,
//         providerUrl: stream.sd_stream_url,
//         scores,
//       });
//     }

//     if (newGameVideos.length > 0) {
//       await this.streamRepository.createGameVideos(newGameVideos);
//       await this.scheduleRepository.updateMany(scheduleMatchUpdates);
//     }

//     return newFbData;
//   }

//   private async updateStreams(
//     { provider }: { provider: string },
//     streamMap: Map<string, BallerTvStream>,
//     eventMatches: ScheduleMatchWithStreams[],
//   ) {
//     const matchesWithStreams = eventMatches.filter((match) => match.game_video_id);

//     const gameVideoUpdates: { gameVideoId: number; scores?: string }[] = [];
//     const updateFbData = new Map<string, string>();
//     const scheduleMatchUpdates: (Prisma.ScheduleMatchUpdateInput & {
//       shedule_match_id: number;
//     })[] = [];

//     for (const match of matchesWithStreams) {
//       const partnerId = this.getPartnerGameId(match.match_id, provider);
//       const foundStream = streamMap.get(partnerId);

//       if (!foundStream) {
//         if (match.stream_status === StreamStatus.live) {
//           scheduleMatchUpdates.push({
//             shedule_match_id: match.shedule_match_id,
//             stream_status: StreamStatus.replay,
//           });
//           updateFbData.set(`${match.shedule_match_id}/stream_status`, StreamStatus.replay);
//         }
//         continue;
//       }

//       const ballerTvScores = this.getBallerTvScores(foundStream);
//       if (match.scores !== ballerTvScores) {
//         gameVideoUpdates.push({
//           gameVideoId: match.game_video_id,
//           scores: ballerTvScores,
//         });
//         updateFbData.set(`${match.shedule_match_id}/scores`, ballerTvScores);
//       }

//       if (match.stream_status !== foundStream.stream_state) {
//         scheduleMatchUpdates.push({
//           shedule_match_id: match.shedule_match_id,
//           stream_status: foundStream.stream_state,
//         });
//         updateFbData.set(`${match.shedule_match_id}/stream_status`, foundStream.stream_state);
//       }
//     }

//     await Promise.all([
//       gameVideoUpdates.length && this.streamRepository.updateGameVideos(gameVideoUpdates),
//       scheduleMatchUpdates.length && this.scheduleRepository.updateMany(scheduleMatchUpdates),
//     ]);

//     return updateFbData;
//   }
