import { Injectable } from '@nestjs/common';

// import { ACTIVE_ROSTER_STATUSES } from '@/server/constants/constants';
import { PrismaService } from '@/server/prisma';

import { FetchedTeam } from '../parsing/parsing.interface';

@Injectable()
export class TeamRepository {
  constructor(private readonly prisma: PrismaService) {}

  public async getEventTeams(eventId: number): Promise<FetchedTeam[]> {
    return this.prisma.$queryRaw`
    SELECT tm.team_master_id,
      tr.roster_source_team_row_id provider_team_id,
      tr.team_name,
      tm.team_name AS master_team_name,
      tm.schedule_name,
      tm.age,
      IFNULL(tr.division, tm.division) division,
      tr.organization_code organization_code,
      tm.organization_code master_organization_code
    FROM team_roster tr
      INNER JOIN team_master tm ON (tm.team_master_id = tr.team_master_id)
    WHERE tr.tournament_id = ${eventId}
      and tr.roster_source_status IN ('' , 'new', 'updated')
    ORDER BY tm.team_name`;
  }

  public async getEventUaAndMappedProviderTeams(eventId: number) {
    const results: any[] = await this.prisma.$queryRaw`
    WITH team_map AS (
    SELECT stm.team_master_id,
        stm.tournament_id,
        stm.provider_team_id,
        stm.provider_team_name,
        stm.provider_div,
        stm.provider_club_name,
        stm.provider_team_code
    FROM scheduler_team_map stm
    WHERE stm.tournament_id = ${eventId}
    )
    SELECT tm.team_master_id,
      tm.team_name AS master_team_name,
      tr.roster_source_team_row_id provider_team_id,
      tr.scheduler_is_hidden,
      cm.club_name,
      tr.organization_code organization_code,
      IFNULL(tr.division, tm.division) division,
     map.provider_team_id,
     map.provider_team_name,
     map.provider_div,
     map.provider_club_name,
     map.provider_team_code,
     (
        SELECT COUNT(*)
        FROM athlete_roster ar
        WHERE 
            ar.team_roster_id = tr.team_roster_id
            AND ar.roster_source_status IN ('', 'new', 'updated')
    ) AS active_athletes
    FROM team_roster tr
      INNER JOIN team_master tm ON (tm.team_master_id = tr.team_master_id)
      LEFT JOIN team_map map ON (map.team_master_id = tr.team_master_id)
      LEFT JOIN club_master cm ON (tm.club_master_id = cm.club_master_id)
    WHERE tr.tournament_id = ${eventId}
      AND tr.roster_source_status IN ('', 'new', 'updated')
    ORDER BY CASE
        WHEN map.provider_team_id IS NOT NULL OR map.provider_team_name IS NOT NULL THEN 1
        ELSE 0
    END,
    master_team_name;`;

    return results.map((row) => ({
      uaTeam: {
        teamMasterId: row.team_master_id,
        masterTeamName: row.master_team_name,
        division: row.division,
        clubName: row.club_name,
        organizationCode: row.organization_code,
        isHidden: row.scheduler_is_hidden,
        activeAthletesCount: Number(row.active_athletes),
      },
      providerTeam:
        row.provider_team_id === null && row.provider_team_name === null
          ? null
          : {
            providerTeamId: row.provider_team_id,
            providerTeamName: row.provider_team_name,
            providerDiv: row.provider_div,
            providerClubName: row.provider_club_name,
            providerTeamCode: row.provider_team_code,
          },
    }));
  }

  public async deleteOne(eventId: number, teamId: number) {
    await this.prisma.$transaction(async () => {
      await this.prisma.$queryRaw`
      UPDATE team_roster
      SET roster_source_status = 'old',
          locked_row = 'y'
      WHERE team_master_id = ${teamId}
        AND tournament_id = ${eventId}
      `;

      await this.prisma.$queryRaw`
      UPDATE athlete_roster
      SET roster_source_status = 'old',
          locked_row = 'y'
      WHERE team_master_id = ${teamId}
        AND tournament_id = ${eventId}
      `;
    });
  }
}
