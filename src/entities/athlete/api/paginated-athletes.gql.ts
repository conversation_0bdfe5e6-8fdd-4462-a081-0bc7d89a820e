import { gql } from '@apollo/client';

export const PAGINATED_ATHLETES_QUERY = gql`
	query PaginatedAthletes($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
		paginatedAthletes(eventKey: $eswId, page: $page, pageSize: $pageSize, search: $search) {
			items {
				athlete_id
				first
				last
				club_name
				team_id
				team_name
				organization_code
				state
				short_position
				uniform
			}
			page_info {
				page
				page_size
				page_count
				item_count
			}
		}
	}
`;
