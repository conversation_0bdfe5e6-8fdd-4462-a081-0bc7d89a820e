import { DivisionDetails } from '@entities/event-overview';

import { RoundGroup, RoundGroupKeyType } from '@shared/domain/round';

export type RoundDetails = DivisionDetails['rounds'][number];

export type DivisionRoundsManagerParams = {
	eswId: string;
	divisionId: string;
};

export type DivisionRoundsManagerReturnType = {
	rounds: RoundDetails[] | null;
	roundsGroups: RoundGroup<RoundDetails>[] | null;
	getRoundGroupKeyByRoundId(roundId: string): RoundGroupKeyType | null;
	getRoundGroupByGroupKey(groupKey: RoundGroupKeyType): RoundGroup<RoundDetails> | null;
	getRoundsByGroupKey(groupKey: RoundGroupKeyType): RoundDetails[] | null;
	loading: boolean;
};
