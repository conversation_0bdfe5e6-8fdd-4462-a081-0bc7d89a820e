import { useSignals } from '@preact/signals-react/runtime';
import { useCallback, useEffect } from 'react';

import { favoriteTeamsService } from '../services';
import { favoriteTeamsIdsSignal } from '../signals';
import { FavoriteClubRef, FavoriteTeamRef, FavoriteTeamsManagerReturnType } from '../types';

const NO_FAVORITE_TEAMS: string[] = [];

export const useFavoriteTeamsManager = (eswId: string): FavoriteTeamsManagerReturnType => {
	useSignals();

	useEffect(() => {
		// Initialize favorite teams from localStorage on mount and ensure it happens only once
		const { teamsIds, eswId: teamsEswId } = favoriteTeamsIdsSignal.value;
		if (teamsEswId !== eswId || !teamsIds) {
			favoriteTeamsIdsSignal.value = {
				teamsIds: favoriteTeamsService.getFavoriteTeamsIds(eswId),
				eswId,
			};
		}
	}, [eswId]);

	const toggleTeamFavorite = useCallback(
		(team: FavoriteTeamRef) => {
			if (eswId) {
				favoriteTeamsService.toggleTeamFavorite(eswId, team);
				favoriteTeamsIdsSignal.value = {
					teamsIds: favoriteTeamsService.getFavoriteTeamsIds(eswId),
					eswId,
				};
			}
		},
		[eswId],
	);

	const toggleClubTeamsFavorite = useCallback(
		(club: FavoriteClubRef) => {
			if (eswId) {
				favoriteTeamsService.toggleClubTeamsFavorite(eswId, club);
				favoriteTeamsIdsSignal.value = {
					teamsIds: favoriteTeamsService.getFavoriteTeamsIds(eswId),
					eswId,
				};
			}
		},
		[eswId],
	);

	return {
		favoriteTeamsIds: favoriteTeamsIdsSignal.value.teamsIds || NO_FAVORITE_TEAMS,
		toggleTeamFavorite,
		toggleClubTeamsFavorite,
	};
};
