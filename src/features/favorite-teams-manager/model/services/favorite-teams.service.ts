import { FavoriteClubRef, FavoriteTeamRef } from '../types';

const STORAGE_KEY = 'favoriteTeams';

/**
 * Service for managing favorite teams in localStorage
 */
class FavoriteTeamsService {
	private static instance: FavoriteTeamsService | null = null;

	public static getInstance(): FavoriteTeamsService {
		if (!FavoriteTeamsService.instance) {
			FavoriteTeamsService.instance = new FavoriteTeamsService();
		}
		return FavoriteTeamsService.instance;
	}

	private constructor() {
		// Initialize storage if it doesn't exist
		if (typeof window !== 'undefined' && !localStorage.getItem(STORAGE_KEY)) {
			const favorites = {};
			localStorage.setItem(STORAGE_KEY, JSON.stringify(favorites));
		}
	}

	private getFavorites(): Record<string, FavoriteTeamRef[]> {
		if (typeof window === 'undefined') return {};

		const favorites = localStorage.getItem(STORAGE_KEY);
		return favorites ? JSON.parse(favorites) : {};
	}

	private saveFavorites(favorites: Record<string, FavoriteTeamRef[]>): void {
		if (typeof window === 'undefined') return;

		localStorage.setItem(STORAGE_KEY, JSON.stringify(favorites));
	}

	public getFavoriteTeams(eswId: string): FavoriteTeamRef[] {
		const favorites = this.getFavorites();
		return favorites[eswId] || [];
	}

	public getFavoriteTeamsIds(eswId: string): string[] {
		return this.getFavoriteTeams(eswId).map((team: FavoriteTeamRef) => team.team_id) || [];
	}

	public isTeamFavorite(eswId: string, teamId: string): boolean {
		return this.getFavoriteTeamsIds(eswId).includes(teamId);
	}

	public addTeamToFavorite(eswId: string, team: FavoriteTeamRef): void {
		const favorites = this.getFavorites();
		if (!favorites[eswId]) {
			favorites[eswId] = [];
		}
		if (!this.isTeamFavorite(eswId, team.team_id)) {
			const { team_id, club_id } = team;
			favorites[eswId].push({ team_id, club_id });
		}
		this.saveFavorites(favorites);
	}

	public removeTeamFromFavorite(eswId: string, teamId: string): void {
		const favorites = this.getFavorites();
		if (favorites[eswId]) {
			favorites[eswId] = favorites[eswId].filter((team) => team.team_id !== teamId);
			if (!favorites[eswId].length) {
				delete favorites[eswId]; // Remove the eventId if no teams left
			}
			this.saveFavorites(favorites);
		}
	}

	public toggleTeamFavorite(eswId: string, team: FavoriteTeamRef): void {
		if (this.isTeamFavorite(eswId, team.team_id)) {
			this.removeTeamFromFavorite(eswId, team.team_id);
		} else {
			this.addTeamToFavorite(eswId, team);
		}
	}

	public toggleClubTeamsFavorite(eswId: string, club: FavoriteClubRef): void {
		const { roster_club_id, teams } = club;
		const clubTeamsIds = teams.map((team) => team.team_id);
		const favoriteTeams = this.getFavoriteTeams(eswId).filter(
			(team) => team.club_id === roster_club_id,
		);
		const favoriteTeamIds = favoriteTeams.map((team) => team.team_id);

		const allTeamsSelected = clubTeamsIds.every((teamId) => favoriteTeamIds.includes(teamId));

		if (allTeamsSelected) {
			// Unselect all teams if all are already selected
			clubTeamsIds.forEach((teamId) => this.removeTeamFromFavorite(eswId, teamId));
		} else {
			// Add all teams if not all are selected
			teams.forEach((team) => this.addTeamToFavorite(eswId, team));
		}
	}
}

export const favoriteTeamsService = FavoriteTeamsService.getInstance();
