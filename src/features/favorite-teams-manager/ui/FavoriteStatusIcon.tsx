import favoriteChecked from '@shared/assets/icons/favoriteChecked-icon.svg';
import favoriteUnchecked from '@shared/assets/icons/favoriteUnchecked-icon.svg';
import halfFavoriteBigIcon from '@shared/assets/icons/halfFavoriteBig-icon.svg';

import { FavoriteStatus } from '../model';

type Props = {
	status: FavoriteStatus;
	onClick?: (e: React.MouseEvent) => void;
};

export const FavoriteStatusIcon = ({ status, onClick }: Props) => {
	let icon = favoriteUnchecked;
	if (status === FavoriteStatus.Full) {
		icon = favoriteChecked;
	} else if (status === FavoriteStatus.Partial) {
		icon = halfFavoriteBigIcon;
	}
	return <img src={icon} onClick={onClick} alt="" style={{ cursor: 'pointer' }} />;
};
