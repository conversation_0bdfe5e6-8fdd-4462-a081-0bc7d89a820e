import { Outlet, useParams } from 'react-router';
import styled from 'styled-components';

import { NavigationButtons } from '@shared/ui/components';

const EventLayoutWrapper = styled.div`
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	font-family: 'Public Sans', sans-serif;
`;

const EventHeader = styled.header`
	background-color: ${({ theme }) => theme.colors.primary};
	color: white;
	padding: 1rem;
	text-align: center;
`;

const EventContent = styled.main`
	flex: 1;
	padding: 1rem;
`;

const NavContainer = styled.div`
	background-color: #f5f5f5;
	padding: 1rem;
	border-bottom: 1px solid #ddd;
`;

export const EventLayout = () => {
	const { eswId } = useParams();

	return (
		<EventLayoutWrapper>
			<EventHeader>
				<h1>Event: {eswId}</h1>
			</EventHeader>
			<NavContainer>
				<NavigationButtons showEventLinks showDivisionLinks />
			</NavContainer>
			<EventContent>
				<Outlet />
			</EventContent>
		</EventLayoutWrapper>
	);
};
