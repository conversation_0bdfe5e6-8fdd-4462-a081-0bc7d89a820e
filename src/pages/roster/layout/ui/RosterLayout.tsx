import { Link, Outlet, useLocation, useNavigate, useParams } from 'react-router';
import styled from 'styled-components';

const RosterLayoutWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const TabsContainer = styled.div`
	display: flex;
	margin-bottom: 1rem;
`;

const Tab = styled(Link)<{ $active: boolean }>`
	padding: 0.5rem 1rem;
	margin-right: 0.5rem;
	background-color: ${({ $active, theme }) => ($active ? theme.colors.primary : 'transparent')};
	color: ${({ $active }) => ($active ? 'white' : 'inherit')};
	border-radius: 4px;
	text-decoration: none;
	cursor: pointer;
`;

const Content = styled.div`
	padding: 1rem;
	border: 1px solid #eee;
	border-radius: 4px;
`;

const NavigationButtons = styled.div`
	display: flex;
	gap: 1rem;
	margin-bottom: 1rem;
	padding: 0 1rem;
`;

const NavButton = styled.button`
	padding: 0.5rem 1rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	background: #f4f6f8;
	cursor: pointer;

	&:hover {
		background: #e8eaed;
	}
`;

export const RosterLayout = () => {
	const { eswId } = useParams();
	const location = useLocation();
	const navigate = useNavigate();
	const path = location.pathname;
	const isAthletesActive = path.includes('/roster/athletes') || path.endsWith('/roster');
	const isStaffActive = path.includes('/roster/staff');

	const handleNavigateToEvent = () => {
		navigate(`/event/${eswId}`);
	};

	const handleNavigateToTeams = () => {
		navigate(`/event/${eswId}/teams`);
	};

	return (
		<RosterLayoutWrapper>
			<Title>Roster</Title>
			<NavigationButtons>
				<NavButton onClick={handleNavigateToEvent}>← Back to Event</NavButton>
				<NavButton onClick={handleNavigateToTeams}>View Teams</NavButton>
			</NavigationButtons>
			<TabsContainer>
				<Tab to={`/event/${eswId}/roster/athletes`} $active={isAthletesActive}>
					Athletes
				</Tab>
				<Tab to={`/event/${eswId}/roster/staff`} $active={isStaffActive}>
					Staff
				</Tab>
			</TabsContainer>
			<Content>
				<Outlet />
			</Content>
		</RosterLayoutWrapper>
	);
};
