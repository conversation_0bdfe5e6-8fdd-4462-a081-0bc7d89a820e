import { useParams, useSearchParams } from 'react-router';
import styled from 'styled-components';

import { DivisionTeamsStandingWidget } from '@widgets/division-teams-standing';

const StandingsPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const NavigationButtons = styled.div`
	display: flex;
	gap: 1rem;
	margin-bottom: 1rem;
`;

const NavButton = styled.button`
	padding: 0.5rem 1rem;
	border: 1px solid ${({ theme }) => theme.colors.primary || '#007bff'};
	background: white;
	color: ${({ theme }) => theme.colors.primary || '#007bff'};
	border-radius: 4px;
	cursor: pointer;
	text-decoration: none;

	&:hover {
		background: ${({ theme }) => theme.colors.primary || '#007bff'};
		color: white;
	}
`;

export const StandingsPage = () => {
	const { eswId, divisionId } = useParams();
	const [searchParams] = useSearchParams();
	const search = searchParams.get('search');

	const handleNavigateToTeams = () => {
		window.location.href = `/event/${eswId}/teams/${divisionId}`;
	};

	const handleNavigateToPoolBracket = () => {
		window.location.href = `/event/${eswId}/pool-bracket`;
	};

	return (
		<StandingsPageWrapper>
			<Title>Division Standings</Title>
			<NavigationButtons>
				<NavButton onClick={handleNavigateToTeams}>View Teams</NavButton>
				<NavButton onClick={handleNavigateToPoolBracket}>View Pool Brackets</NavButton>
			</NavigationButtons>
			<DivisionTeamsStandingWidget eswId={eswId!} divisionId={divisionId!} search={search} />
		</StandingsPageWrapper>
	);
};
