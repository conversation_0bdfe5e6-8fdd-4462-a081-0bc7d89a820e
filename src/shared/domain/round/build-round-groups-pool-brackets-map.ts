import { getRoundGroupK<PERSON> } from './get-round-group-key';
import { RoundGroupKeyType, RoundGroupablePoolBracket, RoundRef } from './types';

export const buildRoundGroupsPoolBracketsMap = <
	R extends RoundRef,
	PB extends RoundGroupablePoolBracket,
>(
	rounds: R[],
	poolBrackets: PB[],
): Map<RoundGroupKeyType, PB[]> => {
	const groupedPoolBracketsMap = new Map<RoundGroupKeyType, PB[]>();

	rounds.forEach((round) => {
		const roundId = round.uuid;
		const roundPoolBrackets = poolBrackets.filter(({ round_id }) => round_id === roundId);

		const groupKey = getRoundGroupKey(round);
		const existing = groupedPoolBracketsMap.get(groupKey);

		if (!existing) {
			groupedPoolBracketsMap.set(groupKey, roundPoolBrackets);
		} else {
			groupedPoolBracketsMap.set(groupKey, [...existing, ...roundPoolBrackets]);
		}
	});

	return groupedPoolBracketsMap;
};
