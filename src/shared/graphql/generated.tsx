import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
};

export type Athlete = {
  __typename?: 'Athlete';
  athlete_id: Scalars['ID']['output'];
  club_name: Scalars['String']['output'];
  first: Scalars['String']['output'];
  last: Scalars['String']['output'];
  organization_code?: Maybe<Scalars['String']['output']>;
  short_position?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
  uniform?: Maybe<Scalars['String']['output']>;
};

export type Bracket = PoolOrBracketBase & {
  __typename?: 'Bracket';
  consolation?: Maybe<Scalars['Float']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id: Scalars['ID']['output'];
  external: PoolOrBracketExternal;
  flow_chart?: Maybe<Scalars['String']['output']>;
  group_id?: Maybe<Scalars['ID']['output']>;
  is_pool: Scalars['Float']['output'];
  match_count: Scalars['Float']['output'];
  matches: Array<Match>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes: Array<TeamAdvancement>;
  pb_seeds: Array<PoolOrBracketSeedItem>;
  pb_stats: Array<PoolOrBracketStatItem>;
  round_id: Scalars['ID']['output'];
  settings?: Maybe<PoolOrBracketSettings>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  team_count: Scalars['Float']['output'];
  teams: Array<Team>;
  uuid: Scalars['ID']['output'];
};


export type BracketTeamsArgs = {
  filterTeamsIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type BracketMatch = {
  __typename?: 'BracketMatch';
  court_id?: Maybe<Scalars['String']['output']>;
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_number?: Maybe<Scalars['Float']['output']>;
  ref_code?: Maybe<Scalars['String']['output']>;
  ref_name?: Maybe<Scalars['String']['output']>;
  ref_roster_id?: Maybe<Scalars['Float']['output']>;
  results?: Maybe<BracketMatchResults>;
  scores?: Maybe<Scalars['String']['output']>;
  show_previously_accepted_bid_team1?: Maybe<Scalars['String']['output']>;
  show_previously_accepted_bid_team2?: Maybe<Scalars['String']['output']>;
  source?: Maybe<BracketMatchSourceTeam>;
  team1_code?: Maybe<Scalars['String']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team1_rank?: Maybe<Scalars['Float']['output']>;
  team1_roster_id?: Maybe<Scalars['Float']['output']>;
  team1_temp_name?: Maybe<Scalars['String']['output']>;
  team1_temp_roster_id?: Maybe<Scalars['String']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
  team2_rank?: Maybe<Scalars['Float']['output']>;
  team2_roster_id?: Maybe<Scalars['Float']['output']>;
  team2_temp_name?: Maybe<Scalars['String']['output']>;
  team2_temp_roster_id?: Maybe<Scalars['Float']['output']>;
  temp_score?: Maybe<Scalars['String']['output']>;
  winner?: Maybe<Scalars['String']['output']>;
  winning_roster_id?: Maybe<Scalars['Float']['output']>;
  winning_team_id?: Maybe<Scalars['Float']['output']>;
  winning_team_name?: Maybe<Scalars['String']['output']>;
  winning_temp_name?: Maybe<Scalars['String']['output']>;
};

export type BracketMatchResults = {
  __typename?: 'BracketMatchResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  set3?: Maybe<Scalars['String']['output']>;
  team1?: Maybe<BracketMatchResultsTeam>;
  team2?: Maybe<BracketMatchResultsTeam>;
  winner?: Maybe<Scalars['String']['output']>;
};

export type BracketMatchResultsTeam = {
  __typename?: 'BracketMatchResultsTeam';
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
};

export type BracketMatchSourceTeam = {
  __typename?: 'BracketMatchSourceTeam';
  ref?: Maybe<BracketMatchSourceTeamItem>;
  team1?: Maybe<BracketMatchSourceTeamItem>;
  team2?: Maybe<BracketMatchSourceTeamItem>;
};

export type BracketMatchSourceTeamItem = {
  __typename?: 'BracketMatchSourceTeamItem';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  reseedRank?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['Float']['output']>;
};

export type BracketPool = {
  __typename?: 'BracketPool';
  flow_chart?: Maybe<Scalars['String']['output']>;
};

export type Club = {
  __typename?: 'Club';
  club_code: Scalars['String']['output'];
  club_name: Scalars['String']['output'];
  roster_club_id: Scalars['ID']['output'];
  state?: Maybe<Scalars['String']['output']>;
  teams: Array<Team>;
  teams_count: Scalars['Int']['output'];
};

export type ClubShortInfo = {
  __typename?: 'ClubShortInfo';
  club_code: Scalars['String']['output'];
  club_name: Scalars['String']['output'];
  roster_club_id: Scalars['ID']['output'];
  state?: Maybe<Scalars['String']['output']>;
};

export type Court = {
  __typename?: 'Court';
  name: Scalars['String']['output'];
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority?: Maybe<Scalars['Float']['output']>;
  uuid: Scalars['ID']['output'];
};

export type CourtGrid = {
  __typename?: 'CourtGrid';
  courts?: Maybe<Array<CourtMatchesCourt>>;
  divisions?: Maybe<Array<CourtMatchesDivision>>;
  hours?: Maybe<Array<CourtMatchesTime>>;
};

export type CourtMatchesCourt = {
  __typename?: 'CourtMatchesCourt';
  court_id?: Maybe<Scalars['String']['output']>;
  court_name?: Maybe<Scalars['String']['output']>;
  event_id?: Maybe<Scalars['Float']['output']>;
  matches?: Maybe<Array<CourtMatchesCourtMatch>>;
  short_name?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesCourtMatch = {
  __typename?: 'CourtMatchesCourtMatch';
  color?: Maybe<Scalars['String']['output']>;
  date_end?: Maybe<Scalars['Float']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_name?: Maybe<Scalars['String']['output']>;
  results?: Maybe<CourtMatchesCourtMatchResults>;
  secs_finished?: Maybe<Scalars['Float']['output']>;
  team1_roster_id?: Maybe<Scalars['Float']['output']>;
  team2_roster_id?: Maybe<Scalars['Float']['output']>;
  team_1_name?: Maybe<Scalars['String']['output']>;
  team_2_name?: Maybe<Scalars['String']['output']>;
  team_ref_name?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesCourtMatchResults = {
  __typename?: 'CourtMatchesCourtMatchResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  team1?: Maybe<CourtMatchesCourtMatchResultsTeam>;
  team2?: Maybe<CourtMatchesCourtMatchResultsTeam>;
  winner?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesCourtMatchResultsTeam = {
  __typename?: 'CourtMatchesCourtMatchResultsTeam';
  scores?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesDivision = {
  __typename?: 'CourtMatchesDivision';
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  level?: Maybe<Scalars['String']['output']>;
  level_sort_order?: Maybe<Scalars['Float']['output']>;
  max_age?: Maybe<Scalars['Float']['output']>;
  sort_order?: Maybe<Scalars['Float']['output']>;
};

export type CourtMatchesTime = {
  __typename?: 'CourtMatchesTime';
  default?: Maybe<Scalars['String']['output']>;
  time?: Maybe<Scalars['Float']['output']>;
  time12?: Maybe<Scalars['String']['output']>;
};

export type CourtShortInfo = {
  __typename?: 'CourtShortInfo';
  short_name?: Maybe<Scalars['String']['output']>;
  uuid: Scalars['ID']['output'];
};

export type Division = {
  __typename?: 'Division';
  division_id: Scalars['ID']['output'];
  has_flow_chart?: Maybe<Scalars['Boolean']['output']>;
  matches_time_ranges: Array<MatchesTimeRange>;
  media: Array<EventMedia>;
  name: Scalars['String']['output'];
  qualified_teams: Array<Team>;
  rounds: Array<Round>;
  short_name?: Maybe<Scalars['String']['output']>;
  teams_count: Scalars['Float']['output'];
};


export type DivisionMediaArgs = {
  filterFileTypes?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type DivisionPool = {
  __typename?: 'DivisionPool';
  court_start?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  is_empty_pb_stats?: Maybe<Scalars['Boolean']['output']>;
  is_pool?: Maybe<Scalars['Float']['output']>;
  pb_name?: Maybe<Scalars['String']['output']>;
  pb_short_name?: Maybe<Scalars['String']['output']>;
  r_name?: Maybe<Scalars['String']['output']>;
  r_short_name?: Maybe<Scalars['String']['output']>;
  r_sort_priority?: Maybe<Scalars['Float']['output']>;
  r_uuid?: Maybe<Scalars['String']['output']>;
  rr_name?: Maybe<Scalars['String']['output']>;
  rr_short_name?: Maybe<Scalars['String']['output']>;
  rr_sort_priority?: Maybe<Scalars['Float']['output']>;
  settings?: Maybe<PoolOrBracketSettings>;
  sort_priority?: Maybe<Scalars['Float']['output']>;
  team_count?: Maybe<Scalars['Float']['output']>;
  teams?: Maybe<Array<DivisionPoolTeam>>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type DivisionPoolTeam = {
  __typename?: 'DivisionPoolTeam';
  info: DivisionPoolTeamInfo;
  is_empty_team_pb_stats?: Maybe<Scalars['Boolean']['output']>;
  matches_lost?: Maybe<Scalars['String']['output']>;
  matches_won?: Maybe<Scalars['String']['output']>;
  opponent_organization_code?: Maybe<Scalars['String']['output']>;
  opponent_team_id?: Maybe<Scalars['Float']['output']>;
  opponent_team_name?: Maybe<Scalars['String']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['String']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['String']['output']>;
};

export type DivisionPoolTeamInfo = {
  __typename?: 'DivisionPoolTeamInfo';
  heading?: Maybe<Scalars['String']['output']>;
  heading_sort_priority?: Maybe<Scalars['Float']['output']>;
  seed_current?: Maybe<Scalars['Float']['output']>;
  seed_original?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type DivisionStanding = {
  __typename?: 'DivisionStanding';
  division_id: Scalars['ID']['output'];
  division_standing_id: Scalars['ID']['output'];
  heading: Scalars['String']['output'];
  heading_priority: Scalars['Float']['output'];
  matches_lost: Scalars['Float']['output'];
  matches_won: Scalars['Float']['output'];
  points?: Maybe<Scalars['Float']['output']>;
  points_ratio: Scalars['Float']['output'];
  rank: Scalars['Float']['output'];
  seed?: Maybe<Scalars['Float']['output']>;
  seed_original?: Maybe<Scalars['Float']['output']>;
  sets_lost: Scalars['Float']['output'];
  sets_pct: Scalars['Float']['output'];
  sets_won: Scalars['Float']['output'];
  team_id: Scalars['ID']['output'];
};

export type Event = {
  __typename?: 'Event';
  address?: Maybe<Scalars['String']['output']>;
  athletes: Array<EventAthlete>;
  city?: Maybe<Scalars['String']['output']>;
  clubs: Array<Club>;
  date_start?: Maybe<Scalars['String']['output']>;
  days?: Maybe<Array<Scalars['String']['output']>>;
  divisions: Array<EventDivision>;
  event_id?: Maybe<Scalars['ID']['output']>;
  event_notes?: Maybe<Scalars['String']['output']>;
  has_officials?: Maybe<Scalars['Boolean']['output']>;
  has_rosters?: Maybe<Scalars['Boolean']['output']>;
  hide_seeds?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['Float']['output']>;
  is_require_recipient_name_for_each_ticket?: Maybe<Scalars['Boolean']['output']>;
  is_with_prev_qual?: Maybe<Scalars['Boolean']['output']>;
  locations?: Maybe<Array<EventLocation>>;
  long_name?: Maybe<Scalars['String']['output']>;
  modified?: Maybe<Scalars['DateTime']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  schedule_published?: Maybe<Scalars['Boolean']['output']>;
  small_logo?: Maybe<Scalars['String']['output']>;
  sport_sanctioning?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  teams: Array<TeamList>;
  teams_settings?: Maybe<EventTeamsSettings>;
  tickets_code?: Maybe<Scalars['String']['output']>;
  tickets_published?: Maybe<Scalars['Boolean']['output']>;
};

export type EventAthlete = {
  __typename?: 'EventAthlete';
  age?: Maybe<Scalars['Int']['output']>;
  club_name: Scalars['String']['output'];
  division_id?: Maybe<Scalars['Float']['output']>;
  first?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  jersey?: Maybe<Scalars['Float']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
  team_organization_code?: Maybe<Scalars['String']['output']>;
};

export type EventBracket = {
  __typename?: 'EventBracket';
  display_name?: Maybe<Scalars['String']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  matches?: Maybe<Array<BracketMatch>>;
  pool?: Maybe<BracketPool>;
};

export type EventDivision = {
  __typename?: 'EventDivision';
  division_id?: Maybe<Scalars['Float']['output']>;
  event_id?: Maybe<Scalars['ID']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  short_name?: Maybe<Scalars['String']['output']>;
  teams_count?: Maybe<Scalars['Float']['output']>;
};

export type EventDivisionDetails = {
  __typename?: 'EventDivisionDetails';
  standing: Array<Standing>;
};

export type EventLocation = {
  __typename?: 'EventLocation';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  location_name?: Maybe<Scalars['String']['output']>;
  number?: Maybe<Scalars['Float']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export type EventMedia = {
  __typename?: 'EventMedia';
  division_id: Scalars['ID']['output'];
  file_type: Scalars['String']['output'];
  media_id: Scalars['ID']['output'];
  path: Scalars['String']['output'];
};

export type EventOfficial = {
  __typename?: 'EventOfficial';
  additional_restrictions?: Maybe<Scalars['String']['output']>;
  departure_time?: Maybe<Scalars['String']['output']>;
  event_official_id: Scalars['ID']['output'];
  external: EventOfficialExternal;
  official_additional_role?: Maybe<Scalars['String']['output']>;
  official_id: Scalars['ID']['output'];
  schedule_name?: Maybe<Scalars['String']['output']>;
  schedules: Array<EventOfficialSchedule>;
};

export type EventOfficialExternal = {
  __typename?: 'EventOfficialExternal';
  official_info: OfficialShortInfo;
  user_info: UserShortInfo;
};

export type EventOfficialSchedule = {
  __typename?: 'EventOfficialSchedule';
  court_id?: Maybe<Scalars['ID']['output']>;
  division_id: Scalars['ID']['output'];
  event_official_group_id?: Maybe<Scalars['ID']['output']>;
  event_official_id: Scalars['ID']['output'];
  match?: Maybe<Match>;
  match_id: Scalars['ID']['output'];
  match_name: Scalars['String']['output'];
  match_start_time?: Maybe<Scalars['Float']['output']>;
  published: Scalars['Boolean']['output'];
  schedule_id: Scalars['ID']['output'];
};

export type EventPool = {
  __typename?: 'EventPool';
  display_name?: Maybe<Scalars['String']['output']>;
  display_name_short?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  event_id?: Maybe<Scalars['Float']['output']>;
  is_pool?: Maybe<Scalars['Boolean']['output']>;
  next?: Maybe<NextPrevPool>;
  pb_finishes?: Maybe<PoolFuture>;
  prev?: Maybe<NextPrevPool>;
  r_uuid?: Maybe<Scalars['String']['output']>;
  results?: Maybe<Array<PoolResults>>;
  settings?: Maybe<PoolOrBracketSettings>;
  standings?: Maybe<Array<PoolStandings>>;
  team_count?: Maybe<Scalars['Float']['output']>;
  upcoming_matches?: Maybe<Array<PoolUpcomingMatches>>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type EventTeamRoster = {
  __typename?: 'EventTeamRoster';
  club_name?: Maybe<Scalars['String']['output']>;
  event_id: Scalars['ID']['output'];
  event_name?: Maybe<Scalars['String']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  roster_athletes?: Maybe<Array<EventTeamRosterAthlete>>;
  roster_staff?: Maybe<Array<EventTeamRosterStaff>>;
  roster_team_id?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type EventTeamRosterAthlete = {
  __typename?: 'EventTeamRosterAthlete';
  first?: Maybe<Scalars['String']['output']>;
  gradyear?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  last?: Maybe<Scalars['String']['output']>;
  short_position?: Maybe<Scalars['String']['output']>;
  uniform?: Maybe<Scalars['Int']['output']>;
};

export type EventTeamRosterStaff = {
  __typename?: 'EventTeamRosterStaff';
  first?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  last?: Maybe<Scalars['String']['output']>;
  role_name?: Maybe<Scalars['String']['output']>;
  sort_order?: Maybe<Scalars['Int']['output']>;
};

export type EventTeamsSettings = {
  __typename?: 'EventTeamsSettings';
  baller_tv_available?: Maybe<Scalars['Boolean']['output']>;
  hide_standings?: Maybe<Scalars['Boolean']['output']>;
  sort_by?: Maybe<Scalars['String']['output']>;
};

export type EventUpcoming = {
  __typename?: 'EventUpcoming';
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['String']['output']>;
  date_start_formatted?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  footnote_play?: Maybe<Scalars['String']['output']>;
  footnote_team1?: Maybe<Scalars['String']['output']>;
  footnote_team2?: Maybe<Scalars['String']['output']>;
  is_pool?: Maybe<Scalars['Float']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_type?: Maybe<Scalars['String']['output']>;
  opponent_organization_code?: Maybe<Scalars['String']['output']>;
  opponent_team_name?: Maybe<Scalars['String']['output']>;
  pb_name?: Maybe<Scalars['String']['output']>;
  pool_bracket_id?: Maybe<Scalars['String']['output']>;
  pool_name?: Maybe<Scalars['String']['output']>;
  results?: Maybe<Scalars['String']['output']>;
  round_name?: Maybe<Scalars['String']['output']>;
  settings?: Maybe<PoolOrBracketSettings>;
  unix_finished?: Maybe<Scalars['String']['output']>;
};

export type FeatureMatches = {
  __typename?: 'FeatureMatches';
  court_name?: Maybe<Scalars['String']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_start?: Maybe<Scalars['String']['output']>;
  team1_id?: Maybe<Scalars['String']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team2_id?: Maybe<Scalars['String']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
};

export type FilterInfo = {
  __typename?: 'FilterInfo';
  filtered_items: Scalars['Float']['output'];
  is_filtered: Scalars['Boolean']['output'];
  total_items: Scalars['Float']['output'];
};

export type FilteredMatches = {
  __typename?: 'FilteredMatches';
  filter_info: FilterInfo;
  items: Array<Match>;
};

export type Match = {
  __typename?: 'Match';
  court_id: Scalars['ID']['output'];
  division_id: Scalars['ID']['output'];
  division_name?: Maybe<Scalars['String']['output']>;
  external: MatchExternal;
  finishes?: Maybe<MatchFinishes>;
  is_tb?: Maybe<Scalars['Float']['output']>;
  match_id: Scalars['ID']['output'];
  match_name?: Maybe<Scalars['String']['output']>;
  match_number?: Maybe<Scalars['Float']['output']>;
  opponent_id?: Maybe<Scalars['ID']['output']>;
  pool_bracket_id: Scalars['ID']['output'];
  ref_team_id?: Maybe<Scalars['ID']['output']>;
  results?: Maybe<MatchResults>;
  secs_end?: Maybe<Scalars['Float']['output']>;
  secs_finished?: Maybe<Scalars['Float']['output']>;
  secs_start?: Maybe<Scalars['Float']['output']>;
  source?: Maybe<MatchSource>;
  team_id?: Maybe<Scalars['ID']['output']>;
};

export type MatchExternal = {
  __typename?: 'MatchExternal';
  court_info: CourtShortInfo;
  opponent_display_name: Scalars['String']['output'];
  opponent_info?: Maybe<TeamShortInfo>;
  opponent_pb_seed?: Maybe<PoolOrBracketSeedItem>;
  pool_bracket_info: PoolOrBracketShortInfo;
  ref_team_display_name: Scalars['String']['output'];
  ref_team_info?: Maybe<TeamShortInfo>;
  ref_team_pb_seed?: Maybe<PoolOrBracketSeedItem>;
  team_display_name: Scalars['String']['output'];
  team_info?: Maybe<TeamShortInfo>;
  team_pb_seed?: Maybe<PoolOrBracketSeedItem>;
};

export type MatchFinishes = {
  __typename?: 'MatchFinishes';
  looser?: Maybe<TeamAdvancement>;
  winner?: Maybe<TeamAdvancement>;
};

export type MatchReference = {
  __typename?: 'MatchReference';
  court?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  external?: Maybe<MatchReferenceExternal>;
  match_id?: Maybe<Scalars['String']['output']>;
  secs_start?: Maybe<Scalars['Float']['output']>;
  start_time_string?: Maybe<Scalars['String']['output']>;
  week_day?: Maybe<Scalars['String']['output']>;
};

export type MatchReferenceExternal = {
  __typename?: 'MatchReferenceExternal';
  pool_bracket_info?: Maybe<PoolOrBracketShortInfo>;
};

export type MatchResults = {
  __typename?: 'MatchResults';
  sets: Array<Scalars['String']['output']>;
  team1?: Maybe<MatchTeamResults>;
  team2?: Maybe<MatchTeamResults>;
  winner?: Maybe<Scalars['String']['output']>;
  winner_team?: Maybe<MatchTeamResults>;
};

export type MatchSource = {
  __typename?: 'MatchSource';
  opponent?: Maybe<MatchSourceItem>;
  ref?: Maybe<MatchSourceItem>;
  team?: Maybe<MatchSourceItem>;
};

export type MatchSourceItem = {
  __typename?: 'MatchSourceItem';
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  reseedRank?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['Float']['output']>;
};

export type MatchTeamResults = {
  __typename?: 'MatchTeamResults';
  roster_team_id?: Maybe<Scalars['String']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
};

export type MatchesTimeRange = {
  __typename?: 'MatchesTimeRange';
  day: Scalars['String']['output'];
  division_id: Scalars['ID']['output'];
  end_time: Scalars['String']['output'];
  start_time: Scalars['String']['output'];
};

/** Generate a link to the next pool */
export type NextPrevPool = {
  __typename?: 'NextPrevPool';
  is_pool?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type OfficialShortInfo = {
  __typename?: 'OfficialShortInfo';
  official_id: Scalars['ID']['output'];
  rank?: Maybe<Scalars['String']['output']>;
  user_id: Scalars['ID']['output'];
};

export type PageInfo = {
  __typename?: 'PageInfo';
  item_count: Scalars['Float']['output'];
  page: Scalars['Float']['output'];
  page_count: Scalars['Float']['output'];
  page_size: Scalars['Float']['output'];
};

export type PaginatedAthletes = {
  __typename?: 'PaginatedAthletes';
  items: Array<Athlete>;
  page_info: PageInfo;
};

export type PaginatedClubs = {
  __typename?: 'PaginatedClubs';
  items: Array<Club>;
  page_info: PageInfo;
};

export type PaginatedEvents = {
  __typename?: 'PaginatedEvents';
  items: Array<Event>;
  page_info: PageInfo;
};

export type PaginatedStaff = {
  __typename?: 'PaginatedStaff';
  items: Array<Staff>;
  page_info: PageInfo;
};

export type PaginatedTeams = {
  __typename?: 'PaginatedTeams';
  items: Array<Team>;
  page_info: PageInfo;
};

export type Pool = PoolOrBracketBase & {
  __typename?: 'Pool';
  consolation?: Maybe<Scalars['Float']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id: Scalars['ID']['output'];
  external: PoolOrBracketExternal;
  group_id?: Maybe<Scalars['ID']['output']>;
  is_pool: Scalars['Float']['output'];
  match_count: Scalars['Float']['output'];
  matches: Array<Match>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes: Array<TeamAdvancement>;
  pb_seeds: Array<PoolOrBracketSeedItem>;
  pb_stats: Array<PoolOrBracketStatItem>;
  round_id: Scalars['ID']['output'];
  settings?: Maybe<PoolOrBracketSettings>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  team_count: Scalars['Float']['output'];
  teams: Array<Team>;
  uuid: Scalars['ID']['output'];
};


export type PoolTeamsArgs = {
  filterTeamsIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type PoolFuture = {
  __typename?: 'PoolFuture';
  team_ids: Array<Scalars['String']['output']>;
  teams: Array<TeamAdvancement>;
};

export type PoolOrBracket = Bracket | Pool;

export type PoolOrBracketBase = {
  consolation?: Maybe<Scalars['Float']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id: Scalars['ID']['output'];
  external: PoolOrBracketExternal;
  group_id?: Maybe<Scalars['ID']['output']>;
  is_pool: Scalars['Float']['output'];
  match_count: Scalars['Float']['output'];
  matches: Array<Match>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes: Array<TeamAdvancement>;
  pb_seeds: Array<PoolOrBracketSeedItem>;
  pb_stats: Array<PoolOrBracketStatItem>;
  round_id: Scalars['ID']['output'];
  settings?: Maybe<PoolOrBracketSettings>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  team_count: Scalars['Float']['output'];
  teams: Array<Team>;
  uuid: Scalars['ID']['output'];
};


export type PoolOrBracketBaseTeamsArgs = {
  filterTeamsIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type PoolOrBracketExternal = {
  __typename?: 'PoolOrBracketExternal';
  courts_short_info: Array<CourtShortInfo>;
};

export type PoolOrBracketSeedItem = {
  __typename?: 'PoolOrBracketSeedItem';
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  reseedRank?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['Float']['output']>;
};

export type PoolOrBracketSettings = {
  __typename?: 'PoolOrBracketSettings';
  DoubleRR?: Maybe<Scalars['Boolean']['output']>;
  Duration?: Maybe<Scalars['Float']['output']>;
  FinalPoints?: Maybe<Scalars['Float']['output']>;
  NoWinner?: Maybe<Scalars['Boolean']['output']>;
  PlayAllSets?: Maybe<Scalars['Boolean']['output']>;
  SetCount?: Maybe<Scalars['Float']['output']>;
  WinningPoints?: Maybe<Scalars['Float']['output']>;
};

export type PoolOrBracketShortInfo = PoolOrBracketShortInfoInterface & {
  __typename?: 'PoolOrBracketShortInfo';
  is_pool: Scalars['Float']['output'];
  uuid: Scalars['ID']['output'];
};

export type PoolOrBracketShortInfoInterface = {
  is_pool: Scalars['Float']['output'];
  uuid: Scalars['ID']['output'];
};

export type PoolOrBracketStatItem = {
  __typename?: 'PoolOrBracketStatItem';
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_id?: Maybe<Scalars['ID']['output']>;
};

export type PoolResults = {
  __typename?: 'PoolResults';
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  ref_roster_id?: Maybe<Scalars['String']['output']>;
  ref_team_code?: Maybe<Scalars['String']['output']>;
  ref_team_name?: Maybe<Scalars['String']['output']>;
  results: PoolResultsResults;
  team1_code?: Maybe<Scalars['String']['output']>;
  team1_master_id?: Maybe<Scalars['Float']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team1_roster_id?: Maybe<Scalars['Float']['output']>;
  team2_code?: Maybe<Scalars['String']['output']>;
  team2_master_id?: Maybe<Scalars['Float']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
  team2_roster_id?: Maybe<Scalars['Float']['output']>;
};

export type PoolResultsResults = {
  __typename?: 'PoolResultsResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  set3?: Maybe<Scalars['String']['output']>;
  team1?: Maybe<PoolResultsResultsTeam>;
  team2?: Maybe<PoolResultsResultsTeam>;
};

export type PoolResultsResultsTeam = {
  __typename?: 'PoolResultsResultsTeam';
  heading?: Maybe<Scalars['String']['output']>;
  heading_sort?: Maybe<Scalars['Float']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type PoolStandings = {
  __typename?: 'PoolStandings';
  display_name?: Maybe<Scalars['String']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  pb_stats: Array<PoolStandingsStats>;
};

export type PoolStandingsStats = {
  __typename?: 'PoolStandingsStats';
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_id?: Maybe<Scalars['String']['output']>;
};

export type PoolUpcomingMatches = {
  __typename?: 'PoolUpcomingMatches';
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  footnote_play?: Maybe<Scalars['String']['output']>;
  footnote_team1?: Maybe<Scalars['String']['output']>;
  footnote_team2?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  ref_roster_id?: Maybe<Scalars['String']['output']>;
  ref_team_code?: Maybe<Scalars['String']['output']>;
  ref_team_name?: Maybe<Scalars['String']['output']>;
  results?: Maybe<Scalars['String']['output']>;
  team1_code?: Maybe<Scalars['String']['output']>;
  team1_master_id?: Maybe<Scalars['String']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team1_roster_id?: Maybe<Scalars['String']['output']>;
  team2_code?: Maybe<Scalars['String']['output']>;
  team2_master_id?: Maybe<Scalars['String']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
  team2_roster_id?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  bracket?: Maybe<EventBracket>;
  courtMatches: CourtGrid;
  courts: Array<Court>;
  dayRangeMatches: FilteredMatches;
  divisionDetails?: Maybe<EventDivisionDetails>;
  divisionPoolBrackets: Array<PoolOrBracket>;
  divisionRounds: Array<Round>;
  divisionTeamsStanding: Array<Team>;
  divisions: Array<Division>;
  event?: Maybe<Event>;
  eventOfficials: Array<EventOfficial>;
  eventOfficialsSchedules: Array<EventOfficialSchedule>;
  favoriteTeams: Array<Team>;
  featureMatches: Array<FeatureMatches>;
  paginatedAthletes: PaginatedAthletes;
  paginatedClubs: PaginatedClubs;
  paginatedDivisionTeams: PaginatedTeams;
  paginatedEvents: PaginatedEvents;
  paginatedStaff: PaginatedStaff;
  pool: EventPool;
  poolIdByTeamId?: Maybe<EventPool>;
  pools: Array<DivisionPool>;
  qualifiedTeams: Array<Team>;
  relatedCourts: Array<Court>;
  roster: Array<EventTeamRoster>;
  teamSingle: TeamSingle;
  upcomingMatches?: Maybe<Array<UpcomingMatches>>;
};


export type QueryBracketArgs = {
  id: Scalars['ID']['input'];
  poolId: Scalars['String']['input'];
};


export type QueryCourtMatchesArgs = {
  day: Scalars['String']['input'];
  division: Scalars['String']['input'];
  hour: Scalars['String']['input'];
  hours: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};


export type QueryCourtsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryDayRangeMatchesArgs = {
  after: Scalars['String']['input'];
  before: Scalars['String']['input'];
  divisionId?: InputMaybe<Scalars['ID']['input']>;
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionDetailsArgs = {
  divisionIds: Array<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
};


export type QueryDivisionPoolBracketsArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionRoundsArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionTeamsStandingArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryEventArgs = {
  id: Scalars['ID']['input'];
};


export type QueryEventOfficialsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryEventOfficialsSchedulesArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryFavoriteTeamsArgs = {
  eventKey: Scalars['ID']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
  teamsIds: Array<Scalars['ID']['input']>;
};


export type QueryFeatureMatchesArgs = {
  id: Scalars['ID']['input'];
};


export type QueryPaginatedAthletesArgs = {
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPaginatedClubsArgs = {
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPaginatedDivisionTeamsArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPaginatedEventsArgs = {
  asc?: InputMaybe<Scalars['Boolean']['input']>;
  endAfter?: InputMaybe<Scalars['String']['input']>;
  endBefore?: InputMaybe<Scalars['String']['input']>;
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
  startAfter?: InputMaybe<Scalars['String']['input']>;
  startBefore?: InputMaybe<Scalars['String']['input']>;
  years?: InputMaybe<Array<Scalars['String']['input']>>;
};


export type QueryPaginatedStaffArgs = {
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPoolArgs = {
  id: Scalars['ID']['input'];
  poolId: Scalars['ID']['input'];
};


export type QueryPoolIdByTeamIdArgs = {
  id: Scalars['ID']['input'];
  poolId?: InputMaybe<Scalars['ID']['input']>;
  teamId: Scalars['ID']['input'];
};


export type QueryPoolsArgs = {
  divisionId: Scalars['ID']['input'];
  id: Scalars['ID']['input'];
};


export type QueryQualifiedTeamsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryRelatedCourtsArgs = {
  uniqKey?: InputMaybe<Scalars['String']['input']>;
};


export type QueryRosterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryTeamSingleArgs = {
  id: Scalars['ID']['input'];
  teamId: Scalars['ID']['input'];
};


export type QueryUpcomingMatchesArgs = {
  id: Scalars['ID']['input'];
};

export type Round = {
  __typename?: 'Round';
  division_id: Scalars['ID']['output'];
  first_match_start?: Maybe<Scalars['Float']['output']>;
  last_match_start?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  pool_brackets: Array<PoolOrBracket>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  uuid: Scalars['ID']['output'];
};

export type Staff = {
  __typename?: 'Staff';
  club_name: Scalars['String']['output'];
  first: Scalars['String']['output'];
  last: Scalars['String']['output'];
  organization_code?: Maybe<Scalars['String']['output']>;
  role_name?: Maybe<Scalars['String']['output']>;
  staff_id: Scalars['ID']['output'];
  state?: Maybe<Scalars['String']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
};

export type Standing = {
  __typename?: 'Standing';
  heading_group: Scalars['String']['output'];
  teams: Array<TeamDetails>;
};

export type StandingInfo = {
  __typename?: 'StandingInfo';
  points?: Maybe<Scalars['Float']['output']>;
};

export type Team = {
  __typename?: 'Team';
  club_id: Scalars['ID']['output'];
  division_id: Scalars['ID']['output'];
  division_name?: Maybe<Scalars['String']['output']>;
  division_standing?: Maybe<DivisionStanding>;
  external: TeamExternal;
  extra?: Maybe<TeamExtra>;
  master_team_id?: Maybe<Scalars['ID']['output']>;
  matches: Array<Match>;
  next_match?: Maybe<Match>;
  pool_bracket_stat?: Maybe<PoolOrBracketStatItem>;
  team_code?: Maybe<Scalars['String']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
};

export type TeamAdvancement = {
  __typename?: 'TeamAdvancement';
  next_match?: Maybe<MatchReference>;
  next_ref?: Maybe<MatchReference>;
  team_id?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type TeamDetails = {
  __typename?: 'TeamDetails';
  division_id?: Maybe<Scalars['Float']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  info?: Maybe<StandingInfo>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  seed_current?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type TeamExternal = {
  __typename?: 'TeamExternal';
  club_info: ClubShortInfo;
};

export type TeamExtra = {
  __typename?: 'TeamExtra';
  earned_at?: Maybe<Scalars['String']['output']>;
  prev_qual?: Maybe<Scalars['Boolean']['output']>;
  prev_qual_age?: Maybe<Scalars['String']['output']>;
  prev_qual_division?: Maybe<Scalars['String']['output']>;
  show_accepted_bid?: Maybe<Scalars['Boolean']['output']>;
  show_previously_accepted_bid?: Maybe<Scalars['String']['output']>;
};

export type TeamList = {
  __typename?: 'TeamList';
  club_name?: Maybe<Scalars['String']['output']>;
  club_state?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  roster_club_id?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type TeamShortInfo = {
  __typename?: 'TeamShortInfo';
  master_team_id?: Maybe<Scalars['ID']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
};

export type TeamSingle = {
  __typename?: 'TeamSingle';
  athletes?: Maybe<Array<TeamSingleAthlete>>;
  bracket_finishes?: Maybe<MatchFinishes>;
  club_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  manual_club_name?: Maybe<Scalars['String']['output']>;
  master_team_id?: Maybe<Scalars['Float']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  pb_info?: Maybe<Array<TeamSinglePbInfo>>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  results?: Maybe<Array<TeamSingleResults>>;
  roster_club_id?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  staff?: Maybe<Array<TeamSingleStaff>>;
  state?: Maybe<Scalars['String']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
  upcoming?: Maybe<Array<EventUpcoming>>;
};

export type TeamSingleAthlete = {
  __typename?: 'TeamSingleAthlete';
  first?: Maybe<Scalars['String']['output']>;
  gradyear?: Maybe<Scalars['Float']['output']>;
  height?: Maybe<Scalars['String']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  short_position?: Maybe<Scalars['String']['output']>;
  uniform?: Maybe<Scalars['Float']['output']>;
};

export type TeamSingleMatch = {
  __typename?: 'TeamSingleMatch';
  date_start?: Maybe<Scalars['Float']['output']>;
  date_start_formatted?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_type?: Maybe<Scalars['String']['output']>;
  opponent_organization_code?: Maybe<Scalars['String']['output']>;
  opponent_team_id?: Maybe<Scalars['Float']['output']>;
  opponent_team_name?: Maybe<Scalars['String']['output']>;
  pool_bracket_id?: Maybe<Scalars['String']['output']>;
  results?: Maybe<TeamSingleMatchResults>;
  unix_finished?: Maybe<Scalars['Float']['output']>;
};

export type TeamSingleMatchResults = {
  __typename?: 'TeamSingleMatchResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  set3?: Maybe<Scalars['String']['output']>;
  team1: TeamSingleMatchResultsTeam;
  team2: TeamSingleMatchResultsTeam;
  winner?: Maybe<Scalars['String']['output']>;
};

export type TeamSingleMatchResultsTeam = {
  __typename?: 'TeamSingleMatchResultsTeam';
  heading?: Maybe<Scalars['String']['output']>;
  heading_sort?: Maybe<Scalars['Float']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type TeamSinglePbInfo = {
  __typename?: 'TeamSinglePbInfo';
  is_pool?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes?: Maybe<PoolFuture>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type TeamSingleResults = {
  __typename?: 'TeamSingleResults';
  is_pool?: Maybe<Scalars['Float']['output']>;
  matches: Array<TeamSingleMatch>;
  pb_name?: Maybe<Scalars['String']['output']>;
  pb_stats?: Maybe<Array<PoolStandingsStats>>;
  round_name?: Maybe<Scalars['String']['output']>;
  sort_priority?: Maybe<Scalars['Float']['output']>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type TeamSingleStaff = {
  __typename?: 'TeamSingleStaff';
  first?: Maybe<Scalars['String']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  role_name?: Maybe<Scalars['String']['output']>;
  sort_order?: Maybe<Scalars['Float']['output']>;
};

export type UpcomingMatches = {
  __typename?: 'UpcomingMatches';
  club_code?: Maybe<Scalars['String']['output']>;
  club_id?: Maybe<Scalars['Float']['output']>;
  club_name?: Maybe<Scalars['String']['output']>;
  club_state?: Maybe<Scalars['String']['output']>;
  court_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  opponent_id?: Maybe<Scalars['Float']['output']>;
  opponent_name?: Maybe<Scalars['String']['output']>;
  secs_finished?: Maybe<Scalars['Float']['output']>;
  secs_start?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_code?: Maybe<Scalars['String']['output']>;
  team_id?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type UserShortInfo = {
  __typename?: 'UserShortInfo';
  first?: Maybe<Scalars['String']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  user_id: Scalars['ID']['output'];
};

export type PaginatedAthletesQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedAthletesQuery = { __typename?: 'Query', paginatedAthletes: { __typename?: 'PaginatedAthletes', items: Array<{ __typename?: 'Athlete', athlete_id: string, first: string, last: string, club_name: string, team_id: string, team_name: string, organization_code?: string | null, state?: string | null, short_position?: string | null, uniform?: string | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type PaginatedClubsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedClubsQuery = { __typename?: 'Query', paginatedClubs: { __typename?: 'PaginatedClubs', items: Array<{ __typename?: 'Club', roster_club_id: string, club_name: string, state?: string | null, club_code: string, teams_count: number, teams: Array<{ __typename?: 'Team', team_id: string, club_id: string, team_name: string, division_id: string, division_name?: string | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, seed?: number | null, rank: number } | null, next_match?: { __typename?: 'Match', secs_start?: number | null, external: { __typename?: 'MatchExternal', opponent_display_name: string, court_info: { __typename?: 'CourtShortInfo', short_name?: string | null } } } | null }> }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type CourtsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
}>;


export type CourtsQuery = { __typename?: 'Query', courts: Array<{ __typename?: 'Court', uuid: string, name: string, short_name?: string | null }> };

export type EventOverviewQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
}>;


export type EventOverviewQuery = { __typename?: 'Query', event?: { __typename?: 'Event', id?: number | null, long_name?: string | null, city?: string | null, state?: string | null, tickets_published?: boolean | null, schedule_published?: boolean | null, is_require_recipient_name_for_each_ticket?: boolean | null, tickets_code?: string | null, hide_seeds?: boolean | null, days?: Array<string> | null, has_rosters?: boolean | null, is_with_prev_qual?: boolean | null, event_notes?: string | null, address?: string | null, sport_sanctioning?: string | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, teams_settings?: { __typename?: 'EventTeamsSettings', baller_tv_available?: boolean | null, hide_standings?: boolean | null, sort_by?: string | null } | null } | null, divisions: Array<{ __typename?: 'Division', division_id: string, name: string, teams_count: number, short_name?: string | null, has_flow_chart?: boolean | null, media: Array<{ __typename?: 'EventMedia', media_id: string, division_id: string, file_type: string, path: string }>, matches_time_ranges: Array<{ __typename?: 'MatchesTimeRange', day: string, start_time: string, end_time: string }>, rounds: Array<{ __typename?: 'Round', uuid: string, division_id: string, sort_priority: number, name?: string | null, short_name?: string | null, first_match_start?: number | null, last_match_start?: number | null }> }> };

export type PaginatedEventsQueryVariables = Exact<{
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
  startBefore?: InputMaybe<Scalars['String']['input']>;
  startAfter?: InputMaybe<Scalars['String']['input']>;
  endBefore?: InputMaybe<Scalars['String']['input']>;
  endAfter?: InputMaybe<Scalars['String']['input']>;
  years?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
  asc?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type PaginatedEventsQuery = { __typename?: 'Query', paginatedEvents: { __typename?: 'PaginatedEvents', items: Array<{ __typename?: 'Event', event_id?: string | null, long_name?: string | null, city?: string | null, date_start?: string | null, state?: string | null, address?: string | null, schedule_published?: boolean | null, small_logo?: string | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type RangeMatchesQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  after: Scalars['String']['input'];
  before: Scalars['String']['input'];
  divisionId?: InputMaybe<Scalars['ID']['input']>;
}>;


export type RangeMatchesQuery = { __typename?: 'Query', dayRangeMatches: { __typename?: 'FilteredMatches', items: Array<{ __typename?: 'Match', team_id?: string | null, opponent_id?: string | null, ref_team_id?: string | null, match_id: string, court_id: string, is_tb?: number | null, match_name?: string | null, division_id: string, division_name?: string | null, secs_start?: number | null, secs_end?: number | null, secs_finished?: number | null, results?: { __typename?: 'MatchResults', winner_team?: { __typename?: 'MatchTeamResults', scores?: string | null, roster_team_id?: string | null } | null } | null, external: { __typename?: 'MatchExternal', team_display_name: string, opponent_display_name: string, ref_team_display_name: string, pool_bracket_info: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string }, team_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null } | null, opponent_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null } | null } }>, filter_info: { __typename?: 'FilterInfo', is_filtered: boolean } } };

export type DivisionPoolBracketsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
}>;


export type DivisionPoolBracketsQuery = { __typename?: 'Query', divisionPoolBrackets: Array<{ __typename?: 'Bracket', is_pool: number, uuid: string, round_id: string, sort_priority: number, short_name?: string | null, display_name?: string | null, date_start?: number | null, division_id: string, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null, external: { __typename?: 'PoolOrBracketExternal', courts_short_info: Array<{ __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null }> }, teams: Array<{ __typename?: 'Team', team_id: string, club_id: string, team_name: string, master_team_id?: string | null, extra?: { __typename?: 'TeamExtra', show_accepted_bid?: boolean | null, show_previously_accepted_bid?: string | null } | null, division_standing?: { __typename?: 'DivisionStanding', seed?: number | null, heading: string, heading_priority: number } | null, pool_bracket_stat?: { __typename?: 'PoolOrBracketStatItem', sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, matches_won?: number | null, matches_lost?: number | null, points_ratio?: number | null } | null }>, matches: Array<{ __typename?: 'Match', team_id?: string | null, opponent_id?: string | null, ref_team_id?: string | null, match_id: string, match_name?: string | null, match_number?: number | null, secs_start?: number | null, secs_end?: number | null, secs_finished?: number | null, source?: { __typename?: 'MatchSource', team?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, ref?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null } | null, results?: { __typename?: 'MatchResults', sets: Array<string>, winner_team?: { __typename?: 'MatchTeamResults', scores?: string | null, roster_team_id?: string | null } | null } | null, external: { __typename?: 'MatchExternal', team_display_name: string, opponent_display_name: string, ref_team_display_name: string, team_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, court_info: { __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null } } }>, pb_finishes: Array<{ __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', uuid: string, is_pool: number } | null } | null } | null }> } | { __typename?: 'Pool', is_pool: number, uuid: string, round_id: string, sort_priority: number, short_name?: string | null, display_name?: string | null, date_start?: number | null, division_id: string, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null, external: { __typename?: 'PoolOrBracketExternal', courts_short_info: Array<{ __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null }> }, teams: Array<{ __typename?: 'Team', team_id: string, club_id: string, team_name: string, master_team_id?: string | null, extra?: { __typename?: 'TeamExtra', show_accepted_bid?: boolean | null, show_previously_accepted_bid?: string | null } | null, division_standing?: { __typename?: 'DivisionStanding', seed?: number | null, heading: string, heading_priority: number } | null, pool_bracket_stat?: { __typename?: 'PoolOrBracketStatItem', sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, matches_won?: number | null, matches_lost?: number | null, points_ratio?: number | null } | null }>, matches: Array<{ __typename?: 'Match', team_id?: string | null, opponent_id?: string | null, ref_team_id?: string | null, match_id: string, match_name?: string | null, match_number?: number | null, secs_start?: number | null, secs_end?: number | null, secs_finished?: number | null, source?: { __typename?: 'MatchSource', team?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, ref?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null } | null, results?: { __typename?: 'MatchResults', sets: Array<string>, winner_team?: { __typename?: 'MatchTeamResults', scores?: string | null, roster_team_id?: string | null } | null } | null, external: { __typename?: 'MatchExternal', team_display_name: string, opponent_display_name: string, ref_team_display_name: string, team_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, court_info: { __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null } } }>, pb_finishes: Array<{ __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', uuid: string, is_pool: number } | null } | null } | null }> }> };

export type PaginatedStaffQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedStaffQuery = { __typename?: 'Query', paginatedStaff: { __typename?: 'PaginatedStaff', items: Array<{ __typename?: 'Staff', staff_id: string, first: string, last: string, club_name: string, team_id: string, team_name: string, role_name?: string | null, organization_code?: string | null, state?: string | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type DivisionTeamsStandingQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
}>;


export type DivisionTeamsStandingQuery = { __typename?: 'Query', divisionTeamsStanding: Array<{ __typename?: 'Team', team_id: string, team_name: string, team_code?: string | null, extra?: { __typename?: 'TeamExtra', show_previously_accepted_bid?: string | null, show_accepted_bid?: boolean | null } | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, sets_pct: number, points_ratio: number, points?: number | null, rank: number, seed?: number | null, heading: string, heading_priority: number } | null }> };

export type FavoriteTeamsStandingQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  teamsIds: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type FavoriteTeamsStandingQuery = { __typename?: 'Query', favoriteTeams: Array<{ __typename?: 'Team', club_id: string, team_id: string, team_name: string, team_code?: string | null, division_id: string, division_name?: string | null, next_match?: { __typename?: 'Match', secs_start?: number | null, external: { __typename?: 'MatchExternal', pool_bracket_info: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string }, court_info: { __typename?: 'CourtShortInfo', short_name?: string | null } } } | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, sets_pct: number, points_ratio: number, rank: number, seed?: number | null, heading: string, heading_priority: number } | null }> };

export type PaginatedDivisionTeamsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedDivisionTeamsQuery = { __typename?: 'Query', paginatedDivisionTeams: { __typename?: 'PaginatedTeams', items: Array<{ __typename?: 'Team', team_id: string, club_id: string, team_name: string, division_id: string, next_match?: { __typename?: 'Match', secs_start?: number | null, external: { __typename?: 'MatchExternal', opponent_display_name: string, court_info: { __typename?: 'CourtShortInfo', short_name?: string | null }, pool_bracket_info: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } } } | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, rank: number } | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type QualifiedTeamsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
}>;


export type QualifiedTeamsQuery = { __typename?: 'Query', qualifiedTeams: Array<{ __typename?: 'Team', team_id: string, team_name: string, team_code?: string | null, division_id: string, extra?: { __typename?: 'TeamExtra', prev_qual_age?: string | null, prev_qual_division?: string | null, earned_at?: string | null } | null }> };


export const PaginatedAthletesDocument = gql`
    query PaginatedAthletes($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedAthletes(
    eventKey: $eswId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      athlete_id
      first
      last
      club_name
      team_id
      team_name
      organization_code
      state
      short_position
      uniform
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedAthletesQuery__
 *
 * To run a query within a React component, call `usePaginatedAthletesQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedAthletesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedAthletesQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedAthletesQuery(baseOptions: Apollo.QueryHookOptions<PaginatedAthletesQuery, PaginatedAthletesQueryVariables> & ({ variables: PaginatedAthletesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>(PaginatedAthletesDocument, options);
      }
export function usePaginatedAthletesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>(PaginatedAthletesDocument, options);
        }
export function usePaginatedAthletesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>(PaginatedAthletesDocument, options);
        }
export type PaginatedAthletesQueryHookResult = ReturnType<typeof usePaginatedAthletesQuery>;
export type PaginatedAthletesLazyQueryHookResult = ReturnType<typeof usePaginatedAthletesLazyQuery>;
export type PaginatedAthletesSuspenseQueryHookResult = ReturnType<typeof usePaginatedAthletesSuspenseQuery>;
export type PaginatedAthletesQueryResult = Apollo.QueryResult<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>;
export const PaginatedClubsDocument = gql`
    query PaginatedClubs($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedClubs(
    eventKey: $eswId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      roster_club_id
      club_name
      state
      club_code
      teams_count
      teams {
        team_id
        club_id
        team_name
        division_id
        division_name
        division_standing {
          matches_won
          matches_lost
          sets_won
          sets_lost
          seed
          rank
        }
        next_match {
          secs_start
          external {
            opponent_display_name
            court_info {
              short_name
            }
          }
        }
      }
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedClubsQuery__
 *
 * To run a query within a React component, call `usePaginatedClubsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedClubsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedClubsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedClubsQuery(baseOptions: Apollo.QueryHookOptions<PaginatedClubsQuery, PaginatedClubsQueryVariables> & ({ variables: PaginatedClubsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedClubsQuery, PaginatedClubsQueryVariables>(PaginatedClubsDocument, options);
      }
export function usePaginatedClubsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedClubsQuery, PaginatedClubsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedClubsQuery, PaginatedClubsQueryVariables>(PaginatedClubsDocument, options);
        }
export function usePaginatedClubsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<PaginatedClubsQuery, PaginatedClubsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedClubsQuery, PaginatedClubsQueryVariables>(PaginatedClubsDocument, options);
        }
export type PaginatedClubsQueryHookResult = ReturnType<typeof usePaginatedClubsQuery>;
export type PaginatedClubsLazyQueryHookResult = ReturnType<typeof usePaginatedClubsLazyQuery>;
export type PaginatedClubsSuspenseQueryHookResult = ReturnType<typeof usePaginatedClubsSuspenseQuery>;
export type PaginatedClubsQueryResult = Apollo.QueryResult<PaginatedClubsQuery, PaginatedClubsQueryVariables>;
export const CourtsDocument = gql`
    query Courts($eswId: ID!) {
  courts(eventKey: $eswId) {
    uuid
    name
    short_name
  }
}
    `;

/**
 * __useCourtsQuery__
 *
 * To run a query within a React component, call `useCourtsQuery` and pass it any options that fit your needs.
 * When your component renders, `useCourtsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCourtsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *   },
 * });
 */
export function useCourtsQuery(baseOptions: Apollo.QueryHookOptions<CourtsQuery, CourtsQueryVariables> & ({ variables: CourtsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<CourtsQuery, CourtsQueryVariables>(CourtsDocument, options);
      }
export function useCourtsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<CourtsQuery, CourtsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<CourtsQuery, CourtsQueryVariables>(CourtsDocument, options);
        }
export function useCourtsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<CourtsQuery, CourtsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<CourtsQuery, CourtsQueryVariables>(CourtsDocument, options);
        }
export type CourtsQueryHookResult = ReturnType<typeof useCourtsQuery>;
export type CourtsLazyQueryHookResult = ReturnType<typeof useCourtsLazyQuery>;
export type CourtsSuspenseQueryHookResult = ReturnType<typeof useCourtsSuspenseQuery>;
export type CourtsQueryResult = Apollo.QueryResult<CourtsQuery, CourtsQueryVariables>;
export const EventOverviewDocument = gql`
    query EventOverview($eswId: ID!) {
  event(id: $eswId) {
    id
    long_name
    city
    state
    tickets_published
    schedule_published
    is_require_recipient_name_for_each_ticket
    tickets_code
    hide_seeds
    days
    has_rosters
    is_with_prev_qual
    event_notes
    address
    sport_sanctioning
    locations {
      location_name
    }
    teams_settings {
      baller_tv_available
      hide_standings
      sort_by
    }
  }
  divisions(eventKey: $eswId) {
    division_id
    name
    teams_count
    short_name
    has_flow_chart
    media(filterFileTypes: ["flowchart"]) {
      media_id
      division_id
      file_type
      path
    }
    matches_time_ranges {
      day
      start_time
      end_time
    }
    rounds {
      uuid
      division_id
      sort_priority
      name
      short_name
      first_match_start
      last_match_start
    }
  }
}
    `;

/**
 * __useEventOverviewQuery__
 *
 * To run a query within a React component, call `useEventOverviewQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventOverviewQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventOverviewQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *   },
 * });
 */
export function useEventOverviewQuery(baseOptions: Apollo.QueryHookOptions<EventOverviewQuery, EventOverviewQueryVariables> & ({ variables: EventOverviewQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventOverviewQuery, EventOverviewQueryVariables>(EventOverviewDocument, options);
      }
export function useEventOverviewLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventOverviewQuery, EventOverviewQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventOverviewQuery, EventOverviewQueryVariables>(EventOverviewDocument, options);
        }
export function useEventOverviewSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<EventOverviewQuery, EventOverviewQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventOverviewQuery, EventOverviewQueryVariables>(EventOverviewDocument, options);
        }
export type EventOverviewQueryHookResult = ReturnType<typeof useEventOverviewQuery>;
export type EventOverviewLazyQueryHookResult = ReturnType<typeof useEventOverviewLazyQuery>;
export type EventOverviewSuspenseQueryHookResult = ReturnType<typeof useEventOverviewSuspenseQuery>;
export type EventOverviewQueryResult = Apollo.QueryResult<EventOverviewQuery, EventOverviewQueryVariables>;
export const PaginatedEventsDocument = gql`
    query PaginatedEvents($page: Float!, $pageSize: Float!, $search: String, $startBefore: String, $startAfter: String, $endBefore: String, $endAfter: String, $years: [String!], $asc: Boolean) {
  paginatedEvents(
    page: $page
    pageSize: $pageSize
    search: $search
    startBefore: $startBefore
    startAfter: $startAfter
    endBefore: $endBefore
    endAfter: $endAfter
    years: $years
    asc: $asc
  ) {
    items {
      event_id
      long_name
      city
      date_start
      state
      address
      schedule_published
      small_logo
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedEventsQuery__
 *
 * To run a query within a React component, call `usePaginatedEventsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedEventsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedEventsQuery({
 *   variables: {
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *      startBefore: // value for 'startBefore'
 *      startAfter: // value for 'startAfter'
 *      endBefore: // value for 'endBefore'
 *      endAfter: // value for 'endAfter'
 *      years: // value for 'years'
 *      asc: // value for 'asc'
 *   },
 * });
 */
export function usePaginatedEventsQuery(baseOptions: Apollo.QueryHookOptions<PaginatedEventsQuery, PaginatedEventsQueryVariables> & ({ variables: PaginatedEventsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedEventsQuery, PaginatedEventsQueryVariables>(PaginatedEventsDocument, options);
      }
export function usePaginatedEventsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedEventsQuery, PaginatedEventsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedEventsQuery, PaginatedEventsQueryVariables>(PaginatedEventsDocument, options);
        }
export function usePaginatedEventsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<PaginatedEventsQuery, PaginatedEventsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedEventsQuery, PaginatedEventsQueryVariables>(PaginatedEventsDocument, options);
        }
export type PaginatedEventsQueryHookResult = ReturnType<typeof usePaginatedEventsQuery>;
export type PaginatedEventsLazyQueryHookResult = ReturnType<typeof usePaginatedEventsLazyQuery>;
export type PaginatedEventsSuspenseQueryHookResult = ReturnType<typeof usePaginatedEventsSuspenseQuery>;
export type PaginatedEventsQueryResult = Apollo.QueryResult<PaginatedEventsQuery, PaginatedEventsQueryVariables>;
export const RangeMatchesDocument = gql`
    query RangeMatches($eswId: ID!, $after: String!, $before: String!, $divisionId: ID) {
  dayRangeMatches(
    eventKey: $eswId
    after: $after
    before: $before
    divisionId: $divisionId
  ) {
    items {
      team_id
      opponent_id
      ref_team_id
      match_id
      court_id
      is_tb
      match_name
      division_id
      division_name
      secs_start
      secs_end
      secs_finished
      results {
        winner_team {
          scores
          roster_team_id
        }
      }
      external {
        team_display_name
        opponent_display_name
        ref_team_display_name
        pool_bracket_info {
          is_pool
          uuid
        }
        team_pb_seed {
          id
        }
        opponent_pb_seed {
          id
        }
      }
    }
    filter_info {
      is_filtered
    }
  }
}
    `;

/**
 * __useRangeMatchesQuery__
 *
 * To run a query within a React component, call `useRangeMatchesQuery` and pass it any options that fit your needs.
 * When your component renders, `useRangeMatchesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useRangeMatchesQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      after: // value for 'after'
 *      before: // value for 'before'
 *      divisionId: // value for 'divisionId'
 *   },
 * });
 */
export function useRangeMatchesQuery(baseOptions: Apollo.QueryHookOptions<RangeMatchesQuery, RangeMatchesQueryVariables> & ({ variables: RangeMatchesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<RangeMatchesQuery, RangeMatchesQueryVariables>(RangeMatchesDocument, options);
      }
export function useRangeMatchesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<RangeMatchesQuery, RangeMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<RangeMatchesQuery, RangeMatchesQueryVariables>(RangeMatchesDocument, options);
        }
export function useRangeMatchesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<RangeMatchesQuery, RangeMatchesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<RangeMatchesQuery, RangeMatchesQueryVariables>(RangeMatchesDocument, options);
        }
export type RangeMatchesQueryHookResult = ReturnType<typeof useRangeMatchesQuery>;
export type RangeMatchesLazyQueryHookResult = ReturnType<typeof useRangeMatchesLazyQuery>;
export type RangeMatchesSuspenseQueryHookResult = ReturnType<typeof useRangeMatchesSuspenseQuery>;
export type RangeMatchesQueryResult = Apollo.QueryResult<RangeMatchesQuery, RangeMatchesQueryVariables>;
export const DivisionPoolBracketsDocument = gql`
    query DivisionPoolBrackets($eswId: ID!, $divisionId: ID!) {
  divisionPoolBrackets(eventKey: $eswId, divisionId: $divisionId) {
    ... on Pool {
      is_pool
      uuid
      round_id
      sort_priority
      short_name
      display_name
      date_start
      division_id
      settings {
        SetCount
        PlayAllSets
        WinningPoints
      }
      external {
        courts_short_info {
          uuid
          short_name
        }
      }
      teams {
        team_id
        club_id
        team_name
        master_team_id
        extra {
          show_accepted_bid
          show_previously_accepted_bid
        }
        division_standing {
          seed
          heading
          heading_priority
        }
        pool_bracket_stat {
          sets_pct
          sets_won
          sets_lost
          matches_won
          matches_lost
          points_ratio
        }
      }
      matches {
        team_id
        opponent_id
        ref_team_id
        match_id
        match_name
        match_number
        secs_start
        secs_end
        secs_finished
        source {
          team {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          ref {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
        }
        results {
          sets
          winner_team {
            scores
            roster_team_id
          }
        }
        external {
          team_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          court_info {
            uuid
            short_name
          }
          team_display_name
          opponent_display_name
          ref_team_display_name
        }
      }
      pb_finishes {
        team_name
        team_id
        next_match {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              uuid
              is_pool
            }
          }
        }
      }
    }
    ... on Bracket {
      is_pool
      uuid
      round_id
      sort_priority
      short_name
      display_name
      date_start
      division_id
      settings {
        SetCount
        PlayAllSets
        WinningPoints
      }
      external {
        courts_short_info {
          uuid
          short_name
        }
      }
      teams {
        team_id
        club_id
        team_name
        master_team_id
        extra {
          show_accepted_bid
          show_previously_accepted_bid
        }
        division_standing {
          seed
          heading
          heading_priority
        }
        pool_bracket_stat {
          sets_pct
          sets_won
          sets_lost
          matches_won
          matches_lost
          points_ratio
        }
      }
      matches {
        team_id
        opponent_id
        ref_team_id
        match_id
        match_name
        match_number
        secs_start
        secs_end
        secs_finished
        source {
          team {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          ref {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
        }
        results {
          sets
          winner_team {
            scores
            roster_team_id
          }
        }
        external {
          team_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          court_info {
            uuid
            short_name
          }
          team_display_name
          opponent_display_name
          ref_team_display_name
        }
      }
      pb_finishes {
        team_name
        team_id
        next_match {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              uuid
              is_pool
            }
          }
        }
      }
    }
  }
}
    `;

/**
 * __useDivisionPoolBracketsQuery__
 *
 * To run a query within a React component, call `useDivisionPoolBracketsQuery` and pass it any options that fit your needs.
 * When your component renders, `useDivisionPoolBracketsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDivisionPoolBracketsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      divisionId: // value for 'divisionId'
 *   },
 * });
 */
export function useDivisionPoolBracketsQuery(baseOptions: Apollo.QueryHookOptions<DivisionPoolBracketsQuery, DivisionPoolBracketsQueryVariables> & ({ variables: DivisionPoolBracketsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DivisionPoolBracketsQuery, DivisionPoolBracketsQueryVariables>(DivisionPoolBracketsDocument, options);
      }
export function useDivisionPoolBracketsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DivisionPoolBracketsQuery, DivisionPoolBracketsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DivisionPoolBracketsQuery, DivisionPoolBracketsQueryVariables>(DivisionPoolBracketsDocument, options);
        }
export function useDivisionPoolBracketsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<DivisionPoolBracketsQuery, DivisionPoolBracketsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DivisionPoolBracketsQuery, DivisionPoolBracketsQueryVariables>(DivisionPoolBracketsDocument, options);
        }
export type DivisionPoolBracketsQueryHookResult = ReturnType<typeof useDivisionPoolBracketsQuery>;
export type DivisionPoolBracketsLazyQueryHookResult = ReturnType<typeof useDivisionPoolBracketsLazyQuery>;
export type DivisionPoolBracketsSuspenseQueryHookResult = ReturnType<typeof useDivisionPoolBracketsSuspenseQuery>;
export type DivisionPoolBracketsQueryResult = Apollo.QueryResult<DivisionPoolBracketsQuery, DivisionPoolBracketsQueryVariables>;
export const PaginatedStaffDocument = gql`
    query PaginatedStaff($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedStaff(
    eventKey: $eswId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      staff_id
      first
      last
      club_name
      team_id
      team_name
      role_name
      organization_code
      state
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedStaffQuery__
 *
 * To run a query within a React component, call `usePaginatedStaffQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedStaffQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedStaffQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedStaffQuery(baseOptions: Apollo.QueryHookOptions<PaginatedStaffQuery, PaginatedStaffQueryVariables> & ({ variables: PaginatedStaffQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedStaffQuery, PaginatedStaffQueryVariables>(PaginatedStaffDocument, options);
      }
export function usePaginatedStaffLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedStaffQuery, PaginatedStaffQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedStaffQuery, PaginatedStaffQueryVariables>(PaginatedStaffDocument, options);
        }
export function usePaginatedStaffSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<PaginatedStaffQuery, PaginatedStaffQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedStaffQuery, PaginatedStaffQueryVariables>(PaginatedStaffDocument, options);
        }
export type PaginatedStaffQueryHookResult = ReturnType<typeof usePaginatedStaffQuery>;
export type PaginatedStaffLazyQueryHookResult = ReturnType<typeof usePaginatedStaffLazyQuery>;
export type PaginatedStaffSuspenseQueryHookResult = ReturnType<typeof usePaginatedStaffSuspenseQuery>;
export type PaginatedStaffQueryResult = Apollo.QueryResult<PaginatedStaffQuery, PaginatedStaffQueryVariables>;
export const DivisionTeamsStandingDocument = gql`
    query DivisionTeamsStanding($eswId: ID!, $divisionId: ID!) {
  divisionTeamsStanding(eventKey: $eswId, divisionId: $divisionId) {
    team_id
    team_name
    team_code
    extra {
      show_previously_accepted_bid
      show_accepted_bid
    }
    division_standing {
      matches_won
      matches_lost
      sets_won
      sets_lost
      sets_pct
      points_ratio
      points
      rank
      seed
      heading
      heading_priority
    }
  }
}
    `;

/**
 * __useDivisionTeamsStandingQuery__
 *
 * To run a query within a React component, call `useDivisionTeamsStandingQuery` and pass it any options that fit your needs.
 * When your component renders, `useDivisionTeamsStandingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDivisionTeamsStandingQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      divisionId: // value for 'divisionId'
 *   },
 * });
 */
export function useDivisionTeamsStandingQuery(baseOptions: Apollo.QueryHookOptions<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables> & ({ variables: DivisionTeamsStandingQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>(DivisionTeamsStandingDocument, options);
      }
export function useDivisionTeamsStandingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>(DivisionTeamsStandingDocument, options);
        }
export function useDivisionTeamsStandingSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>(DivisionTeamsStandingDocument, options);
        }
export type DivisionTeamsStandingQueryHookResult = ReturnType<typeof useDivisionTeamsStandingQuery>;
export type DivisionTeamsStandingLazyQueryHookResult = ReturnType<typeof useDivisionTeamsStandingLazyQuery>;
export type DivisionTeamsStandingSuspenseQueryHookResult = ReturnType<typeof useDivisionTeamsStandingSuspenseQuery>;
export type DivisionTeamsStandingQueryResult = Apollo.QueryResult<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>;
export const FavoriteTeamsStandingDocument = gql`
    query FavoriteTeamsStanding($eswId: ID!, $teamsIds: [ID!]!, $search: String) {
  favoriteTeams(eventKey: $eswId, teamsIds: $teamsIds, search: $search) {
    club_id
    team_id
    team_name
    team_code
    division_id
    division_name
    next_match {
      secs_start
      external {
        pool_bracket_info {
          is_pool
          uuid
        }
        court_info {
          short_name
        }
      }
    }
    division_standing {
      matches_won
      matches_lost
      sets_won
      sets_lost
      sets_pct
      points_ratio
      rank
      seed
      heading
      heading_priority
    }
  }
}
    `;

/**
 * __useFavoriteTeamsStandingQuery__
 *
 * To run a query within a React component, call `useFavoriteTeamsStandingQuery` and pass it any options that fit your needs.
 * When your component renders, `useFavoriteTeamsStandingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFavoriteTeamsStandingQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      teamsIds: // value for 'teamsIds'
 *      search: // value for 'search'
 *   },
 * });
 */
export function useFavoriteTeamsStandingQuery(baseOptions: Apollo.QueryHookOptions<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables> & ({ variables: FavoriteTeamsStandingQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>(FavoriteTeamsStandingDocument, options);
      }
export function useFavoriteTeamsStandingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>(FavoriteTeamsStandingDocument, options);
        }
export function useFavoriteTeamsStandingSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>(FavoriteTeamsStandingDocument, options);
        }
export type FavoriteTeamsStandingQueryHookResult = ReturnType<typeof useFavoriteTeamsStandingQuery>;
export type FavoriteTeamsStandingLazyQueryHookResult = ReturnType<typeof useFavoriteTeamsStandingLazyQuery>;
export type FavoriteTeamsStandingSuspenseQueryHookResult = ReturnType<typeof useFavoriteTeamsStandingSuspenseQuery>;
export type FavoriteTeamsStandingQueryResult = Apollo.QueryResult<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>;
export const PaginatedDivisionTeamsDocument = gql`
    query PaginatedDivisionTeams($eswId: ID!, $divisionId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedDivisionTeams(
    eventKey: $eswId
    divisionId: $divisionId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      team_id
      club_id
      team_name
      division_id
      next_match {
        secs_start
        external {
          opponent_display_name
          court_info {
            short_name
          }
          pool_bracket_info {
            is_pool
            uuid
          }
        }
      }
      division_standing {
        matches_won
        matches_lost
        sets_won
        sets_lost
        rank
      }
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedDivisionTeamsQuery__
 *
 * To run a query within a React component, call `usePaginatedDivisionTeamsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedDivisionTeamsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedDivisionTeamsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      divisionId: // value for 'divisionId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedDivisionTeamsQuery(baseOptions: Apollo.QueryHookOptions<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables> & ({ variables: PaginatedDivisionTeamsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>(PaginatedDivisionTeamsDocument, options);
      }
export function usePaginatedDivisionTeamsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>(PaginatedDivisionTeamsDocument, options);
        }
export function usePaginatedDivisionTeamsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>(PaginatedDivisionTeamsDocument, options);
        }
export type PaginatedDivisionTeamsQueryHookResult = ReturnType<typeof usePaginatedDivisionTeamsQuery>;
export type PaginatedDivisionTeamsLazyQueryHookResult = ReturnType<typeof usePaginatedDivisionTeamsLazyQuery>;
export type PaginatedDivisionTeamsSuspenseQueryHookResult = ReturnType<typeof usePaginatedDivisionTeamsSuspenseQuery>;
export type PaginatedDivisionTeamsQueryResult = Apollo.QueryResult<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>;
export const QualifiedTeamsDocument = gql`
    query QualifiedTeams($eswId: ID!) {
  qualifiedTeams(eventKey: $eswId) {
    team_id
    team_name
    team_code
    division_id
    extra {
      prev_qual_age
      prev_qual_division
      earned_at
    }
  }
}
    `;

/**
 * __useQualifiedTeamsQuery__
 *
 * To run a query within a React component, call `useQualifiedTeamsQuery` and pass it any options that fit your needs.
 * When your component renders, `useQualifiedTeamsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useQualifiedTeamsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *   },
 * });
 */
export function useQualifiedTeamsQuery(baseOptions: Apollo.QueryHookOptions<QualifiedTeamsQuery, QualifiedTeamsQueryVariables> & ({ variables: QualifiedTeamsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<QualifiedTeamsQuery, QualifiedTeamsQueryVariables>(QualifiedTeamsDocument, options);
      }
export function useQualifiedTeamsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<QualifiedTeamsQuery, QualifiedTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<QualifiedTeamsQuery, QualifiedTeamsQueryVariables>(QualifiedTeamsDocument, options);
        }
export function useQualifiedTeamsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<QualifiedTeamsQuery, QualifiedTeamsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<QualifiedTeamsQuery, QualifiedTeamsQueryVariables>(QualifiedTeamsDocument, options);
        }
export type QualifiedTeamsQueryHookResult = ReturnType<typeof useQualifiedTeamsQuery>;
export type QualifiedTeamsLazyQueryHookResult = ReturnType<typeof useQualifiedTeamsLazyQuery>;
export type QualifiedTeamsSuspenseQueryHookResult = ReturnType<typeof useQualifiedTeamsSuspenseQuery>;
export type QualifiedTeamsQueryResult = Apollo.QueryResult<QualifiedTeamsQuery, QualifiedTeamsQueryVariables>;