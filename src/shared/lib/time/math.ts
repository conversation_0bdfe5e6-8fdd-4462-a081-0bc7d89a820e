export { add, addSeconds, addMinutes, addHours, addDays, addMonths, addYears } from 'date-fns';

/**
 * Aligns a date to the nearest time step boundary
 * @param date - The date to align
 * @param stepMinutes - The step size in minutes
 * @param toFloor - Whether to round down (true) or up (false)
 * @returns The aligned date
 */
export const alignToStep = (date: Date, stepMinutes: number, toFloor: boolean = true): Date => {
	const newDate = new Date(date);
	const totalMinutes = newDate.getUTCHours() * 60 + newDate.getUTCMinutes();
	const remainder = totalMinutes % stepMinutes;

	if (remainder !== 0) {
		if (toFloor) {
			// round down
			const floored = totalMinutes - remainder;
			newDate.setUTCHours(Math.floor(floored / 60), floored % 60, 0, 0);
		} else {
			// round up
			const needed = stepMinutes - remainder;
			const ceiled = totalMinutes + needed;
			newDate.setUTCHours(Math.floor(ceiled / 60), ceiled % 60, 0, 0);
		}
	}

	return newDate;
};
