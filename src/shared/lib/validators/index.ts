import { validate } from 'uuid';

export const isESWID = (value: unknown): boolean => {
	return typeof value === 'string' && /^[0-9A-F]{9}$/i.test(value);
};

export const isNumericId = (value: unknown): value is string => {
	return typeof value === 'string' && /^\d{1,9}$/.test(value);
};

export const isValidEventKey = (value: unknown): value is string => {
	return isESWID(value) || isNumericId(value);
};

export const isUUID = (value: unknown): value is string => {
	return validate(value);
};

export const isNumericIndex = (value: unknown): value is string => {
	return typeof value === 'string' && /^\d+$/.test(value);
};

// Validates a time format in HH:mm (24-hour format)
export const isTimeFormat = (value: unknown): value is string => {
	return typeof value === 'string' && /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(value);
};
