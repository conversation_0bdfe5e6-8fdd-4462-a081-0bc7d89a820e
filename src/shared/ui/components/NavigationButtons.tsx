import { useNavigate, useParams } from 'react-router';
import styled from 'styled-components';

const ButtonsContainer = styled.div`
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-top: 1.5rem;
`;

const NavButton = styled.button`
	padding: 0.5rem 1rem;
	background-color: ${({ theme }) => theme.colors.primary};
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-family: ${({ theme }) => theme.fonts.primary};
	font-size: 0.875rem;
	transition: background-color 0.2s;

	&:hover {
		background-color: ${({ theme }) => theme.colors.secondary};
	}
`;

type NavigationButtonsProps = {
	showEventLinks?: boolean;
	showDivisionLinks?: boolean;
};

export const NavigationButtons = ({
	showEventLinks = true,
	showDivisionLinks = false,
}: NavigationButtonsProps) => {
	const navigate = useNavigate();
	const { eswId } = useParams();

	return (
		<ButtonsContainer>
			<NavButton onClick={() => navigate('/events/current')}>Events</NavButton>

			{showEventLinks && eswId && (
				<>
					<NavButton onClick={() => navigate(`/event/${eswId}/favorites`)}>Favorites</NavButton>
					<NavButton onClick={() => navigate(`/event/${eswId}/qualified`)}>Qualified</NavButton>
					<NavButton onClick={() => navigate(`/event/${eswId}/clubs-teams`)}>
						Clubs & Teams
					</NavButton>
					<NavButton onClick={() => navigate(`/event/${eswId}/court-grid`)}>Court Grid</NavButton>
					<NavButton onClick={() => navigate(`/event/${eswId}/roster`)}>Roster</NavButton>
				</>
			)}

			{showDivisionLinks && eswId && (
				<>
					<NavButton onClick={() => navigate(`/event/${eswId}/teams`)}>Teams</NavButton>
					<NavButton onClick={() => navigate(`/event/${eswId}/standings`)}>Standings</NavButton>
					<NavButton onClick={() => navigate(`/event/${eswId}/pool-bracket`)}>
						Pool Bracket
					</NavButton>
				</>
			)}
		</ButtonsContainer>
	);
};
