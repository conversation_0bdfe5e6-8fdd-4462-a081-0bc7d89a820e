import { RuleType } from '@prisma/client';

import { SchedulerConfig } from '../core/scheduler/scheduler.interface';

export function isSchedulerConfig(value: any): value is SchedulerConfig {
  // Check if the value is an array
  if (!Array.isArray(value)) {
    return false;
  }

  // Get all valid RuleType enum values
  const validRuleTypes: string[] = Object.values(RuleType);

  // Regular expression for HH:MM:SS format
  const timeRegex = /^([0-1]\d|2[0-3]):([0-5]\d):([0-5]\d)$/;
  const dateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
  // Iterate through each element in the array
  for (const rule of value) {
    // Check if rule is an object
    if (typeof rule !== 'object' || rule === null) {
      return false;
    }

    // Check for required properties: ruleType and frequency
    if (!('ruleType' in rule) || !('frequency' in rule)) {
      return false;
    }

    // Validate ruleType
    if (
      typeof rule.ruleType !== 'string' // Assuming RuleType is a string enum
      || !validRuleTypes.includes(rule.ruleType)
    ) {
      return false;
    }

    // Validate frequency
    if (typeof rule.frequency !== 'number') {
      return false;
    }

    // Validate optional properties if they exist

    // startDatetime
    if (
      'startDatetime' in rule
      && rule.startDatetime !== null
      && !dateTimeRegex.test(rule.startDatetime)
    ) {
      return false;
    }

    // endDatetime
    if (
      'endDatetime' in rule
      && rule.endDatetime !== null
      && !dateTimeRegex.test(rule.endDatetime)
    ) {
      return false;
    }

    // dayOfWeek
    if ('dayOfWeek' in rule) {
      if (!Array.isArray(rule.dayOfWeek)) {
        return false;
      }
      for (const day of rule.dayOfWeek) {
        if (typeof day !== 'number' || day < 0 || day > 6) {
          return false;
        }
      }
    }

    // startTime
    if ('startTime' in rule && rule.startTime !== null) {
      if (typeof rule.startTime !== 'string' || !timeRegex.test(rule.startTime)) {
        return false;
      }
    }

    // endTime
    if ('endTime' in rule && rule.endTime !== null) {
      if (typeof rule.endTime !== 'string' || !timeRegex.test(rule.endTime)) {
        return false;
      }
    }
  }

  // All checks passed
  return true;
}
