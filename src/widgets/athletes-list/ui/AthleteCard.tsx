import { FavoriteStatus, FavoriteStatusIcon } from '@features/favorite-teams-manager';

import { PaginatedAthlete } from '../model';
import {
	RosterCard,
	RosterCardContent,
	RosterCardContentWrapper,
	R<PERSON>er<PERSON><PERSON><PERSON>ooter,
	RosterCardHeader,
} from './styled';

export type Props = {
	athlete: PaginatedAthlete;
	belongsToFavouriteTeam: boolean;
	onHeaderClick: (athlete: PaginatedAthlete) => void;
};

export const AthleteCard = ({ athlete, belongsToFavouriteTeam, onHeaderClick }: Props) => {
	const header = `${athlete.uniform ? `#${athlete.uniform} ` : ''} ${
		athlete.short_position || ''
	} ${athlete.first} ${athlete.last}`;
	const footer = `${athlete.club_name}, ${athlete.state}`;
	const content = `${athlete.team_name} - ${athlete.organization_code}`;

	const handleHeaderClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		onHeaderClick(athlete);
	};

	return (
		<RosterCard>
			<RosterCardHeader className="roster-card__header" onClick={handleHeaderClick}>
				<FavoriteStatusIcon
					status={belongsToFavouriteTeam ? FavoriteStatus.Full : FavoriteStatus.None}
				/>
				{header}
			</RosterCardHeader>
			<RosterCardContentWrapper>
				<RosterCardContent>{content}</RosterCardContent>
				<RosterCardFooter>{footer}</RosterCardFooter>
			</RosterCardContentWrapper>
		</RosterCard>
	);
};
