import styled from 'styled-components';

import { RangeMatch } from '@entities/match';

import { DateTimeFormat, formatInUTC } from '@shared/lib/time';

const MatchCardWrapper = styled.div<{
	$isActive?: boolean;
	$isBlur?: boolean;
	$isFinished?: boolean;
	$minPeriod: number;
}>`
	position: absolute;
	top: 4px;
	left: 4px;
	right: 4px;
	background: ${({ $isFinished }) => ($isFinished ? '#f8f9fa' : '#fff')};
	border: 2px solid
		${({ $isActive, $isFinished }) => ($isActive ? '#007bff' : $isFinished ? '#6c757d' : '#dfe3e8')};
	border-radius: 4px;
	padding: 8px;
	font-size: 11px;
	cursor: pointer;
	transition: all 0.2s ease;
	opacity: ${({ $isBlur }) => ($isBlur ? 0.3 : 1)};
	z-index: ${({ $isActive }) => ($isActive ? 5 : 1)};

	&:hover {
		border-color: #007bff;
		box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
	}
`;

const MatchHeader = styled.div`
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 4px;
`;

const MatchTitle = styled.div`
	font-weight: 600;
	color: ${({ theme }) => theme.colors.primary};
	font-size: 10px;
	line-height: 1.2;
	flex: 1;
	margin-right: 4px;
`;

const MatchTime = styled.div`
	font-size: 9px;
	color: ${({ theme }) => theme.colors.text};
	white-space: nowrap;
`;

const TeamsContainer = styled.div`
	display: flex;
	flex-direction: column;
	gap: 2px;
`;

const TeamRow = styled.div<{ $isWinner?: boolean }>`
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 10px;
	font-weight: ${({ $isWinner }) => ($isWinner ? 600 : 400)};
	color: ${({ $isWinner, theme }) => ($isWinner ? theme.colors.primary : theme.colors.text)};
`;

const TeamName = styled.span`
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: 4px;
`;

const TeamScore = styled.span`
	font-weight: 600;
	min-width: 20px;
	text-align: right;
`;

const TbIndicator = styled.div`
	position: absolute;
	top: 2px;
	right: 2px;
	background: #ff6b6b;
	color: white;
	font-size: 8px;
	padding: 1px 3px;
	border-radius: 2px;
	font-weight: 600;
`;

type Props = {
	match: RangeMatch;
	focusedMatchId?: string | null;
	minPeriod: number;
	onMatchClick?: (matchId: string) => void;
};

export const MatchCard = ({ match, focusedMatchId, minPeriod, onMatchClick }: Props) => {
	const {
		match_id,
		division_name,
		match_name,
		secs_start,
		secs_finished,
		is_tb,
		external,
		results,
	} = match;

	const isActive = focusedMatchId === match_id;
	const isBlur = !!focusedMatchId && focusedMatchId !== match_id;
	const isFinished = !!secs_finished;
	const isTbMatch = !!is_tb;

	const title = `${division_name} ${match_name}`;
	const startTime = secs_start ? formatInUTC(secs_start, DateTimeFormat.TwelveHour) : '';

	// Parse scores if available
	const winnerTeam = results?.winner_team;
	const scores = winnerTeam?.scores || '';
	const [score1, score2] = scores.split(' ')[0]?.split('-') || ['', ''];

	const handleClick = () => {
		if (onMatchClick) {
			onMatchClick(match_id);
		}
	};

	return (
		<MatchCardWrapper
			$isActive={isActive}
			$isBlur={isBlur}
			$isFinished={isFinished}
			$minPeriod={minPeriod}
			onClick={handleClick}
		>
			{isTbMatch && <TbIndicator>TB</TbIndicator>}

			<MatchHeader>
				<MatchTitle>{title}</MatchTitle>
				<MatchTime>{startTime}</MatchTime>
			</MatchHeader>

			<TeamsContainer>
				<TeamRow $isWinner={winnerTeam?.roster_team_id === match.team_id}>
					<TeamName>{external?.team_display_name || 'Team 1'}</TeamName>
					{score1 && <TeamScore>{score1}</TeamScore>}
				</TeamRow>
				<TeamRow $isWinner={winnerTeam?.roster_team_id === match.opponent_id}>
					<TeamName>{external?.opponent_display_name || 'Team 2'}</TeamName>
					{score2 && <TeamScore>{score2}</TeamScore>}
				</TeamRow>
			</TeamsContainer>
		</MatchCardWrapper>
	);
};
