import styled from 'styled-components';

import { MatchDelaySeverity } from '@shared/domain/match';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	height: 100%;
	overflow: hidden;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;
	color: ${({ theme }) => theme.colors.text};
`;

export const NoDataMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;
	color: ${({ theme }) => theme.colors.text};
	font-style: italic;
`;

export const GridContainer = styled.div`
	overflow: auto;
	flex: 1;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
`;

export const GridTable = styled.table`
	width: 100%;
	border-collapse: collapse;
	position: relative;

	thead {
		position: sticky;
		top: 0;
		z-index: 10;
		background-color: #f2f7ff;

		th {
			font-weight: normal;
			border: 1px solid #dfe3e8;
			padding: 8px;
			text-align: center;
			min-width: 105px;
			height: 40px;

			&:first-child {
				min-width: 80px;
				text-align: left;
				font-weight: 700;
				color: ${({ theme }) => theme.colors.primary};
				position: sticky;
				left: 0;
				z-index: 11;
				background-color: #f2f7ff;
			}
		}
	}

	tbody {
		td {
			border: 1px solid #dfe3e8;
			height: 120px;
			position: relative;
			vertical-align: top;

			&:first-child {
				position: sticky;
				left: 0;
				z-index: 9;
				background: #fff;
				font-size: 12px;
				font-weight: 700;
				color: ${({ theme }) => theme.colors.primary};
				padding: 8px;
				width: 80px;
				min-width: 80px;
			}
		}
	}
`;

export const CourtCell = styled.div<{ $delaySeverity?: MatchDelaySeverity }>`
	display: flex;
	align-items: center;
	height: 100%;
	padding-left: 8px;
	border-left: 3px solid
		${({ $delaySeverity }) =>
			$delaySeverity === MatchDelaySeverity.Critical
				? '#FF6161'
				: $delaySeverity === MatchDelaySeverity.Warning
					? '#ff997a'
					: '#dfe3e8'};
	color: ${({ $delaySeverity, theme }) =>
		$delaySeverity === MatchDelaySeverity.Critical
			? '#FF6161'
			: $delaySeverity === MatchDelaySeverity.Warning
				? '#ff997a'
				: theme.colors.primary};
`;

export const TimeHeaderCell = styled.th`
	font-size: 12px;
	line-height: 1.2;
`;

export const MatchCell = styled.td`
	padding: 4px;
	position: relative;
`;
