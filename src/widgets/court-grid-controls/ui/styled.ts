import styled from 'styled-components';

export const ControlsWrapper = styled.div`
	display: flex;
	gap: 1rem;
	margin-bottom: 1rem;
	padding: 1rem;
	background: #f8f9fa;
	border-radius: 4px;
	flex-wrap: wrap;
`;

export const ControlGroup = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
`;

export const Label = styled.label`
	font-size: 12px;
	font-weight: 600;
	color: ${({ theme }) => theme.colors.text};
`;

export const Input = styled.input`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;

export const Select = styled.select`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;
