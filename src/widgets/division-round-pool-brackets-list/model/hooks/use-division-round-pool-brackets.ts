import { useMemo } from 'react';

import { useDivisionRoundsManager } from '@features/division-rounds-manager';

import { buildRoundGroupsPoolBracketsMap } from '@shared/domain/round';

import { DivisionRoundPoolBracketsParams, DivisionRoundPoolBracketsReturnType } from '../types';
import { useDivisionPoolBrackets } from './use-division-pool-brackets';

export const useDivisionRoundPoolBrackets = ({
	eswId,
	divisionId,
	roundId,
}: DivisionRoundPoolBracketsParams): DivisionRoundPoolBracketsReturnType => {
	// Get all pool brackets for the division
	const { poolBrackets: allPoolBrackets, loading: poolBracketsLoading } = useDivisionPoolBrackets({
		eswId,
		divisionId,
	});

	// Get information about rounds and grouped rounds
	const {
		rounds,
		getRoundGroupKeyByRoundId,
		getRoundGroupByGroupKey,
		loading: roundsManagerLoading,
	} = useDivisionRoundsManager({ eswId, divisionId });

	// Build the round groups pool brackets map
	const roundGroupsPoolBracketsMap = useMemo(() => {
		if (!allPoolBrackets || !rounds) return null;
		return buildRoundGroupsPoolBracketsMap(rounds, allPoolBrackets);
	}, [allPoolBrackets, rounds]);

	const roundGroupKey = useMemo(
		() => getRoundGroupKeyByRoundId(roundId),
		[getRoundGroupKeyByRoundId, roundId],
	);

	const poolBrackets = useMemo(() => {
		if (!roundGroupsPoolBracketsMap || !roundGroupKey) return null;
		return roundGroupsPoolBracketsMap.get(roundGroupKey) ?? null;
	}, [roundGroupsPoolBracketsMap, roundGroupKey]);

	const roundGroup = useMemo(() => {
		return roundGroupKey ? getRoundGroupByGroupKey(roundGroupKey) : null;
	}, [roundGroupKey, getRoundGroupByGroupKey]);

	return {
		poolBrackets,
		roundGroup,
		roundGroupKey,
		loading: poolBracketsLoading || roundsManagerLoading,
	};
};
