import { useMemo } from 'react';

import {
	FavoriteStatus,
	FavoriteStatusIcon,
	type FavoriteTeamsManagerReturnType,
} from '@features/favorite-teams-manager';

import { DivisionPoolBracket } from '@entities/pool-bracket';

import { FavoriteIcon, TeamItem as StyledTeamItem, TeamInfo, TeamName, TeamStats } from './styled';

type PoolBracketTeam = DivisionPoolBracket['teams'][number];

export type Props = {
	team: PoolBracketTeam;
	onTeamClick?: (teamId: string) => void;
} & Pick<FavoriteTeamsManagerReturnType, 'favoriteTeamsIds' | 'toggleTeamFavorite'>;

export const TeamItem = ({ team, favoriteTeamsIds, toggleTeamFavorite, onTeamClick }: Props) => {
	const isFavorite = useMemo(() => {
		return favoriteTeamsIds.includes(team.team_id);
	}, [favoriteTeamsIds, team.team_id]);

	const canShowStat = useMemo(() => {
		return !!team.pool_bracket_stat?.points_ratio;
	}, [team.pool_bracket_stat]);

	const handleTeamClick = () => {
		if (onTeamClick) {
			onTeamClick(team.team_id);
		}
	};

	const handleFavoriteClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		toggleTeamFavorite({
			team_id: team.team_id,
			club_id: team.club_id,
		});
	};

	const hasExtraInfo = !!(
		team?.extra?.show_accepted_bid && team?.extra?.show_previously_accepted_bid
	);

	return (
		<StyledTeamItem $isShowStat={canShowStat} onClick={handleTeamClick}>
			<TeamInfo>
				<FavoriteIcon onClick={handleFavoriteClick}>
					<FavoriteStatusIcon status={isFavorite ? FavoriteStatus.Full : FavoriteStatus.None} />
				</FavoriteIcon>
				<TeamName $extraOffset={hasExtraInfo}>{team.team_name}</TeamName>
			</TeamInfo>

			{canShowStat && (
				<TeamStats>
					<div>
						<span>
							<i>{team.pool_bracket_stat?.matches_won || 0}</i>-
							<i>{team.pool_bracket_stat?.matches_lost || 0}</i>
						</span>
					</div>
					<div>
						<span>
							<i>{team.pool_bracket_stat?.sets_won || 0}</i>-
							<i>{team.pool_bracket_stat?.sets_lost || 0}</i>
						</span>
					</div>
					<div>
						<span>
							<i>{team.pool_bracket_stat?.points_ratio?.toFixed(2) || '0.00'}</i>
						</span>
					</div>
				</TeamStats>
			)}
		</StyledTeamItem>
	);
};
