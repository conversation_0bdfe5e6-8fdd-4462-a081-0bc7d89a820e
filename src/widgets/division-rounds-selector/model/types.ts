import { RoundDetails } from '@features/division-rounds-manager';

import { RoundGroup } from '@shared/domain/round';

export type DivisionRoundsSelectorParams = {
	eswId: string;
	divisionId: string;
	roundId: string;
	onRoundSelect: (round: RoundDetails) => void;
};

export type DivisionRoundsSelectorReturnType = {
	roundsGroups: RoundGroup<RoundDetails>[] | null;
	selectedRoundGroup: RoundGroup<RoundDetails> | null;
	handleRoundGroupSelect: (roundGroup: RoundGroup<RoundDetails>) => void;
	loading: boolean;
};
