import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

export const WidgetTitle = styled.h3`
	margin: 0 0 1rem 0;
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
`;

export const RoundGroupsList = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
`;

export const RoundGroupItem = styled.button<{ $isActive?: boolean }>`
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	padding: 0.75rem;
	border: 2px solid
		${({ $isActive, theme }) => ($isActive ? theme?.colors?.primary || '#007bff' : '#e9ecef')};
	border-radius: 6px;
	background: ${({ $isActive }) => ($isActive ? '#f8f9ff' : '#fff')};
	cursor: pointer;
	transition: all 0.2s ease;
	text-align: left;
	width: 100%;

	&:hover {
		border-color: ${({ theme }) => theme?.colors?.primary || '#007bff'};
		background: #f8f9ff;
	}

	&:focus {
		outline: none;
		box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
	}
`;

export const RoundGroupName = styled.div`
	font-size: 0.875rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.25rem;
`;

export const RoundGroupDuration = styled.div`
	font-size: 0.75rem;
	color: #666;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	color: #666;
`;

export const NoRoundsMessage = styled.div`
	text-align: center;
	padding: 2rem;
	color: #666;
	font-style: italic;
`;
