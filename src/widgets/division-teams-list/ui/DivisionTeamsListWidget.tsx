import { useCallback, useEffect, useRef } from 'react';

import { useFavoriteTeamsManager } from '@features/favorite-teams-manager';

import { usePaginatedDivisionTeams } from '../model';
import { TeamItem } from './TeamItem';
import {
	LoadingWrapper,
	NoTeamsMessage,
	TeamItem as StyledTeamItem,
	TeamsList,
	WidgetWrapper,
} from './styled';

export type Props = {
	eswId: string;
	divisionId: string;
	search?: string | null;
};

export const DivisionTeamsListWidget = ({ eswId, divisionId, search }: Props) => {
	const {
		data: teams,
		loading,
		fetchNext,
	} = usePaginatedDivisionTeams({
		eswId,
		divisionId,
		search,
	});

	const { favoriteTeamsIds, toggleTeamFavorite } = useFavoriteTeamsManager(eswId);

	const listRef = useRef<HTMLUListElement>(null);

	// Handle scroll to load more teams
	const handleScroll = useCallback(() => {
		if (!listRef.current) return;

		const { scrollTop, scrollHeight, clientHeight } = listRef.current;

		// If we're near the bottom, load more
		if (scrollTop + clientHeight >= scrollHeight - 100 && !loading) {
			fetchNext();
		}
	}, [fetchNext, loading]);

	// Add scroll event listener
	useEffect(() => {
		const listElement = listRef.current;
		if (listElement) {
			listElement.addEventListener('scroll', handleScroll);
			return () => {
				listElement.removeEventListener('scroll', handleScroll);
			};
		}
	}, [handleScroll]);

	if (loading && (!teams || teams.length === 0)) {
		return (
			<LoadingWrapper>
				<div>Loading...</div>
			</LoadingWrapper>
		);
	}

	if (!teams || teams.length === 0) {
		return <NoTeamsMessage>No teams found</NoTeamsMessage>;
	}

	return (
		<WidgetWrapper>
			<TeamsList ref={listRef}>
				{teams.map((team) => (
					<StyledTeamItem key={team.team_id}>
						<TeamItem
							team={team}
							favoriteTeamsIds={favoriteTeamsIds}
							toggleTeamFavorite={toggleTeamFavorite}
						/>
					</StyledTeamItem>
				))}
				{loading && (
					<LoadingWrapper>
						<div>Loading more...</div>
					</LoadingWrapper>
				)}
			</TeamsList>
		</WidgetWrapper>
	);
};
