import { useMemo } from 'react';

import {
	FavoriteStatus,
	FavoriteStatusIcon,
	type FavoriteTeamsManagerReturnType,
} from '@features/favorite-teams-manager';

import { DateTimeFormat, formatInUTC } from '@shared/lib/time';

import { PaginatedDivisionTeam } from '../model';
import {
	CourtInfo,
	MatchTime,
	OpponentInfo,
	StandingInfo,
	TeamDetails,
	TeamInfo,
	TeamName,
	TeamStatus,
} from './styled';

export type Props = {
	team: PaginatedDivisionTeam;
} & Pick<FavoriteTeamsManagerReturnType, 'favoriteTeamsIds' | 'toggleTeamFavorite'>;

export const TeamItem = ({ team, favoriteTeamsIds, toggleTeamFavorite }: Props) => {
	const hasMatch = !!team.next_match;
	const isFavorite = useMemo(
		() => favoriteTeamsIds.includes(team.team_id),
		[favoriteTeamsIds, team.team_id],
	);

	const onToggleTeamFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		toggleTeamFavorite(team);
	};

	const getStandingResult = () => {
		if (team.division_standing) {
			const { matches_won, matches_lost, rank } = team.division_standing;
			if (rank) {
				return `#${rank}`;
			}
			if (matches_won !== null && matches_lost !== null) {
				return `${matches_won}-${matches_lost}`;
			}
		}
		return null;
	};

	return (
		<TeamInfo $isPlayed={!hasMatch}>
			<TeamDetails>
				<span>
					<FavoriteStatusIcon
						status={isFavorite ? FavoriteStatus.Full : FavoriteStatus.None}
						onClick={onToggleTeamFavorite}
					></FavoriteStatusIcon>
				</span>
				<TeamName>{team.team_name}</TeamName>
				{hasMatch && (
					<OpponentInfo>vs {team.next_match?.external.opponent_display_name}</OpponentInfo>
				)}
			</TeamDetails>

			<TeamStatus $isPlayed={!hasMatch}>
				{!hasMatch ? (
					<StandingInfo>{getStandingResult()}</StandingInfo>
				) : (
					<>
						<MatchTime>
							{team.next_match?.secs_start &&
								formatInUTC(team.next_match.secs_start, DateTimeFormat.WeekdayShortTime)}
						</MatchTime>
						<CourtInfo>{team.next_match?.external.court_info?.short_name || 'TBD'}</CourtInfo>
					</>
				)}
			</TeamStatus>
		</TeamInfo>
	);
};
