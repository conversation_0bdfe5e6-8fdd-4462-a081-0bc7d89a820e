import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
`;

export const TeamsList = styled.ul`
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 70vh;
	overflow-y: auto;
`;

export const TeamItem = styled.li`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	margin: 0 0 1rem;
	position: relative;
	background: #fff;
`;

export const TeamInfo = styled.div<{ $isPlayed?: boolean }>`
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 1rem;
	cursor: pointer;

	&:hover {
		background-color: #f8f9fa;
	}
`;

export const TeamDetails = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
	flex: 1;
`;

export const TeamName = styled.div`
	font-weight: 700;
	font-size: 1rem;
	color: #333;
`;

export const OpponentInfo = styled.div`
	font-size: 0.875rem;
	color: #666;
`;

export const TeamStatus = styled.div<{ $isPlayed?: boolean }>`
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 0.25rem;
	min-width: 120px;
	text-align: right;
`;

export const MatchTime = styled.p`
	margin: 0;
	font-size: 0.875rem;
	color: #666;
`;

export const CourtInfo = styled.p`
	margin: 0;
	font-size: 0.875rem;
	color: #666;
`;

export const StandingInfo = styled.p`
	margin: 0;
	font-weight: 700;
	color: #333;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	color: #666;
`;

export const NoTeamsMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	color: #666;
	font-style: italic;
`;
