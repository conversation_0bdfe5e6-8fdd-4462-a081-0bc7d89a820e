import { useMemo } from 'react';

import { FavoriteTeam, useFavoriteTeamsStandingQuery } from '@entities/team';

import { FavoriteTeamsParams, FavoriteTeamsReturnType } from '../types';

export const useFavoriteTeams = ({
	eswId,
	favoriteTeamsIds,
	search,
}: FavoriteTeamsParams): FavoriteTeamsReturnType => {
	const limitedTeamsIds = useMemo(() => favoriteTeamsIds.slice(0, 200), [favoriteTeamsIds]);

	const { data, previousData, loading } = useFavoriteTeamsStandingQuery({
		skip: !favoriteTeamsIds.length,
		variables: {
			eswId,
			teamsIds: limitedTeamsIds,
			search: search || '',
		},
	});

	const teams: FavoriteTeam[] = useMemo(() => {
		if (!favoriteTeamsIds.length) {
			return [];
		}
		return data?.favoriteTeams || previousData?.favoriteTeams || [];
	}, [favoriteTeamsIds, data, previousData]);

	return {
		teams,
		loading,
	};
};
