import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
`;

export const TeamsList = styled.ul`
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 70vh;
	overflow-y: auto;
`;

export const TeamItem = styled.li`
	border-bottom: 1px solid #e0e0e0;
	padding: 1rem;

	&:last-child {
		border-bottom: none;
	}
`;

export const TeamCard = styled.div`
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 1rem;
`;

export const TeamInfo = styled.div`
	display: flex;
	flex-direction: column;
	flex: 1;
`;

export const TeamName = styled.h3`
	margin: 0 0 0.25rem 0;
	color: ${({ theme }) => theme.colors.primary};
	font-size: 1.1rem;
	cursor: pointer;

	&:hover {
		text-decoration: underline;
	}
`;

export const TeamDetails = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
	color: ${({ theme }) => theme.colors.text};
	font-size: 0.9rem;
`;

export const DivisionName = styled.span`
	font-weight: 500;
`;

export const TeamCode = styled.span`
	color: #666;
`;

export const NextMatchInfo = styled.div`
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 0.25rem;
	font-size: 0.9rem;
`;

export const MatchTime = styled.span`
	font-weight: 500;
	color: ${({ theme }) => theme.colors.primary};
`;

export const CourtName = styled.span`
	color: #666;
`;

export const StandingInfo = styled.div`
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 0.25rem;
	font-size: 0.9rem;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	color: ${({ theme }) => theme.colors.text};
`;

export const NoTeamsMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	color: #666;
	font-style: italic;
`;
