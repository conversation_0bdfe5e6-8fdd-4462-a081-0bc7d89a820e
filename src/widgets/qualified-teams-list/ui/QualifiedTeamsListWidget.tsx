import { FC } from 'react';

import { useQualifiedTeams, useQualifiedTeamsSearchFilter } from '../model';
import {
	LoadingWrapper,
	NoTeamsMessage,
	QualifiedTeamsTable,
	QualifiedTeamsWrapper,
} from './styled';

interface QualifiedTeamsWidgetProps {
	eswId: string;
	search?: string | null;
}

export const QualifiedTeamsListWidget: FC<QualifiedTeamsWidgetProps> = ({ eswId, search }) => {
	const { qualifiedTeams, loading } = useQualifiedTeams(eswId);
	const filteredTeams = useQualifiedTeamsSearchFilter(qualifiedTeams, search);

	if (loading) {
		return (
			<LoadingWrapper>
				<div>Loading...</div>
			</LoadingWrapper>
		);
	}

	if (!filteredTeams || filteredTeams.length === 0) {
		return <NoTeamsMessage>No qualified teams found</NoTeamsMessage>;
	}

	return (
		<QualifiedTeamsWrapper>
			<QualifiedTeamsTable>
				<thead>
					<tr>
						<td>Division</td>
						<td>Team Name</td>
						<td>Earned Bid</td>
						<td>Earned At</td>
					</tr>
				</thead>
				<tbody>
					{filteredTeams.map((team) => (
						<tr key={team.team_id}>
							<td>{team.division.name}</td>
							<td>{team.team_name}</td>
							<td>{team.bid_earned}</td>
							<td>{team.extra?.earned_at || ''}</td>
						</tr>
					))}
				</tbody>
			</QualifiedTeamsTable>
		</QualifiedTeamsWrapper>
	);
};
