import numpy as np
import numpy as np
import soundfile as sf
from scipy.signal import butter, sosfilt
import matplotlib.pyplot as plt

# Load IQ
data, fs = sf.read("./signals/vtx_12_20_center-80dbm_2025-05-27_10.53.42.482199_1360.0_MHz.wav", always_2d=True)
iq = data[:,0] + 1j*data[:,1]

N = 2**18  # or as much as fits in RAM
spectrum = np.fft.fftshift(np.fft.fft(iq[:N]))
freqs = np.fft.fftshift(np.fft.fftfreq(N, 1/fs))

plt.figure(figsize=(12,5))
plt.plot(freqs/1e6, 20*np.log10(np.abs(spectrum)+1e-12))
plt.title("Raw IQ Spectrum")
plt.xlabel("Frequency (MHz)")
plt.ylabel("Magnitude (dB)")
plt.grid(True)
plt.show()

from matplotlib.colors import Normalize

nfft = 4096
overlap = 0.75
step = int(nfft * (1 - overlap))
n_windows = (len(iq) - nfft) // step
waterfall = []
for i in range(n_windows):
    start = i * step
    segment = iq[start:start + nfft]
    spectrum = np.fft.fftshift(np.fft.fft(segment))
    power = 20 * np.log10(np.abs(spectrum) + 1e-12)
    waterfall.append(power)
waterfall = np.array(waterfall)
freqs = np.fft.fftshift(np.fft.fftfreq(nfft, 1/fs)) / 1e6
times = np.arange(waterfall.shape[0]) * step / fs

plt.figure(figsize=(12,6))
plt.imshow(
    waterfall.T,
    aspect='auto',
    extent=[times[0], times[-1], freqs[0], freqs[-1]],
    origin='lower',
    cmap='viridis',
    norm=Normalize(vmin=np.percentile(waterfall, 5), vmax=np.percentile(waterfall, 99))
)
plt.colorbar(label='Power (dB)')
plt.xlabel('Time (s)')
plt.ylabel('Frequency (MHz)')
plt.title('Waterfall')
plt.tight_layout()
plt.show()