#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FM-modulated (PAL / NTSC) video inspector & demodulator
======================================================

What it does
------------
1.  Load 32-bit stereo WAV containing complex IQ samples.
2.  Show waterfall + short time trace of the raw stream.
3.  (Optional) swap I↔Q, apply a manual frequency offset.
4.  Bring a 6.5 MHz-wide FM channel to DC and low-pass it.
5.  Show spectrum / waterfall / time trace of that channel.
6.  FM-demodulate → composite-video baseband.
7.  Offer two ways to fix “weird / mirrored / distorted” video:
      • *flip_iq*   – conjugates the spectrum (I/Q swap);
      • *flip_fm*   – inverts the discriminator slope (± sign).
8.  Plot the demodulated video and optionally write it to WAV.

Author : ChatLLM Teams assistant (July 2025)
---------------------------------------------------------------------------
"""
from pathlib import Path
import numpy as np
import soundfile as sf
import matplotlib.pyplot as plt
from scipy.signal import butter, sosfiltfilt

# ------------------------------------------------------------------------- #
#  USER-TWEAKABLE SETTINGS
# ------------------------------------------------------------------------- #
# wav_path         = Path("./signals/PAL Video 6.5MHz.wav")   # << change here
# wav_path         = Path("./signals/PAL Video 6.5MHz.wav")   # << change here
wav_path =  Path("./signals/vtx_12_20_center-80dbm_2025-05-27_10.53.42.482199_1360.0_MHz.wav")
flip_iq          = False     # True  → swap I & Q  (complex-conjugate)
flip_fm          = False     # True  → multiply discriminator by –1
manual_shift_MHz = 0.0       # fine centre-frequency tweak  (+ or –)
channel_bw_MHz   = 6.5       # FM video IF bandwidth
save_wav         = False     # set True to dump composite video to disk
# ------------------------------------------------------------------------- #

# ----------------------------- plotting utils ----------------------------- #
def waterfall(iq, fs, nfft=4096, overlap=0.75, max_rows=800, title="waterfall"):
    hop  = int(nfft * (1 - overlap))
    nwin = max(0, (len(iq) - nfft) // hop)
    stride = max(1, nwin // max_rows) if nwin > max_rows else 1
    spec_rows = []
    for i in range(0, nwin, stride):
        seg = iq[i * hop : i * hop + nfft]
        if len(seg) < nfft:
            break
        pwr = 20*np.log10(np.abs(np.fft.fftshift(np.fft.fft(seg))) + 1e-12)
        spec_rows.append(pwr)
    if not spec_rows:
        print("[waterfall] recording too short, skipped")
        return
    spec = np.asarray(spec_rows)
    f = np.fft.fftshift(np.fft.fftfreq(nfft, 1/fs))/1e6  # in MHz
    t = np.arange(spec.shape[0])*hop/fs
    plt.figure(figsize=(12,5))
    plt.imshow(spec.T, extent=[t[0], t[-1], f[0], f[-1]],
               aspect='auto', origin='lower', cmap='viridis')
    plt.colorbar(label="dB")
    plt.xlabel("time (s)");  plt.ylabel("freq (MHz)");  plt.title(title)
    plt.tight_layout();  plt.show()

def spectrum(sig, fs, title, Nfft=1<<19):
    N = min(Nfft, len(sig))
    f = np.fft.fftshift(np.fft.fftfreq(N, 1/fs))/1e6
    P = 20*np.log10(np.abs(np.fft.fftshift(np.fft.fft(sig[:N]))) + 1e-12)
    plt.figure(figsize=(10,4))
    plt.plot(f, P);  plt.title(title)
    plt.xlabel("freq (MHz)");  plt.ylabel("dB");  plt.grid(True)
    plt.tight_layout();  plt.show()

def timeplot(sig, fs, title, N=40_000):
    N = min(N, len(sig))
    t = np.arange(N)/fs*1e3   # ms
    plt.figure(figsize=(10,3))
    plt.plot(t, sig[:N]);  plt.title(title)
    plt.xlabel("time (ms)");  plt.grid(True);  plt.tight_layout();  plt.show()

# ------------------------------- DSP blocks ------------------------------- #
def freq_shift(iq, fs, shift_hz):
    if abs(shift_hz) < 1:            # ~0 → skip
        return iq
    t = np.arange(len(iq)) / fs
    return iq * np.exp(2j*np.pi*shift_hz*t)

def lowpass(iq, fs, bw_MHz, order=8):
    nyq = fs/2
    wn  = (bw_MHz*1e6/2)/nyq
    if wn >= 1:                      # guard for “critical freq ≥ 1”
        print(f"[lowpass] cutoff ≥ Nyquist → skipping filter")
        return iq
    sos = butter(order, wn, btype='low', output='sos')
    return sosfiltfilt(sos, iq)

def fm_discriminator(iq):
    return np.angle(iq[1:] * np.conj(iq[:-1]))

# --------------------------------- main ---------------------------------- #
def main():
    if not wav_path.exists():
        raise FileNotFoundError(wav_path)

    raw, fs = sf.read(wav_path, always_2d=True)
    I, Q = raw[:,0], raw[:,1]
    if flip_iq:
        I, Q = Q, I
    iq = I.astype(np.float64) + 1j*Q.astype(np.float64)

    print(f"\nLoaded {len(iq):,} complex samples  @  {fs/1e6:.3f} MS/s"
          f"  ({len(iq)/fs:.2f} s)")

    # ---- RAW diagnostics -------------------------------------------------- #
    waterfall(iq, fs, title="raw I/Q waterfall")
    timeplot(np.real(iq), fs, "raw I – short trace")

    # ---- Baseband shift & LP filter --------------------------------------- #
    shift_Hz = (fs/2 + manual_shift_MHz*1e6)   # assumed carrier ≈ centre
    iq_bb  = freq_shift(iq, fs, -shift_Hz)
    iq_flt = lowpass(iq_bb, fs, channel_bw_MHz)

    waterfall(iq_flt, fs, title="channel (~6.5 MHz) waterfall")
    spectrum(iq_flt, fs, "channel spectrum")
    timeplot(np.real(iq_flt), fs, "channel – time trace")

    # ---- FM demod --------------------------------------------------------- #
    video = fm_discriminator(iq_flt)
    if flip_fm:
        video = -video
    video -= np.mean(video)

    spectrum(video, fs, "FM-demodulated (composite video) spectrum")
    timeplot(video, fs, "FM-demodulated – time trace")

    if save_wav:
        out = wav_path.with_suffix(".demod.wav")
        sf.write(out, video.astype(np.float32), int(fs))
        print("composite video saved →", out)

# ------------------------------------------------------------------------- #
if __name__ == "__main__":
    main()