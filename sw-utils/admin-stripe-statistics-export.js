
global.sails = {
    config: {
        stripe_api: require('../config/stripe').stripe_api,
        redis_queue: require('../config/redis_queue').redis_queue
    }
}

global._ = require('lodash');
global.StripeService = require('../api/services/StripeService');
global.knex = require('knex')({client: 'pg'});

const StatisticsService = require('../api/services/admin-statistics/financial-report/__StatisticsDataService');
const FinancialReportService = require('../api/services/admin-statistics/__FinancialReportService');
const MinStripePercentService = require('../api/services/admin-statistics/financial-report/__MinStripePercentService');
const xlsx = require('xlsx');
const argv = require('optimist').argv;
const Logger = require('./log');
const log = new Logger();
const pg = require('pg');

const
    DB_CONNECTION_STR 	= argv.connection,
    OUTPUT_FILEPATH 	= argv.path,
    DATE_START  	    = argv.start,
    DATE_END 		    = argv.end,
    EVENT_ID            = argv.event,
    PAYMENT_FOR         = argv.payment_for.split(',');

run()
    .then(() => process.exit(0))
    .catch(err => {
        process.stderr.write(err.message);
        process.exit(1);
    })

async function run () {
    let query = StatisticsService.getStatsQuery(
        DATE_START, DATE_END, PAYMENT_FOR, EVENT_ID, StatisticsService.STATISTIC_MODE.EXPORT
    );

    let eventsList = await getData(query);

    if(!eventsList || !eventsList.length) {
        log.debug('Nothing to export.');
        throw new Error('Nothing to export.');
    }

    eventsList = eventsList.map(event => __calculateStripeDelta(event));

    log.debug('Got data from DB.', eventsList.length, 'rows');

    let sheet = {};

    let headerCells = Object.keys(eventsList[0]);

    headerCells.forEach(function writeHeader (title, index) {
        sheet[xlsx.utils.encode_cell({ c: index, r: 0 })] = { v: title, t: 's' };
    });

    eventsList.forEach(function (row, index) {
        let sheetLine = (index + 1);

        headerCells.forEach(function (columnName, columnIndex) {
            sheet[xlsx.utils.encode_cell({ c: columnIndex, r: sheetLine })] = { v: String(row[columnName]) || '', t: 's' };
        });
    });

    sheet['!ref'] = xlsx.utils.encode_range({
        s: { c: 0, r: 0 },
        e: { c: headerCells.length, r: (eventsList.length + 1) }
    });

    let sheetName = 'Stripe Data';

    let workbook = {
        SheetNames 	: [sheetName],
        Sheets 		: {}
    };

    workbook.Sheets[sheetName] = sheet;
    // writing option used in xlsx is 'w'
    // 			 -  Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
    xlsx.writeFile(workbook, OUTPUT_FILEPATH,  { font: { name: 'Verdana' }, bookSST: true });
    log.debug('WorkBook written');
}

async function getData (query) {
    let connectionParams = JSON.parse(Buffer.from(DB_CONNECTION_STR, 'base64').toString('utf-8'));

    log.debug('Connecting to the DB');
    let dbClient = new pg.Client(connectionParams);
    dbClient.connect();

    let data = await dbClient.query(query.toString()).then(result => result.rows);

    log.debug('Closing db connection.');
    // no need to hold the connection any more
    await dbClient.end();

    return data;
}

function __calculateStripeDelta (data) {
    let LABELS = StatisticsService.EXPORT_FIELDS_LABELS;

    let amount          = Number(data[LABELS.CHARGE_AMOUNT]);
    let appFee          = Number(data[LABELS.APPLICATION_FEE]);
    let stripePercent   = data[LABELS.STRIPE_PERCENT];
    let feePayer        = data.stripe_tickets_fee_payer;
    let initAmount      = Number(data.initial_charge_amount);
    let stripeDelta     = 0;
    let paymentFor      = data[LABELS.PAYMENT_FOR];

    if(['tickets', 'teams', 'booths'].includes(paymentFor)) {
        let typesServiceAlias = __getTypesServiceAlias(paymentFor);
        let typeService = FinancialReportService.TYPES_SERVICE[typesServiceAlias];

        typeService.STRIPE_PERCENT_MIN = MinStripePercentService.getMinStripePercent({
            created: data.created, stripe_charge_id: data[LABELS.CHARGE_ID]
        });

        let getDStripeFeeDelta = typeService.__getStripeFeeDelta.bind(typeService, amount);

        ({ stripeDelta } = paymentFor === 'tickets'
            ? getDStripeFeeDelta(appFee, stripePercent, feePayer, initAmount)
            : getDStripeFeeDelta(stripePercent, initAmount));
    }

    data = __removeOnlyForCalculationFields(data);

    data['Stripe Delta'] = String(stripeDelta);

    return data;
}

function __getTypesServiceAlias (paymentFor) {
    return paymentFor === 'tickets' ? 'tickets' : 'teams';
}

function __removeOnlyForCalculationFields (data) {
    return _.omit(data, ['initial_charge_amount', 'stripe_tickets_fee_payer', 'created']);
}
