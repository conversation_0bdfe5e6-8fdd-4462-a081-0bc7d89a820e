
'use strict';

const 
	argv 	= require('optimist').argv,
	xlsx 	= require('xlsx'),
	pg      = require('pg'),
	co 		= require('co'),
	Logger 	= require('./log'),
	log 	= new Logger();

const
	DB_CONNECTION_STR 	= argv.connection,
	OUTPUT_FILEPATH 	= argv.path,
	ROSTER_TEAMS_SQL  	= argv.teams_query,
	MEMBER_TYPE 		= argv.type; 

co(function* () {
	let connectionParams = JSON.parse(Buffer.from(DB_CONNECTION_STR, 'base64').toString('utf-8'));

	let teamsSQL = Buffer.from(ROSTER_TEAMS_SQL, 'base64').toString('utf-8');

	let membersSQL = getMembersSQL(MEMBER_TYPE, teamsSQL);

	log.debug(membersSQL);

	log.debug('Connecting to the DB');
	let dbClient = new pg.Client(connectionParams);
	dbClient.connect();

	let memberRows = yield (new Promise((resolve, reject) => {
		dbClient.query(membersSQL, (err, result) => {
			if(err) {
				reject(err);
			} else {
				resolve(result.rows);
			}
		})
	}));

	log.debug('Closing db connection.');
	// no need to hold the connection any more
	dbClient.end();

	log.debug('Got data from DB.', memberRows.length, 'rows');

	if(!memberRows.length) {
		throw new Error('Nothing to export.');
	}

	let headerCells = Object.keys(memberRows[0]),
		sheet 		= {};

	headerCells.forEach(function writeHeader (title, index) {
		sheet[xlsx.utils.encode_cell({ c: index, r: 0 })] = { v: title, t: 's' };
	});

	memberRows.forEach(function (row, index) {
		let sheetLine = (index + 1);

		headerCells.forEach(function (columnName, columnIndex) {
			sheet[xlsx.utils.encode_cell({ c: columnIndex, r: sheetLine })] = { v: row[columnName] || '', t: 's' };
		});
	});

	sheet['!ref'] = xlsx.utils.encode_range({
		s: { c: 0, r: 0 }, 
		e: { c: headerCells.length, r: (memberRows.length + 1) }
	});

	let sheetName = (MEMBER_TYPE === 'staff')?'Staff':'Athletes';

	let workbook = {
		SheetNames 	: [sheetName],
		Sheets 		: {}
	};

	log.debug(JSON.stringify(sheet, null, ' '));

	workbook.Sheets[sheetName] = sheet;
	// writing option used in xlsx is 'w' 
	// 			 -  Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
	xlsx.writeFile(workbook, OUTPUT_FILEPATH,  { font: { name: 'Verdana' }, bookSST: true });
	log.debug('WorkBook written')
}).catch(err => {
	console.error(err);
	process.exit(1)
});

function getMembersSQL (type, teamsSQL) {
	if(type === 'athlete') {
		return `SELECT DISTINCT ON (ma.usav_number, ma.aau_membership_id)
				     INITCAP(ma.first) "First Name", INITCAP(ma.last) "Last Name", ma.email "Email",
                     ma.usav_number "USAV Code", ma.aau_membership_id "AAU Code",
                     rt.organization_code "Organization Code"
				 FROM "roster_athlete" ra
				 INNER JOIN "master_athlete" ma 
				     ON ma.master_athlete_id = ra.master_athlete_id
                 INNER JOIN "roster_team" rt 
				     ON ra.roster_team_id = rt.roster_team_id
				 WHERE ra.deleted is null 
				     AND ra.deleted_by_user IS NULL 
				     AND ra.roster_team_id IN (
				         ${teamsSQL}
				     )`;
	} else {
		return `SELECT DISTINCT ON (ms.usav_number, ms.aau_membership_id)
				     INITCAP(ms.first) "First", INITCAP(ms.last) "Last", ms.email "Email"
				 FROM "roster_staff_role" rsr 
				 INNER JOIN "master_staff" ms 
				     ON ms.master_staff_id = rsr.master_staff_id
				 WHERE rsr.deleted is null 
				     AND rsr.deleted_by_user IS NULL 
				     AND rsr.roster_team_id IN (
				         ${teamsSQL}
				     )`;
	}
}


