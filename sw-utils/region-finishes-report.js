'use strict';

const argv = require('optimist').argv,
    xlsx = require('xlsx'),
    pg = require('pg'),
    Logger = require('./log'),
    _ = require('lodash'),
    log = new Logger();
const { formatSheetName, getColumnWidths } = require('./export-helper');

const EVENT_ID = Number(argv.event),
    REGION = argv.region || void 0,
    DB_CONNECTION_STR = argv.connection,
    OUTPUT_FILEPATH = argv.path,
    REGION_CODES = {
        GE: 'GEVA',
        SC: 'SCVA',
    },
    HEADERS_BY_REGON = {
        [REGION_CODES.GE]: [
            'Round',
            'Match',
            'T1Finish',
            'T2Finish',
            'Team1Name',
            'Team1Code',
            'Team2Name',
            'Team2Code',
            'Result',
            'Score',
            'Event',
            'Venue',
            'Date',
            'Div',
        ],
        [REGION_CODES.SC]: [
            'Event Name',
            'Team Name',
            'Team Code',
            'Opposing Team Name',
            'Opposing Team Code',
            'Match Date',
            'Match Time',
            'Outcome',
            'Score 1',
            'Score 2',
            'Score 3',
        ]
    },
    ROW_HANDLERS_BY_REGION = {
        [REGION_CODES.GE]: function (sheet, sheetLine, row) {
            sheet[xlsx.utils.encode_cell({ c: 0, r: sheetLine })] = {
                v: row.round || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 1, r: sheetLine })] = {
                v: row.match || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 2, r: sheetLine })] = {
                v: row.team_1_finish || 0,
                t: 'n',
            };
            sheet[xlsx.utils.encode_cell({ c: 3, r: sheetLine })] = {
                v: row.team_2_finish || 0,
                t: 'n',
            };
            sheet[xlsx.utils.encode_cell({ c: 4, r: sheetLine })] = {
                v: row.team_1_name || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 5, r: sheetLine })] = {
                v: row.team_1_code || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 6, r: sheetLine })] = {
                v: row.team_2_name || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 7, r: sheetLine })] = {
                v: row.team_2_code || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 8, r: sheetLine })] = {
                v: row.result || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 9, r: sheetLine })] = {
                v: row.scores || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 10, r: sheetLine })] = {
                v: row.event_name || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 11, r: sheetLine })] = {
                v: row.venue || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 12, r: sheetLine })] = {
                v: `${row.date} at ${row.time}` || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 13, r: sheetLine })] = {
                v: row.div || '',
                t: 's',
            };
        },
        [REGION_CODES.SC]: function (sheet, sheetLine, row) {
            const scores = (row.scores || '').split(',');

            sheet[xlsx.utils.encode_cell({ c: 0, r: sheetLine })] = {
                v: row.event_name || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 1, r: sheetLine })] = {
                v: row.team_1_name || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 2, r: sheetLine })] = {
                v: row.team_1_code || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 3, r: sheetLine })] = {
                v: row.team_2_name || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 4, r: sheetLine })] = {
                v: row.team_2_code || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 5, r: sheetLine })] = {
                v: row.date || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 6, r: sheetLine })] = {
                v: row.time || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 7, r: sheetLine })] = {
                v: row.result || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 8, r: sheetLine })] = {
                v: scores[0] || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 9, r: sheetLine })] = {
                v: scores[1] || '',
                t: 's',
            };
            sheet[xlsx.utils.encode_cell({ c: 10, r: sheetLine })] = {
                v: scores[2] || '',
                t: 's',
            };
        },
    };

let connectionParams;

try {
    connectionParams = JSON.parse(Buffer.from(DB_CONNECTION_STR, 'base64').toString('utf-8'));
} catch (e) {
    console.error(e);
    process.exit(1);
}

log.debug('Connecting to the DB');
let dbClient = new pg.Client(connectionParams);
dbClient.connect();

new Promise((resolve, reject) => {
    let sqlParams = [EVENT_ID];

    dbClient.query(
        `SELECT e.name "event_name",
                substr(m.display_name, 0, position('M' IN m.display_name)) "round",
                CONCAT('Match ', m.match_number) "match",
                ds1.rank "team_1_finish",
                ds2.rank "team_2_finish",
                rt1.team_name "team_1_name",
                rt1.organization_code "team_1_code",
                rt2.team_name "team_2_name",
                rt2.organization_code "team_2_code",
                CASE WHEN (m.results::JSON->>'winner')::INT = 1 THEN 'Won'
        WHEN (m.results::JSON->>'winner')::INT = 2 THEN 'Lost'
        ELSE 'Unknown result' END "result",
        substr(m.results::JSON->'team1'->>'scores', 7, length(m.results::JSON->'team1'->>'scores')-7) "scores",
        c.short_name "venue",
        to_char(m.secs_start, 'MM/DD/YYYY') "date",
        to_char(m.secs_start, 'HH12:MI AM') "time",
        d.name "div"
        FROM event e
        INNER JOIN matches m ON e.event_id = m.event_id
        INNER JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id AND rt1.deleted IS NULL
        INNER JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id AND rt2.deleted IS NULL
        INNER JOIN division d ON d.division_id = m.division_id
        INNER JOIN division_standing ds1 ON ds1.team_id = rt1.roster_team_id AND ds1.event_id = e.event_id
        INNER JOIN division_standing ds2 ON ds2.team_id = rt2.roster_team_id AND ds2.event_id = e.event_id
        INNER JOIN courts c ON m.court_id = c.uuid
        WHERE e.event_id = $1
        ORDER BY DATE(m.secs_start), d.short_name, m.secs_start`,
        sqlParams,
        (err, result) => {
            if (err) {
                reject(err);
            } else {
                resolve(result.rows || []);
            }
        }
    );
}).then((resultRows) => {
    log.debug('Got', resultRows.length, 'result rows from DB.');
    const regionCode = REGION_CODES[REGION];
    const sheet = {};
    const sheetTitles = HEADERS_BY_REGON[regionCode] || [];
    const rowHandler = ROW_HANDLERS_BY_REGION[regionCode];

    sheetTitles.forEach(function (title, index) {
        sheet[xlsx.utils.encode_cell({ c: index, r: 0 })] = {
            v: title,
            t: 's',
        };
    });

    resultRows.forEach(function (row, index) {
        let sheetLine = index + 1;

        rowHandler(sheet, sheetLine, row);
    });

    sheet['!ref'] = xlsx.utils.encode_range({
        s: { c: 0, r: 0 },
        e: { c: sheetTitles.length, r: resultRows.length + 1 },
    });

    sheet['!cols'] = getColumnWidths(sheet, resultRows.length + 1, sheetTitles.length);

    let event_name = (resultRows[0] && resultRows[0].event_name) || '';

    return {
        name: formatSheetName(`${event_name} $1`, [`results for ${regionCode}`]),
        content: sheet,
    };
}).then((sheetData) => {
    log.debug('Sheet Generated');
    let workbook = {
        SheetNames: [sheetData.name],
        Sheets: {},
    };

    workbook.Sheets[sheetData.name] = sheetData.content;
    // writing option used in xlsx is 'w'
    // 			 -  Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
    xlsx.writeFile(workbook, OUTPUT_FILEPATH, {
        font: { name: 'Verdana' },
        bookSST: true,
    });
    log.debug('WorkBook written');
}).then(() => {
    process.exit(0);
})
.catch((err) => {
    log.error(err);
    process.exit(1);
});
