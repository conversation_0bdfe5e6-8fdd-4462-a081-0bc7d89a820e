'use strict';
const {argv} = require('optimist');
const xlsx = require('xlsx');
const {Client} = require('pg');
const path = require('path');
const {
    DEFAULT_WRITE_OPTIONS,
    parseParameterObject,
    showError,
    fieldMaps,
    formatTable,
} = require('./export-helper');


const $EVENT_ID = parseInt(argv.event, 10);
const $ROLE = argv.role;
const $CONN_STR = argv.connection;
const $TRAVEL_LIST_QUERY = argv.travelListQuery;

$EVENT_ID || showError('Invalid Event Identifier');
$ROLE || showError('Invalid role');
$CONN_STR || showError('Invalid connection string');
$TRAVEL_LIST_QUERY || showError('Invalid travel list query');

const fileID = Date.now();

(async () => {
    const fieldsMap = (()=>{
        switch ($ROLE) {
            case 'official':
                return fieldMaps.officialTravelInfo;
            case 'staff':
                return fieldMaps.staffTravelInfo;
            default:
                showError('Invalid role');
        }
    })();
    const connectionString = await parseParameterObject($CONN_STR);
    const travelListQuery = await parseParameterObject($TRAVEL_LIST_QUERY);

    const client = new Client(connectionString);
    await client.connect();
    const result = await client.query(travelListQuery);
    if (result.rowCount === 0) {
        showError('Event has no entries');
    }
    const exportDirectoryPath = path.resolve(__dirname, '..', '..', 'export');
    const filePath = path.resolve(exportDirectoryPath, `${$EVENT_ID}_${$ROLE}_travel_${fileID}.xlsx`);
    const sheetName = 'Travel info';

    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(formatTable(result.rows, fieldsMap));
    xlsx.utils.book_append_sheet(workbook, sheet, sheetName);
    xlsx.writeFile(workbook, filePath, DEFAULT_WRITE_OPTIONS);
    process.stdout.write(fileID.toString());
    await client.end();
})().catch(err => {
    showError(err.message || err);
});

