/**
 * Run predefined tasks whenever watched file patterns are added, changed or deleted.
 *
 * ---------------------------------------------------------------
 *
 * Watch for changes on
 * - files in the `assets` folder
 * - the `tasks/pipeline.js` file
 * and re-run the appropriate tasks.
 *
 * For usage docs see:
 * 		https://github.com/gruntjs/grunt-contrib-watch
 *
 */
module.exports = function(grunt) {

	grunt.config.set('watch', {
		api: {

			// API files to watch:
			files: ['api/**/*']
		},
		frontend: {

			// Assets to watch:
			files: ['frontend/**/*', 'assets/index.html', 'assets/styles/**/*'],  // ['assets/**/*', 'frontend/**/*', 'tasks/pipeline.js']

			// When assets are changed:
			tasks: ['syncAssets']
		},
		frontend_test_layout: {
			files: ['frontend/**/*', 'assets/index.html', 'assets/styles/**/*'],
			tasks: ['syncAssets']
		},
		frontend_event: {

			// Assets to watch:
			files: ['frontend_event/**/*', 'assets/stylesEsw/**/*'],

			// When assets are changed:
			tasks: ['syncAssetsESW']
		},
		frontend_admin: {

			// Assets to watch:
			files: ['frontend_admin/**/*', 'assets/styles/**/*'],

			// When assets are changed:
			tasks: ['syncAssetsASW']
		},
        styles: {

            // watch styles:
            files: ['assets/styles/**/*'],

            // styles are changed:
            tasks: ['sass:server', 'sync:dev']
        },
		admin: {

			// watch styles:
			files: ['assets/admin/**/*'],

			// styles are changed:
			tasks: ['sync:dev']
		},
        tickets: {
            files: ['frontend_tickets/**/*'],
            tasks: ['buildAssetsTickets']
        }
	});

	grunt.loadNpmTasks('grunt-contrib-watch');
};
