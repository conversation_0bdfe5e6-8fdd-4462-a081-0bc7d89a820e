'use strict';

const argv = require('optimist').argv;

const DB_CONNECTION = require(`../../config/env/${ argv.prod ? 'production' : 'development' }`).connections.postgres;

const CWD = '.tmp/public';

module.exports = function (grunt) {
    grunt.config.set('hash_generator', {
        prod_esw: {
            files       : [{
                cwd: CWD,
                src: ['scripts/{vendor_base,main}.*.js', 'stylesEsw/main.css', 'stylesEsw/vendor.css']
            }],
           hash_dest    : '.tmp/public/hashes/static.hash',
           key          : 'esw',
           connection   : DB_CONNECTION
        },
        prod_asw: {
            files       : [{
                cwd: CWD,
                src: ['scripts/{vendor_base,main}.*.js', 'styles/admin.css', 'styles/vendor.css']
            }],
           key          : 'asw',
           connection   : DB_CONNECTION
        },
        prod_sw: {
            files       : [{
                cwd: CWD,
                src: ['scripts/**/*.js', 'styles/main.css', 'styles/vendor.css']
            }],
           key          : 'sw',
           connection   : DB_CONNECTION
        },
        dev_esw: {
            files       : [{
                cwd: CWD,
                src: ['scripts/{vendor_base,main}.*.js', 'stylesEsw/main.css', 'stylesEsw/vendor.css']
            }],
           key          : 'esw',
           connection   : DB_CONNECTION
        },
        dev_asw: {
            files       : [{
                cwd: CWD,
                src: ['scripts/{vendor_base,main}.*.js', 'styles/admin.css', 'styles/vendor.css']
            }],
           key          : 'asw',
           connection   : DB_CONNECTION
        },
        dev_sw: {
            files       : [{
                cwd: CWD,
                src: ['scripts/**/*.js', 'styles/main.css', 'styles/vendor.css']
            }],
            key         : 'sw',
            connection  : DB_CONNECTION  
        }       
    });
};
