'use strict';

const 
    pgClient    = require('pg').Client,
    co          = require('co'),
    crypto      = require('crypto'),
    path        = require('path');


const DB_REG_EXP = /^postgres:\/\/.+:.+@.+:\d+\/(.+)$/i;

function getDBName (connection) {
    if (toString.call(connection) === '[object Object]') {
        return connection.database;
    } else if (DB_REG_EXP.test(connection)) {

        let res = connection.match(DB_REG_EXP);

        return res[1];
    } else {
        throw new Error('Invalid connection string!');
    }
}

module.exports = function (grunt) {

    grunt.registerMultiTask('hash_generator', 'test descr', function () {
        let done = this.async();

        co(function* () {

            if (!this.data.connection) {
                throw new Error('Connection required');
            }

            if (!this.data.key) {
                throw new Error('Storage Key not specified');
            }

            let startTime   = new Date(),  
                cwd         = this.files[0].cwd,
                hash        = crypto.createHash('md5'),
                filesList   = this.filesSrc,
                result_hash = ''; 

            for (let fileName of filesList) {
                let filePath = path.resolve(cwd, fileName); 

                grunt.log.writeln(filePath);    

                if (!grunt.file.isDir(filePath)) {
                    hash.update(
                        grunt.file.read(filePath, { encoding: 'utf-8' })
                    );
                }
            }

            result_hash = hash.digest('hex') + '-' + new Date().getTime();

            grunt.log.writeln('Connecting to', `"${getDBName(this.data.connection)}"`, 'db');

            let client = new pgClient(this.data.connection);

            client.connect();

            let result = yield (saveHash(client, result_hash, this.data.key));

            client.end();

            if (result.rowCount === 0) {
                throw new Error('"static_hash" row not found!');
            }

            grunt.log.writeln('created hash', this.data.key, result_hash);
            grunt.log.writeln('exec time: ', new Date() - startTime, 'ms.'); 

        }.bind(this)).then(() => done())
        .catch(done);

    });

};

// TO Support old pg
function saveHash (client, hash, storageKey) {
    return new Promise((resolve, reject) => {
        client.query(
            `UPDATE "settings" s
             SET "value" = d."value"
             FROM (
                 SELECT JSONB_SET(s."value", ARRAY[$2::TEXT], TO_JSON($1::TEXT)::JSONB) "value", s."key"
                 FROM "settings" s
                 WHERE s."key" = $3
             ) "d"
             WHERE s."key" = d."key"`,
            [hash, storageKey, 'static_hash'],
            (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result);
                }
            }
        );
    });
}