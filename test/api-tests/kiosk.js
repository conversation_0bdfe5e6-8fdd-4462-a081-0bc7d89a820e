'use strict';

const co        = require('co');
const request   = require('request-promise');
const moment = require('moment-timezone');

const eventData         = require('./fixture/kiosk/event');
const eventTicketData   = require('./fixture/kiosk/event_ticket').rows;

const CHANGED_FIRST = 'Changed First';
const CHANGED_LAST  = 'Changed LAST';
const CHANGED_EMAIL = '<EMAIL>';
const CHANGED_PHONE = '10000000000';

const EXPECTED_APP_FEE = eventTicketData.reduce((sum, t) =>
                                        sum + (t.application_fee || eventData.tickets_sw_fee), 0);
const INITIAL_BALANCE = eventData.tickets_sw_balance;

function addEventRow () {
    const data = {
        ...eventData,
        date_start: moment().tz(eventData.timezone).add(1, 'minutes').format(),
        date_end: moment().tz(eventData.timezone).add(2, 'hours').format(),
        tickets_purchase_date_start: moment().tz(eventData.timezone).add(-1, 'minutes').format(),
        tickets_purchase_date_end: moment().tz(eventData.timezone).add(30, 'minutes').format(),
    };
    let query = squel.insert().into('event').setFields(data).returning('event_id');

    return Db.query(query).then(result => result.rows[0] && result.rows[0].event_id);
}

function addEventTicketRow (eventID) {
    return Promise.all(
        eventTicketData.map(ticket => {
            ticket.event_id = eventID;

            let query = squel.insert().into('event_ticket').setFields(ticket).returning('*');

            return Db.query(query).then(result => result.rows[0]);
        })
    )
}

function createPendingPayment (eventTickets, eventID, params) {
    let sum = 0;

    let receipts = eventTickets.map(ticket => {
        sum += Number(ticket.current_price);

        return {
            "id"        : ticket.event_ticket_id,
            "price"     : ticket.current_price,
            "quantity"  : 1,
            "first"     : "John",
            "last"      : "Smith"
        }
    });

    return request({
        headers: {
            'cache-control' : 'no-cache',
            'content-type'  : 'application/json'
        },
        method 	: 'POST',
        uri 	: `http://${HOST}/api/kiosk/buy`,
        body 	: {
            "receipt"  : receipts,
            "user_data":{
                "email"     :"<EMAIL>",
                "phone"     :null,
                "zip"       :"32114",
                "country"   :"United States",
                "first"     :"F",
                "last"      :"L",
                "password"  : "123123"
            },
            "event" : eventID,
            "total" : sum,
            "method":"pending-payment",
            "payment_discount":0,
            "kiosk": {
                "name"    : "test",
                "location": "test"
            }
        },
        resolveWithFullResponse: true,
        json: true
    }).then(response => {
        let data = response.body;

        if(!data || !data.payments) {
            return null;
        }

        let payment = null;
        let tickets = [];

        data.payments.forEach(p => {
            if(p.is_payment) {
                payment = p;
            }

            if(p.is_ticket) {
                tickets.push(p);
            }
        });

        return generatePaymentBody({payment, tickets}, params);
    })
}

function generatePaymentBody (data, params) {
    let { amount, zip, kiosk, event_id, first: user_last, last: user_first, email, purchase_id, phone } = data.payment;
    let {
        isCash, scanOnlyFirstTicket, changeTicketHolderName, skipDuplicateCardPaymentCheck,
        payOnlyForFirstTicket, changePayerData, addOneTicketToPayment
    } = params;

    let tickets = data.tickets.map(ticket => {
        return {
            price               : ticket.items[0].price,
            quantity            : 1,
            purchase_ticket_id  : ticket.items[0].purchase_ticket_id,
            mark_as_scanned     : !scanOnlyFirstTicket
        };
    });

    if(scanOnlyFirstTicket) {
        tickets[0].mark_as_scanned = true;
    }

    if(changeTicketHolderName) {
        tickets[0].first = CHANGED_FIRST;
        tickets[0].last  = CHANGED_LAST;
    }

    if(payOnlyForFirstTicket) {
        amount -= tickets[0].price;
        tickets = tickets.splice(1,1);
    }

    if(addOneTicketToPayment) {
        amount += data.tickets[0].items[0].price;
        tickets.push({
            "first"     : "Additional_First",
            "last"      : "Additional_Last",
            "quantity"  : 1,
            "price"     : data.tickets[0].items[0].price,
            "mark_as_scanned": true,
            "id"        : data.tickets[0].items[0].id
        });
    }

    let result = {
        "zip"                   : zip,
        "type"                  : isCash ? "cash" : "card",
        "amount"                : amount,
        "scanner"               : kiosk.location,
        "tickets"               : tickets,
        "event_id"              : event_id,
        "location"              : kiosk.location,
        "user_last"             : user_last,
        "user_first"            : user_first,
        "address_zip"           : zip,
        "email"                 : email,
        "phone"                 : phone,
        "payment_id"            : purchase_id,
        "skip_duplicate_check"  : false,
        "mark_as_scanned"       : false
    };

    if(changePayerData) {
        result["user_last"]  = CHANGED_LAST;
        result["user_first"] = CHANGED_FIRST;
        result["email"]      = CHANGED_EMAIL;
        result["phone"]      = CHANGED_PHONE;
    }

    if(skipDuplicateCardPaymentCheck) {
        result["skip_duplicate_check"] = true;
    }

    if(!isCash) {
        result["card_num"]      = '****************';
        result["card_month"]    = '12';
        result["card_year"]     = moment().add(1, 'year').format('YY');
        result["card_first"]    = 'John';
        result["card_last"]     = 'Smith';
    }

    return result;
}

function pay (body) {
    return request({
        headers: {
            'cache-control' : 'no-cache',
            'content-type'  : 'application/json'
        },
        method 	: 'POST',
        uri 	: `http://${HOST}/api/swt/pending/pay`,
        body 	: body,
        json    : true
    })
}

describe('Kiosk API tests', () => {
    let eventID, eventTickets;

    let checkPaymentParams = function (response) {
        response.should.have.property('success');
        response.should.have.property('id');
        response.should.have.property('message');
        response.should.have.property('tickets');
    };

    let getTicketData = function (purchaseID, isPaymentCheck) {
        let query = squel.select().from('purchase')
            .where('purchase_id = ?', purchaseID)
            .where('first = ?', CHANGED_FIRST)
            .where('last = ?', CHANGED_LAST);

        if(isPaymentCheck) {
            query.where('email = ?', CHANGED_EMAIL)
                .where('phone = ?', CHANGED_PHONE);
        }

        return Db.query(query).then(result => result.rows.length || null);
    };

    let getEventBalance = function (eventID) {
        let query = squel.select().from('event')
            .field('COALESCE("tickets_sw_balance", 0)', 'tickets_sw_balance')
            .where('event_id = ?', eventID);

        return Db.query(query).then(result => result.rows[0] && result.rows[0].tickets_sw_balance);
    };

    let restoreEventBalance = function (eventID) {
        return Db.query(
            `UPDATE "event"
             SET "tickets_sw_balance" = $2 
             WHERE "event_id" = $1`,
            [eventID, eventData.tickets_sw_balance]
        ).then(() => {})
    }

    let paymentBodyCheck = function (response) {
        checkPaymentParams(response);

        response.tickets.length.should.be.equal(2);
        response.canceled_tickets.length.should.be.equal(0);

        for(let ticket of response.tickets) {
            ticket.should.have.property('available');
            ticket.available.should.be.equal(0);

            ticket.should.have.property('is_scanned');
            ticket.is_scanned.should.be.equal(true);

            ticket.should.have.property('status');
            ticket.status.should.be.equal('paid');
        }
    };

    before(() => {
        return co(function* () {
            eventID         = yield addEventRow();
            eventTickets    = yield addEventTicketRow(eventID);
        });
    });

    after(async function removeTestDbRows () {
        await Promise.all([
            Db.query('DELETE FROM "event" WHERE "event_id" = $1', [eventID]),
            Db.query('DELETE FROM "event_ticket" WHERE "event_id" = $1', [eventID]),
            Db.query('DELETE FROM "purchase" WHERE "event_id" = $1', [eventID]),
            Db.query('DELETE FROM "user"'),
        ]);
        await Db.query(
            `DELETE FROM "purchase_ticket" pt WHERE NOT EXISTS (
                SELECT p.purchase_id FROM "purchase" p
                WHERE p.purchase_id = pt.purchase_id
            )`
        );
    });

    context('POST /api/swt/pending/pay', () => {

        let restoreBalance = null;

        before(() => {
            restoreBalance = restoreEventBalance.bind(null, eventID);
        })

        beforeEach(() => restoreBalance())

        it('Should make successful payment by card', () => {
            return createPendingPayment(eventTickets, eventID, {})
                .then(paymentBody => pay(paymentBody))
                .then(response => paymentBodyCheck(response))
        }).timeout(100 * 1000);

        it('Should return card duplicate error', () => {
            return createPendingPayment(eventTickets, eventID, {})
                .then(paymentBody => pay(paymentBody))
                .catch(err => {
                    let error = err.error;

                    error.should.have.property('success');
                    error.success.should.be.equal(false);
                })
        }).timeout(100 * 1000);

        it('Should make successful payment by same card', () => {
            let params = { skipDuplicateCardPaymentCheck: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(2);
                    response.canceled_tickets.length.should.be.equal(0);

                    for(let ticket of response.tickets) {
                        ticket.should.have.property('available');
                        ticket.available.should.be.equal(0);

                        ticket.should.have.property('is_scanned');
                        ticket.is_scanned.should.be.equal(true);

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }
                })
        }).timeout(100 * 1000);

        it('Should make successful payment by card and check event balance', () => {
            let params = { skipDuplicateCardPaymentCheck: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(2);
                    response.canceled_tickets.length.should.be.equal(0);

                    for(let ticket of response.tickets) {
                        ticket.should.have.property('available');
                        ticket.available.should.be.equal(0);

                        ticket.should.have.property('is_scanned');
                        ticket.is_scanned.should.be.equal(true);

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }

                    return getEventBalance(eventID).then(balance => {
                        /**
                         * The app takes an extra fee when paying by card. So the balance grows.
                         */
                        let expectedBalanceAdjustment   = EXPECTED_APP_FEE;
                        let expectedBalance             =
                                String(INITIAL_BALANCE + expectedBalanceAdjustment);

                        balance.should.be.equal(expectedBalance);
                    })
                })
        });


        it('Should make successful payment by cash and scan all tickets', () => {
            return createPendingPayment(eventTickets, eventID, { isCash: true })
                .then(paymentBody => pay(paymentBody))
                .then(response => paymentBodyCheck(response))
        }).timeout(100 * 1000);

        it('Should make successful payment by cash and scan only one ticket', () => {
            let params = { isCash: true, scanOnlyFirstTicket: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(2);
                    response.canceled_tickets.length.should.be.equal(0);

                    for(let i = 0; i < response.tickets.length; i++) {
                        let ticket = response.tickets[i];

                        ticket.should.have.property('available');
                        ticket.should.have.property('is_scanned');

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }
                });
        });


        it('Should make successful payment by cash and scan only one ticket', () => {
            let params = { isCash: true, scanOnlyFirstTicket: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(2);
                    response.canceled_tickets.length.should.be.equal(0);

                    let notScannedTickets = response.tickets.filter(t => t.available = 1 && !t.is_scanned);

                    notScannedTickets.length.should.be.equal(1);

                    for(let i = 0; i < response.tickets.length; i++) {
                        let ticket = response.tickets[i];

                        ticket.should.have.property('available');
                        ticket.should.have.property('is_scanned');

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }
                })
        });

        it('Should make successful payment by cash and change one ticket data', () => {
            let params = { isCash: true, changeTicketHolderName: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(2);
                    response.canceled_tickets.length.should.be.equal(0);

                    let changedNameTicket = response.tickets.filter(t => {
                        return t.first === CHANGED_FIRST && t.last === CHANGED_LAST
                    });

                    changedNameTicket.length.should.be.equal(1);

                    for(let ticket of response.tickets) {
                        ticket.should.have.property('available');
                        ticket.available.should.be.equal(0);

                        ticket.should.have.property('is_scanned');
                        ticket.is_scanned.should.be.equal(true);

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }

                    return getTicketData(changedNameTicket[0].purchase_id)
                        .then(dbRowExists => {
                            dbRowExists.should.be.equal(1);
                        })
                })
        });

        it('Should make successful payment by cash and change payer data', () => {
            let params = { isCash: true, changePayerData: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(2);
                    response.canceled_tickets.length.should.be.equal(0);

                    for(let ticket of response.tickets) {
                        ticket.should.have.property('available');
                        ticket.available.should.be.equal(0);

                        ticket.should.have.property('is_scanned');
                        ticket.is_scanned.should.be.equal(true);

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }

                    return getTicketData(response.id, true)
                        .then(dbRowExists => {
                            dbRowExists.should.be.equal(1);
                        })
                })
        });

        it('Should make successful payment by cash with one ticket cancellation', () => {
            let params = { isCash: true, payOnlyForFirstTicket: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(1);
                    response.canceled_tickets.length.should.be.equal(1);

                    for(let ticket of response.tickets) {
                        ticket.should.have.property('available');
                        ticket.available.should.be.equal(0);

                        ticket.should.have.property('is_scanned');
                        ticket.is_scanned.should.be.equal(true);

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }
                })
        });

        it('Should make successful payment by cash with one ticket addition', () => {
            let params = { isCash: true, addOneTicketToPayment: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(3);
                    response.canceled_tickets.length.should.be.equal(0);

                    for(let ticket of response.tickets) {
                        ticket.should.have.property('available');
                        ticket.available.should.be.equal(0);

                        ticket.should.have.property('is_scanned');
                        ticket.is_scanned.should.be.equal(true);

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }
                })
        });

        it('Should make successful payment by cash and check reserve balance', () => {
            let params = { isCash: true };

            return createPendingPayment(eventTickets, eventID, params)
                .then(paymentBody => pay(paymentBody))
                .then(response => {
                    checkPaymentParams(response);

                    response.tickets.length.should.be.equal(2);
                    response.canceled_tickets.length.should.be.equal(0);

                    for(let ticket of response.tickets) {
                        ticket.should.have.property('available');
                        ticket.available.should.be.equal(0);

                        ticket.should.have.property('is_scanned');
                        ticket.is_scanned.should.be.equal(true);

                        ticket.should.have.property('status');
                        ticket.status.should.be.equal('paid');
                    }

                    return getEventBalance(eventID).then(balance => {
                        /**
                         * The app cannot take any fee if paid by cash/check.
                         * So the balance decreases to take such "not-taken" fee when paid by card.
                         */
                        let expectedBalanceAdjustment   = EXPECTED_APP_FEE * -1;
                        let expectedBalance             =
                                String(INITIAL_BALANCE + expectedBalanceAdjustment);

                        balance.should.be.equal(expectedBalance);
                    })
                })
        })
    })
});
