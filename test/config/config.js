const utils = require('../../api/lib/swUtils');
const parse = require('pg-connection-string').parse;
const { WORKERS_QUEUE_PREFIX_TEST } = require('../../api/constants/workers-queue');
const REDIS_URL = utils.getConnection('REDIS_URL') || 'redis://127.0.0.1:6379';
const CONNECTION_STRING_TYPE = REDIS_URL.startsWith('redis:') ? 'url' : 'socket';
const redis = utils.parseBullRedisCs(REDIS_URL);
// load .env variables
console.log('Loading .env variables...');
require('dotenv').config();

module.exports = {
    dev:true,
    port:8000,
    hooks: {
        "grunt"       : false,
        "orm"         : false,
        "pubsub"      : false,
        "sockets"     : false,
        "i18n"        : false,
        "async"       : false,
        "blueprints"  : false,
        "swagger"     : false
    },
    environment:"test",
    security: { 
        csrf: false 
    },
    log:{
        level:"verbose"
    },
    firebase: {
        apiKey: "",
        authDomain: "sw-tickets.firebaseapp.com",
        databaseURL: "https://sw-tickets-default-rtdb.firebaseio.com",
        projectId: "sw-tickets",
        storageBucket: "sw-tickets.appspot.com",
        messagingSenderId: "",
        appId: "",
        measurementId: "",
    },
    rabbitmq: {
        uri: process.env.RABBITMQ_URL,
        emailExchange: 'notification',
        emailRoutingKey: 'email.send',
        highPriorityEmailRoutingKey: 'email.send.high',
        timeout: 5000,
    },
    session: {
        adapter:"redis",
        secret:"f386db9378dffbb5925247a57cbb7191",
        [CONNECTION_STRING_TYPE]: REDIS_URL,
        ttl:86400,
        db:1,
        prefix:"sess-test:",
        key:"sw.sid",
        cookie:{
            maxAge:**********
        }
    },
    db: {
        connection: "test"
    },
    redis_queue: {
        workers_queue: {
            redis,
            prefix: WORKERS_QUEUE_PREFIX_TEST,
            defaultJobOptions: {
                removeOnComplete: true,
                removeOnFail: true,
                backoff: {
                    type: "exponential",
                    delay: 10000
                },
                attempts: 1
            },
            concurrency: 10
        }
    },
    connections: {
        test: parse(process.env.TEST_DB_CONNECTION)
    },
    tr: {
        inactivityTimeout: 10 * 1000
    },
    bootstrap: function (next) {
        sails.on('lifted', function () {
            loggers.debug_log.warn(
                'App connected to'      , utils.getDBName(sails.config.connections[sails.config.db.connection]),
                'database. App mode:'   , sails.config.environment
            );
        });

        next();
    },
};
