'use strict';

const purchaseRow = require('./fixture/purchase.row.json');

const Task = require('../../scheduler/tasks/set-payment-available-on-date');

/**
 * NOTE: this script (when it is tested) uses the globals of the SailsJS web-app, but when it is 
 * used in the scheduler, the scheduler's globals are used. (Generally the mentioned globals are
 * same)
 */
describe('Cron Task: set-payment-available-on-date.js', function () {
    
    let _getChargeAvailableOnDateStub = null;
    let availableOnDate = parseInt(Date.now() / 1000, 10);
    

    before(() => {
        _getChargeAvailableOnDateStub = sinon.stub(StripeService.webhook.charge, 'getChargeAvailableOnDate')
        .callsFake(() => Promise.resolve(availableOnDate));
        return Db.query(squel.delete().from('purchase'));
    })

    after(() => {
        _getChargeAvailableOnDateStub.restore();
    })



    it('should return 0 when there is no rows to update', () => {
        return Task.run()
        .then(updatedRowsQty => {
            expect(updatedRowsQty).to.equal(0);
        })
    });



    context('', () => {
        let purchaseID = null;
        let _findPaymentsToSetAvailabilitySpy = null;
        let _setPaymentAvailableOnDateSpy = null;

        before(() => {
            // created date should be after season start YYYY-09-01
            purchaseRow.created = (sails.config.sw_season.current - 1) + "-09-23 13:23:00.543494";

            _findPaymentsToSetAvailabilitySpy = sinon.spy(
                                                PaymentService, 'findPaymentsToSetAvailability');
            _setPaymentAvailableOnDateSpy = sinon.spy(PaymentService, 'setPaymentAvailableOnDate');

            return Db.query(
                squel.insert().into('purchase').setFields(purchaseRow).returning('purchase_id'))
            .then(res => {
                purchaseID = res.rows[0].purchase_id;
            })
        })

        after(() => {
            _findPaymentsToSetAvailabilitySpy.restore();
            _setPaymentAvailableOnDateSpy.restore();

            return Db.query(squel.delete().from('purchase').where('purchase_id = ?', purchaseID));
        })



        it('should set "purchase"."stripe_balance_available"', () => {
            return Task.run()
            .then(updatedRowsQty => {
                expect(updatedRowsQty).to.equal(1);
                expect(_findPaymentsToSetAvailabilitySpy.calledTwice).to.be.true;
                expect(_setPaymentAvailableOnDateSpy.calledOnce).to.be.true;
            })
            .then(() => {
                return Db.query(
                    /* https://stackoverflow.com/a/29536958/3532316 */
                    `SELECT 
                        EXTRACT(EPOCH FROM "stripe_balance_available"::TIMESTAMPTZ AT TIME ZONE 'UTC')::BIGINT 
                                                                                     "available_on",
                        "stripe_balance_available"
                     FROM "purchase" WHERE "purchase_id" = $1`, 
                    [purchaseID]
                ).then(res => {
                    expect(res.rows.length).to.equal(1);

                    let purchase = res.rows[0];

                    expect(purchase).to.be.an('object');
                    expect(Number(purchase.available_on)).to.equal(availableOnDate);
                })
            })
        })
    })

})
