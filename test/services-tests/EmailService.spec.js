'use strict';

const LINE_BREAK = require('os').EOL;


describe('EmailService', function () {
    
    let Service, subject, emailQueueExists;
    
    before(() => {
        Service = sails.services.emailservice;
        subject = `Test ${Date.now()}`;

        emailQueueExists = !!Service.getQueue();
    });

    context('sendEmail()', () => {

        it('should reject if HTML and TEXT not passed', () =>
            Service.sendEmail({ subject }, false)
            .should.be.rejectedWith(Error, /Cannot send an empty letter. At least body text should be provided/)
        );

        it('should reject if Subject not passed', () =>
            Service.sendEmail({ html: '<b>I\'m Bold</b>', text: 'I\'m bold' }, { wrap: false })
            .should.be.rejectedWith(Error, /Subject requried/)
        );

        it('should reject if From not passed', () =>
            Service.sendEmail({ html: '<b>I\'m Bold</b>', text: 'I\'m bold', subject: 'test' }, { wrap: false })
            .should.be.rejectedWith(Error, /Subject requried/)
        );

        it('should reject if To not passed', () =>
            Service.sendEmail({
                html 		: '<b>I\'m Bold</b>',
                text 		: 'I\'m bold',
                subject,
                from 		: '<EMAIL>'
            }, { wrap: false })
            .should.be.rejectedWith(Error, /Subject requried/)
        );

        it('should wrap HTML if "wrap" parameter passed', async () => {

            const { job_id } = await Service.sendEmail({
                html 		: '<b>I\'m Bold</b>',
                text 		: 'I\'m bold',
                subject,
                from 		: '<EMAIL>',
                to 			: '<EMAIL>'
            }, { wrap: true });

            if(emailQueueExists) {
                const row = await getJob(job_id);

                expect(row).to.be.a('object');

                expect(row.html).to.equal(
                    `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>${subject}</title></head>` +
                    '<body><b>I\'m Bold</b></body></html>'
                )
            }
            
        });

        it('should convert TEXT to HTML if HTML not passed', async () => {
           const { job_id } =  await Service.sendEmail({
                text 		: `Some test text${LINE_BREAK}multiline`,
                subject,
                from 		: '<EMAIL>',
                to 			: '<EMAIL>'
            }, { wrap: true });

            if(emailQueueExists) {
                const row = await getJob(job_id);

                expect(row).to.be.a('object');
                expect(row.html).to.equal(
                    `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>${subject}</title></head>` +
                    '<body>Some test text<br/>multiline</body></html>'
                );
            }
        });

        it('should use a custom client to send a letter',
            async () => {
                const tr = Service.beginEmailTransaction(
                    {
                        from: '<EMAIL>',
                        text: `Some test text 2${LINE_BREAK}multiline`,
                        subject,
                    }
                );

                const [{job_id}] = await Promise.all([
                    tr.sendEmail({
                        to: '<EMAIL>'
                    }, { wrap: true }),
                    tr.commit(),
                ]);
                
                if(emailQueueExists) {
                    const row = await getJob(job_id);

                    expect(row).to.be.a('object');
                    expect(row.html).to.equal(
                        `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>${subject}</title></head>` +
                        '<body>Some test text 2<br/>multiline</body></html>'
                    );
                    expect(row.from).to.equal('<EMAIL>');
                    expect(row.personalizations[0].to).to.equal('<EMAIL>');
                    expect(row.subject).to.equal(subject);
                }
            }
        );

        it('should send letter without any "additional" operations', async () => {
            const {job_id} = await Service.sendEmail({
                html 		: '<b>Some Text</b>',
                text 		: 'Some Text',
                subject,
                from 		: '<EMAIL>',
                to 			: '<EMAIL>'
            })

            if(emailQueueExists) {
                const row = await getJob(job_id);

                expect(row).to.be.a('object');
                expect(row.html).to.equal('<b>Some Text</b>');
                expect(row.text).to.equal('Some Text');
                expect(row.from).to.equal('<EMAIL>');
                expect(row.personalizations[0].to).to.equal('<EMAIL>');
                expect(row.subject).to.equal(subject);
            }
        });
    });
});

    async function getJob (job_id) {
        const job = await EmailService.getQueue().getJob(job_id);
        return job.data;
    }
