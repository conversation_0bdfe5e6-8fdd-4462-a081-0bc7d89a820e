'use strict';

const customPaymentRows = require('./fixture/EventCustomPaymentRefundService/custom-payment.rows.json');
const mockPayment = require('./fixture/EventCustomPaymentRefundService/payment.mock.json');

const FullCustomPaymentRefund = require('../../api/services/custom-payment/refund/_FullCustomPaymentRefund');
const PartialCustomPaymentRefund = require('../../api/services/custom-payment/refund/_PartialCustomPaymentRefund');

const { CUSTOM_PAYMENT: { PAYMENT_STATUS } } = require('../../api/constants/payments');


describe('EventCustomPaymentRefundService', function () {
    let service;

    before(() => {
        service = sails.services.eventpaymentmethodservice.customPaymentRefund;
        return insertWithIdentity('custom_payment', customPaymentRows);
    });

    after(() => {
        return Db.query(
            `DELETE FROM "custom_payment" WHERE "custom_payment_id" IN (${customPaymentRows
                .map((row) => row.custom_payment_id)
                .join(', ')});`
        );
    });

    describe('__isFullRefund', () => {
        it('should return true if full refund', () => {
            const isFullRefund = service.__isFullRefund(
                mockPayment.amount + mockPayment.amount_refunded,
                mockPayment
            );
            expect(isFullRefund).to.be.equal(true);
        });
        it('should return false if not full refund', () => {
            const isFullRefund = service.__isFullRefund(
                mockPayment.amount,
                mockPayment
            );
            expect(isFullRefund).to.be.equal(false);
        });
    });

    describe('__getRefundTypeInstance', () => {
        let isFullRefundStub;

        before(() => {
            isFullRefundStub = sinon.stub(service, '__isFullRefund');
        });

        after(() => {
            isFullRefundStub.restore();
        });

        it('should return FullCustomPaymentRefund service', async () => {
            isFullRefundStub.returns(true);

            const refundServiceInstance = service.__getRefundTypeInstance(
                mockPayment.amount,
                mockPayment,
                'dashboard'
            );
            const FullCustomPaymentRefund =
                require.cache[
                    require.resolve(
                        '../../api/services/custom-payment/refund/_FullCustomPaymentRefund'
                    )
                ].exports;

            expect(refundServiceInstance).to.be.an.instanceOf(
                FullCustomPaymentRefund
            );
        });

        it('should return PartialCustomPaymentRefund service', async () => {
            isFullRefundStub.returns(false);

            const refundServiceInstance = service.__getRefundTypeInstance(
                mockPayment.amount,
                mockPayment,
                'dashboard'
            );

            const PartialCustomPaymentRefund =
                require.cache[
                    require.resolve(
                        '../../api/services/custom-payment/refund/_PartialCustomPaymentRefund'
                    )
                ].exports;

            expect(refundServiceInstance).to.be.instanceOf(
                PartialCustomPaymentRefund
            );
        });
    });

    describe('proceed', () => {
        let fullRefundProceedSpy;
        let partialRefundProceedSpy;
        let __getRefundTypeInstanceStub;

        const FullRefundService = new FullCustomPaymentRefund(
            mockPayment,
            'dashboard'
        );
        const PartialRefundService = new PartialCustomPaymentRefund(
            mockPayment,
            'dashboard'
        );

        before(() => {
            __getRefundTypeInstanceStub = sinon.stub(
                service,
                '__getRefundTypeInstance'
            );
            fullRefundProceedSpy = sinon.spy(FullRefundService, 'proceed');
            partialRefundProceedSpy = sinon.spy(
                PartialRefundService,
                'proceed'
            );
        });

        after(() => {
            fullRefundProceedSpy.restore();
            partialRefundProceedSpy.restore();
            __getRefundTypeInstanceStub.restore();
        });

        const amountRefunded = mockPayment.amount;

        it('should throw error', async () => {
            await expect(service.proceed(amountRefunded)).to.be.rejectedWith(
                'Payment is empty'
            );

            await expect(
                service.proceed(amountRefunded, {})
            ).to.be.rejectedWith('Payment is empty');
            await expect(
                service.proceed(amountRefunded, mockPayment)
            ).to.be.rejectedWith('Refund source is not allowed');

            await expect(
                service.proceed(null, mockPayment, 'dashboard')
            ).to.be.rejectedWith('Amount refunded not passed or is 0');
        });

        it('should proceed with full refund', async () => {
            __getRefundTypeInstanceStub.returns(FullRefundService);

            await service.proceed(amountRefunded, mockPayment, 'dashboard');

            expect(fullRefundProceedSpy.calledOnce).to.be.true;
            expect(fullRefundProceedSpy.calledWith(amountRefunded)).to.be.true;
        });

        it('should proceed with partial refund', async () => {
            __getRefundTypeInstanceStub.returns(PartialRefundService);

            await service.proceed(amountRefunded, mockPayment, 'dashboard');

            expect(partialRefundProceedSpy.calledOnce).to.be.true;
            expect(partialRefundProceedSpy.calledWith(amountRefunded)).to.be.true;
        });
    });
});

function insertWithIdentity(tableName, data) {
    const columns = Object.keys(data[0]).join(', ');
    const values = data.map((row) => {
        const sqlValues = Object.values(row)
            .map((value) =>
                value === null
                    ? 'null'
                    : typeof value === 'string'
                    ? `'${value}'`
                    : value
            )
            .join(', ');
        return '( ' + sqlValues + ' )';
    });
    return Db.query(`
        INSERT INTO ${tableName} ( ${columns} )
        OVERRIDING SYSTEM VALUE 
            VALUES ${values.join(', ')};
    `);
}
