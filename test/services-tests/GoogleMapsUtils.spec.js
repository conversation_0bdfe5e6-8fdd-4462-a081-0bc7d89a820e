'use strict';

const Service = require('../../api/lib/GoogleMapsUtils');

describe('GoogleMapsUtils', function () {

    let _initSpy = sinon.spy(Service, '_init');

    after(() => {
        _initSpy.restore();
    })
	
	context('findLocation()', function () {
		
		it.skip('should find location for existing US zip code', () => {
			return Service.findLocation(99501, 'US').then(location => {
				expect(location).to.be.a('object');
				expect(location).to.contain.all.keys(['lat', 'lng']);
				expect(Math.round(location.lat * 1000) / 1000).to.equal(61.219);
				expect(Math.round(location.lng * 1000) / 1000).to.equal(-149.85);

                expect(_initSpy.calledOnce).to.be.true;
			})
		})

		it('should reject if invalid zip code passed', () => {
			return Service.findLocation('qoweiurqwer', 'US').should.be.rejectedWith({
				validation: 'No Location found for address qoweiurqwer US' 
			}).then(() => {
                expect(_initSpy.calledOnce).to.be.true;
            })
		})

	})

})
