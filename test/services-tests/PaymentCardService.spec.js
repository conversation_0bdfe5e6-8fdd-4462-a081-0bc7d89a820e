"use strict";

const eventRow = require("./fixture/PaymentCardService/event.row");
const eventOwnersRows = require("./fixture/PaymentCardService/event_owner.rows");
const userStripeCustomerRows = require("./fixture/PaymentCardService/user_stripe_customer.rows");
const userRows = require("./fixture/PaymentCardService/user.rows");
const stripePaymentMethodRows = require("./fixture/PaymentCardService/stripe_payment_method.rows");
const eventUserPermissionRows = require("./fixture/PaymentCardService/event_user_permission.rows");

const mockPaymentMethodObject = require("./fixture/PaymentCardService/mock_stripe_payment_method");
const mockStripeCustomer = require("./fixture/PaymentCardService/mock_stripe_customer");
const mockStripeSetupIntent = require("./fixture/PaymentCardService/mock_stripe_setup_intent");

const { expect } = require("chai");

describe("PaymentCardService", function () {
    let service;

    before(function () {
        service = sails.services.stripeservice.paymentCard;
    });

    after(() => clearDB());

    context("_createCardCustomerUser()", function () {
        let createStripeCustomerStub;

        before(function () {
            createStripeCustomerStub = sinon
                .stub(
                    service.stripeService,
                    "createStripeCustomer"
                )
                .resolves(mockStripeCustomer);
        });

        after(() => {
            createStripeCustomerStub.restore();
        });

        it("should create card customer user", () =>
            service
                ._createCardCustomerUser(1)
                .then((result) => {
                    expect(result).to.be.a("string");
                    expect(result).to.be.equal(mockStripeCustomer.id);
                    return result;
                })
                .then(async (customer_id) => {
                    const customer = await Db.query(
                        `SELECT * FROM user_stripe_customer WHERE stripe_customer_id='${customer_id}'`
                    ).then((res) => res?.rows?.[0] || null);

                    expect(customer).not.to.be.null;
                    expect(customer.stripe_customer_object).to.be.an("object");
                    expect(customer.stripe_customer_object).eql(
                        mockStripeCustomer
                    );
                }));

        it("should throw error if no userId was passed", () =>
            service
                ._createCardCustomerUser()
                .should.be.rejectedWith({ message: "User ID required" }));
    });

    context("saveUserPaymentMethod()", function () {
        let getPaymentMethodStub;

        before(function () {
            getPaymentMethodStub = sinon
                .stub(service.stripeService, "getPaymentMethod")
                .resolves(mockPaymentMethodObject);
        });

        after(() => {
            getPaymentMethodStub.restore();
        });

        it("should throw error if no userId was passed", () =>
            service
                .saveUserPaymentMethod()
                .should.be.rejectedWith({ message: "User ID required" }));

        it("should throw error if no paymentMethodID was passed", () =>
            service.saveUserPaymentMethod(1).should.be.rejectedWith({
                message: "Stripe Payment Method ID required",
            }));

        it("should save user payment method", () =>
            service
                .saveUserPaymentMethod(1, mockPaymentMethodObject.id)
                .then(async () => {
                    const paymentMethod = await Db.query(
                        `SELECT * FROM stripe_payment_method WHERE stripe_payment_method_id='${mockPaymentMethodObject.id}'`
                    ).then((res) => res?.rows?.[0] || null);

                    expect(paymentMethod).not.to.be.null;
                    expect(paymentMethod.payment_object).to.be.an("object");
                    expect(paymentMethod.payment_object).eql(
                        mockPaymentMethodObject
                    );
                }));
    });

    context("setDefault()", function () {
        it("should throw error if no userId was passed", () =>
            service
                .setDefault()
                .should.be.rejectedWith({ message: "User ID required" }));

        it("should throw error if no paymentMethodID was passed", () =>
            service.setDefault(1).should.be.rejectedWith({
                message: "Stripe Payment Method ID required",
            }));

        it("should set default flag", () =>
            service.setDefault(1, mockPaymentMethodObject.id).then(async () => {
                const paymentMethod = await Db.query(
                    `SELECT * FROM stripe_payment_method WHERE stripe_payment_method_id='${mockPaymentMethodObject.id}'`
                ).then((res) => res?.rows?.[0] || null);

                expect(paymentMethod).not.to.be.null;
                expect(paymentMethod.is_default).to.be.true;
            }));
    });

    context("getUserCustomerStripeSetupIntent()", function () {
        let createStripeSetupIntent;

        before(function () {
            createStripeSetupIntent = sinon
                .stub(service.stripeService, "createSetupIntent")
                .resolves(mockStripeSetupIntent.client_secret);
        });

        after(() => {
            createStripeSetupIntent.restore();
        });

        it("should throw error if no userId was passed", () =>
            service
                .setDefault()
                .should.be.rejectedWith({ message: "User ID required" }));

        it("should return stripe setup intent client secret", () =>
            service.getUserCustomerStripeSetupIntent(1).then((res) => {
                expect(res).to.be.a("string");
                expect(res).to.be.equal(mockStripeSetupIntent.client_secret);
            }));
    });

    context("removeUserPaymentMethod()", function () {
        let detachCustomerPaymentMethodStub;

        before(function () {
            detachCustomerPaymentMethodStub = sinon
                .stub(
                    service.stripeService,
                    "detachCustomerPaymentMethod"
                )
                .resolves(mockPaymentMethodObject);
        });

        after(() => {
            detachCustomerPaymentMethodStub.restore();
        });

        it("should throw error if no userId was passed", () =>
            service
                .removeUserPaymentMethod()
                .should.be.rejectedWith({ message: "User ID required" }));

        it("should throw error if no paymentMethodID was passed", () =>
            service.removeUserPaymentMethod(1).should.be.rejectedWith({
                message: "Stripe Payment Method ID required",
            }));

        it("should remove user payment method", () =>
            service
                .removeUserPaymentMethod(1, mockPaymentMethodObject.id)
                .then(async () => {
                    const paymentMethod = await Db.query(
                        `SELECT * FROM stripe_payment_method WHERE stripe_payment_method_id='${mockPaymentMethodObject.id}'`
                    ).then((res) => res?.rows?.[0] || null);

                    expect(paymentMethod).to.be.null;
                }));
    });

    context("getPaymentCards()", function () {
        before(function () {
            return Promise.all([
                insertFixture("event_owner", eventOwnersRows),
                insertFixture("event", [eventRow]),
                insertFixture("event_user_permission", eventUserPermissionRows),
                insertFixture("user", userRows),
                insertFixture("user_stripe_customer", userStripeCustomerRows),
                insertFixture("stripe_payment_method", stripePaymentMethodRows),
            ]);
        });

        it("should throw error if no userId was passed", () =>
            service
                .getPaymentCards()
                .should.be.rejectedWith({ message: "User OR EO ID required" }));

        it("should return user payment method cards when passed isAdmin true", () =>
            service
                .getPaymentCards(
                    userStripeCustomerRows[0].user_id,
                    eventOwnersRows[0].event_owner_id,
                    eventRow.event_id,
                    true
                )
                .then(async (res) => {
                    expect(res).to.be.a("array");
                    expect(res[0]).to.exist;
                    expect(res[0]).to.be.a("object");

                    expect(res[0]).to.contain.all.keys([
                        "stripe_payment_method_id",
                        "card_last_4",
                        "card_brand",
                        "card_exp_month",
                        "card_exp_year",
                        "is_default",
                        "holder_name",
                    ]);
                }));

        it("should return user payment method cards when passed isAdmin false", () =>
            service
                .getPaymentCards(
                    userStripeCustomerRows[0].user_id,
                    eventOwnersRows[0].event_owner_id,
                    eventRow.event_id,
                    false
                )
                .then(async (res) => {
                    expect(res).to.be.a("array");
                    expect(res[0]).to.exist;
                    expect(res[0]).to.be.a("object");

                    expect(res[0]).to.contain.all.keys([
                        "stripe_payment_method_id",
                        "card_last_4",
                        "card_brand",
                        "card_exp_month",
                        "card_exp_year",
                        "is_default",
                        "holder_name",
                    ]);
                }));
    });
});

function insertFixture(tableName, data) {
    return Db.query(knex(tableName).insert(data));
}

function clearDB() {
    let queries = [
        `DELETE FROM "event_owner" WHERE "event_owner_id" IN (${eventOwnersRows
            .map((eo) => eo.event_owner_id)
            .join(", ")});`,
        `DELETE FROM "event" WHERE "event_id"=${eventRow.event_id};`,
        `DELETE FROM "event_user_permission" WHERE "event_id"=${eventRow.event_id};`,
        `DELETE FROM "user" WHERE "user_id" IN (${userRows
            .map((u) => u.user_id)
            .join(", ")});`,
        `DELETE FROM "user_stripe_customer" WHERE "user_id" IN (${userStripeCustomerRows
            .map((d) => d.user_id)
            .join(", ")});`,
        `DELETE FROM "stripe_payment_method" WHERE "stripe_payment_method_id" IN (${stripePaymentMethodRows
            .map((spm) => `'${spm.stripe_payment_method_id}'`)
            .join(", ")});`,
        `DELETE FROM "user_stripe_customer" WHERE  stripe_customer_id='${mockStripeCustomer.id}';`,
        `DELETE FROM "stripe_payment_method" WHERE "stripe_payment_method_id"='${mockPaymentMethodObject}';`,
    ];

    return Promise.all(queries.map((q) => Db.query(q))).then(() => {});
}


