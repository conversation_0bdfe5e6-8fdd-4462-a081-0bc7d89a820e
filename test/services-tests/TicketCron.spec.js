'use strict';

describe('TicketsCron', function () {

	let Service;

	before(() => {
		Service = sails.services.ticketscron;
	})

	context('__findPriceOfLatestChange__', function () {
		
		/* Errors */
		it('should throw error for non-numeric current change index', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, '1');

			expect(fn).to.throw(Error);
		})

		it('should throw error for current change index less than 0', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, -0.1);

			expect(fn).to.throw(Error);
		})

		it('should throw error if changes list is not null and not array', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, 0, 'test');

			expect(fn).to.throw(Error);
		})

		it('should throw error if ticket is not an object', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, 0, [], null);

			expect(fn).to.throw(Error);
		})

		it('should throw error if initial price of a ticket is not convertable to numeric', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, 0, [], { initial_price: 'test' });

			expect(fn).to.throw(Error);
		})

		it('should throw error if initial price of a ticket is less than 0', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, 0, [], { initial_price: -0.1 });

			expect(fn).to.throw(Error);
		})


		it('should return initial price if there is no changes (event ones)', () => {
			let res = Service.__findPriceOfLatestChange__(1, null, { initial_price: 10 });

			expect(res).to.be.a('number');
			expect(res).to.equal(10);

			res = Service.__findPriceOfLatestChange__(1, [], { initial_price: 10 });

			expect(res).to.be.a('number');
			expect(res).to.equal(10);

		})

		it('should return initial price if there is no changes for ticket', () => {
			let res = Service.__findPriceOfLatestChange__(1, [1, 0], { initial_price: 10 });

			expect(res).to.be.a('number');
			expect(res).to.equal(10);
		})

		it('should return initial price if nothing found during price changes look up', () => {
			let res = Service.__findPriceOfLatestChange__(1, [1, 0], {
				initial_price 	: 10, 
				prices 			: {
					'2': { value: 25 } 
				} 
			});

			expect(res).to.be.a('number');
			expect(res).to.equal(10);

		})

		it('should return current price for specified price changes', () => {
			let res = Service.__findPriceOfLatestChange__(2, [2, 1, 0], { 
				initial_price 	: 10, 
				prices 			: { '1': { value: 15 }, '0': { value: 12 } } 
			});

			expect(res).to.be.a('number');
			expect(res).to.equal(15);
		})


		it('should throw error if found current price is non-numeric', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, 2, [2, 1, 0], { 
				initial_price 	: 10, 
				prices 			: { '1': { value: 'test' }, '0': { value: 12 } } 
			});

			expect(fn).to.throw(Error);
		})

		it('should throw error if found current price is less than 0', () => {
			const fn = Service.__findPriceOfLatestChange__.bind(Service, 2, [2, 1, 0], { 
				initial_price 	: 10, 
				prices 			: { '1': { value: -0.1 }, '0': { value: 12 } } 
			});

			expect(fn).to.throw(Error);
		})

	})
	
	context('__findTicketPrice__()', function () {

		let findPriceSpy;

		before(() => {
			findPriceSpy = sinon.spy(Service, '__findPriceOfLatestChange__');
		})

		afterEach(() => {
			findPriceSpy.resetHistory();
		})

		after(() => {
			findPriceSpy.restore();
		})

		it('should throw error if ticket is not an object', () => {
			const fn = Service.__findTicketPrice__.bind(Service, 'test');

			expect(fn).to.throw(Error);
		})

		it('should throw error if "current_price_value" is below zero', () => {
			const fn = Service.__findTicketPrice__.bind(Service, {
				current_price_value: -0.1
			});

			expect(fn).to.throw(Error);
		})

		it('should throw error if "current_price" is non-numeric', () => {
			const fn = Service.__findTicketPrice__.bind(Service, {
				current_price_value: 0,
				current_price: 'test'
			});

			expect(fn).to.throw(Error);
		})

		it('should throw error if "current_price" is zero', () => {
			const fn = Service.__findTicketPrice__.bind(Service, {
				current_price_value: 0,
				current_price: 0
			});

			expect(fn).to.throw(Error);
		})

		it('should throw error if "current_price" is below zero', () => {
			const fn = Service.__findTicketPrice__.bind(Service, {
				current_price_value: 0,
				current_price: -0.1
			});

			expect(fn).to.throw(Error);
		})

		it('should throw error if initial price of a ticket is not convertable to numeric', () => {
			const fn = Service.__findTicketPrice__.bind(Service, 0, { 
				initial_price 			: 'test',
				current_price_value 	: 0,
				current_price 			: 0
			});

			expect(fn).to.throw({ message: 'Initial Price should be a number!' });
		})

		it('should throw error if initial price of a ticket is less than 0', () => {
			const fn = Service.__findTicketPrice__.bind(Service, 0, { 
				initial_price 			: -0.1,
				current_price_value 	: 0,
				current_price 			: 0
			});

			expect(fn).to.throw({ message: 'Initial Price should be greater than 0!' });
		})

		it('should return price if current price not equal to ticket price', () => {
			let res = Service.__findTicketPrice__({
				current_price_value: 15,
				current_price: 10,
				initial_price: 1
			})

			expect(res).to.be.a('number');
			expect(res).to.equal(15);
			expect(findPriceSpy.callCount).to.equal(0);
		})

		it('should return null if current price equals to ticket price', () => {
			let res = Service.__findTicketPrice__({
				current_price_value: 10,
				current_price: 10,
				initial_price: 1
			})

			expect(res).to.be.null;
			expect(findPriceSpy.callCount).to.equal(0);
		})

		it('should return found price (latest actual for the ticket)', () => {
			let res = Service.__findTicketPrice__({
				current_price: 10,
				initial_price: 10,
				prices: {
					'1': { value: 15 }
				}
			}, {
				current_change: 2,
				sorted_changes: [2, 1, 0]
			})

			expect(res).to.be.a('number');
			expect(res).to.equal(15);
			expect(findPriceSpy.calledOnce).to.be.true
		})

		it('should return null if found price (latest actual for the ticket) equals to current', () => {
			let res = Service.__findTicketPrice__({
				current_price: 10,
				initial_price: 10,
				prices: {
					'1': { value: 10 }
				}
			}, {
				current_change: 2,
				sorted_changes: [2, 1, 0]
			})

			expect(res).to.be.null;
			expect(findPriceSpy.calledOnce).to.be.true
		})

		it('should return null if "current_change" is null, there is no current price value ' + 
			'and "initial_price" equals to "current_price"', () => {
			let res = Service.__findTicketPrice__({
				current_price: 10,
				initial_price: 10,
				prices: {
					'1': { value: 10 }
				}
			}, {
				current_change: null,
				/* if current change is null, sorted changes are also null, see the comments inside module */
				sorted_changes: null 
			})

			expect(res).to.be.null;
			expect(findPriceSpy.callCount).to.equal(0);
		})

		it('should return null if "current_change" is null, there is no current price value ' + 
			'and "current_price" is NOT null', () => {
			let res = Service.__findTicketPrice__({
				current_price: 10,
				initial_price: 10,
				prices: {
					'1': { value: 10 }
				}
			}, {
				current_change: null,
				/* if current change is null, sorted changes are also null, see the comments inside module */
				sorted_changes: null 
			})

			expect(res).to.be.null;
			expect(findPriceSpy.callCount).to.equal(0);
		})

		it('should return "initial_price" if "current_change" is null, ' + 
			'there is no current price value ' + 
			'and "current_price" is null', () => {
			let res = Service.__findTicketPrice__({
				current_price: null,
				initial_price: 10,
				prices: {
					'1': { value: 10 }
				}
			}, {
				current_change: null,
				/* if current change is null, sorted changes are also null, see the comments inside module */
				sorted_changes: null 
			})

			expect(res).to.equal(10);
			expect(findPriceSpy.callCount).to.equal(0);
		})

		it('should return price if "current_price" is null', () => {
			let res = Service.__findTicketPrice__({
				current_price  			: null,
				current_price_value 	: 10,
				initial_price 			: 1
			});

			expect(res).to.be.a('number');
			expect(res).to.equal(10);
			expect(findPriceSpy.callCount).to.equal(0);
		})

		it('should return initial price if there are no price changes ' + 
		   'and "current_price" differs from the initial price', () => {
		   	let res = Service.__findTicketPrice__({
				current_price  			: 11,
				initial_price 			: 10
			}, {
				current_change: null,
				sorted_changes: null
			});

			expect(res).to.be.a('number');
			expect(res).to.equal(10);
			expect(findPriceSpy.callCount).to.equal(0);
		})

		it('should return initial price if there are no actual price changes of the given ' + 
		   'ticket, but event\'s price change date has come', () => {
		   	let initialPrice = 10;

		   	let res = Service.__findTicketPrice__({
				current_price: 9, /* Somehow this contains an invalid value! */
				initial_price: initialPrice,
				prices: {
					'2': { value: 15 }
				}
			}, {
				current_change: 1,
				sorted_changes: [1] 
			})

			expect(res).to.equal(initialPrice);
			expect(findPriceSpy.callCount).to.equal(1);
		})
	})

})
