
const moment = require('moment');

class XlsxResultsCheckHelper {
    constructor(XLSXUtils) {
        this.XLSXUtils = XLSXUtils;
        this.currentSeason = sails.config.sw_season.current;
    }

    async checkMembers(membersFromFile, clubAthletes, clubStaffers) {
        const groupedMembers = await this.XLSXUtils.groupMembersByMemberType(membersFromFile);

        this.#checkAthletes(groupedMembers, clubAthletes);
        this.#checkStaffers(groupedMembers, clubStaffers);
    }

    #checkAthletes(groupedMembers, clubAthletes) {
        const athletesFromFile = groupedMembers[this.XLSXUtils.MEMBER_TYPE.ATHLETE];
        const athletesFromDb = this.#extractMembers(clubAthletes);

        this.#checkMembersCounts(athletesFromFile, athletesFromDb);
        this.#checkMembersData(athletesFromFile, athletesFromDb, this.XLSXUtils.MEMBER_TYPE.ATHLETE);
    }

    #checkStaffers(groupedMembers, clubStaffers) {
        const staffersFromFile = groupedMembers[this.XLSXUtils.MEMBER_TYPE.STAFF];
        const staffersFromDb = this.#extractMembers(clubStaffers);

        this.#checkMembersCounts(staffersFromFile, staffersFromDb);
        this.#checkMembersData(staffersFromFile, staffersFromDb, this.XLSXUtils.MEMBER_TYPE.STAFF);
    }

    #checkMembersCounts(membersFromFile, clubMembers) {
        this.#checkEquality(membersFromFile.length, clubMembers.length);
    }

    #extractMembers(members) {
        return members.filter(member => !member.aau_membership_id && !member.usav_number);
    }

    #checkMembersData(membersFromFile, membersFromDB, memberType) {
        for(const memberFromDB of membersFromDB) {
            const [memberFromFile] = this.#findMemberFromFile(membersFromFile, memberFromDB);

            if(!_.isEmpty(memberFromFile)) {
                this.#checkMemberData(memberType, memberFromFile, memberFromDB);
            }
        }
    }

    #findMemberFromFile(membersFromFile, memberFromDB) {
        return membersFromFile.filter(
            member => {
                return member[this.XLSXUtils.XLSX_IMPORT_FIELDS.FIRST].trim().toLowerCase() === memberFromDB.first.trim().toLowerCase()
                    && member[this.XLSXUtils.XLSX_IMPORT_FIELDS.LAST].trim().toLowerCase() === memberFromDB.last.trim().toLowerCase()
                    && this.#birthdateEqual(member[this.XLSXUtils.XLSX_IMPORT_FIELDS.BIRTHDATE], memberFromDB.birthdate)
            }
        );
    }

    #birthdateEqual (fileBirthdate, dbBirthdate) {
        const birthdateFromFile = moment(fileBirthdate, 'MM/DD/YYYY').format('YYYY-MM-DD');
        const birthdateFromDb = moment(dbBirthdate).format('YYYY-MM-DD');

        return birthdateFromFile === birthdateFromDb;
    }

    #checkMemberData(memberType, memberFromFile, memberFromDB) {
        const XLSX_FIELD_NAMES = this.XLSXUtils.XLSX_IMPORT_FIELDS;
        const genderCode = memberFromFile[XLSX_FIELD_NAMES.GENDER];

        this.#checkEquality(
            memberFromDB.first.trim().toLowerCase(),
            memberFromFile[XLSX_FIELD_NAMES.FIRST].trim().toLowerCase()
        );
        this.#checkEquality(
            memberFromDB.last.trim().toLowerCase(),
            memberFromFile[XLSX_FIELD_NAMES.LAST].trim().toLowerCase()
        );
        this.#checkEquality(
            moment(memberFromDB.birthdate).format('YYYY-MM-DD'), 
            moment(memberFromFile[XLSX_FIELD_NAMES.BIRTHDATE], 'MM/DD/YYYY').format('YYYY-MM-DD')
        );
        this.#checkEquality(memberFromDB.gender, this.XLSXUtils.GENDER[genderCode]);
        this.#checkEquality(memberFromDB.season, this.currentSeason);

        expect(memberFromDB.aau_membership_id).to.be.null;
        expect(memberFromDB.aau_sync).to.be.null;

        expect(memberFromDB.usav_number).to.be.null;
        expect(memberFromDB.sportengine_sync).to.be.null;

        if(memberType === this.XLSXUtils.MEMBER_TYPE.ATHLETE) {
            const minAge = this.XLSXUtils.getMinAge(memberFromFile[XLSX_FIELD_NAMES.BIRTHDATE]);

            this.#checkEquality(memberFromDB.age, minAge);
            this.#checkEquality(
                memberFromDB.gradyear,
                Number(memberFromFile[XLSX_FIELD_NAMES.GRAD_YEAR])
            );
        }
    }

    #checkEquality(actualValue, expectedValue) {
        expect(actualValue).to.be.equal(expectedValue);
    }
}

module.exports = XlsxResultsCheckHelper;
