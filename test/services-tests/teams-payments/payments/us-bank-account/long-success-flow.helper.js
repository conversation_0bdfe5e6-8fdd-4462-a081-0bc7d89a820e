
const fixturesPath = '../../../fixture/teams-payments/payments/USBankAccountPayments/';
const rosterTeamData = require(fixturesPath + 'db_data/roster_team.row.json');

const FIXTURES = {
    REQUIRES_ACTION: {
        WEBHOOK: require(fixturesPath + 'stripe_data/long-success-flow/requires_action.webhook.json'),
    },
    PROCESSING: {
        WEBHOOK: require(fixturesPath + 'stripe_data/long-success-flow/processing.webhook.json'),
        EXPANDED_CHARGE: require(fixturesPath + 'stripe_data/long-success-flow/processing.expanded.charge.json'),
    },
    SUCCESS: {
        WEBHOOK: require(fixturesPath + 'stripe_data/long-success-flow/success.webhook.json'),
        EXPANDED_CHARGE: require(fixturesPath + 'stripe_data/long-success-flow/success.expanded.charge.json'),
    },
    PAYMENT_METHOD: require(fixturesPath + 'stripe_data/long-success-flow/payment_method.json'),
};

const EXPECTED_VALUES = {
    purchase: {
        amount: 10777,
        status: 'paid',
        stripe_charge_id: FIXTURES.SUCCESS.EXPANDED_CHARGE.id,
        type: 'ach',
        email: FIXTURES.SUCCESS.EXPANDED_CHARGE.billing_details.email,
        phone: FIXTURES.SUCCESS.EXPANDED_CHARGE.metadata.phone,
        payment_for: 'teams',
        first: null,
        last: null,
        additional_fee_amount: 0,
        net_profit: 10668.51,
        stripe_fee: 103.49,
        collected_sw_fee: 5,
        stripe_percent: 0.8,
        stripe_account_id: null,
        payment_intent_id: FIXTURES.SUCCESS.WEBHOOK.data.object.id,
    },
    purchase_team: {
        amount: 10777,
        surcharge: 0,
        event_fee: 10777,
        sw_fee: 5,
    },
    roster_team: {
        status_paid: 22,
    },
    stripe_charge: {
        amount: 10777,
        fee: 5,
        collected_fee: 0,
        stripe_payment_id: 'py_1Kw0MkLDYnTA1fGW76wjaU3t',
        stripe_account_id: FIXTURES.SUCCESS.EXPANDED_CHARGE.destination,
    },
    stripe_payment_intent: {
        payment_intent_status: 'succeeded',
        stripe_charge_id: FIXTURES.SUCCESS.EXPANDED_CHARGE.id,
        amount: 10777,
        stripe_fee: 108.49,
        stripe_percent: 0.8,
    }
};

const SEARCH_RESULTS_VALUES = {
    stripe_payment_intent: FIXTURES.SUCCESS.WEBHOOK.data.object.id,
    stripe_charge: FIXTURES.SUCCESS.EXPANDED_CHARGE.id,
    roster_team: rosterTeamData.roster_team_id,
};

const PAYMENT_SESSION = require(fixturesPath + 'session_data/long-success-flow.session.json');

module.exports = {
    FIXTURES,
    EXPECTED_VALUES,
    SEARCH_RESULTS_VALUES,
    PAYMENT_SESSION
}
