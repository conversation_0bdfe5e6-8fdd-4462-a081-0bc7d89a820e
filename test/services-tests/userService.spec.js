'use strict';

const userRow = require('./fixture/UserService/user.row');
const deleteUserData = require('./fixture/UserService/deleteUser.data');
const { expect } = require('chai');

const UNIQUE_RECORD_ERROR_CODE = '23505';

describe('UserService', function () {
    let UserService;

    before(function () {
        UserService = sails.services.userservice;

        return Db.query(
            squel
                .insert()
                .into('user')
                .setFields(userRow)
                .returning('*')
                .toString()
        );
    });

    after(function () {
        return Db.query('DELETE FROM "user" WHERE "user_id" = $1', [
            userRow.user_id,
        ]);
    });

    context('reg', function () {
        let service;

        before(function () {
            service = UserService.reg;
        });

        context('retrievePassportUser()', function () {
            it('should return a user', () => {
                return service
                    .retrievePassportUser('email', '<EMAIL>')
                    .then((user) => {
                        expect(user).to.be.a('object');

                        let fieldsToContain = _.union(
                            service._USER_TABLE_FIELDS,
                            [
                                'event_owner_id',
                                'club_owner_id',
                                'master_club_id',
                                'sponsor_ids',
                                'sales_manager_id',
                                'official_id',
                                'events',
                                'shared_events',
                                'eo_list',
                            ]
                        );

                        expect(user).to.contain.all.keys(fieldsToContain);
                    });
            });

            it('should return null if user does not exist', () => {
                return service
                    .retrievePassportUser('email', '<EMAIL>')
                    .then((user) => {
                        expect(user).to.be.null;
                    });
            });
        });

        context('retrievePassportUser() wrappers', function () {
            let retrievePassportUserSpy;

            before(function () {
                retrievePassportUserSpy = sinon.spy(
                    service,
                    'retrievePassportUser'
                );
            });

            afterEach(function () {
                retrievePassportUserSpy.resetHistory();
            });

            after(function () {
                retrievePassportUserSpy.restore();
            });

            context('retrievePassportUserByToken()', function () {
                it('should return a user', () => {
                    return service
                        .retrievePassportUserByToken(userRow.remember_me)
                        .then((user) => {
                            expect(user).to.be.a('object');

                            expect(retrievePassportUserSpy.calledOnce).to.be
                                .true;
                            expect(
                                retrievePassportUserSpy.calledWith(
                                    'remember_me',
                                    userRow.remember_me
                                )
                            ).to.be.true;
                        });
                });
            });

            context('retrievePassportUserByEmail()', function () {
                it('should return a user', () => {
                    return service
                        .retrievePassportUserByEmail(userRow.email)
                        .then((user) => {
                            expect(user).to.be.a('object');

                            expect(retrievePassportUserSpy.calledOnce).to.be
                                .true;
                            expect(
                                retrievePassportUserSpy.calledWith(
                                    'email',
                                    userRow.email
                                )
                            ).to.be.true;
                        });
                });
            });
        });

        context('dropRememberMeToken()', function () {
            it('should reject if user not found', () => {
                let notExistingUserID = 1;

                return service
                    .dropRememberMeToken(notExistingUserID)
                    .should.be.rejectedWith(Error, /User not found/);
            });

            it('should drop user "remember me" token', () => {
                return service
                    .dropRememberMeToken(userRow.user_id)
                    .then(() => {
                        return Db.query(
                            'SELECT "remember_me" FROM "user" WHERE "user_id" = $1',
                            [userRow.user_id]
                        );
                    })
                    .then((result) => result.rows[0])
                    .then((user) => {
                        expect(user.remember_me).to.be.null;
                    });
            });
        });

        context('genAndSetRememberMeToken()', function () {
            let genSaltSpy;

            before(() => {
                genSaltSpy = sinon.spy(service, '_genSalt');
            });

            afterEach(() => {
                genSaltSpy.resetHistory();
            });

            after(() => {
                genSaltSpy.restore();
            });

            it('should reject if user not found', () => {
                let notExistingUserID = 1;

                return service
                    .genAndSetRememberMeToken(notExistingUserID)
                    .should.be.rejectedWith(Error, /User not found/)
                    .then(() => {
                        expect(genSaltSpy.calledOnce).to.be.true;
                    });
            });

            it('should create user "remember me" token', () => {
                let _token;

                return service
                    .genAndSetRememberMeToken(userRow.user_id)
                    .then((token) => {
                        expect('token').to.be.a('string');

                        _token = token;

                        return Db.query(
                            'SELECT "remember_me" FROM "user" WHERE "user_id" = $1',
                            [userRow.user_id]
                        );
                    })
                    .then((result) => result.rows[0])
                    .then((user) => {
                        expect(user.remember_me).to.be.equal(_token);

                        expect(genSaltSpy.calledOnce).to.be.true;
                        expect(genSaltSpy.returnValues[0]).to.be.equal(
                            user.remember_me
                        );
                    });
            });
        });

        context('deleteUser()', function () {
            const allUsers = Object.values(deleteUserData);

            const spectator = deleteUserData.spectator;

            const undeletableUserKeys = [
                'eventOwner',
                'salesManager',
                'clubDirector',
                'staff',
                'sponsor',
            ];

            before(() => {
                return Promise.all(allUsers.map((item) => __createUser(item)));
            });

            after(() => {
                const email = allUsers.map(({ email }) => email);
                return __deleteUsersByEmails(email);
            });

            it('check other roles than spectator cannot be deleted', async () => {
                await Promise.all(
                    undeletableUserKeys.map((userKey) => {
                        const user = deleteUserData[userKey];
                        return service
                            .deleteUser(user.user_id)
                            .should.be.rejectedWith({
                                validation:
                                    'You have one more role in SW. If you want to delete the account please contact support',
                            });
                    })
                );
            });

            it('check spectator can be deleted', async () => {
                await service
                    .deleteUser(spectator.user_id)
                    .should.not.be.rejected();
            });

            it('check user is soft deleted', async () => {
                const users = await __findUsersByEmail(spectator.email);

                expect(users.length).to.be.equal(1);

                const user = users[0];
                expect(user).to.have.property('deleted_at');
                expect(user.deleted_at).not.to.be.null;
            });

            it('check user cannot be deleted twice', async () => {
                return service
                    .deleteUser(spectator.user_id)
                    .should.be.rejectedWith({
                        validation: "User doesn't exist",
                    });
            });

            it('check user with same user can be created', async () => {
                await __createUser(_.omit(spectator, 'user_id'));

                const users = await __findUsersByEmail(spectator.email);

                expect(users.length).to.be.equal(2);
            });

            it('check only one active user can be created', async () => {
                await __createUser(_.omit(spectator, 'user_id')).catch((err) =>
                    expect(err.code).to.be.equal(UNIQUE_RECORD_ERROR_CODE)
                );
            });
        });

        context('_getUser()', function () {
            const user = deleteUserData.spectator;

            before(() => {
                return __createUser(user);
            });

            after(() => {
                return __deleteUsersByEmails([user.email]);
            });

            it('should get user', async () => {
				const result = await service._getUser(user.email);

				expect(result).not.be.empty;
				expect(result.email).to.be.equal(user.email);
			});

			it('should not get soft deleted user', async () => {
				await service.deleteUser(user.user_id);

				const result = await service._getUser(user.email);

				expect(result).to.be.empty;
			});
        });
    });
});

function __deleteUsersByEmails(emails) {
    return Db.query(knex('user').delete().whereIn('email', emails));
}

function __createUser(user) {
    return Db.query(knex('user').insert(user));
}

function __findUsersByEmail(email) {
    return Db.query(knex('user').select().where('email', email)).then(
        ({ rows }) => rows
    );
}
