{
	"compilerOptions": {
		"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": ["ES2023", "DOM", "DOM.Iterable", "ESNext.Array", "ESNext"],
		"module": "ESNext",
		"skipLibCheck": true,
		"baseUrl": ".",
		"paths": {
			"@app/*": ["./src/app/*"],
			"@pages/*": ["./src/pages/*"],
			"@widgets/*": ["./src/widgets/*"],
			"@features/*": ["./src/features/*"],
			"@entities/*": ["./src/entities/*"],
			"@shared/*": ["./src/shared/*"],
		},
		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true
	},
	"include": ["src"],
	"exclude": ["_legacy"],
	"references": [{ "path": "./tsconfig.node.json" }]
}
