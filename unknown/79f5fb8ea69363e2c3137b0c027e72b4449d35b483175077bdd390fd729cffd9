{"name": "esw_web", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=20.0.0", "npm": "10.x"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "format": "prettier --write .", "codegen": "graphql-codegen --config codegen.ts", "fsd-verify": "steiger ./src", "prepare": "husky install"}, "dependencies": {"@apollo/client": "3.13.8", "@preact/signals-core": "1.8.0", "@preact/signals-react": "3.0.1", "@sentry/react": "9.17.0", "@sentry/vite-plugin": "3.4.0", "axios": "1.9.0", "date-fns": "4.1.0", "date-fns-tz": "3.2.0", "dotenv": "16.5.0", "graphql": "16.11.0", "lodash": "4.17.21", "react": "19.1.0", "react-dom": "19.1.0", "react-ga4": "2.1.0", "react-helmet": "6.1.0", "react-router": "7.6.0", "styled-components": "6.1.18", "use-debounce": "10.0.4", "use-query-params": "2.2.1", "uuid": "11.1.0"}, "devDependencies": {"@babel/core": "7.27.1", "@eslint/compat": "1.2.9", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.26.0", "@feature-sliced/steiger-plugin": "0.5.6", "@graphql-codegen/cli": "5.0.6", "@graphql-codegen/client-preset": "4.8.1", "@graphql-codegen/introspection": "4.0.3", "@graphql-codegen/typescript-react-apollo": "4.3.2", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/lodash": "4.17.16", "@types/react": "19.1.3", "@types/react-dom": "^19.1.3", "@types/react-helmet": "^6.1.11", "@types/react-router": "5.1.20", "@typescript-eslint/eslint-plugin": "8.32.0", "@typescript-eslint/parser": "8.32.0", "@vitejs/plugin-react": "4.4.1", "babel-plugin-styled-components": "2.1.4", "eslint": "9.26.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.20", "globals": "^16.1.0", "husky": "8.0.3", "prettier": "3.5.3", "prettier-eslint": "16.4.2", "steiger": "0.5.7", "typescript": "5.8.3", "typescript-eslint": "8.32.0", "vite": "6.3.5"}}