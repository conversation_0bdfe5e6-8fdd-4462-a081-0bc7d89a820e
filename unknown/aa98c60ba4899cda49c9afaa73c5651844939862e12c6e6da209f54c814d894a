

module.exports = {
    /**
     *
     * @api {post} /api/club/event/:event/team/assign Enter Event
     * @apiDescription Assign team on event
     * @apiGroup Club Event Teams
     *
     */
    'post /api/club/event/:event/team/assign': 'Club/RosterTeamController.enterEvent',

    /**
     *
     * @api {post} /api/club/event/:event/team/update Update Team Entry
     * @apiDescription Update team's event entry
     * @apiGroup Club Event Teams
     *
     */
    'post /api/club/event/:event/team/update': 'Club/RosterTeamController.updateEntry',

    /**
     *
     * @api {post} /api/club/event/:event/team/update Members List
     * @apiDescription Roster team members list
     * @apiGroup Club Event Teams
     *
     */
    'get /api/club/event/:event/team/:team/members': 'Club/RosterTeamController.getMembersList',

    /**
     *
     * @api {get} /api/club/event/:event/team/:team/validate-jersey Validate Team Jerseys
     * @apiDescription Checks if all jerseys in team are unique
     * @apiGroup Club Event Teams
     *
     */
    'get /api/club/event/:event/team/:team/validate-jersey': 'Club/RosterTeamController.validateJerseys',

    /**
     *
     * @api {get} /api/club/event/:event/team/:team/validate-aau-jersey Validate Team AAU Jerseys
     * @apiDescription Checks if all aau jerseys in team are unique
     * @apiGroup Club Event Teams
     *
     */
    'get /api/club/event/:event/team/:team/validate-aau-jersey': 'Club/RosterTeamController.validateAAUJerseys',

    /**
     *
     * @api {get} /api/club/event/:event/team/:team/validate-roster Validate Team Roster
     * @apiDescription Validates team's roster
     * @apiGroup Club Event Teams
     *
     */
    'get /api/club/event/:event/team/:team/validate-roster': 'Club/RosterTeamController.validateRoster',

    /**
     *
     * @api {post} /api/club/event/:event/teams/validate-roster Validate Teams' Roster
     * @apiDescription Returns multiple teams' roster validation result
     * @apiGroup Club Event Teams
     *
     */
    'post /api/club/event/:event/teams/validate-roster': 'Club/RosterTeamController.validateTeamsRosters',

    /**
     *
     * @api {get} /api/club/event/:event/teams/assigned Assigned Teams List
     * @apiDescription Returns assigned on event teams list
     * @apiGroup Club Event Teams
     *
     */
    'get /api/club/event/:event/teams/assigned': 'Club/RosterTeamController.assignedTeamsList',

    /**
     *
     * @api {get} /api/club/event/:event/checkin Print Checkin Teams List
     * @apiDescription Prints teams list available for checkin
     * @apiGroup Club Event Teams
     *
     */
    'get /api/club/event/:event/checkin': 'Club/CheckInRosterController.getCLubTeams',
}
