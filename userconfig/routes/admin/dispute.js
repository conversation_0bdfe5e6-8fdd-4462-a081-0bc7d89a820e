module.exports = {
    /**
     *
     * @api {get} /api/admin/v2/dispute Disputes List
     * @apiDescription Returns SW disputes list
     * @apiGroup Admin Dispute
     *
     */
    'GET /api/admin/v2/dispute': { action: 'v2/Admin/dispute/disputes' },

    /**
     *
     * @api {get} /api/admin/v2/dispute Disputes List
     * @apiDescription Returns SW disputes list
     * @apiGroup Admin Dispute
     *
     */
    'GET /api/admin/v2/dispute/export': { action: 'v2/Admin/dispute/export-disputes' },

    /**
     *
     * @api {get} /api/admin/v2/dispute/:purchase Dispute details
     * @apiDescription Returns SW dispute detail
     * @apiGroup Admin Dispute
     *
     */
     'GET /api/admin/v2/dispute/:purchase': { action: 'v2/Admin/dispute/dispute' },

    /**
     * Submits existing evidence with additional data
     * @api {get} /api/admin/v2/dispute/:purchase Dispute evidence submission
     * @apiDescription Returns submitted dispute evidence
     * @apiGroup Admin Dispute
     *
     */
     'POST /api/admin/v2/dispute/:purchase/evidence/submit': { action: 'v2/Admin/dispute/submit-evidence' },

     /**
     * Create dispute evidence, but does not submit it. Fills evidence with all the default values.
     * @api {get} /api/admin/v2/dispute/:purchase Init Dispute Evidence
     * @apiDescription Returns dispute evidence with default values
     * @apiGroup Admin Dispute
     *
     */
      'POST /api/admin/v2/dispute/:purchase/evidence/init': { action: 'v2/Admin/dispute/init-evidence' },

     /**
     *
     * @api {get} /api/admin/v2/dispute/:purchase Dispute evidence details
     * @apiDescription Returns dispute evidence
     * @apiGroup Admin Dispute
     *
     */
      'GET /api/admin/v2/dispute/:purchase/evidence': { action: 'v2/Admin/dispute/evidence' },
}
