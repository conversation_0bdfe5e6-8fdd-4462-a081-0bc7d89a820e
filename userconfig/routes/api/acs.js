module.exports = {

    /**
     *
     * @api {post} /api/acs/events/:event_id/teams/:team_code/pin-code/verify Verify team ACS pin code
     * @apiDescription Checks if event team pin code is valid
     * @apiGroup API Action Clip Streaming
     *
     */
    'POST /api/acs/events/:event_id/teams/:team_code/pin-code/verify': {
        action: 'v2/API/acs/pin-code/verify',
        csrf: false
    },

    /**
     *
     * @api {get} /api/acs/events/:event_id/teams/:team_code/pin-code Get team ACS pin code
     * @apiDescription Returns event team's ACS pin code
     * @apiGroup API Action Clip Streaming
     *
     */
    'GET /api/acs/events/:event_id/teams/:team_code/pin-code': { action: 'v2/API/acs/pin-code/get' },

    /**
     *
     * @api {post} /api/acs/events/:event_id/teams/:team_code/pin-code/regenerate Regenerate team's ACS pin code
     * @apiDescription Returns new event teams ACS pin code
     * @apiGroup API Action Clip Streaming
     *
     */
    'POST /api/acs/events/:event_id/teams/:team_code/pin-code/regenerate': {
        action: 'v2/API/acs/pin-code/regenerate',
        csrf: false
    },

    /**
     *
     * @api {post} /api/acs/events/:event_id/teams/:team_code/pin-code/notify Send ACS pin code
     * @apiDescription Send SMS or email notification with ACS team pin code
     * @apiGroup API Action Clip Streaming
     *
     */
    'POST /api/acs/events/:event_id/teams/:team_code/pin-code/notify': {
        action: 'v2/API/acs/pin-code/notify',
        csrf: false
    },
    
    /**
     *
     * @api {get} /api/acs/events/:event_id/teams Get teams by properties
     * @apiDescription Returns teams in the specified event
     * @apiGroup API Action Clip Streaming
     *
     */
    'GET /api/acs/events/:event_id/teams': { action: 'v2/API/acs/teams/list' },
    
    /**
     *
     * @api {get} /api/acs/events/:event_id/teams/:team_code/schedule Team's schedule
     * @apiDescription Returns event schedule for a specific team
     * @apiGroup API Action Clip Streaming
     *
     */
    'GET /api/acs/events/:event_id/teams/:team_code/schedule': { action: 'v2/API/acs/schedule/team' },

    /**
     *
     * @api {get} /api/acs/events/:event_id/teams/:team_code/schedule Event schedule
     * @apiDescription Returns event schedule
     * @apiGroup API Action Clip Streaming
     *
     */
    'GET /api/acs/events/:event_id/schedule': { action: 'v2/API/acs/schedule/event' },

    /**
     *
     * @api {get} /api/acs/events/:event_id/teams/:team_code Event team
     * @apiDescription Returns event team data
     * @apiGroup API Action Clip Streaming
     *
     */
    'GET /api/acs/events/:event_id/teams/:team_code': { action: 'v2/API/acs/teams/get' }
};
