'use strict';

const ctrl = 'API/ESWController';
const swaggerProj = 'ESW';

let eswRoutes =  {
    /**
     *
     * @api {get} /api/esw/public_sw_events Events List (public)
     * @apiDescription Returns events list (public) - not used in SW
     * @apiGroup ESW
     *
     */
    'GET /api/esw/public_sw_events': 'API/ESWController.public_sw_events',

    /**
     *
     * @api {get} /api/esw/events Events List
     * @apiDescription Returns events list
     * @apiGroup ESW
     *
     */
	'GET /api/esw/events': {
		controller: ctrl,
		action: 'events',
		swagger: {
			tags: ['Event'],
			summary: 'Returns events list',
			description: 
				`**This action returns such events:**
				- Published events *without* schedule
				- Published events *with* schedule`,
			produces: ['application/json'],
			parameters: [],
			responses: {
				200: {
					description: 'A list if event objects',
					schema: {
						type: 'array',
						items: {
							type: 'object',
							properties: {
								event_id 		: { type: ['integer', 'string'] },
								name 			: { type: 'string' },
								long_name 		: { type: 'string' },
								date_start 		: { type: 'string' },
								date_end 		: { type: 'string' },
								timezone 		: { type: 'string' },
								city 			: { type: 'string' },
								state 			: { type: 'string' },
								address 		: { type: 'string' },
								has_coed_teams 	: { type: 'boolean' },
								has_female_teams: { type: 'boolean' },
								has_male_teams 	: { type: 'boolean' },
								email 			: { type: 'string' },
								website 		: { type: 'string' },
								has_athletes 	: { type: 'boolean' },
								has_staff 		: { type: 'boolean' },
								current 		: { type: 'boolean' }
							}
						}
					},
					examples: {
						'application/json': [{
						    event_id: "e76f3bd97",
						    name: "LBS",
						    long_name: "Lil' Big South",
						    date_start: "2015-01-17T05:00:00.000Z",
						    date_end: "2015-01-20T04:59:00.000Z",
						    timezone: "America/New_York",
						    city: "Atlanta",
						    state: "GA",
						    address: "285 Andrew Young Intl Blvd., NW",
						    has_coed_teams: false,
						    has_female_teams: true,
						    has_male_teams: false,
						    email: "<EMAIL>",
						    website: "http://www.lilbigsouth.com",
						    has_athletes: true,
						    has_staff: true,
						    current: false
						}, {
						    event_id: "93db70ec8",
						    name: "DB100",
						    long_name: "Daytona Beach 100",
						    date_start: "2015-01-31T05:00:00.000Z",
						    date_end: "2015-02-02T04:59:59.000Z",
						    timezone: "America/New_York",
						    city: "Daytona Beach",
						    state: "FL",
						    address: "101 North Atlantic Avenue",
						    has_coed_teams: false,
						    has_female_teams: true,
						    has_male_teams: false,
						    email: "<EMAIL>",
						    website: "http://www.jvctournaments.com",
						    has_athletes: true,
						    has_staff: true,
						    current: false
						}]
					}				
				},
				500: {
					description: 'Error response'
				}
			},
			schemes: ['http', 'https']
		}
	},
    /**
     *
     * @api {get} /api/esw/:event Event Info
     * @apiDescription Returns event info
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event': {
    	controller: ctrl,
    	action: 'event',
    	swagger: {
    		tags: ['Event'],
    		summary: 'Returns event info',
    		description: '**Returns event info.**',
    		produces: ['application/json'],
    		parameters: [{
    			name: 'event',
    			in: 'path',
    			description: `**Event Identifier**. Can be either 9-length hash or integer`,
    			required: true,
    			type: 'string'
    		}],
    		responses: {
    			200: {
    				description: 'Event Info object',
    				schema: {
    					type: 'object',
    					properties: {
    						event_id 				: { type: ['integer', 'string'] },
							id 						: { type: 'integer' },
							name 					: { type: 'string' },
							long_name 				: { type: 'string' },
							reg_fee 				: { type: 'integer' },
							date_start 				: { type: 'integer' },
							date_end 				: { type: 'integer' },
							timezone 				: { type: 'string' },
							has_coed_teams 			: { type: 'boolean' },
							has_female_teams		: { type: 'boolean' },
							has_male_teams 			: { type: 'boolean' },
							email 					: { type: 'string' },
							website 				: { type: 'string' },
							has_athletes 			: { type: 'boolean' },
							has_staff 				: { type: 'boolean' },
							city 					: { type: 'string' },
							state 					: { type: 'string' },
							address 				: { type: 'string' },
							schedule_published 		: { type: 'boolean' },
							registration_method 	: { type: 'string' },
							has_match_barcodes 		: { type: 'boolean' },
							social_links 			: { type: 'object' },
							event_notes				: { type: 'string' },
							date_reg_open 			: { type: 'integer' },
							date_reg_close 			: { type: 'integer' },
							roster_deadline 		: { type: 'integer' },
							doubles_reg_available 	: { type: 'boolean' },
							location_name 			: { type: 'string' },
							allow_teams_registration: { type: 'boolean' },
							tickets_code 			: { type: 'string' },
							tickets_published 		: { type: 'boolean' }
    					}
    				},
    				examples: {
    					'application/json': {
							event_id: 'e76f3bd97',
							id: 22,
							name: 'LBS',
							long_name: 'Lil\' Big South',
							reg_fee: 465,
							date_start: 1421452800000,
							date_end: 1421711940000,
							timezone: 'America/New_York',
							has_coed_teams: false,
							has_female_teams: true,
							has_male_teams: false,
							email: '<EMAIL>',
							website: 'http://www.lilbigsouth.com',
							has_athletes: true,
							has_staff: true,
							city: 'Atlanta',
							state: 'GA',
							address: '285 Andrew Young Intl Blvd., NW',
							schedule_published: true,
							registration_method: 'club',
							has_match_barcodes: true,
							social_links: null,
							event_notes: null,
							date_reg_open: 1414368000000,
							date_reg_close: 1420588740000,
							roster_deadline: 1420502400000,
							doubles_reg_available: false,
							location_name: 'Georgia World Congress Center',
							allow_teams_registration: true,
							tickets_code: '257979151',
							tickets_published: false
						}
    				}
    			},
    			500: {
    				description: 'Error response'
    			}
    		},
    		schemes: ['http', 'https']
    	}
    },
    /**
     *
     * @api {get} /api/esw/:event/divisions Event Divisions
     * @apiDescription Returns a list of divisions for specified even
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/divisions': {
    	controller: ctrl,
    	action: 'divisions',
    	swagger: {
	    	tags: ['Division'],
	    	summary: 'Returns a list of divisions for specified event',
	    	description: 
	    		`Return a list of divisions for specified event. 
	    		 Available *only* by 9-length hash`,
	    	produces: ['application/json'],
	    	parameters: [{
	    		$ref: '#/parameters/esw_id'
	    	}],
	    	responses: {
	    		200: {
	    			description: 'A List of Division objects',
	    			schema: {
	    				type: 'array',
	    				items: {
	    					type: 'object',
	    					properties: {
	    						event_id 		: { type: 'string', description: '9-length hash' },
							    name 			: { type: 'string' },
							    short_name 		: { type: 'string' },
							    max_teams 		: { type: 'number' },
							    division_id 	: { type: 'number' },
							    gender 			: { $ref: '#/definitions/gender' },
							    event_name 		: { type: 'string' },
							    teams_count 	: { type: 'number' },
							    has_flow_chart 	: { type: 'number' }
	    					}
	    				}
	    			},
	    			examples: {
	    				'application/json': [{
						    "event_id": "e76f3bd97",
						    "name": "15 Open",
						    "short_name": "15O",
						    "max_teams": 12,
						    "division_id": 183,
						    "gender": "female",
						    "event_name": "LBS",
						    "teams_count": 12,
						    "has_flow_chart": 0
						}, {
						    "event_id": "e76f3bd97",
						    "name": "15 Club",
						    "short_name": "15C",
						    "max_teams": 24,
						    "division_id": 182,
						    "gender": "female",
						    "event_name": "LBS",
						    "teams_count": 24,
						    "has_flow_chart": 0
						}]
	    			}
	    		},
	    		500: {
	    			description: 'Error response'
	    		}
	    	},
	    	schemes: ['http', 'https']
    	}
    },
    /**
     *
     * @api {get} /api/esw/:event/divisions/:division/teams Division Teams
     * @apiDescription Returns a list of event teams filtered by division
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/divisions/:division/teams': {
    	controller: ctrl,
    	action: 'division_teams',
    	swagger: {
    		tags: ['Team'],
    		summary: 'Returns a list of event teams filtered by division',
    		description: 'A list of event teams in specified division',
    		produces: ['application/json'],
    		parameters: [{
	    		$ref: '#/parameters/esw_id'
	    	}, {
	    		name: 'division',
	    		in: 'path',
	    		description: 'division identifier',
	    		required: true,
	    		type: 'integer'
	    	}],
	    	responses: {
	    		200: {
	    			description: 'A List of division team objects',
	    			schema: {
	    				type: 'array',
	    				items: {
	    					type: 'object',
	    					properties: {
	    						roster_team_id 		: { type: 'integer' },
								organization_code 	: { type: 'string' },
								team_name 			: { type: 'string' },
								short_name 			: { type: 'string' },
								state 				: { type: 'string' },
								club_name 			: { type: 'string' }
	    					}
	    				}
	    			},
	    			examples: {
	    				'application/json': [{
							roster_team_id: 2319,
							organization_code: "FJ5CAJVB1SO",
							team_name: "Cobb Atlanta 15-1 Kortney",
							short_name: "15O",
							state: "GA",
							club_name: "Cobb Atlanta"
						}, {
							roster_team_id: 3188,
							organization_code: "FJ5TSUNA1SO",
							team_name: "Tsunami 15-Morgann",
							short_name: "15O",
							state: "GA",
							club_name: "Tsunami Volleyball"
						}]
	    			}
	    		},
	    		500: {
	    			description: 'Error response'
	    		}
	    	},
	    	schemes: ['http', 'https']
    	}
    },

    /**
     *
     * @api {get} /api/esw/:event/divisions/:division/pools Division Pools
     * @apiDescription List of pools with a teams sublist
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/divisions/:division/pools': {
    	controller: ctrl,
    	action: 'division_pools',
    	swagger: {
    		tags: ['Pool', 'Team'],
    		summary: 'Returns a list of pools for specified division with a sublist of teams',
    		description: `List of pools with a teams sublist.`,
    		produces: ['application/json'],
    		parameters: [{
	    		$ref: '#/parameters/esw_id'
	    	}, {
	    		name: 'division',
	    		in: 'path',
	    		description: 'division identifier',
	    		required: true,
	    		type: 'integer'
	    	}],
	    	responses: {
	    		200: {
	    			description: 'A list of pools with sublists of teams',
	    			schema: {
	    				type: 'array',
	    				items: {
	    					type: 'object',
	    					properties: {
	    						r_uuid 				: { $ref: '#/definitions/round_uuid' },
								r_sort_priority 	: { type: 'integer' },
								sort_priority 		: { type: 'integer' },
								rr_sort_priority 	: { type: 'integer' },
								r_name 				: { type: 'string' },
								r_short_name 		: { type: 'string' },
								is_pool 			: { type: 'integer' },
								pb_name 			: { type: 'string' },
								pb_short_name 		: { type: 'string' },
								display_name 		: { type: 'string' },
								team_count 			: { type: 'integer' },
								uuid 				: { type: 'string' },
								settings 			: { type: 'string' },
								rr_name 			: { type: 'string' },
								rr_short_name 		: { type: 'string' },
								division_short_name : { type: 'string' },
								date_start 			: { type: 'string' },
								court_start 		: { type: 'string' },
								teams: {
									type: 'array',
									items: {
										type: 'object',
										properties: {
											opponent_team_name 			: { type: 'string' },
											opponent_organization_code 	: { type: 'string' },
											opponent_team_id 			: { type: 'integer' },
											info 						: { type: 'string' },
											rank 						: { type: 'string' }
										}
									}
								}
							}
	    				}
	    			},
	    			examples: {
		    			'application/json': [{
							r_uuid: "cf4098d6-7ad4-4142-9586-8b23008e17c6",
							r_sort_priority: 1,
							sort_priority: 1,
							rr_sort_priority: null,
							r_name: "Round 1",
							r_short_name: "R1",
							is_pool: 1,
							pb_name: "Pool 1",
							pb_short_name: "P1",
							display_name: "R1P1",
							team_count: 4,
							uuid: "0ec6d448-d641-419f-aff8-3447821889b1",
							settings: "",
							rr_name: null,
							rr_short_name: null,
							division_short_name: "15O",
							date_start: "1421505000000",
							court_start: "Ct 8",
							teams: [{
								opponent_team_name: "Cobb Atlanta 15-2 Amy",
								opponent_organization_code: "FJ5CAJVB2SO",
								opponent_team_id: 2320,
								info: null,
								rank: null
							}]
						}]
		    		}
	    		},
	    		500: {
	    			description: 'Error response'
	    		}	    	
	    	},
	    	schemes: ['http', 'https']
    	}
    },
    /**
     *
     * @api {get} /api/esw/:event/divisions/:division/standings Division Standings
     * @apiDescription List of division standings
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/divisions/:division/standings': {
    	controller: ctrl,
    	action: 'division_standings'
    },

    /**
     *
     * @api {get} /api/esw/:event/pool/:pool/standings Pool Standings
     * @apiDescription List of pool standings
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/pool/:pool/standings': 'API/ESWController.pool_standings',

    /**
     *
     * @api {get} /api/esw/:event/clubs Clubs List
     * @apiDescription Returns list of clubs
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/clubs': 'API/ESWController.clubs',

    /**
     *
     * @api {get} /api/esw/:event/clubs/:club Club Teams
     * @apiDescription Returns list of club teams
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/clubs/:club': 'API/ESWController.club_teams',

    /**
     *
     * @api {get} /api/esw/:event/athletes Team Athletes
     * @apiDescription Returns list of team athletes
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/athletes': 'API/ESWController.athlete_teams',

    /**
     *
     * @api {get} /api/esw/:event/staff Team Staff
     * @apiDescription Returns list of team staffers
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/staff': 'API/ESWController.staff_teams',

    /**
     *
     * @api {get} /api/esw/:event/teams Event Teams
     * @apiDescription Returns list of event's teams
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/teams': 'API/ESWController.division_teams',

    /**
     *
     * @api {get} /api/esw/:event/teams/:team Team Info
     * @apiDescription Returns specific team data
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/teams/:team': 'API/ESWController.team',

    /**
     *
     * @api {get} /api/esw/:event/courts Courts List
     * @apiDescription Returns event courts list
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/courts': 'API/ESWController.courts',

    /**
     *
     * @api {get} /api/esw/:event/courts Courts Details
     * @apiDescription Returns specific court details
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/courts/:court': 'API/ESWController.court_details',

    /**
     *
     * @api {get} /api/esw/:event/matches/:match Match Info
     * @apiDescription Returns specific match data
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/matches/:match': 'API/ESWController.match',

    /**
     *
     * @api {get} /api/esw/:event/pools/:pool Pool Info
     * @apiDescription Returns specific pool data
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/pools/:pool': 'API/ESWController.pool',

    /**
     *
     * @api {get} /api/esw/:event/prevqual Prev Qualified Teams List
     * @apiDescription Returns previously qualified teams list
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/prevqual': 'API/ESWController.getPrevqualTeamsList',

    /**
     *
     * @api {get} /api/esw/:event/brackets/:bracket/matches Bracket Matches
     * @apiDescription Returns specific bracket matches list
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/brackets/:bracket/matches': 'API/ESWController.bracket_matches',

    /**
     *
     * @api {get} /api/esw/:event/courts_matches/:day/hour/:hour/hours/:hours/:division Court Matches
     * @apiDescription Returns specific court matches list
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/courts_matches/:day/hour/:hour/hours/:hours/:division': 'API/ESWController.courts_matches',

    /**
     *
     * @api {get} /api/esw/pool/standings Pool Standings (Deprecated)
     * @apiDescription Returns specific pool standings list
     * @apiGroup ESW
     *
     */
    'GET /api/esw/pool/standings':                       'API/ESWController.pool_standings1',

    /**
     *
     * @api {get} /api/esw/pb_for_match/:uuid Poolbracket for Match
     * @apiDescription Gets poolbracket match data
     * @apiGroup ESW
     *
     */
    'GET /api/esw/pb_for_match/:uuid':                   'API/ESWController.pb_for_match',

    /**
     *
     * @api {get} /api/esw/:event/manual-clubs Manual Clubs List
     * @apiDescription Gets manual clubs list
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/manual-clubs':                  'API/ESWController.manualClubsList',

    /**
     *
     * @api {get} /api/esw/:event/manual-club-teams Manual Clubs Teams List
     * @apiDescription Gets manual club teams list
     * @apiGroup ESW
     *
     */
    'GET /api/esw/:event/manual-club-teams':             'API/ESWController.manualClubTeamsList',

    /**
     *
     * @api {get} /api/esw/club/event/:event/info Event Info (SW Home popup)
     * @apiDescription Gets event info for SW Home popup
     * @apiGroup ESW
     *
     */
    'GET /api/esw/club/event/:event/info': 'API/ESWController.get_info',

    /**
     *
     * @api {get} /api/esw/club/event/:event/divisions_list Team Divisions List (SW Home popup)
     * @apiDescription Gets divisions and teams
     * @apiGroup ESW
     *
     */
    'GET /api/esw/club/event/:event/divisions_list': 'API/ESWController.roster_for_event',

    /**
     *
     * @api {get} /api/esw/club/event/:event/division/:division/teams_list Team Roster List (SW Home popup)
     * @apiDescription Gets roster for teams
     * @apiGroup ESW
     *
     */
    'GET /api/esw/club/event/:event/division/:division/teams_list': 'API/ESWController.teams_for_event'
};

Object.keys(eswRoutes).forEach(route => {
	let routeDef = eswRoutes[route];

	if(routeDef.swagger) {
		routeDef.swagger.project = swaggerProj;
	}
});

module.exports = eswRoutes;

// 'GET /api/esw/:event/athletes/:athlete':             'API/ESWController.division_teams',
// 'GET /api/esw/:event/staff/:staff':                  'API/ESWController.staff_teams',
