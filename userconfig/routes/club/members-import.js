
const CTRL 	= 'Club/ClubController.';

module.exports = {
    /**
     *
     * @api {post} /api/club/roster-import Foreign Roster Import
     * @apiDescription Imports XLSX/CSV file with roster for foreign clubs
     * @apiGroup Club Members Import
     *
     */
    'POST /api/club/roster-import': `${CTRL}foreignRosterImport`,

    /**
     *
     * @api {post} /api/club/import/:provider(webpoint|sportengine|aau) SportEngine / Webpoint / AAU import
     * @apiDescription Imports club members from SportEngine or Webpoint or AAU
     * @apiGroup Club Members Import
     *
     */
    'POST /api/club/import/:provider(webpoint|sportengine|aau)': `${CTRL}import`,

    /**
     *
     * @api {get} /api/club/webpoint/import Webpoint Import Init Data
     * @apiDescription Gets information for Webpoint import initiation
     * @apiGroup Club Members Import
     *
     */
    'GET /api/club/webpoint/import': `${CTRL}wp_data`,

    /**
     *
     * @api {get} /api/club/sportengine/import SportEngine Import Init Data
     * @apiDescription Gets information for SportEngine import initiation
     * @apiGroup Club Members Import
     *
     */
    'GET /api/club/sportengine/import': `${CTRL}sportEngineImportData`,

    /**
     *
     * @api {get} /api/club/aau/import AAU Import Init Data
     * @apiDescription Gets information for AAU import initiation
     * @apiGroup Club Members Import
     *
     */
    'GET /api/club/aau/import': `${CTRL}aauImportData`,
}
