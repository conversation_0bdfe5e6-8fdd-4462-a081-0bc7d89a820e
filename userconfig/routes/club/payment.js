'use strict';

const CTRL = 'Club/PaymentController.';

module.exports = {

    /**
     *
     * @api {post} /api/club/purchase Pay for Team
     * @apiDescription Creates Club payment for team
     * @apiGroup Club Payment
     *
     */
	'POST /api/club/purchase': `${CTRL}save`,

    /**
     *
     * @api {delete} /api/club/event/:event/purchase/:purchase/cancel Cancel Payment
     * @apiDescription Cancels team club payment
     * @apiGroup Club Payment
     *
     */
    'DELETE /api/club/event/:event/purchase/:purchase/cancel': `${CTRL}cancel`,

    /**
     *
     * @api {get} /api/club/event/:event/purchase-list Payments List
     * @apiDescription Club event payments list
     * @apiGroup Club Payment
     *
     */
    'GET /api/club/event/:event/purchase-list': `${CTRL}clubPayments`,

    /**
     *
     * @api {get} /api/club/event/:event/teams/to_pay To Pay Tams List
     * @apiDescription Teams require payment list
     * @apiGroup Club Payment
     *
     */
    'GET /api/club/event/:event/teams/to_pay': `${CTRL}getTeamsPaymentData`,

    /**
     *
     * @api {get} /api/club/event/:event/purchase/:purchase Get Payment
     * @apiDescription Returns existing club payment
     * @apiGroup Club Payment
     *
     */
    'GET /api/club/event/:event/purchase/:purchase': {
        controller  : 'Club/PaymentController',
        action      : 'getPayment',
        skipAssets  : 'true',
        swagger     : {
            summary: 'Returns existing club payment',
            description: 'Lorem ipsum dolor sit amet',
            produces: ['application/json'],
            tags: ['Club/PaymentController'],
            parameters: [{
                in: 'path',
                name: 'event',
                description: 'Event Identifier',
                required: true,
                type: 'integer'
            }, {
                in: 'path',
                name: 'purchase',
                description: 'Purchase Identifier',
                required: true,
                type: 'integer'
            }],
            responses: {
                '200': {
                    description: 'Empty successful response',
                    schema: {}
                }
            }
        }
    },
    /**
     *
     * @api {put} /api/club/event/:event/purchase/:purchase/change-type Change Payment Type
     * @apiDescription Changes payment type for pending check payments
     * @apiGroup Club Payment
     *
     */
    'put /api/club/event/:event/purchase/:purchase/change-type' : {
        controller 	: 'Club/PaymentController',
        action 		: 'changePaymentType',
        skipAssets 	: 'true',
        swagger 	: {
            summary: 'Changes payment type for pending check payments',
            description: '-',
            produces: ['application/json'],
            project: 'Test',
            parameters: [{
            	in: 'path',
				name: 'event',
				description: 'Event Identifier',
				required: true,
				type: 'integer'
			}, {
            	in: 'path',
				name: 'purchase',
				description: 'Purchase Identifier',
				required: true,
				type: 'integer'
			}],
            responses: {
                '200': {
                    description: 'Empty successful response',
                    schema: {}
                }
            }
        }
    }
};
