module.exports = {
    /**
     *
     * @api {post} /api/v2/club/purchase Create Payment
     * @apiDescription Creates payment intent
     * @apiGroup Club Team Payment V2
     *
     */
    'post /api/v2/club/purchase': 'Club/PaymentV2Controller.createPayment',

    /**
     *
     * @api {put} /api/v2/club/purchase Update Payment
     * @apiDescription Verifies and updates payment intent before confirmation
     * @apiGroup Club Team Payment V2
     *
     */
    'put /api/v2/club/purchase': 'Club/PaymentV2Controller.updatePayment',

    /**
     *
     * @api {put} /api/v2/club/purchase/change-type Change payment type
     * @apiDescription Changes payment type from check to ach | card
     * @apiGroup Club Team Payment V2
     *
     */
     'put /api/v2/club/purchase/change-type': 'Club/PaymentV2Controller.changePaymentType',


    /**
     *
     * @api {delete} /api/v2/club/purchase Remove Payment Session
     * @apiDescription Tries to remove a payment session
     * @apiGroup Club Team Payment V2
     *
     */
    'delete /api/v2/club/purchase': 'Club/PaymentV2Controller.removeNotFinishedPayment',
}
