

module.exports = {
    /**
     *
     * @api {get} /api/sports SW Sports
     * @apiDescription Returns all sport types on SW
     * @apiGroup Common
     *
     */
    'GET /api/sports': 'EventController.allSports',

    /**
     *
     * @api {get} /api/countries SW Countries
     * @apiDescription Returns all countries on SW
     * @apiGroup Common
     *
     */
    'GET /api/countries': 'GeoController.get_countries',

    /**
     *
     * @api {get} /api/states SW States
     * @apiDescription Returns all US states on SW
     * @apiGroup Common
     *
     */
    'GET /api/states': 'GeoController.get_states',

    /**
     *
     * @api {get} /api/regions US USAV Regions
     * @apiDescription Returns all US USAV Regions
     * @apiGroup Common
     *
     */
    'GET /api/regions': 'GeoController.get_regions',

    /**
     *
     * @api {get} /api/sport_variation SW Sport Variations
     * @apiDescription Returns all sport variations
     * @apiGroup Common
     *
     */
    'get /api/sport_variation': 'SportVariationController.find',

    /**
     *
     * @api {get} /api/sport_sanctioning SW Sport Sanctionings
     * @apiDescription Returns all sport sanctionings
     * @apiGroup Common
     *
     */
    'get /api/sport_sanctioning': 'SportSanctioningController.find',

    /**
     *
     * @api {get} /api/csrfToken CSRF Token
     * @apiDescription Returns CSRF Token
     * @apiGroup Common
     *
     */
    'GET /api/csrfToken': { action: 'security/grant-csrf-token' }
}
