
module.exports = {
    /**
     *
     * @api {get} /api/event/:event/club-invoice/clubs Get Active Clubs List
     * @apiDescription Returns active clubs list for a specific event
     * @apiGroup Event Club Invoices
     *
     */
    'get /api/event/:event/club-invoice/clubs': { action: 'v2/event/club-invoice/clubs-list' },

    /**
     *
     * @api {post} /api/event/:event/club-invoice Create Club Invoice
     * @apiDescription Creates club invoice for a specific event
     * @apiGroup Event Club Invoices
     *
     */
    'post /api/event/:event/club-invoice': { action: 'v2/event/club-invoice/invoice/create-invoice'},

    /**
     *
     * @api {get} /api/event/:event/club-invoice Get Club Invoice List
     * @apiDescription Returns club invoices list for a specific event
     * @apiGroup Event Club Invoices
     *
     */
    'get /api/event/:event/club-invoice': { action: 'v2/event/club-invoice/invoice/list'},

    /**
     *
     * @api {get} /api/event/:event/club-invoice/:invoice Get Club Invoice Details
     * @apiDescription Returns details for a specific club invoice
     * @apiGroup Event Club Invoices
     *
     */
    'get /api/event/:event/club-invoice/:invoice': { action: 'v2/event/club-invoice/invoice/details'},

    /**
     *
     * @api {post} /api/event/:event/club-invoice/:invoice/refund/full Club Invoice Refund
     * @apiDescription Makes a full refund for Club Invoice
     * @apiGroup Event Club Invoices
     *
     */
    'post /api/event/:event/club-invoice/:invoice/refund/full': { action: 'v2/event/club-invoice/invoice/full-refund'},

    /**
     *
     * @api {post} /api/event/:event/club-invoice/:invoice/refund/full Club Invoice Cancel
     * @apiDescription Makes a Club Invoice canceled
     * @apiGroup Event Club Invoices
     *
     */
    'post /api/event/:event/club-invoice/:invoice/cancel': { action: 'v2/event/club-invoice/invoice/cancel-invoice'},
};
