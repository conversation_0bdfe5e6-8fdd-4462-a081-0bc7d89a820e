
module.exports = {
    /**
     *
     * @api {get} /api/custom-form-builder/event/:event/form Get Event Custom Forms List
     * @apiDescription Returns specified by event custom forms list
     * @apiGroup Event Custom Form Builder
     *
     */
    'get /api/custom-form-builder/event/:event/form': { action: 'v2/event/custom-form/builder/list' },

    /**
     *
     * @api {get} /api/custom-form-builder/event/:event/form/:form_id Get Event Custom Form Data
     * @apiDescription Returns specified by event and form ID custom form for editing
     * @apiGroup Event Custom Form Builder
     *
     */
    'get /api/custom-form-builder/event/:event/form/:form_id': { action: 'v2/event/custom-form/builder/get-form' },

    /**
     *
     * @api {put} /api/custom-form-builder/event/:event/form:form_id Update Event Custom Form Data
     * @apiDescription Updates specified by event and form ID custom form
     * @apiGroup Event Custom Form Builder
     *
     */
    'put /api/custom-form-builder/event/:event/form/:form_id': { action: 'v2/event/custom-form/builder/update-form' },

    /**
     *
     * @api {post} /api/custom-form-builder/event/:event/form Create Event Custom Form Data
     * @apiDescription Creates specified by event custom form
     * @apiGroup Event Custom Form Builder
     *
     */
    'post /api/custom-form-builder/event/:event/form': { action: 'v2/event/custom-form/builder/create-form' },

    /**
     *
     * @api {delete} /api/custom-form/event/:event/form/:form_id Delete Event Custom Form
     * @apiDescription Deletes specified by event custom form
     * @apiGroup Event Custom Form Builder
     *
     */
    'delete /api/custom-form-builder/event/:event/form/:form_id': {
        action: 'v2/event/custom-form/builder/delete-form'
    },

    /**
     *
     * @api {post} /api/custom-form/event/:event/form/:form_id Create Event Custom Form Field Data
     * @apiDescription Creates specified by event custom form field
     * @apiGroup Event Custom Form Builder
     *
     */
    'post /api/custom-form-builder/event/:event/form/:form_id/field': {
        action: 'v2/event/custom-form/builder/field/create'
    },

    /**
     *
     * @api {put} /api/custom-form/event/:event/form/:form_id/field/:field_id Update Event Custom Form Field Data
     * @apiDescription Updates specified by event custom form field
     * @apiGroup Event Custom Form Builder
     *
     */
    'put /api/custom-form-builder/event/:event/form/:form_id/field/:field_id': {
        action: 'v2/event/custom-form/builder/field/update'
    },

    /**
     *
     * @api {delete} /api/custom-form/event/:event/form/:form_id/field/:field_id Delete Event Custom Form Field
     * @apiDescription Deletes specified by event custom form field
     * @apiGroup Event Custom Form Builder
     *
     */
    'delete /api/custom-form-builder/event/:event/form/:form_id/field/:field_id': {
        action: 'v2/event/custom-form/builder/field/delete'
    },

    /**
     *
     * @api {put} /api/custom-form/event/:event/form/:form_id/field/:field_id Custom Event Form Fields Order Changing
     * @apiDescription Change custom form field order a specific event form
     * @apiGroup Event Custom Form Builder
     *
     */
    'put /api/custom-form-builder/event/:event/form/:form_id/fields-order': {
        action: 'v2/event/custom-form/builder/field/update-fields-order'
    },

}
