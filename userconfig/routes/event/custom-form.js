
module.exports = {
    /**
     *
     * @api {get} /api/custom-form/event/:event/form/:form_id Get Event Custom Form
     * @apiDescription Returns specified by event and form type custom form
     * @apiGroup Event Custom Form
     *
     */
    'get /api/custom-form/event/:event/form/:form_id': {
        action: 'v2/event/custom-form/submitting/form',
        csrf: false
    },

    /**
     *
     * @api {post} /api/custom-form/event/:event/form/:form_id Get Event Custom Form submit
     * @apiDescription Submits specified by event and form type custom form
     * @apiGroup Event Custom Form
     *
     */
    'post /api/custom-form/event/:event/form/:form_id': {
        action: 'v2/event/custom-form/submitting/save',
        csrf: false
    },

    /**
     *
     * @api {get} /api/custom-form/event/:event/form/:form_id/export Get Event Custom Form Results (XLSX)
     * @apiDescription Returns XLSX file with results specified by event and form type
     * @apiGroup Event Custom Form
     *
     */
    'get /api/custom-form/event/:event/form/:form_id/export': {
        action: 'v2/event/custom-form/submitting/export-results'
    },
}
