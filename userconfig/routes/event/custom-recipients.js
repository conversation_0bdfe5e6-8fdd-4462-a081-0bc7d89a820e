'use strict';

const CTRL = 'Event/AEM/CustomRecipientsController.';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/aem/custom-recipients Event Custom Recipients Lists
     * @apiDescription Returns all custom recipients lists on event
     * @apiGroup Event Custom Recipients
     *
     */
    'GET /api/event/:event/aem/custom-recipients': `${CTRL}list`,

    /**
     *
     * @api {get} /api/event/:event/aem/custom-recipients/:list Event Custom Recipients List
     * @apiDescription Returns specific custom recipients list
     * @apiGroup Event Custom Recipients
     *
     */
    'GET /api/event/:event/aem/custom-recipients/:list': `${CTRL}find`,

    /**
     *
     * @api {put} /api/event/:event/aem/custom-recipients/:list/title Event Custom Recipients Title Change
     * @apiDescription Updates custom recipients list title
     * @apiGroup Event Custom Recipients
     *
     */
    'PUT /api/event/:event/aem/custom-recipients/:list/title': `${CTRL}updateTitle`,

    /**
     *
     * @api {delete} /api/event/:event/aem/custom-recipients/:list Event Custom Recipients Removing
     * @apiDescription Removes custom recipients list
     * @apiGroup Event Custom Recipients
     *
     */
    'DELETE /api/event/:event/aem/custom-recipients/:list': `${CTRL}delete`,

    /**
     *
     * @api {post} /api/event/:event/aem/custom-recipients/:list/recipient Event Custom Recipient Addition
     * @apiDescription Add recipient to the custom list
     * @apiGroup Event Custom Recipients
     *
     */
    'POST /api/event/:event/aem/custom-recipients/:list/recipient': `${CTRL}addRecipient`,

    /**
     *
     * @api {put} /api/event/:event/aem/custom-recipients/:list/recipient/:recipient Event Custom Recipient Update
     * @apiDescription Updates custom list recipient
     * @apiGroup Event Custom Recipients
     *
     */
    'PUT /api/event/:event/aem/custom-recipients/:list/recipient/:recipient': `${CTRL}updateRecipient`,

    /**
     *
     * @api {delete} /api/event/:event/aem/custom-recipients/:list/recipient/:recipient Event Custom Recipient Removing
     * @apiDescription Removes recipient from custom list
     * @apiGroup Event Custom Recipients
     *
     */
    'DELETE /api/event/:event/aem/custom-recipients/:list/recipient/:recipient': `${CTRL}deleteRecipient`,

    /**
     *
     * @api {post} /api/event/:event/aem/custom-recipients/:list? Event Custom Recipients List Upsert
     * @apiDescription Creates or update custom recipients list
     * @apiGroup Event Custom Recipients
     *
     */
    'POST /api/event/:event/aem/custom-recipients/:list?': `${CTRL}create`
};
