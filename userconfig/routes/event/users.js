'use strict';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/users Event Users List
     * @apiDescription Returns event users list
     * @apiGroup Event Users
     *
     */
	'GET /api/event/:event/users': 'Event/EventUserController.all',

    /**
     *
     * @api {get} /api/event/:event/users/finds Event co-Owners List
     * @apiDescription Returns event co-Owner users list
     * @apiGroup Event Users
     *
     */
	'GET /api/event/:event/users/find': 'Event/EventUserController.findEligibleUsers',

    /**
     *
     * @api {delete} /api/event/:event/user Delete User from Event
     * @apiDescription Marks user as deleted on event (removes all permissions)
     * @apiGroup Event Users
     *
     */
	'DELETE /api/event/:event/user': 'Event/EventUserController.markAsDeleted',

    /**
     *
     * @api {post} /api/event/:event/user/add Add User to Event
     * @apiDescription Adds user to event co-Owners list using email search
     * @apiGroup Event Users
     *
     */
	'POST /api/event/:event/user/add': 'Event/EventUserController.addUserByEmail',

    /**
     *
     * @api {get} /api/user-permissions User Permissions List
     * @apiDescription Returns a specific user event permissions list
     * @apiGroup Event Users
     *
     */
    'GET /api/user-permissions': 'Event/EventUserController.getUserPermissions',

    /**
     *
     * @api {get} /api/user-permissions/tree User Permissions List with children
     * @apiDescription Returns a specific user event permissions list with children
     * @apiGroup Event Users
     *
     */
    'GET /api/user-permissions/tree': 'Event/EventUserController.getUserPermissionsTree',

    /**
     *
     * @api {put} /api/event/:event/user Update User Permissions
     * @apiDescription Updates a specific user event permissions
     * @apiGroup Event Users
     *
     */
    'PUT /api/event/:event/user': 'Event/EventUserController.updateUserPermissions'
};


