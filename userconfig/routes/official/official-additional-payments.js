'use strict';

const CTRL = 'Event/EventOfficialsAdditionalPaymentsController';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/officials/additional-payment/categories Categories List
     * @apiDescription Returns additional payments categories list
     * @apiGroup Official Additional Payments
     *
     */
    'GET /api/event/:event/officials/additional-payment/categories': `${CTRL}.getCategories`,

    /**
     *
     * @api {put} /api/event/:event/officials/additional-payment/set-defaults Set Default Categories List
     * @apiDescription Sets default additional payments categories list
     * @apiGroup Official Additional Payments
     *
     */
    'PUT /api/event/:event/officials/additional-payment/set-defaults': `${CTRL}.setDefaults`,

    /**
     *
     * @api {put} /api/event/:event/type/:type/additional-payment/update Update additional payment
     * @apiDescription Updates additional payment for Official/Staff
     * @apiGroup Official Additional Payments
     *
     */
    
    'PUT /api/event/:event/type/:type/additional-payment/update': `${CTRL}.update`,
};
