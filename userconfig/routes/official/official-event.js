

module.exports = {
    /**
     *
     * @api {get} /api/official/events Events List
     * @apiDescription Returns allowed for assign for official events
     * @apiGroup Official Events
     *
     */
    'get /api/official/events': 'Official/EventController.index',

    /**
     *
     * @api {post} /api/official/event/:event/check_in Check-In
     * @apiDescription Checkins official on events
     * @apiGroup Official Events
     *
     */
    'post /api/official/event/:event/check_in': 'Official/EventController.check_in',

    /**
     *
     * @api {delete} /api/official/event/:event/check_out Check Out
     * @apiDescription Check out official from events
     * @apiGroup Official Events
     *
     */
    'delete /api/official/event/:event/check_out': 'Official/EventController.check_out',

    /**
     *
     * @api {get} /api/official/event/:event/checked/data Registration Data
     * @apiDescription Return official's registration data on event
     * @apiGroup Official Events
     *
     */
    'get /api/official/event/:event/checked/data': 'Official/EventController.show_checked_data',

    /**
     *
     * @api {update} /api/official/event/:event/update Registration Data Update
     * @apiDescription Updates official's registration data on event
     * @apiGroup Official Events
     *
     */
    'put /api/official/event/:event/update': 'Official/EventController.update',

    /**
     *
     * @api {get} /api/official/event/:event/assignments Event Schedule Data
     * @apiDescription Return events schedule data for a specific
     * @apiGroup Official Events
     *
     */
    'GET /api/official/event/:event/assignments': 'Official/EventController.assignments',

    /**
     *
     * @api {get} /api/official/events/assignments Events Schedule Data
     * @apiDescription Return events schedule data events
     * @apiGroup Official Events
     *
     */
    'GET /api/official/events/assignments': 'Official/EventController.upcomingAssignments',
    
    /**
     *
     * @api {get} /api/staff/events Staff Events
     * @apiDescription Return staff events
     * @apiGroup Official Events
     *
     */
    'GET /api/staff/events': 'Official/EventController.staffEvents',
    
    /**
     *
     * @api {put} /api/official/event/:event/participation Confirm Participation
     * @apiDescription Confirms participation on event
     * @apiGroup Official Events
     *
     */
    'PUT /api/official/event/:event/participation': 'Official/EventController.confirmParticipation',
    'PUT /api/official/event/:event/send-entry-qrcodes': 'Official/EventController.sendEntryQRCodes',

    /**
     *
     * @api {get} /api/official/event/:event/entry-qr-code/:barcode Official Barcode
     * @apiDescription Gets entry qr code for official
     * @apiGroup Official Events
     *
     */
    'GET /api/official/event/:event/entry-qr-code/:barcode': 'Official/EventController.showEntryQRCode',
}
