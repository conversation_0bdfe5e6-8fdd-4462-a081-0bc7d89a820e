

module.exports = {
    /**
     *
     * @api {post} /api/sales/sponsor Create Sponsor
     * @apiDescription Creates new sponsor
     * @apiGroup Sales
     *
     */
    'POST /api/sales/sponsor': 'SalesManager/SponsorController.create',

    /**
     *
     * @api {get} /api/sales/sponsors Sponsors List
     * @apiDescription Returns sponsors list created by specific sales
     * @apiGroup Sales
     *
     */
    'GET /api/sales/sponsors': 'SalesManager/SponsorController.index',

    /**
     *
     * @api {get} /api/sales/sponsors-short Sponsors List (short data)
     * @apiDescription Returns sponsors list created by specific sales (short sponsors data)
     * @apiGroup Sales
     *
     */
    'GET /api/sales/sponsors-short': 'SalesManager/SponsorController.shortList',

    /**
     *
     * @api {get} /api/sales/event/:event/sponsor/:sponsor/info Sponsor Info
     * @apiDescription Returns specific sponsor info
     * @apiGroup Sales
     *
     */
    'GET /api/sales/event/:event/sponsor/:sponsor/info': 'SalesManager/SponsorController.find',

    /**
     *
     * @api {get} /api/sales/exhibitors/export Exhibitors export
     * @apiDescription Exports exhibitors list to XLSX file
     * @apiGroup Sales
     *
     */
    'GET /api/sales/exhibitors/export': 'SalesManager/SponsorController.export',

    /**
     *
     * @api {get} /api/sales/all Sales List
     * @apiDescription Returns all sales list
     * @apiGroup Sales
     *
     */
    'GET /api/sales/all': 'SalesManagerController.index',
    //'DELETE /api/sales/sponsor/:sponsor/remove': 'SalesManager/SponsorController.remove',
}
