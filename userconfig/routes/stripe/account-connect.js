

module.exports = {
    /**
     *
     * @api {get} /stripe/redirect_url Stripe Connect Redirect Handler
     * @apiDescription Handles stripe connected account after finishing account connection to SW Stripe platform
     * @apiGroup Stripe Account Connect
     *
     */
    'get /stripe/redirect_url': 'StripeController.connectHandler',

    /**
     *
     * @api {get} /api/stripe/upd-availability Stripe Connect Charge Availability
     * @apiDescription Checks if charge available on the destination account
     * @apiGroup Stripe Account Connect
     *
     */
    'GET /api/stripe/upd-availability': 'StripeController.updateAvailability',

    /**
     *
     * @api {get} /stripe/express_acc_connect Stripe Connect Express Account Creation Handler
     * @apiDescription Handles stripe express connected creation redirect request
     * @apiGroup Stripe Account Connect
     *
     */
    'GET /stripe/express_acc_connect': 'StripeController.handleExpressAccConnection',
}
