
module.exports = {
    /**
     *
     * @api {get} /api/sales/event/:event/ticket/:code/payment/info Payment Info
     * @apiDescription Returns ticket payment information
     * @apiGroup Sales Event Tickets
     *
     */
    'get /api/sales/event/:event/ticket/:code/payment/info': __getPath('paymentInfo'),

    /**
     *
     * @api {post} /api/sales/event/:event/ticket/:code/payment/update Payment Info Update
     * @apiDescription Updates ticket payment information
     * @apiGroup Sales Event Tickets
     *
     */
    'post /api/sales/event/:event/ticket/:code/payment/update': __getPath('updatePaymentInfo'),

    /**
     *
     * @api {post} /api/sales/event/:event/ticket/:code/action/:action Deactivate Ticket Barcode
     * @apiDescription Deactivates ticket barcode
     * @apiGroup Sales Event Tickets
     *
     */
    'post /api/sales/event/:event/ticket/:code/action/:action': __getPath('deactivateTicketBarcode'),

    /**
     *
     * @api {get} /api/sales/event/:event/ticket/:code/resend Resend Ticket Receipt
     * @apiDescription Resends ticket receipt
     * @apiGroup Sales Event Tickets
     *
     */
    'get /api/sales/event/:event/ticket/:code/resend': __getPath('resendTicketReceipt'),
}

function __getPath(action) {
    return ('SalesManager/TicketsController.' + action);
}
