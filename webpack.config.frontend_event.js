const path = require('node:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const glob = require('glob');

const entryFiles = glob.sync([
    './frontend_event/sport-wrench.module.js',
    './frontend_event/**/*.js',
    './assets/stylesEsw/main.scss',
], { absolute: true });

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: entryFiles,

    plugins: [
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend_event/', from: '**/*.html', to: '[path][name][ext]' },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8078,
    },
};
